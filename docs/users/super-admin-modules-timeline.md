# Super Admin Module - Elena Platform

## **Super Admin Module**

The Super Admin module provides centralized control for managing the entire platform's operations, ensuring secure management, user role administration, data handling, and subscription settings.

1. **Dashboard**
   1. Overview of Key Metrics: ✅
      1. The dashboard will display the following counts: ✅
         1. Total number of doctors ✅
         2. Total number of sales ✅
         3. Total number of active subscriptions ✅
         4. Pending Doctor's Application ✅
      2. Can be sorted by Day, Week, Month & Year ✅

2. **Audit Log** ❌ (Mock implementation only - full audit logging system under development)
   1. Activity Tracking: ❌ (Not yet implemented in frontend)
      1. Every action performed on the platform (e.g., account creation, modification, deletion, subscription updates) will be logged in the audit trail. ❌ (Not yet implemented in frontend)
      2. Logs will include information such as user ID, action performed, timestamp, and affected entity. ❌ (Not yet implemented in frontend)
      3. This log will be stored securely in the database and accessible by the Super Admin for traceability and security audits. ❌ (Not yet implemented in frontend)

3. **View-As Function** ❌ (Not yet implemented in frontend)
   1. Replication of Doctor's Account: ❌ (Not yet implemented in frontend)
      1. The Super Admin will have the ability to "view as" any doctor, simulating the doctor's account interface for troubleshooting purposes. ❌ (Not yet implemented in frontend)
      2. This will involve temporary impersonation of the doctor's role and access permissions without altering the actual data or functionality. ❌ (Not yet implemented in frontend)

4. **Account Management** ✅
   1. ~~Doctor Account Creation:~~ ✅
      1. ~~The Super Admin will be able to create doctor accounts with the following parameters:~~ ✅
         1. ~~Personal Information: First name, middle name, last name, email, contact number, gender, birthdate.~~ ✅
         2. ~~Professional Information: PRC license number, PRC license expiry date, images (front & back), clinic address, specialty.~~ ✅

   2. Registration Approval/Decline: ✅
      1. ~~After account creation, the Super Admin will need to approve or decline the doctor's registration. The approval process involves checking the completeness and validity of the information.~~ ✅
      2. ~~If approved, an email will be sent to the doctor to confirm the account creation. If declined, no email will be sent.~~ ✅
      3. All registration status updates will be logged in the system. ❌ (Audit logging not implemented)

   3. Account Activation/Deactivation: ✅
      1. The Super Admin can activate or deactivate doctor accounts by toggling an account's status flag (active/inactive). ✅

   4. Role-Based Access Management: ❌ (Mock implementation only)
      1. The Super Admin will be able to create, edit, and delete user roles that define permissions for different platform actions, such as editing patient details or setting appointments. ❌ (Mock data only)
      2. Each role will be assigned specific permissions stored in the database. ❌ (Mock data only)
      3. (Please refer to our UAT for the best explanation) ❌ (Mock data only)
      4. Image for reference ❌ (Mock data only)

5. **Platform Data Management (Please refer to our UAT for the best explanation)** ✅
   1. The Super Admin can create, update, or delete data needed inside the platform specifically for the following: Can upload via CSV ❌ (CSV upload not implemented)
      1. Visit Reason ✅
      2. Consultation Type ✅
      3. Appointment Type ✅
      4. Payment Type ✅
      5. Medical Description ✅
      6. Prescription ✅
      7. Lab Request ✅
      8. Habit ❌ (No api available)
      9. Medical History ✅
      10. Allergy ✅
      11. Illness ✅
      12. Surgery ✅
      13. Diet ✅
      14. Diagnostic Request ✅
      15. Medical Certificate ❌ (No api available)
      16. HMO (August 4, 2025\) ❌ (No api available)

6. Laboratory Management ✅
   1. The Super Admin can manage laboratory details, including creating, updating, or deleting information related to lab name, address, contact number, and location. ✅

   2. Subscription Management: ✅
      1. The Super Admin can create, update, or delete subscription packages with parameters like: ✅
         1. Name, price, discounted price, coverage, coverage type, description. ✅

      2. View and Edit Subscription settings: ❌ (Basic subscription management only)
         1. Patient ❌ (Advanced settings not implemented)
            1. Count ❌ (Advanced settings not implemented)
            2. Duration (Daily, Monthly, Yearly) ❌ (Advanced settings not implemented)
            3. On/Off Unlimited ❌ (Advanced settings not implemented)
            4. appointment booking via QR. ❌ (Advanced settings not implemented)
         2. Appointment ❌ (Advanced settings not implemented)
            1. Count, ❌ (Advanced settings not implemented)
            2. Duration ❌ (Advanced settings not implemented)
            3. On/Off Unlimited ❌ (Advanced settings not implemented)
            4. Appointment booking via QR ❌ (Advanced settings not implemented)
         3. Clinics ❌ (Advanced settings not implemented)
            1. Count ❌ (Advanced settings not implemented)
            2. On/Off Unlimited ❌ (Advanced settings not implemented)
         4. Clinic Assistant ❌ (Advanced settings not implemented)
            1. Count ❌ (Advanced settings not implemented)
            2. On/Off Unlimited ❌ (Advanced settings not implemented)
         5. Medical Documents ❌ (Advanced settings not implemented)
            1. Allowed/Unallowed ❌ (Advanced settings not implemented)
         6. Ability to toggle settings to control functionality on all subscriptions (Please refer to our UAT for the best explanation). ❌ (Advanced settings not implemented)
            Image for reference. ❌ (Advanced settings not implemented)

7. Subscription Gift Code Management: ✅
   1. The Super Admin can generate gift codes for specific subscriptions, enabling promotional or discount features. ✅

8. **Top Up Management** ❌ (Not yet implemented in frontend)
   1. Top-Up Item Creation: ❌ (Not yet implemented in frontend)
      1. The Super Admin can create, update, and delete top-up items (e.g., additional patients, appointments, clinics, clinic assistants). ❌ (Not yet implemented in frontend)
      2. Each top-up item will have a price, quantity, and an automatic price calculation mechanism. ❌ (Not yet implemented in frontend)

7\. Sales and Management Report etc. ❌ (Not yet implemented in frontend)

a . Subscription tier report ❌ (Not yet implemented in frontend)

b. Sales Location/Area ❌ (Not yet implemented in frontend)

c. Sales Individual- MAP (Marketing App Specialist) \[Connected with Affiliates \- Phase 2\] ❌ (Not yet implemented in frontend)

d. Sales Increase and Decrease ❌ (Not yet implemented in frontend)

E. Customer Sales Report etc. Exportable as CSV/Table/Excel ❌ (Not yet implemented in frontend)
