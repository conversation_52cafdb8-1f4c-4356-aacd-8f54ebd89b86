# Doctor User Module - Elena Platform

## **Doctor User Module: Registration and Onboarding Flow**

1. **Registration Process** ✅
   1. Registration Fields: ✅
      1. ~~First Name~~ ✅
      2. ~~Middle Name~~ ✅
      3. ~~Last Name~~ ✅
      4. ~~Suffix~~ ✅
      5. ~~Email Address~~ ✅
      6. ~~Contact Number~~ ✅
      7. ~~Gender~~ ✅
      8. ~~Birthdate~~ ✅
      9. ~~PRC License Number~~ ✅
      10. ~~PRC License Expiry Date~~ ✅
      11. ~~PRC Image Front (upload)~~ ✅
      12. ~~PRC Image Back (upload)~~ ✅
      13. ~~Clinic Address (Optional)~~ ✅
      14. ~~Specialty (Optional)~~ ✅

   2. Super Admin Approval: ✅
      1. ~~Upon registration, the doctor's details will be submitted to the Super Admin for approval.~~ ✅
      2. ~~Super Admin will review and approve or decline the registration. Approval will trigger the email verification process.~~ ✅

   3. Post-Approval Email: ✅
      1. ~~Once approved by the Super Admin, the doctor will receive a confirmation email.~~ ✅
      2. ~~The email will contain a "Verify Email" link. The doctor must click the link to verify their email address.~~ ✅
      3. After email verification, the doctor can proceed to set up their login credentials (username and password). ✅

2. **Profile Management (Upon First Login)** ✅
   1. Login and Password Reset: ✅
      1. Upon the first login, the doctor will be prompted to reset their password. ✅
      2. The option to skip the password reset will be available, but this is not recommended for security purposes. ✅

   2. Quick Setup Wizard: ❌ (Not implemented as guided wizard)
      1. After logging in, the doctor will be guided through a setup wizard for profile and clinic management. ❌ (Not implemented as guided wizard)

      2. Step 1: Profile Management: ✅
         1. The doctor will be prompted to update their profile: ✅
            1. Account Address: Ability to update their personal address. ✅
            2. Upload Signature: Option to upload a digital signature (for medical documents purposes). ✅
            3. Specialty: Optional – can skip updating the specialty if not required at the moment. ✅
            4. Prescription Data Management (Pre assigned data for the medical document) ✅
               1. Include preview toggle details to be displayed in medical document ✅
                  1. Name ✅
                  2. Specialty ✅
                  3. Sub Specialty ✅
                  4. Affiliation. ✅
               2. \* ADD Medical Documents Setting- To enhance doctors medical documents centralized personal details for print outs like prescription, med cert etc ✅

3. **Clinic Management** ✅
   1. Step 2: Clinic Setup: ✅
      1. Create and Update Clinic Details: ✅
         1. Clinic Name: The name of the clinic. ✅
         2. Clinic Address: The physical address of the clinic. ✅
         3. Assistant Management (Optional): Option to add clinic assistants (can skip at this stage). ✅
         4. Clinic Number: The contact number for the clinic. ✅
         5. Clinic Email: Official email address of the clinic. ✅
            Clinic Website: Link to the clinic's website. ✅
         6. Date Established: The year the clinic was established. ✅
         7. Clinic Schedule: Specify operating hours for Monday to Friday Sunday. Include placeholders for start time and end time for each day ✅
            (Please refer to our UAT for the best explanation). ✅
         8. Image for reference ❌ (Not implemented)

      2. Google Map Module: ✅
         1. The doctor can pin the Google Maps module for easy clinic location identification. ✅
            (Please refer to our UAT for the best explanation) ✅

4. **Clinic Assistant Management** ✅
   1. Step 3: Clinic Assistant Account Creation: ✅
      1. Doctors can create accounts for clinic assistants: ✅
         1. Username ✅
         2. Password ✅
         3. No Email Address is required ✅
         4. First Name ✅
         5. Last Name ✅
      2. No email verification is required for clinic assistants. ✅

5. **Assigning Clinic Assistant to the Clinic** ✅
   1. Step 4: Assign Clinic Assistant: ✅
      1. After the clinic assistant's account has been created, the doctor can assign the assistant to the clinic. ✅
      2. The assignment will link the assistant's profile to the clinic. ✅
      3. Assign desired clinic assistant user role ( a default can be added for ease) ❌ (Basic role assignment only)
         1. Notice on Disclaimer \[Default assistant access\] ❌ (Basic role assignment only)
         2. View all. ❌ (Basic role assignment only)
         3. Add appointment ❌ (Basic role assignment only)
         4. Edit vital signs ❌ (Basic role assignment only)

6. **Final Step: Dashboard and Welcome** ✅
   1. Step 5: Dashboard: ✅
      1. Upon completing all the steps, the doctor will land on the dashboard. ✅
      2. A welcome message will appear with a prompt: ❌ (Welcome message not implemented)
         1. "You're good to go\! Create your first appointment now with a free trial from Elena\!" ❌ (Welcome message not implemented)
      3. The doctor will have immediate access to the platform's primary features, including the ability to schedule appointments, manage patients, and more. ✅
      4. The default number for free users are the following (if avail Free Trial and Expires) ❌ (Free trial limits not enforced in frontend)
         1. One (1) Clinic ❌ (Free trial limits not enforced in frontend)
         2. One (1) Clinic Assistant ❌ (Free trial limits not enforced in frontend)
         3. Ten (10) Accumulative Patients Monthly ❌ (Free trial limits not enforced in frontend)
         4. Ten (10) Accumulative Appointments Daily ❌ (Free trial limits not enforced in frontend)
            1. Anything more than this will require the doctor to either top up or subscribe. ❌ (Free trial limits not enforced in frontend)

### **User Flow Registration Overview (Doctor):** ✅

1. Doctor Registration (Fields: First name, last name, email, PRC license, etc.) ✅
   → Super Admin Approval ✅
   → Email Verification ✅
2. Login: ✅
   → Password Reset (Optional) ✅
3. Profile Setup (Update address, upload signature, specialty, etc.) ✅
4. Clinic Setup: ✅
   → Create Clinic (Address, schedule, assistant management, Google Map integration) ✅
5. Clinic Assistant Management: ✅
   → Create Assistant Account (No email and verification required) ✅
6. Assign Assistant to Clinic: ✅
   → Assign a clinic assistant to a specific clinic. ✅
7. Dashboard Access: ✅
   → First-time login complete, access to core platform features. ✅

## **Doctor User Flow: Dashboard and Appointment Management**

1. **Doctor's Dashboard Overview** ✅
   1. Upon logging in, the Doctor's Dashboard provides an overview of key metrics and appointment management tools. ✅
      1. Total Appointments: Displays the total number of appointments scheduled for the day. ✅
      2. Total Attended: Displays the number of consultations attended to by the doctor. ✅
      3. Registered Patients: Displays the total number of registered patients in the system(doctor's entire clinic). ✅

   2. Appointment Queue Window ✅
      1. This section displays the doctor's appointment queue and provides filtering options for effective management. ✅
         1. Default View: ✅
            1. Displays all appointments across all clinics. ✅
            2. Option to filter by specific clinic via a dropdown (select a clinic to view only appointments from that clinic). ✅

         2. Search Functionality: ✅
            1. Doctors can search by patient name to locate specific appointments. ✅

      2. Appointments for the Day ✅
         1. Only appointments for the current day will appear in this window. ✅
         2. Upcoming appointments will not be included. ✅

      3. Rearrange Appointments: ❌ (Not yet implemented in frontend)
         1. Doctors can rearrange appointments within the day to adjust the order based on priority or availability. ❌ (Not yet implemented in frontend)

2. **Appointment Creation Process** ✅
   1. Doctors can create a new appointment directly from the Appointment Queue Window with the following steps: ✅

   2. Create Appointment Fields: ✅
      1. Select Clinic: Dropdown selection of clinics (from the clinics created under the doctor's profile). ✅
      2. Select Patient: Dropdown of patients (or option to create a new patient if no previous record exists). ✅
      3. Visit Reason: Select from predefined visit reasons (from Data Management). ✅
      4. Consultation Type: Select from predefined consultation types (from Data Management). ✅
      5. Appointment Type: Select from predefined appointment types (from Data Management). ✅
      6. HMO: Select from predefined HMO options (from Data Management). ❌ (HMO not yet implemented)
      7. Payment Type: Select from predefined payment types (from Data Management). ✅
      8. Request for Medical Certificate: Toggle on/off (whether a medical certificate is required for the visit). ✅
      9. Set Appointment Date and Time: Specify the date and time for the appointment. ✅

   3. Successful Booking Consultation Appointment: ✅
      1. The appointment will be reflected on: ✅
         1. Doctor's Appointment Dashboard: The doctor's list of scheduled appointments. ✅
         2. Clinic Assistant's Dashboard ✅ ✅ ✅: The clinic assistant will have visibility over the appointment. ✅
         3. Patient's Appointment Schedule: The patient will receive an update on their appointment schedule. ❌ (Patient dashboard not fully implemented)

3. **Patient Record Creation (If No Previous Record)** ✅
   1. If the patient is new or has no previous record in the system, the doctor will be prompted to create a patient record. ✅

   2. Patient Record Fields: ✅
      1. Select Clinic: Clinic selection (from clinics associated with the doctor). ✅
      2. Title/Salutation: Patient's title (e.g., Mr., Mrs., Dr.). ✅
      3. First Name ✅
      4. Middle Name ✅
      5. Last Name ✅
      6. Suffix ✅
      7. Nationality ✅
      8. Gender ✅
      9. Contact Number ✅
      10. Birthday ✅
      11. Occupation ✅
      12. Civil Status ✅
      13. Emergency Contact Person ✅
      14. Emergency Contact Number ✅
      15. Create Account: Toggle on/off for creating an account for the patient (if toggled on, an email will be sent to the patient for account verification). ✅
      16. Patient Profile completion ✅
          1. Avatar/Patient photo ✅
          2. Address ✅
             1. Current ✅
             2. Permanent ✅
             3. Blood type: Select from blood type option ✅
                1. Add blood type- Rh null ✅

   3. Email Verification: ✅
      1. If the account creation toggle is on, an email will be sent to the patient containing: ✅
         1. Email Verification Link. ✅
         2. Login Credentials (username and password for the patient to access their account). ✅

4. **Patient Record (If Previous Record Exists)** ✅
   1. If the patient already exists in the system, the doctor will need to link the patient to the new appointment either by clicking on the drop down or scanning the qr generated by the patient and/or the doctor. ✅
      1. If scanning is successful, the patient section will be auto-filled into the appointment creation screen. ✅

5. **Retrievable Data** ✅
   1. All records of the patient can be reflected on their account regardless of when they are registered or not. Patients will have their own username upon creating an appointment and will be listed as registered patients as data continuity from the start. ✅

6. **Attended Consultation List** ✅
   1. Once the appointment has been completed, it will appear in the Attended Consultation Window. ✅
      1. Completed Appointments: ✅
         1. Lists all attended consultations for the day. ✅
      2. End-of-Day Reset: ❌ (Not implemented - manual reset required)
         1. The appointment queue window and attended consultation list will reset every midnight for the new day's appointments. This ensures the system is prepared for the next day's schedule. ❌ (Not implemented - manual reset required)

### **User Flow for Appointment Management:** ✅

1. Dashboard: ✅
   1. View total appointments, total attended, and registered patients. ✅
2. Appointment Queue: ✅
   1. View appointments for the current day. ✅
   2. Filter by clinic or search by patient name. ✅
   3. Rearrange appointments for the day. ❌ (Not yet implemented in frontend)
3. Create Appointment: ✅
   1. Select clinic, patient, visit reason, consultation type, etc. ✅
   2. Option to create a new patient if no previous record exists. ✅
4. Patient Record Creation: ✅
   1. If there is no previous record, create a patient with detailed information. ✅
   2. Send an email for account verification (if applicable). ✅
5. Patient Record (Existing): ✅
   1. If the patient exists, scan the QR code to add them to the appointment or manually select on the drop down. ✅
   2. All Records (personal information,medical documents and consultation appointment time/date details)of the patient will be reflected on the patient's account upon registration. ✅
6. Appointment Confirmation: ✅
   1. Successful booking of the consultation appointment with updates reflected in the doctor's dashboard, clinic assistant's dashboard, and patient's appointment schedule all showing on the calendar module. ✅
7. Attended Consultation: ✅
   1. List of completed consultations for the day. ✅
   2. Reset appointment queue at midnight for the next day. ❌ (Not implemented - manual reset required)

## **Doctor : Appointment attending and Management \[Time Stamp for Everything\]** ✅

Upon creating or viewing an appointment, the following actions are available: ✅

1. **Appointment Status** ✅
   1. Appointment Rescheduling and Follow Up: ✅
      1. After initial creation, doctors can reschedule or schedule a follow-up by modifying the required fields. ✅
         1. Date ✅
         2. Time ✅
      2. Upon successful scheduling, the consultation appointment will be reflected in: ✅
         1. Doctor's Dashboard ✅
         2. Clinic Assistant's Dashboard
         3. Patient's Appointment Schedule ❌ (Patient dashboard not fully implemented)
         4. Calendar Module ✅

   2. Update Appointment Status: ✅
      1. Doctors can update the consultation appointment status using the following predefined options: ✅
         1. Confirmed (default upon creation) ✅
         2. Declined ✅
         3. Waiting ✅
         4. Ongoing ✅
         5. Completed ✅
         6. No Show ✅
         7. Cancelled ✅

      2. The updated status will be reflected in: ✅
         1. Appointment Window on the Doctor's Dashboard ✅
         2. Clinic Assistant's Dashboard
         3. Patient's Appointment Schedule

2. **Attending Patient** ✅
   1. When a doctor attends to a patient during the consultation, they can update various medical details, including vital signs and clinical notes until the end of the day. ✅
      1. Edit Patient's Medical Information: ✅
      2. Chief Complaint: ✅
      3. Diagnosis: ✅
      4. Prognosis: ✅
      5. Doctor's Note:with time stamp ✅

   2. Vitals: ✅
      1. BP (Blood Pressure) ✅
      2. Pulse Rate ✅
      3. Respiration Rate ✅
      4. Height: Height remains persistent for adult patients, but may change for growing patients. ✅
      5. Weight ✅
      6. Temperature ✅
      7. Oxygen Saturation ✅
      8. Capillary Blood Glucose ✅
      9. BMI (Body Mass Index) ✅
         1. Note on Persistence: ✅
            1. Height remains unchanged for adults unless there's a significant change, like a medical condition that affects growth. ✅

   3. Assistant's Notes: ✅
      1. The assistant can leave notes in the assistant's note section. However, the doctor cannot edit the assistant's notes, and vice versa.( For integrity and in cases of multiple Clinic assistants made the notes should be included in the timestamp acronym or C.Assistant code will do) ✅

   4. Upload Medical History ✅

   5. Appointment History Comparison: ❌ (Not yet implemented in frontend)
      1. A dropdown list allows the doctor to compare previous appointments with the current consultation. ❌ (Not yet implemented in frontend)
      2. The doctor can select two past appointments and view their records side by side. ❌ (Not yet implemented in frontend)

3. **Prescription Tab** ✅
   1. Doctors can create, update, and delete prescriptions for the patient during the appointment. ✅
   2. \*Previous /recent Prescription view and option to reuse- to ease up prescription writing ❌ (Not yet implemented in frontend)
   3. Prescription Fields: If possible to monitor most used/preferred to offer it as dropdown predefined list/option system wide. ✅
      1. Medicine (from predefined list): Dropdown list pulled from the medicines database and option (\*add new prescription) adding new Generic (\*add a new generic drug) by MD which is immediately added to the clinic list of generics available in dropdown. ✅
      2. \*Brand-optional/not required ✅
      3. Dosage Form ✅
      4. Dosage ✅
      5. Frequency ✅
      6. Quantity ✅
      7. Instruction ✅

   4. Prescription Record: ✅
      1. After completing the prescription, the details will be saved to the appointment record and made available for viewing/printing.(editing valid within the consultation day; add prescription option maybe use after) ✅
      2. Prescription Image: Doctors can view and print the complete prescription, including the image of the prescription (if applicable). ✅

4. **Lab Requests Tab** ✅
   1. Doctors can create, update, and delete, add lab requests based on the patient's needs. ✅
      1. Lab Request Fields: ✅
      2. Lab Test (from predefined list): Dropdown list of available lab tests pulled from Data Management and/or added by MD that will reflect immediately in the clinic dropdownlist. ✅
      3. Lab Result Upload: Doctors can upload any lab results provided by the patient for data keeping. ✅
   2. Lab Request Record: ✅
      1. Once the lab request is completed, it will be stored in the appointment record and can be viewed, printed, and downloaded. ✅

5. **Diagnostic Requests Tab** ✅
   1. Doctors can manage diagnostic requests for the patient. ✅
   2. Diagnostic Request Fields: ✅
      1. Diagnostic Tests (from predefined list): Dropdown list of diagnostic tests (e.g., X-rays, CT scans) pulled from Data Management. ✅
      2. New Diagnostic Request: Doctors can add new diagnostic tests that will reflect immediately in the clinic dropdown list and Data Management (\*as option/suggestion to ELENA Admin). ✅
   3. Diagnostic Request Record: ✅
      1. After adding a diagnostic request, it will be saved to the appointment record and can be printed, downloaded, and viewed. ✅

6. **Medical Certificate Tab** ✅
   1. Doctors can create, update, and delete medical certificates for patients. ✅
   2. Medical Certificate Fields: ✅
      1. Certificate Type (e.g., sick leave, fitness for work, etc.): Dropdown list pulled from Data Management and others to add new types. ✅
      2. Doctor's Note: Additional notes on the patient's medical condition as it pertains to the certificate. ✅
   3. Medical Certificate Record: ✅
      1. After generating the certificate, it will be stored in the appointment record, and the doctor can view, download, and print the certificate. ✅

7. **Referral Tab** ✅
   1. Doctors can create and update referrals to other medical professionals or specialists. ✅
   2. Referral Fields: ✅
      1. Doctor's Name
      2. Purpose of Referral
   3. Referral Record: ✅
      1. Once the referral is created, it will be stored in the appointment record of the new doctor and can be viewed, downloaded, and printed by the doctor. ✅
      2. \*Medical HIstory Records consist of Personal information, Medical Documents (prescriptions, lab result, diagnostic test result if any) once the Patient gives consent and added to the New MD within ELENA via \*QR Code scan. ❌ (QR code medical history sharing not implemented)

8. **Appointment Locking Mechanism** ❌ (Not implemented - appointments remain editable)
   1. To maintain security and prevent unauthorized changes, the appointment details will be locked after EOD. ❌ (Not implemented - appointments remain editable)
   2. Locking Mechanism: ❌ (Not implemented - appointments remain editable)
      1. After EOD, the appointment details will be locked to ensure that no changes can be made. ❌ (Not implemented - appointments remain editable)
   3. Exceptions: ❌ (Not implemented - appointments remain editable)
      1. Medical Records: New medical records, including lab results, diagnostic reports, or prescriptions, can still be added by the doctor if required. ❌ (Not implemented - appointments remain editable)
      2. Medical Certificates: A new certificate can be issued based on the doctor's discretion, but the original appointment details cannot be modified. ❌ (Not implemented - appointments remain editable)
      3. Doctors' notes can be added as needed in order for the MD to record recent findings in relation to the most current consultation. ❌ (Not implemented - appointments remain editable)

### **User Flow for Appointment Management:** ✅

1. Follow-up/Reschedule: ✅
   1. Schedule follow-ups or reschedule using date/time fields. ✅
   2. Updated information reflects in all dashboards and calendars. ✅
2. Status Update: ✅
   1. Update appointment status (Confirmed, Declined, etc.) in the appointment window. ✅
3. Attending Patient: ✅
   1. Update medical information (vitals, diagnosis, doctor's notes, etc.). ✅
   2. Assistants and doctors have separate note sections. ✅
4. Prescriptions: ✅
   1. Create/update/delete prescriptions for patients. Prescription is stored in the appointment record. ✅
5. Lab Requests: ✅
   1. Create, update, and store lab requests in the appointment record. ✅
6. Diagnostic Requests: ✅
   1. Create and manage diagnostic tests and save them to the appointment. ✅
7. Medical Certificates: ✅
   1. Generate and store medical certificates for the patient. ✅
8. Referral: ✅
   1. Create and store referrals to other doctors. ✅
9. Locking Mechanism: ❌ (Not implemented - appointments remain editable)
   1. After 24 hoursEnd of the day, appointment details are locked, but additional medical documents/ records (e.g., prescriptions, certificates) can be added such docs will be uneditable at the end each day. ❌ (Not implemented - appointments remain editable)
10. Completed Appointments ✅
    1. All completed appointments will be reflected on the patients records le and can be viewed in the history tab ✅

### **Medical Document Module Overview \[Timestamp for Everything\]** ✅

The Medical Document Module enables doctors to create and manage medical documents such as prescriptions, lab requests, diagnostic requests, referrals, and medical certificates outside the scope of a specific consultation appointment. This module focuses on handling repeat prescriptions, immediate lab requests, and medical certifications that don't require an formal actual consultation appointment. It simplifies the process of issuing necessary medical documents for existing patients or new patients when urgent actions are needed. ✅

1. **Medical Document Creation** ✅
   1. Doctors can create medical documents by selecting the appropriate document type and filling in the required details. ✅
      1. Create New Document: ✅
         1. Select Document Type: ✅
            1. Prescription ✅
            2. Lab Request ✅
            3. Diagnostic Request ✅
            4. Referral ✅
            5. Medical Certificate ✅

      2. Select Clinic: ✅
         1. Dropdown list to select the doctor's clinic. ✅
      3. Patient Selection: ✅
         1. If the patient already exists in the system: ✅
            1. Select Patient: Dropdown list of existing patients. ✅
            2. Auto-fill: Patient's details (name, contact number, address, etc.) will be automatically populated. ✅

      4. If the patient is not in the system
         1. First Name
         2. \*Middle Name
         3. Last Name
         4. \*Suffix
         5. Gender
         6. Contact Number
         7. Address
         8. Birthday

            \*not required

      5. After creating a new patient or selecting an existing patient, the doctor can view the patient's details for the medical document. Then proceed with the same procedure above in medical documents. Can also be viewed and printed.

      6. This will be reflected in the patient's (if existing) records upon successful creation of medical documents. Otherwise, all creation and logs should be kept safeguarded for document tracing.

### **History Tab** ✅

Can view all previous appointments and all its details along with medical documents here. ✅

### **Calendar Tab** ✅

Can view all appointments in calendar form. ✅

### **Patient Tab Module** ✅

1. **Patient Profile Management** ✅
   1. Patient Onboard- Add New Patient (Manual onboarding) ✅
      1. Select Clinic with dropdown option ✅
      2. Title/Salutation ✅
      3. \*First Name ✅
      4. Middle Name ✅
      5. \*Last Name ✅
      6. Suffix ✅
      7. \*Nationality ✅
      8. \*Gender ✅
      9. \*Contact Number ✅
      10. \*Birth Date ✅
      11. Occupation ✅
      12. \*Civil Status ✅
      13. Emergency Contact Number ✅
      14. Emergency Contact Person ✅
      15. Toggle Option- For Patient to create an ELENA account ✅
          1. Email Address ✅
             \*Required Field ✅
   2. Update Patient Profile: ✅
      1. Editable Fields: ✅
      2. Contact Number ✅
      3. \*Current Address- Required ✅
      4. Permanent Address ✅
      5. Birthdate ✅
      6. Emergency Contact Name ✅
      7. Emergency Contact Number ✅
      8. Blood Type- add RH Null in the dropdown option ✅
      9. Save Changes: Once updated, the changes will reflect in the patient's profile and medical history. ✅

   3. Archive Patient: ❌ (Not yet implemented in frontend)
      1. Archive Option: ❌ (Not yet implemented in frontend)
         1. The doctor can choose to archive a patient if they are no longer active or need to be removed from the main patients list. ❌ (Not yet implemented in frontend)
         2. Upon archiving, the patient will be: ❌ (Not yet implemented in frontend)
            1. Removed from the active patient tab. ❌ (Not yet implemented in frontend)
            2. Moved to the Archive Management. ❌ (Not yet implemented in frontend)
            3. Data will still be viewable and retrievable in Archive Management. ❌ (Not yet implemented in frontend)
         3. The archived patient will not appear in dropdown lists when selecting patients for new appointments or creating medical documents, but the data will remain stored for future reference. ❌ (Not yet implemented in frontend)

2. **Medical and Personal History** ✅
   1. Add/Update Medical History: ✅
      1. Doctors can add or update the following medical information from Data Management: ✅
         1. Habits ✅
         2. History ✅
         3. Allergies ✅
         4. Illnesses ✅
         5. Surgeries ✅
         6. Diet ✅

3. **Recent Vital Signs** ✅
   1. Display Latest Vital Signs: ✅
      1. The most recent vital signs recorded during the latest appointment will be displayed in the patient's profile.
      2. These vital signs will be pulled from the latest attended appointment for the patient.

4. **Medical Certificate Overview** ✅
   1. Display Medical Certificate: ✅
      1. If the patient has any medical certificates issued by the doctor:
         1. View Medical Certificate: The certificate will be displayed in the patient's profile.
         2. Timestamp: Each medical certificate will have a timestamp showing when it was created.

5. **HMO and Family Information** ❌ (HMO not yet implemented)
   1. Add HMO: ❌ (HMO not yet implemented)
      1. Doctors can add or update HMO (Health Maintenance Organization) information for the patient, which will be pulled from Data Management.

   2. Add Family Information: ❌ (Family linking not yet implemented)
      1. The doctor can link the patient to their family members in the system by selecting family members from a dropdown list of existing patients. ❌ (Family linking not yet implemented)

6. **Check-Up History** ✅
   1. Displays all the previous appointments the patient has had, pulled from the appointment records. ✅
   2. The history is shown as a list, where doctors can easily see the dates and types of check-ups, along with links to detailed records of those appointments. ✅
   3. Compare module Apply here ❌ (Comparison module not yet implemented)

Clinic Management ✅

Setting up for day to day clinic operations, overview of all clinics and its clinic assistant activity. ✅

1. Add new clinic ✅
2. Clinic list ✅

### **Archive Management Tab** ❌ (Not yet implemented in frontend)

All archived patient and medical document is here, can still view for document tracking and security ❌ (Not yet implemented in frontend)

### **Subscription and Top-Up Tab Module Overview** ❌ (Not yet implemented in frontend)

1. **Subscription History** ❌ (Not yet implemented in frontend)
   1. View Subscription History: ❌ (Not yet implemented in frontend)
      1. Doctors can view a history of their subscriptions. ❌ (Not yet implemented in frontend)
      2. Information displayed includes: ❌ (Not yet implemented in frontend)
         1. Subscription Plan: The plan the doctor is currently subscribed to. ❌ (Not yet implemented in frontend)
         2. Start and End Dates: The duration of the subscription. ❌ (Not yet implemented in frontend)
         3. Payment History: Details about previous payments made, including the amount and transaction date. ❌ (Not yet implemented in frontend)

2. **Subscription Plans and Top-Up Options** ❌ (Not yet implemented in frontend)
   1. Available Subscription Plans: ❌ (Not yet implemented in frontend)
      1. Doctors can view a list of available subscription plans with details about each plan's features and pricing. (Which will be the subscription management) ❌ (Not yet implemented in frontend)
3. Top-Up Options: ❌ (Not yet implemented in frontend)
   1. Doctors can also top up for specific services like: ❌ (Not yet implemented in frontend)
      1. Additional patients (above the monthly limit). ❌ (Not yet implemented in frontend)
      2. Additional appointments (above the monthly limit). ❌ (Not yet implemented in frontend)
      3. Extra clinics or clinic assistants. ❌ (Not yet implemented in frontend)

4. **Subscription/Top-Up Purchase Process** ❌ (Not yet implemented in frontend)
   1. Doctors can select a subscription plan or top up and proceed with purchasing it. ❌ (Not yet implemented in frontend)
   2. They can make the payment through the payment gateway integrated into the platform. ❌ (Not yet implemented in frontend)

5. **Free Account Limitations** ❌ (Not enforced in frontend)
   1. Free accounts are limited to the following: ❌ (Not enforced in frontend)
      1. 1 Clinic: Doctors can only manage one clinic under a free account. ❌ (Not enforced in frontend)
      2. 1 Clinic Assistant: Free accounts can only have one clinic assistant. ❌ (Not enforced in frontend)
      3. 10 Additional Patients per Month: Doctors can only add up to 10 additional patients per month without subscribing or topping up. ❌ (Not enforced in frontend)
      4. 10 Additional Appointments per Month: Free accounts are limited to scheduling up to 10 additional appointments per month. ❌ (Not enforced in frontend)

   2. Upgrade/Top-Up Reminder: ❌ (Not enforced in frontend)
      1. Doctors using a free account will be reminded that they need to upgrade their plan or top-up if they exceed these limits. ❌ (Not enforced in frontend)
      2. A notification or alert will be shown if they try to exceed the limits (e.g., adding more patients, clinics, or appointments). ❌ (Not enforced in frontend)

### **Settings Tab** ✅ Change Password ✅

**\*Help and Information (Redirection to exclusive FAQ page can be within or outside ELENA but accessible to public)** ❌ (Not yet implemented in frontend)

### **Clinic Assistant Tab (For Discussion)** ✅ Displays all Clinic Assistant of Doctors All access to the doctor's account is replicated in the clinic assistant account except from the subscription. This will now be limited upon the doctor's or super admin's discretion. Which can be toggled on user roles in this tab. Special Note : Still retain that Doctors can't input on assistants notes and vice versa ✅

## **Summary of User Flow \[Doctor\]:** ✅

**End to End** ✅

1. Doctor Account Creation → Account approval, email verification, profile setup, clinic setup and assign clinic assistant.
2. Doctor Dashboard → View metrics and appointments for the day.
3. Appointment Creation → Choose clinic, patient, and set appointment details reflect on calendars and all users involved.
4. Attend Appointment → Update patient info, generate medical documents, and set appointment status.
5. Follow-Up or Reschedule → Schedule follow-up appointments or adjust existing ones.
6. Complete Appointment → Mark the appointment as completed and lock details after 24at end of the day hours. Only Allowing additional new recordsnewrecords (e.g. doctor's note,Lab request/Result, additional prescription etc.)
7. Subscription Management → If the doctor exceeds the free plan's limits, subscribe or top-up for additional services.

**Other Aspects** ✅

1. Medical Document → Request Medical Documents without proper actual clinic appointments
2. History → List of all patients appointments.
3. Archive Management → List of all archived medical documents and patients
4. Patients → list of all patients and their profile upon viewing.
