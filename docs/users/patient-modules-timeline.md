# Patient User Module - Elena Platform

## **Patient User Module**

1. **~~Registration Process~~** ✅
   1. ~~Registration Fields:~~
      1. ~~Title~~
      2. ~~First Name~~
      3. ~~Middle Name~~
      4. ~~Last Name~~
      5. ~~Suffix~~
      6. ~~Email Address~~
      7. ~~Contact Number~~
      8. ~~Gender~~
      9. ~~Occupation~~
      10. ~~Birthdate~~
      11. ~~Civil Status~~
      12. ~~Current Address~~
          1. ~~Province~~
          2. ~~Town/City~~
          3. ~~Barangay~~
          4. ~~Street Name~~
          5. ~~Lot/Block/Phase/Street No~~
          6. ~~Unit/Room/Floor/Building No~~
          7. ~~Subdivision/Compound/Village/Zone~~
          8. ~~Building/Condominium/Apartment~~
      13. ~~Permanent Address \- Same as above Toggle~~
          1. ~~Province~~
          2. ~~Town/City~~
          3. ~~Barangay~~
          4. ~~Street Name~~
          5. ~~Lot/Block/Phase/Street No~~
          6. ~~Unit/Room/Floor/Building No~~
          7. ~~Subdivision/Compound/Village/Zone~~
          8. ~~Building/Condominium/Apartment~~
      14. ~~Emergency Details~~
          1. ~~Emergency Contact Name~~
          2. ~~Emergency Contact Number~~
      15. ~~Basic Medical Information~~
          1. ~~Blood Type~~
          2. ~~Religion~~
          3. ~~Height~~
             1. ~~Feet~~
             2. ~~Inches~~
          4. ~~Height Type~~
             1. ~~Feet~~
             2. ~~Centimeter~~
          5. ~~Weight~~
             1. ~~Kilogram~~
             2. ~~Pounds~~
          6. ~~Weight Type~~
             1. ~~Kilogram~~
             2. ~~Pounds~~
          7. ~~Clinic(For discussion)~~
             1. ~~Drop Down from Clinics~~
             2. ~~Limited to One Only~~
      16. ~~Toggle for Minor/Elderly/PWD Assistance~~
          1. ~~Guardian Information~~
             1. ~~First Name~~
             2. ~~Middle Name~~
             3. ~~Last Name~~
             4. ~~Contact Number~~
             5. ~~Email~~
             6. ~~Birthdatey~~

   2. ~~Email Confirmation:~~ ✅
      1. ~~The patient will receive a confirmation email.~~
      2. ~~The email will contain a "Verify Email" link. The patientdoctor must click the link to verify their email address.~~
      3. After email verification, the patient can proceed to set up their login credentials (username and password).

2. **Profile Management (Any changes should reflect on Doctor and CA View)** ✅
   1. Update Password (If first login) ✅
   2. Update Profile: ✅
      1. Editable Fields: ✅
      2. Contact Number ✅
      3. Address ✅
      4. Permanent Address ✅
      5. Birthdate ✅
      6. Emergency Contact Name ✅
      7. Emergency Contact Number ✅
      8. Blood Type ✅
      9. Save Changes: Once updated, the changes will reflect in the patient's profile and medical history. This should also be reflected in the doctor and clinic assistant's view. ✅

3. **Medical and Personal History** ❌ (Placeholder implementation only)
   1. Add/Update Medical History: ❌ (Placeholder implementation only)
      1. Patient can add or update the following medical information from Data Management: ❌ (Placeholder implementation only)
         1. Habits ❌ (Placeholder implementation only)
         2. History ❌ (Placeholder implementation only)
         3. Allergies ❌ (Placeholder implementation only)
         4. Illnesses ❌ (Placeholder implementation only)
         5. Surgeries ❌ (Placeholder implementation only)
         6. Diet ❌ (Placeholder implementation only)

4. **Recent Vital Signs** ❌ (Placeholder implementation only)
   1. Display Latest Vital Signs: ❌ (Placeholder implementation only)
      1. The most recent vital signs recorded during the latest appointment will be displayed in the patient's profile. ❌ (Placeholder implementation only)
      2. These vital signs will be pulled from the latest attended appointment for the patient. ❌ (Placeholder implementation only)

5. **Medical Certificate Overview** ❌ (Placeholder implementation only)
   1. Display Medical Certificate: ❌ (Placeholder implementation only)
      1. If the patient has any medical certificates issued by the doctor: ❌ (Placeholder implementation only)
         1. View Medical Certificate: The certificate will be displayed in the patient's profile. ❌ (Placeholder implementation only)
         2. Timestamp: Each medical certificate will have a timestamp showing when it was created. ❌ (Placeholder implementation only)

6. **HMO and Family Information** ❌ (Placeholder implementation only)
   1. Add HMO: ❌ (Placeholder implementation only)
      1. Doctors can add or update HMO (Health Maintenance Organization) information for the patient, which will be pulled from Data Management. ❌ (Placeholder implementation only)

   2. Add Family Information: ❌ (Placeholder implementation only)
      1. The doctor can link the patient to their family members in the system by selecting family members from a dropdown list of existing patients. ❌ (Placeholder implementation only)

7. **Check-Up History** ❌ (Placeholder implementation only)
   1. Displays all the previous appointments the patient has had, pulled from the appointment records. ❌ (Placeholder implementation only)
   2. The history is shown as a list, where doctors can easily see the dates and types of check-ups, along with links to detailed records of those appointments. ❌ (Placeholder implementation only)
   3. Compare module Apply here ❌ (Placeholder implementation only)

### **Patient's "My Appointments" Tab User Flow** ❌ (Placeholder implementation only)

8. View Upcoming Appointments ❌ (Placeholder implementation only)
   1. The patient can view their upcoming appointments, including: ❌ (Placeholder implementation only)
      1. Date and Time of the appointment. ❌ (Placeholder implementation only)
      2. Consultation Type. ❌ (Placeholder implementation only)
      3. Status. ❌ (Placeholder implementation only)
         1. Confirmed, Declined, Waiting, Ongoing, Completed, No Show, Cancelled. ❌ (Placeholder implementation only)

9. View Appointment History ❌ (Placeholder implementation only)
   1. Check Past Appointments: ❌ (Placeholder implementation only)
   2. The patient can see all their previous appointments along with the medical documents during that appointment ❌ (Placeholder implementation only)
   3. The patient can view and download or print the documents (e.g., prescriptions, medical certificates). ❌ (Placeholder implementation only)

10. Reschedule or Follow Up ❌ (Placeholder implementation only)
    1. If the doctor changes or add follow up check ups, this should update and provide another upcoming appointment for the patient ❌ (Placeholder implementation only)

11. Medical Documents ❌ (Placeholder implementation only)
    1. View Medical Documents: ❌ (Placeholder implementation only)
       1. All medical documents associated with an appointment (e.g., prescriptions, lab results, diagnostic tests, medical certificates, etc.) will be displayed here. ❌ (Placeholder implementation only)
       2. The patient can view and download or print the documents (e.g., prescriptions, medical certificates). ❌ (Placeholder implementation only)

12. Documents History: ❌ (Placeholder implementation only)
    1. Patients can see a list of all issued medical certificates and prescriptions with timestamps. ❌ (Placeholder implementation only)

### **Summary of Features for Patient User Module:**

13. Registration \-\> Email Confirmation \-\> Verify \-\> Login ✅
14. Change Password \-\> Update Profile ✅
15. View Upcoming Appointments: Patients can see future appointments with details on the date, time, and status. ❌ (Placeholder implementation only)
16. Appointment History: Patients can view all past appointments, including treatments and prescriptions. Patients can view, download, and print documents such as prescriptions, medical certificates, and test results. ❌ (Placeholder implementation only)
17. Appointment Status: The current status of the appointment (e.g., confirmed, completed, cancelled). ❌ (Placeholder implementation only)
18. Reschedule/Follow Up: Patients view the appointment of a reschedule or follow-up will automatically be updated on their end. ❌ (Placeholder implementation only)
19. Medical Documents: Patients can view, download, and print documents such as prescriptions, medical certificates, and test results. ❌ (Placeholder implementation only)
