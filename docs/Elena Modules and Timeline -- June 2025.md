## **Super Admin Module**

The Super Admin module provides centralized control for managing the entire platform's operations, ensuring secure management, user role administration, data handling, and subscription settings.

1. **Dashboard**
   1. Overview of Key Metrics: ✅
      1. The dashboard will display the following counts: ✅
         1. Total number of doctors ✅
         2. Total number of sales ✅
         3. Total number of active subscriptions ✅
         4. Pending Doctor’s Application ✅
      2. Can be sorted by Day, Week, Month & Year ❌ (Not yet implemented in frontend)

2. **Audit Log** ❌ (Mock implementation only - full audit logging system under development)
   1. Activity Tracking: ❌ (Not yet implemented in frontend)
      1. Every action performed on the platform (e.g., account creation, modification, deletion, subscription updates) will be logged in the audit trail. ❌ (Not yet implemented in frontend)
      2. Logs will include information such as user ID, action performed, timestamp, and affected entity. ❌ (Not yet implemented in frontend)
      3. This log will be stored securely in the database and accessible by the Super Admin for traceability and security audits. ❌ (Not yet implemented in frontend)

3. **View-As Function** ❌ (Not yet implemented in frontend)
   1. Replication of Doctor’s Account: ❌ (Not yet implemented in frontend)
      1. The Super Admin will have the ability to "view as" any doctor, simulating the doctor’s account interface for troubleshooting purposes. ❌ (Not yet implemented in frontend)
      2. This will involve temporary impersonation of the doctor’s role and access permissions without altering the actual data or functionality. ❌ (Not yet implemented in frontend)

4. **Account Management** ✅
   1. ~~Doctor Account Creation:~~ ✅
      1. ~~The Super Admin will be able to create doctor accounts with the following parameters:~~ ✅
         1. ~~Personal Information: First name, middle name, last name, email, contact number, gender, birthdate.~~ ✅
         2. ~~Professional Information: PRC license number, PRC license expiry date, images (front & back), clinic address, specialty.~~ ✅

   2. Registration Approval/Decline: ✅
      1. ~~After account creation, the Super Admin will need to approve or decline the doctor’s registration. The approval process involves checking the completeness and validity of the information.~~ ✅
      2. ~~If approved, an email will be sent to the doctor to confirm the account creation. If declined, no email will be sent.~~ ✅
      3. All registration status updates will be logged in the system. ❌ (Audit logging not implemented)

   3. Account Activation/Deactivation: ✅
      1. The Super Admin can activate or deactivate doctor accounts by toggling an account’s status flag (active/inactive). ✅

   4. Role-Based Access Management: ❌ (Mock implementation only)
      1. The Super Admin will be able to create, edit, and delete user roles that define permissions for different platform actions, such as editing patient details or setting appointments. ❌ (Mock data only)
      2. Each role will be assigned specific permissions stored in the database. ❌ (Mock data only)
      3. (Please refer to our UAT for the best explanation) ❌ (Mock data only)
      4. Image for reference ❌ (Mock data only)

5. **Platform Data Management (Please refer to our UAT for the best explanation)** ✅
   1. The Super Admin can create, update, or delete data needed inside the platform specifically for the following: Can upload via CSV ❌ (CSV upload not implemented)
      1. Visit Reason ✅
      2. Consultation Type ✅
      3. Appointment Type ✅
      4. Payment Type ✅
      5. Medical Description ✅
      6. Prescription ❌ (Not in admin data management)
      7. Lab Request ❌ (Not in admin data management)
      8. Habit ❌ (Not in admin data management)
      9. Medical History ❌ (Not in admin data management)
      10. Allergy ✅
      11. Illness ❌ (Not in admin data management)
      12. Surgery ❌ (Not in admin data management)
      13. Diet ❌ (Not in admin data management)
      14. Diagnostic Request ❌ (Not in admin data management)
      15. Medical Certificate ❌ (Not in admin data management)
      16. HMO (August 4, 2025\) ❌ (Not yet implemented)

6. Laboratory Management ✅
   1. The Super Admin can manage laboratory details, including creating, updating, or deleting information related to lab name, address, contact number, and location. ✅

   2. Subscription Management: ✅
      1. The Super Admin can create, update, or delete subscription packages with parameters like: ✅
         1. Name, price, discounted price, coverage, coverage type, description. ✅

      2. View and Edit Subscription settings: ❌ (Basic subscription management only)
         1. Patient ❌ (Advanced settings not implemented)
            1. Count ❌ (Advanced settings not implemented)
            2. Duration (Daily, Monthly, Yearly) ❌ (Advanced settings not implemented)
            3. On/Off Unlimited ❌ (Advanced settings not implemented)
            4. appointment booking via QR. ❌ (Advanced settings not implemented)
         2. Appointment ❌ (Advanced settings not implemented)
            1. Count, ❌ (Advanced settings not implemented)
            2. Duration ❌ (Advanced settings not implemented)
            3. On/Off Unlimited ❌ (Advanced settings not implemented)
            4. Appointment booking via QR ❌ (Advanced settings not implemented)
         3. Clinics ❌ (Advanced settings not implemented)
            1. Count ❌ (Advanced settings not implemented)
            2. On/Off Unlimited ❌ (Advanced settings not implemented)
         4. Clinic Assistant ❌ (Advanced settings not implemented)
            1. Count ❌ (Advanced settings not implemented)
            2. On/Off Unlimited ❌ (Advanced settings not implemented)
         5. Medical Documents ❌ (Advanced settings not implemented)
            1. Allowed/Unallowed ❌ (Advanced settings not implemented)
         6. Ability to toggle settings to control functionality on all subscriptions (Please refer to our UAT for the best explanation). ❌ (Advanced settings not implemented)
            Image for reference. ❌ (Advanced settings not implemented)

7. Subscription Gift Code Management: ✅
   1. The Super Admin can generate gift codes for specific subscriptions, enabling promotional or discount features. ✅

8. **Top Up Management** ❌ (Not yet implemented in frontend)
   1. Top-Up Item Creation: ❌ (Not yet implemented in frontend)
      1. The Super Admin can create, update, and delete top-up items (e.g., additional patients, appointments, clinics, clinic assistants). ❌ (Not yet implemented in frontend)
      2. Each top-up item will have a price, quantity, and an automatic price calculation mechanism. ❌ (Not yet implemented in frontend)

7\. Sales and Management Report etc. ❌ (Not yet implemented in frontend)

a . Subscription tier report ❌ (Not yet implemented in frontend)

b. Sales Location/Area ❌ (Not yet implemented in frontend)

c. Sales Individual- MAP (Marketing App Specialist) \[Connected with Affiliates \- Phase 2\] ❌ (Not yet implemented in frontend)

d. Sales Increase and Decrease ❌ (Not yet implemented in frontend)

E. Customer Sales Report etc. Exportable as CSV/Table/Excel ❌ (Not yet implemented in frontend)

## **Platform Requirements**

1. **Payment Gateway Integration** ❌ (Not yet implemented in frontend)
   1. Subscription and Top-Up Payments: ❌ (Not yet implemented in frontend)
   2. The system will integrate with a third-party Payment Gateway to handle payments for subscriptions and top-ups. ❌ (Not yet implemented in frontend)
   3. Payment details will be securely stored and processed, including the price calculation for each subscription and top-up item. ❌ (Not yet implemented in frontend)

2. **Google Map Integration** ✅
   1. Clinic locations can be pin pointed in the Google Maps for easy identification and direction. ✅

3. **Appointment Scheduling** ✅
   1. Calendar Management: ✅
      1. The Calendar Module will allow the Doctors to manage appointment schedules, ensuring availability and booking. ✅
      2. This will include features like daily, weekly, and monthly views of appointments, with the ability to add, edit, or delete scheduled appointments. ✅

4. **Email Newsletter Management** ❌ (Not yet implemented in frontend)
   1. Email Newsletter System: ❌ (Not yet implemented in frontend)
      1. The platform will include a system for sending periodic email newsletters to doctors and patients for registrations (This will be provided by the client) ❌ (Not yet implemented in frontend)

5. **Responsiveness** ✅
   1. Should be responsive on different devices \- Mobile, Tablet & Desktop. ✅
      1. Specific resolutions will be provided for certain devices ✅

## **Doctor User Module: Registration and Onboarding Flow**

1. **Registration Process** ✅
   1. Registration Fields: ✅
      1. ~~First Name~~ ✅
      2. ~~Middle Name~~ ✅
      3. ~~Last Name~~ ✅
      4. ~~Suffix~~ ✅
      5. ~~Email Address~~ ✅
      6. ~~Contact Number~~ ✅
      7. ~~Gender~~ ✅
      8. ~~Birthdate~~ ✅
      9. ~~PRC License Number~~ ✅
      10. ~~PRC License Expiry Date~~ ✅
      11. ~~PRC Image Front (upload)~~ ✅
      12. ~~PRC Image Back (upload)~~ ✅
      13. ~~Clinic Address (Optional)~~ ✅
      14. ~~Specialty (Optional)~~ ✅

   2. Super Admin Approval: ✅
      1. ~~Upon registration, the doctor’s details will be submitted to the Super Admin for approval.~~ ✅
      2. ~~Super Admin will review and approve or decline the registration. Approval will trigger the email verification process.~~ ✅

   3. Post-Approval Email: ✅
      1. ~~Once approved by the Super Admin, the doctor will receive a confirmation email.~~ ✅
      2. ~~The email will contain a "Verify Email" link. The doctor must click the link to verify their email address.~~ ✅
      3. After email verification, the doctor can proceed to set up their login credentials (username and password). ✅

2. **Profile Management (Upon First Login)** ✅
   1. Login and Password Reset: ✅
      1. Upon the first login, the doctor will be prompted to reset their password. ✅
      2. The option to skip the password reset will be available, but this is not recommended for security purposes. ✅

   2. Quick Setup Wizard: ❌ (Not implemented as guided wizard)
      1. After logging in, the doctor will be guided through a setup wizard for profile and clinic management. ❌ (Not implemented as guided wizard)

      2. Step 1: Profile Management: ✅
         1. The doctor will be prompted to update their profile: ✅
            1. Account Address: Ability to update their personal address. ✅
            2. Upload Signature: Option to upload a digital signature (for medical documents purposes). ✅
            3. Specialty: Optional – can skip updating the specialty if not required at the moment. ✅
            4. Prescription Data Management (Pre assigned data for the medical document) ✅
               1. Include preview toggle details to be displayed in medical document ✅
                  1. Name ✅
                  2. Specialty ✅
                  3. Sub Specialty ✅
                  4. Affiliation. ✅
               2. \* ADD Medical Documents Setting- To enhance doctors medical documents centralized personal details for print outs like prescription, med cert etc ✅

3. **Clinic Management** ✅
   1. Step 2: Clinic Setup: ✅
      1. Create and Update Clinic Details: ✅
         1. Clinic Name: The name of the clinic. ✅
         2. Clinic Address: The physical address of the clinic. ✅
         3. Assistant Management (Optional): Option to add clinic assistants (can skip at this stage). ✅
         4. Clinic Number: The contact number for the clinic. ✅
         5. Clinic Email: Official email address of the clinic. ✅
            Clinic Website: Link to the clinic's website. ✅
         6. Date Established: The year the clinic was established. ✅
         7. Clinic Schedule: Specify operating hours for Monday to Friday Sunday. Include placeholders for start time and end time for each day ✅
            (Please refer to our UAT for the best explanation). ✅
         8. Image for reference ❌ (Not implemented)

      2. Google Map Module: ✅
         1. The doctor can pin the Google Maps module for easy clinic location identification. ✅
            (Please refer to our UAT for the best explanation) ✅

4. **Clinic Assistant Management** ✅
   1. Step 3: Clinic Assistant Account Creation: ✅
      1. Doctors can create accounts for clinic assistants: ✅
         1. Username ✅
         2. Password ✅
         3. No Email Address is required ✅
         4. First Name ✅
         5. Last Name ✅
      2. No email verification is required for clinic assistants. ✅

5. **Assigning Clinic Assistant to the Clinic** ✅
   1. Step 4: Assign Clinic Assistant: ✅
      1. After the clinic assistant’s account has been created, the doctor can assign the assistant to the clinic. ✅
      2. The assignment will link the assistant’s profile to the clinic. ✅
      3. Assign desired clinic assistant user role ( a default can be added for ease) ❌ (Basic role assignment only)
         1. Notice on Disclaimer \[Default assistant access\] ❌ (Basic role assignment only)
         2. View all. ❌ (Basic role assignment only)
         3. Add appointment ❌ (Basic role assignment only)
         4. Edit vital signs ❌ (Basic role assignment only)

6. **Final Step: Dashboard and Welcome** ✅
   1. Step 5: Dashboard: ✅
      1. Upon completing all the steps, the doctor will land on the dashboard. ✅
      2. A welcome message will appear with a prompt: ❌ (Welcome message not implemented)
         1. "You're good to go\! Create your first appointment now with a free trial from Elena\!" ❌ (Welcome message not implemented)
      3. The doctor will have immediate access to the platform's primary features, including the ability to schedule appointments, manage patients, and more. ✅
      4. The default number for free users are the following (if avail Free Trial and Expires) ❌ (Free trial limits not enforced in frontend)
         1. One (1) Clinic ❌ (Free trial limits not enforced in frontend)
         2. One (1) Clinic Assistant ❌ (Free trial limits not enforced in frontend)
         3. Ten (10) Accumulative Patients Monthly ❌ (Free trial limits not enforced in frontend)
         4. Ten (10) Accumulative Appointments Daily ❌ (Free trial limits not enforced in frontend)
            1. Anything more than this will require the doctor to either top up or subscribe. ❌ (Free trial limits not enforced in frontend)

### **User Flow Registration Overview (Doctor):** ✅

1. Doctor Registration (Fields: First name, last name, email, PRC license, etc.) ✅
   → Super Admin Approval ✅
   → Email Verification ✅
2. Login: ✅
   → Password Reset (Optional) ✅
3. Profile Setup (Update address, upload signature, specialty, etc.) ✅
4. Clinic Setup: ✅
   → Create Clinic (Address, schedule, assistant management, Google Map integration) ✅
5. Clinic Assistant Management: ✅
   → Create Assistant Account (No email and verification required) ✅
6. Assign Assistant to Clinic: ✅
   → Assign a clinic assistant to a specific clinic. ✅
7. Dashboard Access: ✅
   → First-time login complete, access to core platform features. ✅

## **Doctor User Flow: Dashboard and Appointment Management**

1. **Doctor's Dashboard Overview** ✅
   1. Upon logging in, the Doctor’s Dashboard provides an overview of key metrics and appointment management tools. ✅
      1. Total Appointments: Displays the total number of appointments scheduled for the day. ✅
      2. Total Attended: Displays the number of consultations attended to by the doctor. ✅
      3. Registered Patients: Displays the total number of registered patients in the system(doctor’s entire clinic). ✅

   2. Appointment Queue Window ✅
      1. This section displays the doctor’s appointment queue and provides filtering options for effective management. ✅
         1. Default View: ✅
            1. Displays all appointments across all clinics. ✅
            2. Option to filter by specific clinic via a dropdown (select a clinic to view only appointments from that clinic). ✅

         2. Search Functionality: ✅
            1. Doctors can search by patient name to locate specific appointments. ✅

      2. Appointments for the Day ✅
         1. Only appointments for the current day will appear in this window. ✅
         2. Upcoming appointments will not be included. ✅

      3. Rearrange Appointments: ❌ (Not yet implemented in frontend)
         1. Doctors can rearrange appointments within the day to adjust the order based on priority or availability. ❌ (Not yet implemented in frontend)

2. **Appointment Creation Process** ✅
   1. Doctors can create a new appointment directly from the Appointment Queue Window with the following steps: ✅

   2. Create Appointment Fields: ✅
      1. Select Clinic: Dropdown selection of clinics (from the clinics created under the doctor’s profile). ✅
      2. Select Patient: Dropdown of patients (or option to create a new patient if no previous record exists). ✅
      3. Visit Reason: Select from predefined visit reasons (from Data Management). ✅
      4. Consultation Type: Select from predefined consultation types (from Data Management). ✅
      5. Appointment Type: Select from predefined appointment types (from Data Management). ✅
      6. HMO: Select from predefined HMO options (from Data Management). ❌ (HMO not yet implemented)
      7. Payment Type: Select from predefined payment types (from Data Management). ✅
      8. Request for Medical Certificate: Toggle on/off (whether a medical certificate is required for the visit). ✅
      9. Set Appointment Date and Time: Specify the date and time for the appointment. ✅

   3. Successful Booking Consultation Appointment: ✅
      1. The appointment will be reflected on: ✅
         1. Doctor's Appointment Dashboard: The doctor’s list of scheduled appointments. ✅
         2. Clinic Assistant's Dashboard ✅ ✅ ✅: The clinic assistant will have visibility over the appointment. ✅
         3. Patient’s Appointment Schedule: The patient will receive an update on their appointment schedule. ❌ (Patient dashboard not fully implemented)

3. **Patient Record Creation (If No Previous Record)** ✅
   1. If the patient is new or has no previous record in the system, the doctor will be prompted to create a patient record. ✅

   2. Patient Record Fields: ✅
      1. Select Clinic: Clinic selection (from clinics associated with the doctor). ✅
      2. Title/Salutation: Patient’s title (e.g., Mr., Mrs., Dr.). ✅
      3. First Name ✅
      4. Middle Name ✅
      5. Last Name ✅
      6. Suffix ✅
      7. Nationality ✅
      8. Gender ✅
      9. Contact Number ✅
      10. Birthday ✅
      11. Occupation ✅
      12. Civil Status ✅
      13. Emergency Contact Person ✅
      14. Emergency Contact Number ✅
      15. Create Account: Toggle on/off for creating an account for the patient (if toggled on, an email will be sent to the patient for account verification). ✅
      16. Patient Profile completion ✅
          1. Avatar/Patient photo ✅
          2. Address ✅
             1. Current ✅
             2. Permanent ✅
             3. Blood type: Select from blood type option ✅
                1. Add blood type- Rh null ✅

   3. Email Verification: ✅
      1. If the account creation toggle is on, an email will be sent to the patient containing: ✅
         1. Email Verification Link. ✅
         2. Login Credentials (username and password for the patient to access their account). ✅

4. **Patient Record (If Previous Record Exists)** ✅
   1. If the patient already exists in the system, the doctor will need to link the patient to the new appointment either by clicking on the drop down or scanning the qr generated by the patient and/or the doctor. ✅
      1. If scanning is successful, the patient section will be auto-filled into the appointment creation screen. ✅

5. **Retrievable Data** ✅
   1. All records of the patient can be reflected on their account regardless of when they are registered or not. Patients will have their own username upon creating an appointment and will be listed as registered patients as data continuity from the start. ✅

6. **Attended Consultation List** ✅
   1. Once the appointment has been completed, it will appear in the Attended Consultation Window. ✅
      1. Completed Appointments: ✅
         1. Lists all attended consultations for the day. ✅
      2. End-of-Day Reset: ❌ (Not implemented - manual reset required)
         1. The appointment queue window and attended consultation list will reset every midnight for the new day's appointments. This ensures the system is prepared for the next day’s schedule. ❌ (Not implemented - manual reset required)

### **User Flow for Appointment Management:** ✅

1. Dashboard: ✅
   1. View total appointments, total attended, and registered patients. ✅
2. Appointment Queue: ✅
   1. View appointments for the current day. ✅
   2. Filter by clinic or search by patient name. ✅
   3. Rearrange appointments for the day. ❌ (Not yet implemented in frontend)
3. Create Appointment: ✅
   1. Select clinic, patient, visit reason, consultation type, etc. ✅
   2. Option to create a new patient if no previous record exists. ✅
4. Patient Record Creation: ✅
   1. If there is no previous record, create a patient with detailed information. ✅
   2. Send an email for account verification (if applicable). ✅
5. Patient Record (Existing): ✅
   1. If the patient exists, scan the QR code to add them to the appointment or manually select on the drop down. ✅
   2. All Records (personal information,medical documents and consultation appointment time/date details)of the patient will be reflected on the patient’s account upon registration. ✅
6. Appointment Confirmation: ✅
   1. Successful booking of the consultation appointment with updates reflected in the doctor’s dashboard, clinic assistant’s dashboard, and patient’s appointment schedule all showing on the calendar module. ✅
7. Attended Consultation: ✅
   1. List of completed consultations for the day. ✅
   2. Reset appointment queue at midnight for the next day. ❌ (Not implemented - manual reset required)

## **Doctor : Appointment attending and Management \[Time Stamp for Everything\]** ✅

Upon creating or viewing an appointment, the following actions are available: ✅

1. **Appointment Status** ✅
   1. Appointment Rescheduling and Follow Up: ✅
      1. After initial creation, doctors can reschedule or schedule a follow-up by modifying the required fields. ✅
         1. Date ✅
         2. Time ✅
      2. Upon successful scheduling, the consultation appointment will be reflected in: ✅
         1. Doctor’s Dashboard ✅
         2. Clinic Assistant’s Dashboard
         3. Patient’s Appointment Schedule ❌ (Patient dashboard not fully implemented)
         4. Calendar Module ✅

   2. Update Appointment Status: ✅
      1. Doctors can update the consultation appointment status using the following predefined options: ✅
         1. Confirmed (default upon creation) ✅
         2. Declined ✅
         3. Waiting ✅
         4. Ongoing ✅
         5. Completed ✅
         6. No Show ✅
         7. Cancelled ✅

      2. The updated status will be reflected in: ✅
         1. Appointment Window on the Doctor's Dashboard ✅
         2. Clinic Assistant’s Dashboard
         3. Patient’s Appointment Schedule

2. **Attending Patient** ✅
   1. When a doctor attends to a patient during the consultation, they can update various medical details, including vital signs and clinical notes until the end of the day. ✅
      1. Edit Patient’s Medical Information: ✅
      2. Chief Complaint: ✅
      3. Diagnosis: ✅
      4. Prognosis: ✅
      5. Doctor’s Note:with time stamp ✅

   2. Vitals: ✅
      1. BP (Blood Pressure) ✅
      2. Pulse Rate ✅
      3. Respiration Rate ✅
      4. Height: Height remains persistent for adult patients, but may change for growing patients. ✅
      5. Weight ✅
      6. Temperature ✅
      7. Oxygen Saturation ✅
      8. Capillary Blood Glucose ✅
      9. BMI (Body Mass Index) ✅
         1. Note on Persistence: ✅
            1. Height remains unchanged for adults unless there's a significant change, like a medical condition that affects growth. ✅

   3. Assistant's Notes: ✅
      1. The assistant can leave notes in the assistant's note section. However, the doctor cannot edit the assistant’s notes, and vice versa.( For integrity and in cases of multiple Clinic assistants made the notes should be included in the timestamp acronym or C.Assistant code will do) ✅

   4. Upload Medical History ✅

   5. Appointment History Comparison: ❌ (Not yet implemented in frontend)
      1. A dropdown list allows the doctor to compare previous appointments with the current consultation. ❌ (Not yet implemented in frontend)
      2. The doctor can select two past appointments and view their records side by side. ❌ (Not yet implemented in frontend)

3. **Prescription Tab** ✅
   1. Doctors can create, update, and delete prescriptions for the patient during the appointment. ✅
   2. \*Previous /recent Prescription view and option to reuse- to ease up prescription writing ❌ (Not yet implemented in frontend)
   3. Prescription Fields: If possible to monitor most used/preferred to offer it as dropdown predefined list/option system wide. ✅
      1. Medicine (from predefined list): Dropdown list pulled from the medicines database and option (\*add new prescription) adding new Generic (\*add a new generic drug) by MD which is immediately added to the clinic list of generics available in dropdown. ✅
      2. \*Brand-optional/not required ✅
      3. Dosage Form ✅
      4. Dosage ✅
      5. Frequency ✅
      6. Quantity ✅
      7. Instruction ✅

   4. Prescription Record: ✅
      1. After completing the prescription, the details will be saved to the appointment record and made available for viewing/printing.(editing valid within the consultation day; add prescription option maybe use after) ✅
      2. Prescription Image: Doctors can view and print the complete prescription, including the image of the prescription (if applicable). ✅

4. **Lab Requests Tab** ✅
   1. Doctors can create, update, and delete, add lab requests based on the patient’s needs. ✅
      1. Lab Request Fields: ✅
      2. Lab Test (from predefined list): Dropdown list of available lab tests pulled from Data Management and/or added by MD that will reflect immediately in the clinic dropdownlist. ✅
      3. Lab Result Upload: Doctors can upload any lab results provided by the patient for data keeping. ✅
   2. Lab Request Record: ✅
      1. Once the lab request is completed, it will be stored in the appointment record and can be viewed, printed, and downloaded. ✅

5. **Diagnostic Requests Tab** ✅
   1. Doctors can manage diagnostic requests for the patient. ✅
   2. Diagnostic Request Fields: ✅
      1. Diagnostic Tests (from predefined list): Dropdown list of diagnostic tests (e.g., X-rays, CT scans) pulled from Data Management. ✅
      2. New Diagnostic Request: Doctors can add new diagnostic tests that will reflect immediately in the clinic dropdown list and Data Management (\*as option/suggestion to ELENA Admin). ✅
   3. Diagnostic Request Record: ✅
      1. After adding a diagnostic request, it will be saved to the appointment record and can be printed, downloaded, and viewed. ✅

6. **Medical Certificate Tab** ✅
   1. Doctors can create, update, and delete medical certificates for patients. ✅
   2. Medical Certificate Fields: ✅
      1. Certificate Type (e.g., sick leave, fitness for work, etc.): Dropdown list pulled from Data Management and others to add new types. ✅
      2. Doctor’s Note: Additional notes on the patient's medical condition as it pertains to the certificate. ✅
   3. Medical Certificate Record: ✅
      1. After generating the certificate, it will be stored in the appointment record, and the doctor can view, download, and print the certificate. ✅

7. **Referral Tab** ✅
   1. Doctors can create and update referrals to other medical professionals or specialists. ✅
   2. Referral Fields: ✅
      1. Doctor’s Name
      2. Purpose of Referral
   3. Referral Record: ✅
      1. Once the referral is created, it will be stored in the appointment record of the new doctor and can be viewed, downloaded, and printed by the doctor. ✅
      2. \*Medical HIstory Records consist of Personal information, Medical Documents (prescriptions, lab result, diagnostic test result if any) once the Patient gives consent and added to the New MD within ELENA via \*QR Code scan. ❌ (QR code medical history sharing not implemented)

8. **Appointment Locking Mechanism** ❌ (Not implemented - appointments remain editable)
   1. To maintain security and prevent unauthorized changes, the appointment details will be locked after EOD. ❌ (Not implemented - appointments remain editable)
   2. Locking Mechanism: ❌ (Not implemented - appointments remain editable)
      1. After EOD, the appointment details will be locked to ensure that no changes can be made. ❌ (Not implemented - appointments remain editable)
   3. Exceptions: ❌ (Not implemented - appointments remain editable)
      1. Medical Records: New medical records, including lab results, diagnostic reports, or prescriptions, can still be added by the doctor if required. ❌ (Not implemented - appointments remain editable)
      2. Medical Certificates: A new certificate can be issued based on the doctor's discretion, but the original appointment details cannot be modified. ❌ (Not implemented - appointments remain editable)
      3. Doctors' notes can be added as needed in order for the MD to record recent findings in relation to the most current consultation. ❌ (Not implemented - appointments remain editable)

### **User Flow for Appointment Management:** ✅

1. Follow-up/Reschedule: ✅
   1. Schedule follow-ups or reschedule using date/time fields. ✅
   2. Updated information reflects in all dashboards and calendars. ✅
2. Status Update: ✅
   1. Update appointment status (Confirmed, Declined, etc.) in the appointment window. ✅
3. Attending Patient: ✅
   1. Update medical information (vitals, diagnosis, doctor’s notes, etc.). ✅
   2. Assistants and doctors have separate note sections. ✅
4. Prescriptions: ✅
   1. Create/update/delete prescriptions for patients. Prescription is stored in the appointment record. ✅
5. Lab Requests: ✅
   1. Create, update, and store lab requests in the appointment record. ✅
6. Diagnostic Requests: ✅
   1. Create and manage diagnostic tests and save them to the appointment. ✅
7. Medical Certificates: ✅
   1. Generate and store medical certificates for the patient. ✅
8. Referral: ✅
   1. Create and store referrals to other doctors. ✅
9. Locking Mechanism: ❌ (Not implemented - appointments remain editable)
   1. After 24 hoursEnd of the day, appointment details are locked, but additional medical documents/ records (e.g., prescriptions, certificates) can be added such docs will be uneditable at the end each day. ❌ (Not implemented - appointments remain editable)
10. Completed Appointments ✅
    1. All completed appointments will be reflected on the patients records le and can be viewed in the history tab ✅

    ### **Medical Document Module Overview \[Timestamp for Everything\]** ✅

The Medical Document Module enables doctors to create and manage medical documents such as prescriptions, lab requests, diagnostic requests, referrals, and medical certificates outside the scope of a specific consultation appointment. This module focuses on handling repeat prescriptions, immediate lab requests, and medical certifications that don’t require an formal actual consultation appointment. It simplifies the process of issuing necessary medical documents for existing patients or new patients when urgent actions are needed. ✅

1. **Medical Document Creation** ✅
   1. Doctors can create medical documents by selecting the appropriate document type and filling in the required details. ✅
      1. Create New Document: ✅
         1. Select Document Type: ✅
            1. Prescription ✅
            2. Lab Request ✅
            3. Diagnostic Request ✅
            4. Referral ✅
            5. Medical Certificate ✅

      2. Select Clinic: ✅
         1. Dropdown list to select the doctor’s clinic. ✅
      3. Patient Selection: ✅
         1. If the patient already exists in the system: ✅
            1. Select Patient: Dropdown list of existing patients. ✅
            2. Auto-fill: Patient’s details (name, contact number, address, etc.) will be automatically populated. ✅

      4. If the patient is not in the system
         1. First Name
         2. \*Middle Name
         3. Last Name
         4. \*Suffix
         5. Gender
         6. Contact Number
         7. Address
         8. Birthday

            \*not required

      5. After creating a new patient or selecting an existing patient, the doctor can view the patient's details for the medical document. Then proceed with the same procedure above in medical documents. Can also be viewed and printed.

      6. This will be reflected in the patient’s (if existing) records upon successful creation of medical documents. Otherwise, all creation and logs should be kept safeguarded for document tracing.

   ### **History Tab** ✅

Can view all previous appointments and all its details along with medical documents here. ✅

### **Calendar Tab** ✅

Can view all appointments in calendar form. ✅

### **Patient Tab Module** ✅

1. **Patient Profile Management** ✅
   1. Patient Onboard- Add New Patient (Manual onboarding) ✅
      1. Select Clinic with dropdown option ✅
      2. Title/Salutation ✅
      3. \*First Name ✅
      4. Middle Name ✅
      5. \*Last Name ✅
      6. Suffix ✅
      7. \*Nationality ✅
      8. \*Gender ✅
      9. \*Contact Number ✅
      10. \*Birth Date ✅
      11. Occupation ✅
      12. \*Civil Status ✅
      13. Emergency Contact Number ✅
      14. Emergency Contact Person ✅
      15. Toggle Option- For Patient to create an ELENA account ✅
          1. Email Address ✅
             \*Required Field ✅
   2. Update Patient Profile: ✅
      1. Editable Fields: ✅
      2. Contact Number ✅
      3. \*Current Address- Required ✅
      4. Permanent Address ✅
      5. Birthdate ✅
      6. Emergency Contact Name ✅
      7. Emergency Contact Number ✅
      8. Blood Type- add RH Null in the dropdown option ✅
      9. Save Changes: Once updated, the changes will reflect in the patient’s profile and medical history. ✅

   3. Archive Patient: ❌ (Not yet implemented in frontend)
      1. Archive Option: ❌ (Not yet implemented in frontend)
         1. The doctor can choose to archive a patient if they are no longer active or need to be removed from the main patients list. ❌ (Not yet implemented in frontend)
         2. Upon archiving, the patient will be: ❌ (Not yet implemented in frontend)
            1. Removed from the active patient tab. ❌ (Not yet implemented in frontend)
            2. Moved to the Archive Management. ❌ (Not yet implemented in frontend)
            3. Data will still be viewable and retrievable in Archive Management. ❌ (Not yet implemented in frontend)
         3. The archived patient will not appear in dropdown lists when selecting patients for new appointments or creating medical documents, but the data will remain stored for future reference. ❌ (Not yet implemented in frontend)

2. **Medical and Personal History** ✅
   1. Add/Update Medical History: ✅
      1. Doctors can add or update the following medical information from Data Management: ✅
         1. Habits ✅
         2. History ✅
         3. Allergies ✅
         4. Illnesses ✅
         5. Surgeries ✅
         6. Diet ✅

3. **Recent Vital Signs** ✅
   1. Display Latest Vital Signs: ✅
      1. The most recent vital signs recorded during the latest appointment will be displayed in the patient's profile.
      2. These vital signs will be pulled from the latest attended appointment for the patient.

4. **Medical Certificate Overview** ✅
   1. Display Medical Certificate: ✅
      1. If the patient has any medical certificates issued by the doctor:
         1. View Medical Certificate: The certificate will be displayed in the patient’s profile.
         2. Timestamp: Each medical certificate will have a timestamp showing when it was created.

5. **HMO and Family Information** ❌ (HMO not yet implemented)
   1. Add HMO: ❌ (HMO not yet implemented)
      1. Doctors can add or update HMO (Health Maintenance Organization) information for the patient, which will be pulled from Data Management.

   2. Add Family Information: ❌ (Family linking not yet implemented)
      1. The doctor can link the patient to their family members in the system by selecting family members from a dropdown list of existing patients. ❌ (Family linking not yet implemented)

6. **Check-Up History** ✅
   1. Displays all the previous appointments the patient has had, pulled from the appointment records. ✅
   2. The history is shown as a list, where doctors can easily see the dates and types of check-ups, along with links to detailed records of those appointments. ✅
   3. Compare module Apply here ❌ (Comparison module not yet implemented)

Clinic Management ✅

Setting up for day to day clinic operations, overview of all clinics and its clinic assistant activity. ✅

1. Add new clinic ✅
2. Clinic list ✅

   ### **Archive Management Tab** ❌ (Not yet implemented in frontend)

All archived patient and medical document is here, can still view for document tracking and security ❌ (Not yet implemented in frontend)

### **Subscription and Top-Up Tab Module Overview** ❌ (Not yet implemented in frontend)

1. **Subscription History** ❌ (Not yet implemented in frontend)
   1. View Subscription History: ❌ (Not yet implemented in frontend)
      1. Doctors can view a history of their subscriptions. ❌ (Not yet implemented in frontend)
      2. Information displayed includes: ❌ (Not yet implemented in frontend)
         1. Subscription Plan: The plan the doctor is currently subscribed to. ❌ (Not yet implemented in frontend)
         2. Start and End Dates: The duration of the subscription. ❌ (Not yet implemented in frontend)
         3. Payment History: Details about previous payments made, including the amount and transaction date. ❌ (Not yet implemented in frontend)

2. **Subscription Plans and Top-Up Options** ❌ (Not yet implemented in frontend)
   1. Available Subscription Plans: ❌ (Not yet implemented in frontend)
      1. Doctors can view a list of available subscription plans with details about each plan’s features and pricing. (Which will be the subscription management) ❌ (Not yet implemented in frontend)
3. Top-Up Options: ❌ (Not yet implemented in frontend)
   1. Doctors can also top up for specific services like: ❌ (Not yet implemented in frontend)
      1. Additional patients (above the monthly limit). ❌ (Not yet implemented in frontend)
      2. Additional appointments (above the monthly limit). ❌ (Not yet implemented in frontend)
      3. Extra clinics or clinic assistants. ❌ (Not yet implemented in frontend)

4. **Subscription/Top-Up Purchase Process** ❌ (Not yet implemented in frontend)
   1. Doctors can select a subscription plan or top up and proceed with purchasing it. ❌ (Not yet implemented in frontend)
   2. They can make the payment through the payment gateway integrated into the platform. ❌ (Not yet implemented in frontend)

5. **Free Account Limitations** ❌ (Not enforced in frontend)
   1. Free accounts are limited to the following: ❌ (Not enforced in frontend)
      1. 1 Clinic: Doctors can only manage one clinic under a free account. ❌ (Not enforced in frontend)
      2. 1 Clinic Assistant: Free accounts can only have one clinic assistant. ❌ (Not enforced in frontend)
      3. 10 Additional Patients per Month: Doctors can only add up to 10 additional patients per month without subscribing or topping up. ❌ (Not enforced in frontend)
      4. 10 Additional Appointments per Month: Free accounts are limited to scheduling up to 10 additional appointments per month. ❌ (Not enforced in frontend)

   2. Upgrade/Top-Up Reminder: ❌ (Not enforced in frontend)
      1. Doctors using a free account will be reminded that they need to upgrade their plan or top-up if they exceed these limits. ❌ (Not enforced in frontend)
      2. A notification or alert will be shown if they try to exceed the limits (e.g., adding more patients, clinics, or appointments). ❌ (Not enforced in frontend)

   ### **Settings Tab** ✅ Change Password ✅

**\*Help and Information (Redirection to exclusive FAQ page can be within or outside ELENA but accessible to public)** ❌ (Not yet implemented in frontend)

### **Clinic Assistant Tab (For Discussion)** ✅ Displays all Clinic Assistant of Doctors All access to the doctor’s account is replicated in the clinic assistant account except from the subscription. This will now be limited upon the doctor’s or super admin’s discretion. Which can be toggled on user roles in this tab. Special Note : Still retain that Doctors can’t input on assistants notes and vice versa ✅

## **Summary of User Flow \[Doctor\]:** ✅

**End to End** ✅

1. Doctor Account Creation → Account approval, email verification, profile setup, clinic setup and assign clinic assistant.
2. Doctor Dashboard → View metrics and appointments for the day.
3. Appointment Creation → Choose clinic, patient, and set appointment details reflect on calendars and all users involved.
4. Attend Appointment → Update patient info, generate medical documents, and set appointment status.
5. Follow-Up or Reschedule → Schedule follow-up appointments or adjust existing ones.
6. Complete Appointment → Mark the appointment as completed and lock details after 24at end of the day hours. Only Allowing additional new recordsnewrecords (e.g. doctor's note,Lab request/Result, additional prescription etc.)
7. Subscription Management → If the doctor exceeds the free plan’s limits, subscribe or top-up for additional services.

**Other Aspects** ✅

1. Medical Document → Request Medical Documents without proper actual clinic appointments
2. History → List of all patients appointments.
3. Archive Management → List of all archived medical documents and patients
4. Patients → list of all patients and their profile upon viewing.

## **Patient User Module**

1. **~~Registration Process~~** ✅
   1. ~~Registration Fields:~~
      1. ~~Title~~
      2. ~~First Name~~
      3. ~~Middle Name~~
      4. ~~Last Name~~
      5. ~~Suffix~~
      6. ~~Email Address~~
      7. ~~Contact Number~~
      8. ~~Gender~~
      9. ~~Occupation~~
      10. ~~Birthdate~~
      11. ~~Civil Status~~
      12. ~~Current Address~~
          1. ~~Province~~
          2. ~~Town/City~~
          3. ~~Barangay~~
          4. ~~Street Name~~
          5. ~~Lot/Block/Phase/Street No~~
          6. ~~Unit/Room/Floor/Building No~~
          7. ~~Subdivision/Compound/Village/Zone~~
          8. ~~Building/Condominium/Apartment~~
      13. ~~Permanent Address \- Same as above Toggle~~
          1. ~~Province~~
          2. ~~Town/City~~
          3. ~~Barangay~~
          4. ~~Street Name~~
          5. ~~Lot/Block/Phase/Street No~~
          6. ~~Unit/Room/Floor/Building No~~
          7. ~~Subdivision/Compound/Village/Zone~~
          8. ~~Building/Condominium/Apartment~~
      14. ~~Emergency Details~~
          1. ~~Emergency Contact Name~~
          2. ~~Emergency Contact Number~~
      15. ~~Basic Medical Information~~
          1. ~~Blood Type~~
          2. ~~Religion~~
          3. ~~Height~~
             1. ~~Feet~~
             2. ~~Inches~~
          4. ~~Height Type~~
             1. ~~Feet~~
             2. ~~Centimeter~~
          5. ~~Weight~~
             1. ~~Kilogram~~
             2. ~~Pounds~~
          6. ~~Weight Type~~
             1. ~~Kilogram~~
             2. ~~Pounds~~
          7. ~~Clinic(For discussion)~~
             1. ~~Drop Down from Clinics~~
             2. ~~Limited to One Only~~
      16. ~~Toggle for Minor/Elderly/PWD Assistance~~
          1. ~~Guardian Information~~
             1. ~~First Name~~
             2. ~~Middle Name~~
             3. ~~Last Name~~
             4. ~~Contact Number~~
             5. ~~Email~~
             6. ~~Birthdatey~~

   2. ~~Email Confirmation:~~ ✅
      1. ~~The patient will receive a confirmation email.~~
      2. ~~The email will contain a "Verify Email" link. The patientdoctor must click the link to verify their email address.~~
      3. After email verification, the patient can proceed to set up their login credentials (username and password).

2. **Profile Management (Any changes should reflect on Doctor and CA View)** ✅
   1. Update Password (If first login) ✅
   2. Update Profile: ✅
      1. Editable Fields: ✅
      2. Contact Number ✅
      3. Address ✅
      4. Permanent Address ✅
      5. Birthdate ✅
      6. Emergency Contact Name ✅
      7. Emergency Contact Number ✅
      8. Blood Type ✅
      9. Save Changes: Once updated, the changes will reflect in the patient’s profile and medical history. This should also be reflected in the doctor and clinic assistant’s view. ✅

3. **Medical and Personal History** ❌ (Placeholder implementation only)
   1. Add/Update Medical History: ❌ (Placeholder implementation only)
      1. Patient can add or update the following medical information from Data Management: ❌ (Placeholder implementation only)
         1. Habits ❌ (Placeholder implementation only)
         2. History ❌ (Placeholder implementation only)
         3. Allergies ❌ (Placeholder implementation only)
         4. Illnesses ❌ (Placeholder implementation only)
         5. Surgeries ❌ (Placeholder implementation only)
         6. Diet ❌ (Placeholder implementation only)

4. **Recent Vital Signs** ❌ (Placeholder implementation only)
   1. Display Latest Vital Signs: ❌ (Placeholder implementation only)
      1. The most recent vital signs recorded during the latest appointment will be displayed in the patient's profile. ❌ (Placeholder implementation only)
      2. These vital signs will be pulled from the latest attended appointment for the patient. ❌ (Placeholder implementation only)

5. **Medical Certificate Overview** ❌ (Placeholder implementation only)
   1. Display Medical Certificate: ❌ (Placeholder implementation only)
      1. If the patient has any medical certificates issued by the doctor: ❌ (Placeholder implementation only)
         1. View Medical Certificate: The certificate will be displayed in the patient’s profile. ❌ (Placeholder implementation only)
         2. Timestamp: Each medical certificate will have a timestamp showing when it was created. ❌ (Placeholder implementation only)

6. **HMO and Family Information** ❌ (Placeholder implementation only)
   1. Add HMO: ❌ (Placeholder implementation only)
      1. Doctors can add or update HMO (Health Maintenance Organization) information for the patient, which will be pulled from Data Management. ❌ (Placeholder implementation only)

   2. Add Family Information: ❌ (Placeholder implementation only)
      1. The doctor can link the patient to their family members in the system by selecting family members from a dropdown list of existing patients. ❌ (Placeholder implementation only)

7. **Check-Up History** ❌ (Placeholder implementation only)
   1. Displays all the previous appointments the patient has had, pulled from the appointment records. ❌ (Placeholder implementation only)
   2. The history is shown as a list, where doctors can easily see the dates and types of check-ups, along with links to detailed records of those appointments. ❌ (Placeholder implementation only)
   3. Compare module Apply here ❌ (Placeholder implementation only)

   ### **Patient’s "My Appointments" Tab User Flow** ❌ (Placeholder implementation only)

8. View Upcoming Appointments ❌ (Placeholder implementation only)
   1. The patient can view their upcoming appointments, including: ❌ (Placeholder implementation only)
      1. Date and Time of the appointment. ❌ (Placeholder implementation only)
      2. Consultation Type. ❌ (Placeholder implementation only)
      3. Status. ❌ (Placeholder implementation only)
         1. Confirmed, Declined, Waiting, Ongoing, Completed, No Show, Cancelled. ❌ (Placeholder implementation only)

9. View Appointment History ❌ (Placeholder implementation only)
   1. Check Past Appointments: ❌ (Placeholder implementation only)
   2. The patient can see all their previous appointments along with the medical documents during that appointment ❌ (Placeholder implementation only)
   3. The patient can view and download or print the documents (e.g., prescriptions, medical certificates). ❌ (Placeholder implementation only)

10. Reschedule or Follow Up ❌ (Placeholder implementation only)
    1. If the doctor changes or add follow up check ups, this should update and provide another upcoming appointment for the patient ❌ (Placeholder implementation only)

11. Medical Documents ❌ (Placeholder implementation only)
    1. View Medical Documents: ❌ (Placeholder implementation only)
       1. All medical documents associated with an appointment (e.g., prescriptions, lab results, diagnostic tests, medical certificates, etc.) will be displayed here. ❌ (Placeholder implementation only)
       2. The patient can view and download or print the documents (e.g., prescriptions, medical certificates). ❌ (Placeholder implementation only)

12. Documents History: ❌ (Placeholder implementation only)
    1. Patients can see a list of all issued medical certificates and prescriptions with timestamps. ❌ (Placeholder implementation only)

    ### **Summary of Features for Patient User Module:**

13. Registration \-\> Email Confirmation \-\> Verify \-\> Login ✅
14. Change Password \-\> Update Profile ✅
15. View Upcoming Appointments: Patients can see future appointments with details on the date, time, and status. ❌ (Placeholder implementation only)
16. Appointment History: Patients can view all past appointments, including treatments and prescriptions. Patients can view, download, and print documents such as prescriptions, medical certificates, and test results. ❌ (Placeholder implementation only)
17. Appointment Status: The current status of the appointment (e.g., confirmed, completed, cancelled). ❌ (Placeholder implementation only)
18. Reschedule/Follow Up: Patients view the appointment of a reschedule or follow-up will automatically be updated on their end. ❌ (Placeholder implementation only)
19. Medical Documents: Patients can view, download, and print documents such as prescriptions, medical certificates, and test results. ❌ (Placeholder implementation only)

## **Timeline**

**Preparation & Initial Setup (Completed)**

| Phase                                | Dates                | Tasks                                                                                                                                                         |
| :----------------------------------- | :------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| Phase 0: Preparation & Initial Setup | April 16 \- April 20 | Familiarized with the Platform: The team got acquainted with the platform, reviewed architecture, and understood existing features and modules.               |
|                                      | April 21             | Source Code Turnover The source code was handed over to the development team for review and modification.                                                     |
|                                      | April 22 \- April 30 | Source Code Review: The codebase was reviewed to understand the structure, identify gaps, and ensure smooth integration of the new modules.                   |
|                                      | May 1 \- May 6       | Deployment on New Machine: The platform was deployed to a new environment for development purposes.                                                           |
|                                      | May 6 \- May 21      | Elena System Improvement & Bug Fixes: Improvements and fixes to the existing Elena system, including addressing technical debt and preparing for new modules. |
|                                      | May 22               | Face-to-Face Scoping: Meeting to finalize requirements, confirm project scope, and align expectations for the upcoming phases.                                |
|                                      | May 23 \- 29         | Crafting of Documents: Documentation for the modules and system flow was created, detailing features, user flows, and technical specifications.               |

| Phase                                                                 | Dates                  | Tasks                                                                                                                                                                                                                                          |
| :-------------------------------------------------------------------- | :--------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Phase 1: Initial Planning & Requirements Gathering                    | May 28 \- June 4       | Finalize user stories and feature specifications for Super Admin, Doctor, and Patient modules. Gather detailed requirements for Subscription, Appointment, Medical Document, and Profile Management. Review database schema and finalize APIs. |
| Phase 2: Backend Development \- Setup & Core Features                 | June 5 \- June 18      | Set up a database for patient profiles, appointments, medical documents, subscriptions. Develop APIs for Super Admin, Doctor, and Patient modules. Implement subscription management logic (free vs paid).                                     |
| Phase 3: Front-End Development \- Initial Implementation              | June 19 \- July 3      | Develop UI/UX for Super Admin dashboard, Doctor’s Dashboard, and Patient’s Appointment Management. Build appointment creation UI and medical document interfaces. Start responsive design for mobile and desktop.                              |
| Phase 4: Backend Development \- Advanced Features & Integrations      | July 4 \- July 17      | Back-End: Implement medical document management (prescriptions, lab requests). Finalize Doctor’s subscription logic, payment integration. Complete Patient module (medical records, vital signs, documents).                                   |
| Phase 5: Front-End & Back-End Development \- Final Features & Testing | July 18 \- August 10   | Back-End: Finalize API endpoints, test integration for medical documents, appointment management, and subscriptions. Implement security and authentication. Front-End: Complete UI for Doctor’s Dashboard and Patient Appointment.             |
| Phase 6: Integration & Testing                                        | August 11 \- August 17 | Back-End: API testing (Super Admin, Doctor, Patient, Subscription). Perform security and load testing. Front-End: UI/UX testing (cross-device/browser testing, feedback).                                                                      |
| Phase 7: Final Testing & Bug Fixes                                    | August 18 \- August 24 | Back-End: Resolve issues found during API testing. Finalize subscription system (free plan limits, upgrades). Front-End: Fix UI bugs, refine responsive design, and conduct cross-browser testing.                                             |
| Phase 8: Final Review & Launch Preparation                            | August 25 \- August 31 | Back-End & Front-End: System Integration for all modules (Super Admin, Doctor, Patient, Appointment, Subscription). Final User Acceptance Testing (UAT). Go-live prep and final deployment to production. Prepare user documentation.          |

**July 14, 2025**

Patient Registration Additional Module (If in needed assistance)

![][image1]

[image1]: data:image/png;base64,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
