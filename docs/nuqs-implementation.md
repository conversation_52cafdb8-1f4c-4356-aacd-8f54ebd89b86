# nuqs Implementation Guide

This document explains how to use nuqs for managing URL search parameters in the Elena project.

## Overview

nuqs is a type-safe search params state manager for React frameworks. It provides a `useState`-like API but stores the state in the URL query string, making it perfect for:

- Dashboard tabs and navigation
- Search filters and pagination
- Dialog states
- Form parameters
- Any state that should be shareable via URL

## Setup

### 1. NuqsAdapter Configuration

The `NuqsAdapter` is already configured in `src/app/layout.tsx`:

```tsx
import { NuqsAdapter } from 'nuqs/adapters/next/app';

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html>
      <body>
        <NuqsAdapter>
          <ReactQueryProvider>
            <TooltipProvider>{children}</TooltipProvider>
          </ReactQueryProvider>
        </NuqsAdapter>
      </body>
    </html>
  );
}
```

### 2. Shared Search Parameter Parsers

Common search parameter parsers are defined in `src/core/lib/search-params.ts`:

```tsx
export const searchParamParsers = {
  // Dashboard tab parser
  tab: parseAsString.withDefault(EDashboardTab.DOCTOR_OVERVIEW),
  
  // Action parser for dialogs/forms
  action: parseAsStringLiteral(['create', 'edit', 'view']),
  
  // ID parser for entity selection
  id: parseAsInteger,
  
  // Document type parser
  type: parseAsStringLiteral(['prescriptions', 'lab-requests', 'referrals'])
    .withDefault('prescriptions'),
  
  // Search query parser
  search: parseAsString.withDefault(''),
  
  // Pagination parsers
  page: parseAsInteger.withDefault(1),
  pageSize: parseAsInteger.withDefault(10),
};
```

## Usage Patterns

### 1. Single Query State

For managing a single URL parameter:

```tsx
import { useQueryState } from 'nuqs';
import { searchParamParsers } from '@/core/lib/search-params';

function MyComponent() {
  const [currentTab] = useQueryState('tab', searchParamParsers.tab);
  
  // currentTab is type-safe and has a default value
  return <div>Current tab: {currentTab}</div>;
}
```

### 2. Multiple Related Query States

For managing multiple related parameters that should be updated together:

```tsx
import { useQueryStates } from 'nuqs';
import { searchParamParsers } from '@/core/lib/search-params';

function PrescriptionManagement() {
  const [{ action, id, search, page }, setSearchParams] = useQueryStates({
    action: searchParamParsers.action,
    id: searchParamParsers.id,
    search: searchParamParsers.search,
    page: searchParamParsers.page,
  });

  // Update multiple parameters at once
  const handleSearch = (query: string) => {
    setSearchParams({ search: query, page: 1 }); // Reset to first page
  };

  const handleCreateAction = () => {
    setSearchParams({ action: 'create' });
  };

  const navigateToList = () => {
    setSearchParams({ action: null, id: null }); // Clear parameters
  };

  return (
    <div>
      <input 
        value={search} 
        onChange={(e) => handleSearch(e.target.value)} 
      />
      <button onClick={handleCreateAction}>Create</button>
    </div>
  );
}
```

### 3. Custom Parsers

For custom data types, create your own parsers:

```tsx
import { parseAsJson, parseAsArrayOf, parseAsInteger } from 'nuqs';

// For JSON objects
const coordinatesParser = parseAsJson<{ lat: number; lng: number }>()
  .withDefault({ lat: 0, lng: 0 });

// For arrays
const selectedIdsParser = parseAsArrayOf(parseAsInteger)
  .withDefault([]);

// Usage
const [coordinates, setCoordinates] = useQueryState('coords', coordinatesParser);
const [selectedIds, setSelectedIds] = useQueryState('selected', selectedIdsParser);
```

## Migration from useSearchParams

### Before (using useSearchParams)

```tsx
import { useRouter, useSearchParams } from 'next/navigation';

function OldComponent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const currentTab = searchParams.get('tab') || 'overview';
  const searchQuery = searchParams.get('search') || '';
  
  const handleTabChange = (tab: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('tab', tab);
    router.push(`/dashboard?${params.toString()}`);
  };
  
  const handleSearch = (query: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('search', query);
    params.set('page', '1'); // Reset page
    router.push(`/dashboard?${params.toString()}`);
  };
}
```

### After (using nuqs)

```tsx
import { useQueryStates } from 'nuqs';
import { searchParamParsers } from '@/core/lib/search-params';

function NewComponent() {
  const [{ tab, search, page }, setParams] = useQueryStates({
    tab: searchParamParsers.tab,
    search: searchParamParsers.search,
    page: searchParamParsers.page,
  });
  
  const handleTabChange = (newTab: string) => {
    setParams({ tab: newTab });
  };
  
  const handleSearch = (query: string) => {
    setParams({ search: query, page: 1 }); // Reset page
  };
}
```

## Benefits

1. **Type Safety**: All parameters are type-safe with proper TypeScript support
2. **Default Values**: No need for null checks or fallback values
3. **Automatic Batching**: Multiple parameter updates are batched automatically
4. **Cleaner Code**: No manual URLSearchParams manipulation
5. **Better Performance**: Optimized updates and minimal re-renders
6. **Server-Side Support**: Works with Next.js Server Components

## Best Practices

1. **Use shared parsers** for common parameters to ensure consistency
2. **Group related parameters** using `useQueryStates` for better performance
3. **Set default values** to avoid null checks in your components
4. **Reset dependent parameters** (e.g., reset page when searching)
5. **Use descriptive parameter names** that are URL-friendly

## Examples in the Codebase

- `DoctorDashboard`: Uses `useQueryState` for tab management
- `PrescriptionManagement`: Uses `useQueryStates` for multiple parameters
- `src/components/examples/nuqs-example.tsx`: Complete examples of both patterns

## Further Reading

- [nuqs Documentation](https://nuqs.47ng.com/)
- [Next.js App Router Integration](https://nuqs.47ng.com/docs/adapters/next-app)
- [TypeScript Support](https://nuqs.47ng.com/docs/parsers)
