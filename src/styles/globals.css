@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

@theme inline {
	/* fonts */
	--font-sans: var(--font-geist-sans);
	--font-mono: var(--font-geist-mono);
	--font-poppins: var(--font-poppins);
	--font-inter: var(--font-inter);

	/* elena brand colors */
	--color-elena-primary: var(--elena-primary);
	--color-elena-primary-dark: var(--elena-primary-dark);
	--color-elena-primary-light: var(--elena-primary-light);

	/* shadcn ui */
	--color-sidebar-ring: var(--sidebar-ring);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar: var(--sidebar);
	--color-chart-5: var(--chart-5);
	--color-chart-4: var(--chart-4);
	--color-chart-3: var(--chart-3);
	--color-chart-2: var(--chart-2);
	--color-chart-1: var(--chart-1);
	--color-ring: var(--ring);
	--color-input: var(--input);
	--color-border: var(--border);
	--color-destructive: var(--destructive);
	--color-destructive-foreground: var(--destructive-foreground);
	--color-accent-foreground: var(--accent-foreground);
	--color-accent: var(--accent);
	--color-muted-foreground: var(--muted-foreground);
	--color-muted: var(--muted);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-secondary: var(--secondary);
	--color-primary-foreground: var(--primary-foreground);
	--color-primary: var(--primary);
	--color-popover-foreground: var(--popover-foreground);
	--color-popover: var(--popover);
	--color-card-foreground: var(--card-foreground);
	--color-card: var(--card);
	--color-foreground: var(--foreground);
	--color-background: var(--background);
	--radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);
}

:root {
	--radius: 0.625rem;
	/* Elena Theme Colors */
	--elena-primary: oklch(0.7448 0.1256 202.74); /* #0CC3CF */
	--elena-primary-dark: oklch(0.6448 0.1256 202.74); /* Darker shade */
	--elena-primary-light: oklch(0.8448 0.1256 202.74); /* Lighter shade */

	--background: oklch(1 0 0);
	--foreground: oklch(0.13 0.028 261.692);
	--card: oklch(1 0 0);
	--card-foreground: oklch(0.13 0.028 261.692);
	--popover: oklch(1 0 0);
	--popover-foreground: oklch(0.13 0.028 261.692);
	--primary: var(--elena-primary);
	--primary-foreground: oklch(1 0 0);
	--secondary: oklch(0.967 0.003 264.542);
	--secondary-foreground: oklch(0.13 0.028 261.692);
	--muted: oklch(0.967 0.003 264.542);
	--muted-foreground: oklch(0.551 0.027 264.364);
	--accent: oklch(0.95 0.02 202.74);
	--accent-foreground: oklch(0.13 0.028 261.692);
	--destructive: oklch(0.577 0.245 27.325);
	--destructive-foreground: oklch(1 0 0);
	--border: oklch(0.928 0.006 264.531);
	--input: oklch(0.928 0.006 264.531);
	--ring: var(--elena-primary);
	--chart-1: var(--elena-primary);
	--chart-2: oklch(0.6 0.118 184.704);
	--chart-3: oklch(0.398 0.07 227.392);
	--chart-4: oklch(0.828 0.189 84.429);
	--chart-5: oklch(0.769 0.188 70.08);
	--sidebar: oklch(0.985 0.002 247.839);
	--sidebar-foreground: oklch(0.13 0.028 261.692);
	--sidebar-primary: var(--elena-primary);
	--sidebar-primary-foreground: oklch(1 0 0);
	--sidebar-accent: oklch(0.95 0.02 202.74);
	--sidebar-accent-foreground: oklch(0.13 0.028 261.692);
	--sidebar-border: oklch(0.928 0.006 264.531);
	--sidebar-ring: var(--elena-primary);
}

.dark {
	--background: oklch(0.13 0.028 261.692);
	--foreground: oklch(0.985 0.002 247.839);
	--card: oklch(0.21 0.034 264.665);
	--card-foreground: oklch(0.985 0.002 247.839);
	--popover: oklch(0.21 0.034 264.665);
	--popover-foreground: oklch(0.985 0.002 247.839);
	--primary: var(--elena-primary);
	--primary-foreground: oklch(0.13 0.028 261.692);
	--secondary: oklch(0.278 0.033 256.848);
	--secondary-foreground: oklch(0.985 0.002 247.839);
	--muted: oklch(0.278 0.033 256.848);
	--muted-foreground: oklch(0.707 0.022 261.325);
	--accent: oklch(0.25 0.02 202.74);
	--accent-foreground: oklch(0.985 0.002 247.839);
	--destructive: oklch(0.704 0.191 22.216);
	--destructive-foreground: oklch(1 0 0);
	--border: oklch(1 0 0 / 10%);
	--input: oklch(1 0 0 / 15%);
	--ring: var(--elena-primary);
	--chart-1: var(--elena-primary);
	--chart-2: oklch(0.696 0.17 162.48);
	--chart-3: oklch(0.769 0.188 70.08);
	--chart-4: oklch(0.627 0.265 303.9);
	--chart-5: oklch(0.645 0.246 16.439);
	--sidebar: oklch(0.21 0.034 264.665);
	--sidebar-foreground: oklch(0.985 0.002 247.839);
	--sidebar-primary: var(--elena-primary);
	--sidebar-primary-foreground: oklch(0.13 0.028 261.692);
	--sidebar-accent: oklch(0.25 0.02 202.74);
	--sidebar-accent-foreground: oklch(0.985 0.002 247.839);
	--sidebar-border: oklch(1 0 0 / 10%);
	--sidebar-ring: var(--elena-primary);
}

@layer base {
	* {
		@apply border-border outline-ring/50;
	}
	body {
		@apply bg-background text-foreground;
	}
}

@layer utilities {
	.debug {
		@apply outline-red-400 outline-dashed;
	}
	.debug > * {
		@apply outline-green-400 outline-dashed;
	}
	.form-error {
		@apply mt-1 text-sm font-semibold text-red-500 italic;
	}
}

/* Waiting Loading */
@keyframes blink {
	0%,
	100% {
		opacity: 1;
	}
	50% {
		opacity: 0;
	}
}
.dot {
	animation: blink 1.5s infinite;
	animation-delay: calc(0.5s * var(--i));
}
.dot:nth-child(1) {
	--i: 0;
}
.dot:nth-child(2) {
	--i: 1;
}
.dot:nth-child(3) {
	--i: 2;
}

.text-xxs {
	font-size: 0.625rem;
}
