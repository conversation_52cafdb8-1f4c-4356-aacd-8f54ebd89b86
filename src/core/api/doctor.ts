import axios from '@/core/api';
import { TAddressFormData } from '@/features/dashboard/components/doctor-dashboard/onboarding/types/onboarding.type';
import { IDoctorDetails } from '@/features/dashboard/types/doctor-details.type';

// Doctor Profile Management APIs
export const getDoctorProfile = async () => {
	const response = await axios.get('/doctor/profile');
	return response.data.data as IDoctorDetails;
};

export const updateDoctorBasicInfo = async (data: {
	firstName?: string;
	middleName?: string;
	lastName?: string;
	suffix?: string;
	phone?: string;
	gender?: string;
	prcNumber?: string;
}) => {
	const response = await axios.post('/doctor/profile/update-basic-info', data);
	return response.data;
};

export const updateDoctorSpecialization = async (data: {
	specialty?: string;
	subSpecialty?: string;
	subSpecialization?: string;
}) => {
	const response = await axios.post(
		'/doctor/profile/update-specialization',
		data
	);
	return response.data;
};

export const updateDoctorAddress = async (data: TAddressFormData) => {
	const response = await axios.post('/doctor/profile/update-address', data);
	return response.data;
};

export const updateDoctorESignature = async (data: FormData) => {
	const response = await axios.post(
		'/doctor/profile/update-e-signature',
		data,
		{
			headers: {
				'Content-Type': 'multipart/form-data',
			},
		}
	);
	return response.data;
};

export const updateDoctorSocialMedia = async (data: {
	facebook?: string;
	instagram?: string;
	twitter?: string;
	linkedin?: string;
	website?: string;
}) => {
	const response = await axios.post(
		'/doctor/profile/update-social-media',
		data
	);
	return response.data;
};

export const completeOnboarding = async () => {
	const response = await axios.post('/doctor/profile/complete-onboarding');
	return response.data;
};
// Doctor Dashboard Query Keys
export const DOCTOR_QUERY_KEYS = {
	PROFILE: {
		DETAIL: ['doctor', 'profile'],
	},
} as const;
