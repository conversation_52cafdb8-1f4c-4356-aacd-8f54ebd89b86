import apiClient from '@/core/api';

// Types for reference data
export interface IVisitReason {
	id: number;
	name: string;
	description?: string;
	isDeleted: number;
	createdAt: string;
	updatedAt: string;
}

export interface IMedicalDescription {
	id: number;
	name: string;
	description?: string;
	isDeleted: number;
	createdAt: string;
	updatedAt: string;
}

export interface IPrescriptionType {
	id: number;
	name: string;
	description?: string;
	isDeleted: number;
	createdAt: string;
	updatedAt: string;
}

export interface ILabRequestType {
	id: number;
	name: string;
	description?: string;
	isDeleted: number;
	createdAt: string;
	updatedAt: string;
}

export interface IDiagnosticRequestType {
	id: number;
	name: string;
	description?: string;
	isDeleted: number;
	createdAt: string;
	updatedAt: string;
}

export interface IConsultationType {
	id: number;
	name: string;
	description?: string;
	isDeleted: number;
	createdAt: string;
	updatedAt: string;
}

export interface IAppointmentType {
	id: number;
	name: string;
	description?: string;
	isDeleted: number;
	createdAt: string;
	updatedAt: string;
}

export interface IPaymentType {
	id: number;
	name: string;
	description?: string;
	isDeleted: number;
	createdAt: string;
	updatedAt: string;
}

export interface IHmo {
	id: number;
	name: string;
	description?: string;
	isDeleted: number;
	createdAt: string;
	updatedAt: string;
}

// API Response types
export interface IDataResponse<T> {
	status: number;
	data: T[];
}

// Mock data for HMOs (until backend implements HmosController)
const MOCK_HMOS: IHmo[] = [
	{
		id: 1,
		name: 'PhilHealth',
		description: 'Philippine Health Insurance Corporation',
		isDeleted: 0,
		createdAt: new Date().toISOString(),
		updatedAt: new Date().toISOString(),
	},
	{
		id: 2,
		name: 'Maxicare',
		description: 'Maxicare Healthcare Corporation',
		isDeleted: 0,
		createdAt: new Date().toISOString(),
		updatedAt: new Date().toISOString(),
	},
	{
		id: 3,
		name: 'Medicard',
		description: 'Medicard Philippines',
		isDeleted: 0,
		createdAt: new Date().toISOString(),
		updatedAt: new Date().toISOString(),
	},
	{
		id: 4,
		name: 'Intellicare',
		description: 'Intellicare',
		isDeleted: 0,
		createdAt: new Date().toISOString(),
		updatedAt: new Date().toISOString(),
	},
];

// API Functions - using correct backend endpoints
export const getMedicalDescriptions = async (): Promise<
	IDataResponse<IMedicalDescription>
> => {
	const response = await apiClient.get('/data-management/medical-descriptions');
	return response.data;
};

export const getMedicalTypes = async (): Promise<
	IDataResponse<IPrescriptionType>
> => {
	const response = await apiClient.get('/data-management/medical-types');
	return response.data;
};

export const getPrescriptionTypes = async (): Promise<
	IDataResponse<IPrescriptionType>
> => {
	const response = await apiClient.get('/data-management/prescription-types');
	return response.data;
};

export const getLabRequestTypes = async (): Promise<
	IDataResponse<ILabRequestType>
> => {
	const response = await apiClient.get('/data-management/lab-request-types');
	return response.data;
};

export const getDiagnosticRequestTypes = async (): Promise<
	IDataResponse<IDiagnosticRequestType>
> => {
	const response = await apiClient.get(
		'/data-management/diagnostic-request-types'
	);
	return response.data;
};

export const getVisitReasons = async (): Promise<
	IDataResponse<IVisitReason>
> => {
	const response = await apiClient.get('/data-management/visit-reasons');
	return response.data;
};

export const getConsultationTypes = async (): Promise<
	IDataResponse<IConsultationType>
> => {
	const response = await apiClient.get('/data-management/consultation-types');
	return response.data;
};

export const getAppointmentTypes = async (): Promise<
	IDataResponse<IAppointmentType>
> => {
	const response = await apiClient.get('/data-management/appointment-types');
	return response.data;
};

export const getPaymentTypes = async (): Promise<
	IDataResponse<IPaymentType>
> => {
	const response = await apiClient.get('/data-management/payment-types');
	return response.data;
};

export const getHmos = async (): Promise<IDataResponse<IHmo>> => {
	// Note: hmos endpoint doesn't exist in backend routes
	// Using mock data until backend implements this endpoint
	console.warn('HMOs API not implemented, using mock data');
	return {
		status: 1,
		data: MOCK_HMOS,
	};
};

// Query Keys
export const DATA_QUERY_KEYS = {
	VISIT_REASONS: ['data', 'visit-reasons'] as const,
	CONSULTATION_TYPES: ['data', 'consultation-types'] as const,
	APPOINTMENT_TYPES: ['data', 'appointment-types'] as const,
	PAYMENT_TYPES: ['data', 'payment-types'] as const,
	HMOS: ['data', 'hmos'] as const,
	MEDICAL_DESCRIPTIONS: ['data', 'medical-descriptions'] as const,
	PRESCRIPTION_TYPES: ['data', 'prescription-types'] as const,
	LAB_REQUEST_TYPES: ['data', 'lab-request-types'] as const,
	DIAGNOSTIC_REQUEST_TYPES: ['data', 'diagnostic-request-types'] as const,
};
