import axios from '@/core/api';
import {
	AssistantReferralsResponse,
	ICreateDiagnosticRequest,
	ICreateDiagnosticRequestItemRequest,
	ICreateLabRequest,
	ICreateLabRequestItemRequest,
	ICreateMedicalCertificateItemRequest,
	ICreatePatientRequest,
	ICreatePrescriptionItemRequest,
	ICreatePrescriptionRequest,
	ICreateReferralRequest,
	ICreateStandaloneDiagnosticRequestRequest,
	ICreateStandaloneLabRequestRequest,
	ICreateStandalonePrescriptionRequest,
	ICreateStandaloneReferralRequest,
	IDiagnosticRequestDetailResponse,
	IDiagnosticRequestListParams,
	IDiagnosticRequestsResponse,
	ILabRequestDetailResponse,
	ILabRequestListParams,
	ILabRequestsResponse,
	IMedicalCertificateDetailResponse,
	IMedicalCertificateListParams,
	IMedicalCertificatesResponse,
	IPrescriptionDetailResponse,
	IPrescriptionListParams,
	IReferralDetailResponse,
	IReferralListParams,
	IUpdateAppointmentRequest,
	IUpdatePatientRequest,
} from '@/features/dashboard/types/assistant.type';
import {
	IApiResponse,
	IAppointmentParams,
	IAppointmentsResponse,
	IClinicParams,
	IClinicsResponse,
	ICreateAppointmentRequest,
	IDashboardAppointmentsResponse,
	IDoctorDashboardResponse,
	IPatientsResponse,
	ISearchParams,
} from '@/features/dashboard/types/doctor.types';

export const updatePrescriptionPtr = async (
	prescriptionId: number,
	data: {
		professionalTaxReceipt: string;
	}
) => {
	const response = await axios.post(
		`/assistant/prescriptions/${prescriptionId}/update-professional-tax-receipt`,
		data
	);
	return response.data;
};

export const updateLabRequestPtr = async (
	labRequestId: number,
	data: {
		professionalTaxReceipt: string;
	}
) => {
	const response = await axios.post(
		`/assistant/lab-requests/${labRequestId}/update-professional-tax-receipt`,
		data
	);
	return response.data;
};

export const updateMedicalCertificatePtr = async (
	medicalCertificateId: number,
	data: {
		professionalTaxReceipt: string;
	}
) => {
	const response = await axios.post(
		`/assistant/medical-certificates/${medicalCertificateId}/update-professional-tax-receipt`,
		data
	);
	return response.data;
};

export const updateReferralPtr = async (
	referralId: number,
	data: {
		professionalTaxReceipt: string;
	}
) => {
	const response = await axios.post(
		`/assistant/referrals/${referralId}/update-professional-tax-receipt`,
		data
	);
	return response.data;
};

export const updateDiagnosticRequestPtr = async (
	diagnosticRequestId: number,
	data: {
		professionalTaxReceipt: string;
	}
) => {
	const response = await axios.post(
		`/assistant/diagnostic-requests/${diagnosticRequestId}/update-professional-tax-receipt`,
		data
	);
	return response.data;
};

export const updateAppointmentPtr = async (
	appointmentId: number,
	data: {
		professionalTaxReceipt: string;
	}
) => {
	const response = await axios.post(
		`/assistant/appointments/${appointmentId}/update-professional-tax-receipt`,
		data
	);
	return response.data;
};

// Assistant Dashboard Metrics
export const getAssistantDashboardMetrics =
	async (): Promise<IDoctorDashboardResponse> => {
		const response = await axios.get('/assistant/dashboard');
		return response.data;
	};

// Assistant Appointment Management APIs
export const getAssistantAppointments = async (
	params: IAppointmentParams = {}
): Promise<IAppointmentsResponse> => {
	const response = await axios.get('/assistant/appointments', { params });
	return response.data;
};

export const getTodayAssistantAppointments = async (
	params: IAppointmentParams = {}
): Promise<IDashboardAppointmentsResponse> => {
	const response = await axios.get(
		'/assistant/dashboard/todays-queue-appointments',
		{ params }
	);
	return response.data;
};

export const getTodayAssistantAttendedAppointments = async (
	params: IAppointmentParams = {}
): Promise<IDashboardAppointmentsResponse> => {
	const response = await axios.get(
		'/assistant/dashboard/todays-attended-appointments',
		{ params }
	);
	return response.data;
};

export const getAssistantAppointmentById = async (appointmentId: number) => {
	const response = await axios.get(`/assistant/appointments/${appointmentId}`);
	return response.data.data;
};

export const createAssistantAppointment = async (
	appointmentData: ICreateAppointmentRequest
) => {
	const response = await axios.post(
		'/assistant/appointments/create',
		appointmentData
	);
	return response.data;
};

export const updateAssistantAppointment = async (
	appointmentData: IUpdateAppointmentRequest
): Promise<IApiResponse> => {
	const { appointment_id, ...data } = appointmentData;
	const response = await axios.post(
		`/assistant/appointments/${appointment_id}/update`,
		data
	);
	return response.data;
};

export const updateAssistantAppointmentStatus = async (
	appointmentId: number,
	status: string
) => {
	const response = await axios.post(
		`/assistant/appointments/${appointmentId}/update-status`,
		{
			status,
		}
	);
	return response.data;
};

// Update appointment details (chief complaint, diagnosis, prognosis)
export const updateAssistantAppointmentDetails = async (
	appointmentId: number,
	data: {
		chiefComplaint?: string;
		diagnosis?: string;
		prognosis?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/appointments/${appointmentId}/update`,
		data
	);
	return response.data;
};

// Patient Profile Management APIs
export const getPatientById = async (profileId: number) => {
	const response = await axios.get(`/assistant/patients/${profileId}`);
	return response.data.data;
};

export const createPatient = async (patientData: ICreatePatientRequest) => {
	const response = await axios.post('/assistant/patients/create', patientData);
	return response.data;
};

export const updatePatient = async (
	patientData: IUpdatePatientRequest
): Promise<IApiResponse> => {
	const response = await axios.post('/assistant/patients/update', patientData);
	return response.data;
};

export const enablePatient = async (profileId: number) => {
	const response = await axios.post('/assistant/patients/enable', {
		profileId: profileId,
	});
	return response.data;
};

export const disablePatient = async (profileId: number) => {
	const response = await axios.post('/assistant/patients/disable', {
		profileId: profileId,
	});
	return response.data;
};

export const archivePatient = async (profileId: number) => {
	const response = await axios.post('/assistant/patients/archive', {
		profileId: profileId,
	});
	return response.data;
};

// Habits Management
export const addHabitToPatient = async (
	profileId: number,
	habitData: {
		name: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/habit/create`,
		habitData
	);
	return response.data;
};

export const updatePatientHabit = async (
	profileId: number,
	patientHabitId: number,
	habitData: {
		name?: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/habit/${patientHabitId}/update`,
		habitData
	);
	return response.data;
};

export const removeHabitFromPatient = async (
	profileId: number,
	patientHabitId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/habit/${patientHabitId}/remove`
	);
	return response.data;
};

// Allergies Management
export const addAllergyToPatient = async (
	profileId: number,
	allergyData: {
		name: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/allergy/create`,
		allergyData
	);
	return response.data;
};

export const updatePatientAllergy = async (
	profileId: number,
	patientAllergyId: number,
	allergyData: {
		name?: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/allergy/${patientAllergyId}/update`,
		allergyData
	);
	return response.data;
};

export const removeAllergyFromPatient = async (
	profileId: number,
	patientAllergyId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/allergy/${patientAllergyId}/remove`
	);
	return response.data;
};

// Illnesses Management
export const addIllnessToPatient = async (
	profileId: number,
	illnessData: {
		name: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/illness/create`,
		illnessData
	);
	return response.data;
};

export const updatePatientIllness = async (
	profileId: number,
	patientIllnessId: number,
	illnessData: {
		name?: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/illness/${patientIllnessId}/update`,
		illnessData
	);
	return response.data;
};

export const removeIllnessFromPatient = async (
	profileId: number,
	patientIllnessId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/illness/${patientIllnessId}/remove`
	);
	return response.data;
};

// History Management
export const addHistoryToPatient = async (
	profileId: number,
	historyData: {
		name: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/history/create`,
		historyData
	);
	return response.data;
};

export const updatePatientHistory = async (
	profileId: number,
	patientHistoryId: number,
	historyData: {
		name?: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/history/${patientHistoryId}/update`,
		historyData
	);
	return response.data;
};

export const removeHistoryFromPatient = async (
	profileId: number,
	patientHistoryId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/history/${patientHistoryId}/remove`
	);
	return response.data;
};

// Surgeries Management
export const addSurgeryToPatient = async (
	profileId: number,
	surgeryData: {
		name: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/surgery/create`,
		surgeryData
	);
	return response.data;
};

export const updatePatientSurgery = async (
	profileId: number,
	patientSurgeryId: number,
	surgeryData: {
		name?: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/surgery/${patientSurgeryId}/update`,
		surgeryData
	);
	return response.data;
};

export const removeSurgeryFromPatient = async (
	profileId: number,
	patientSurgeryId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/surgery/${patientSurgeryId}/remove`
	);
	return response.data;
};

// Diets Management
export const addDietToPatient = async (
	profileId: number,
	dietData: {
		name: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/diet/create`,
		dietData
	);
	return response.data;
};

export const updatePatientDiet = async (
	profileId: number,
	patientDietId: number,
	dietData: {
		name?: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/diet/${patientDietId}/update`,
		dietData
	);
	return response.data;
};

export const removeDietFromPatient = async (
	profileId: number,
	patientDietId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/diet/${patientDietId}/remove`
	);
	return response.data;
};

// HMO Management - Backend expects: { hmoId, hmoCompany, hmoDetail, hmoProvider }
export const addHmoToPatient = async (
	profileId: number,
	hmoData: {
		hmoId: string;
		hmoCompany: string;
		hmoDetail?: string;
		hmoProvider?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/hmo/create`,
		hmoData
	);
	return response.data;
};

export const updatePatientHmo = async (
	profileId: number,
	patientHmoId: number,
	hmoData: {
		hmoId?: string;
		hmoCompany?: string;
		hmoDetail?: string;
		hmoProvider?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/hmo/${patientHmoId}/update`,
		hmoData
	);
	return response.data;
};

export const removeHmoFromPatient = async (
	profileId: number,
	patientHmoId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/hmo/${patientHmoId}/remove`
	);
	return response.data;
};

// Family Management - Backend expects: { familyProfileId, relationship }
export const addFamilyToPatient = async (
	profileId: number,
	familyData: {
		familyProfileId: number;
		relationship: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/family/create`,
		familyData
	);
	return response.data;
};

export const removeFamilyFromPatient = async (
	profileId: number,
	patientFamilyId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/patients/${profileId}/family/${patientFamilyId}/remove`
	);
	return response.data;
};

// Add assistant note to appointment
export const addAssistantNote = async (
	appointmentId: number,
	note: string
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/appointments/${appointmentId}/add-note`,
		{ note }
	);
	return response.data;
};

// Update vital signs for appointment
export const updateVitalSigns = async (
	appointmentId: number,
	vitalSigns: {
		systolic?: string;
		diastolic?: string;
		pulseRate?: string;
		respiration?: string;
		height?: string;
		heightType?: string;
		weight?: string;
		weightType?: string;
		temperature?: string;
		temperatureType?: string;
		oxygenSaturation?: string;
		capillaryBloodGlucose?: string;
		bodyMassIndex?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/appointments/${appointmentId}/update-vital-sign`,
		vitalSigns
	);
	return response.data;
};

// Prescription Management APIs
export const createPrescription = async (
	appointmentId: number,
	prescriptionData: ICreatePrescriptionRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/appointments/${appointmentId}/prescription/create`,
		prescriptionData
	);
	return response.data;
};

export const removePrescription = async (
	prescriptionId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/prescriptions/${prescriptionId}/remove`
	);
	return response.data;
};

export const createPrescriptionItem = async (
	prescriptionId: number,
	itemData: ICreatePrescriptionItemRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/prescriptions/${prescriptionId}/items/create`,
		itemData
	);
	return response.data;
};

export const removePrescriptionItem = async (
	prescriptionId: number,
	prescriptionItemId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/prescriptions/${prescriptionId}/items/${prescriptionItemId}/remove`
	);
	return response.data;
};

// Reschedule appointment (Assistant)
export const rescheduleAssistantAppointment = async (
	appointmentId: number,
	appointmentDate: string
) => {
	const response = await axios.post(
		`/assistant/appointments/${appointmentId}/reschedule`,
		{ appointmentDate }
	);
	return response.data;
};

// Assistant Clinic Management APIs
export const getAssistantClinics = async (
	params: IClinicParams = {}
): Promise<IClinicsResponse> => {
	const response = await axios.get('/assistant/clinics', { params });
	return response.data;
};

// Assistant Patient Management APIs
export const getAssistantPatients = async (
	params: ISearchParams = {}
): Promise<IPatientsResponse> => {
	const response = await axios.get('/assistant/patients', { params });
	return response.data;
};

// Assistant Appointment History APIs
export const getAssistantAppointmentHistories = async (
	params: IAppointmentParams = {}
): Promise<IAppointmentsResponse> => {
	const response = await axios.get('/assistant/appointments/histories', {
		params,
	});
	return response.data;
};

// Assistant Calendar API
export const getAssistantAppointmentCalendar = async (
	params: {
		clinicId?: number;
		status?: string;
		fromDate?: string;
		toDate?: string;
	} = {}
): Promise<IDashboardAppointmentsResponse> => {
	const response = await axios.get('/assistant/appointments/calendar', {
		params,
	});
	return response.data;
};

// Medical Record Management APIs
export const addAssistantMedicalRecord = async (
	appointmentId: number,
	recordFile: File
): Promise<IApiResponse> => {
	const formData = new FormData();
	formData.append('record', recordFile);

	const response = await axios.post(
		`/assistant/appointments/${appointmentId}/add-medical-record`,
		formData,
		{
			headers: {
				'Content-Type': 'multipart/form-data',
			},
		}
	);
	return response.data;
};

export const removeAssistantMedicalRecord = async (
	appointmentId: number,
	medicalRecordId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/appointments/${appointmentId}/remove-medical-record/${medicalRecordId}`
	);
	return response.data;
};

// Get all medical records for a doctor (across all appointments)
export const getAllAssistantMedicalRecords = async (
	params: { page?: number; pageSize?: number; search?: string } = {}
): Promise<IAppointmentsResponse> => {
	// Since there's no direct endpoint, we'll need to get this through appointments
	// This would require a new backend endpoint or we can work with appointment data
	const response = await axios.get('/assistant/appointments', { params });
	return response.data;
};

// Patient appointment history (Assistant)
export const getAssistantPatientAppointmentHistory = async (
	profileId: number
) => {
	const response = await axios.get(
		`/assistant/patients/${profileId}/appointment-history`
	);
	return response.data.data;
};

// Assistant Medical Documents APIs
export const getAssistantPrescriptions = async (
	params: ISearchParams = {}
): Promise<IApiResponse> => {
	const response = await axios.get('/assistant/prescriptions', { params });
	return response.data;
};

export const getAssistantLabRequests = async (
	params: ISearchParams = {}
): Promise<IApiResponse> => {
	const response = await axios.get('/assistant/lab-requests', { params });
	return response.data;
};

export const getAssistantDiagnosticRequests = async (
	params: ISearchParams = {}
): Promise<IApiResponse> => {
	const response = await axios.get('/assistant/diagnostic-requests', {
		params,
	});
	return response.data;
};

export const getAssistantMedicalCertificates = async (
	params: ISearchParams = {}
): Promise<IApiResponse> => {
	const response = await axios.get('/assistant/medical-certificates', {
		params,
	});
	return response.data;
};

export const getAssistantReferrals = async (
	params: ISearchParams = {}
): Promise<IApiResponse> => {
	const response = await axios.get('/assistant/referrals', { params });
	return response.data;
};

// Assistant Standalone Prescription Management APIs
export const createAssistantStandalonePrescription = async (
	prescriptionData: ICreateStandalonePrescriptionRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		'/assistant/prescriptions/create',
		prescriptionData
	);
	return response.data;
};

export const getAssistantPrescriptionById = async (
	prescriptionId: number
): Promise<IPrescriptionDetailResponse> => {
	const response = await axios.get(
		`/assistant/prescriptions/${prescriptionId}`
	);
	return response.data;
};

export const removeAssistantPrescription = async (
	prescriptionId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/prescriptions/${prescriptionId}/remove`
	);
	return response.data;
};

export const createAssistantPrescriptionItem = async (
	prescriptionId: number,
	itemData: {
		generic: string;
		brand: string;
		dosageForm: string;
		dosage: string;
		frequency: string;
		quantity: number;
		instruction: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/prescriptions/${prescriptionId}/items/create`,
		itemData
	);
	return response.data;
};

export const removeAssistantPrescriptionItem = async (
	prescriptionId: number,
	prescriptionItemId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/prescriptions/${prescriptionId}/items/${prescriptionItemId}/remove`
	);
	return response.data;
};

// Assistant Profile API
export const getAssistantProfile = async () => {
	const response = await axios.get('/assistant/profile');
	return response.data.data;
};

// Referral Management APIs
export const createReferral = async (
	appointmentId: number,
	referralData: ICreateReferralRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/appointments/${appointmentId}/referral/create`,
		referralData
	);
	return response.data;
};

export const createStandaloneReferral = async (
	referralData: ICreateStandaloneReferralRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		'/assistant/referrals/create',
		referralData
	);
	return response.data;
};

export const getAllReferrals = async (
	params: IReferralListParams = {}
): Promise<AssistantReferralsResponse> => {
	const response = await axios.get('/assistant/referrals', { params });
	return response.data;
};

export const getReferralById = async (
	referralId: number
): Promise<IReferralDetailResponse> => {
	const response = await axios.get(`/assistant/referrals/${referralId}`);
	return response.data;
};

export const removeReferral = async (
	referralId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/referrals/${referralId}/remove`
	);
	return response.data;
};

// Laboratory Request Management APIs
export const createLabRequest = async (
	appointmentId: number,
	labRequestData: ICreateLabRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/appointments/${appointmentId}/lab-request/create`,
		labRequestData
	);
	return response.data;
};

export const removeLabRequest = async (
	labRequestId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/lab-requests/${labRequestId}/remove`
	);
	return response.data;
};

export const createLabRequestItem = async (
	labRequestId: number,
	itemData: ICreateLabRequestItemRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/lab-requests/${labRequestId}/items/create`,
		itemData
	);
	return response.data;
};

export const removeLabRequestItem = async (
	labRequestId: number,
	labRequestItemId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/lab-requests/${labRequestId}/items/${labRequestItemId}/remove`
	);
	return response.data;
};

export const uploadLabResult = async (
	appointmentId: number,
	resultData: FormData
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/appointments/${appointmentId}/laboratory-result/upload`,
		resultData,
		{
			headers: {
				'Content-Type': 'multipart/form-data',
			},
		}
	);
	return response.data;
};

// Medical Certificate Management APIs
export const createMedicalCertificate = async (
	appointmentId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/appointments/${appointmentId}/medical-certificate/create`,
		{} // Backend doesn't use request body, sends empty object
	);
	return response.data;
};

export const removeMedicalCertificate = async (
	medicalCertificateId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/medical-certificates/${medicalCertificateId}/remove`
	);
	return response.data;
};

export const createMedicalCertificateItem = async (
	medicalCertificateId: number,
	itemData: ICreateMedicalCertificateItemRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/medical-certificates/${medicalCertificateId}/items/create`,
		itemData
	);
	return response.data;
};

export const removeMedicalCertificateItem = async (
	medicalCertificateId: number,
	medicalCertificateItemId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/medical-certificates/${medicalCertificateId}/items/${medicalCertificateItemId}/remove`
	);
	return response.data;
};

// Standalone Lab Request Management APIs
export const createStandaloneLabRequest = async (
	labRequestData: ICreateStandaloneLabRequestRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		'/assistant/lab-requests/create',
		labRequestData
	);
	return response.data;
};

// Standalone Diagnostic Request Management APIs
export const createStandaloneDiagnosticRequest = async (
	diagnosticRequestData: ICreateStandaloneDiagnosticRequestRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		'/assistant/diagnostic-requests/create',
		diagnosticRequestData
	);
	return response.data;
};

export const getAllLabRequests = async (
	params: ILabRequestListParams = {}
): Promise<ILabRequestsResponse> => {
	const response = await axios.get('/assistant/lab-requests', { params });
	return response.data;
};

export const getLabRequestById = async (
	labRequestId: number
): Promise<ILabRequestDetailResponse> => {
	const response = await axios.get(`/assistant/lab-requests/${labRequestId}`);
	return response.data;
};

export const getMedicalCertificateById = async (
	medicalCertificateId: number
): Promise<IMedicalCertificateDetailResponse> => {
	const response = await axios.get(
		`/assistant/medical-certificates/${medicalCertificateId}`
	);
	return response.data;
};

export const removeStandaloneLabRequest = async (
	labRequestId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/lab-requests/${labRequestId}/remove`
	);
	return response.data;
};

export const getAllDiagnosticRequests = async (
	params: IDiagnosticRequestListParams = {}
): Promise<IDiagnosticRequestsResponse> => {
	const response = await axios.get('/assistant/diagnostic-requests', {
		params,
	});
	return response.data;
};

export const getAllMedicalCertificates = async (
	params: IMedicalCertificateListParams = {}
): Promise<IMedicalCertificatesResponse> => {
	const response = await axios.get('/assistant/medical-certificates', {
		params,
	});
	return response.data;
};

export const removeStandaloneDiagnosticRequest = async (
	diagnosticRequestId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/diagnostic-requests/${diagnosticRequestId}/remove`
	);
	return response.data;
};

export const getDiagnosticRequestById = async (
	diagnosticRequestId: number
): Promise<IDiagnosticRequestDetailResponse> => {
	const response = await axios.get(
		`/assistant/diagnostic-requests/${diagnosticRequestId}`
	);
	return response.data;
};

// Diagnostic Request Management APIs
export const createDiagnosticRequest = async (
	appointmentId: number,
	diagnosticRequestData: ICreateDiagnosticRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/appointments/${appointmentId}/diagnostic-request/create`,
		diagnosticRequestData
	);
	return response.data;
};

export const removeDiagnosticRequest = async (
	diagnosticRequestId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/diagnostic-requests/${diagnosticRequestId}/remove`
	);
	return response.data;
};

export const createDiagnosticRequestItem = async (
	diagnosticRequestId: number,
	itemData: ICreateDiagnosticRequestItemRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/diagnostic-requests/${diagnosticRequestId}/items/create`,
		itemData
	);
	return response.data;
};

export const removeDiagnosticRequestItem = async (
	diagnosticRequestId: number,
	diagnosticRequestItemId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/assistant/diagnostic-requests/${diagnosticRequestId}/items/${diagnosticRequestItemId}/remove`
	);
	return response.data;
};

// Assistant Dashboard Query Keys
export const ASSISTANT_QUERY_KEYS = {
	PROFILE: {
		DETAIL: ['assistant', 'profile'],
	},
	DASHBOARD: {
		METRICS: ['assistant', 'dashboard', 'metrics'],
	},
	CLINICS: {
		ALL: (params: IClinicParams) => ['assistant', 'clinics', 'all', params],
	},
	PATIENTS: {
		ALL: (params: ISearchParams) => ['assistant', 'patients', 'all', params],
		DETAIL: (profileId: number) => [
			'assistant',
			'patients',
			'detail',
			profileId,
		],
		APPOINTMENT_HISTORY: (profileId: number) => [
			'assistant',
			'patients',
			'appointment-history',
			profileId,
		],
		HABITS: (profileId: number) => [
			'assistant',
			'patients',
			profileId,
			'habits',
		],
		HISTORY: (profileId: number) => [
			'assistant',
			'patients',
			profileId,
			'history',
		],
		ALLERGIES: (profileId: number) => [
			'assistant',
			'patients',
			profileId,
			'allergies',
		],
		ILLNESSES: (profileId: number) => [
			'assistant',
			'patients',
			profileId,
			'illnesses',
		],
		SURGERIES: (profileId: number) => [
			'assistant',
			'patients',
			profileId,
			'surgeries',
		],
		DIETS: (profileId: number) => ['assistant', 'patients', profileId, 'diets'],
		HMO: (profileId: number) => ['assistant', 'patients', profileId, 'hmo'],
		FAMILY: (profileId: number) => [
			'assistant',
			'patients',
			profileId,
			'family',
		],
	},
	APPOINTMENTS: {
		ALL: (params: IAppointmentParams) => [
			'assistant',
			'appointments',
			'all',
			params,
		],
		TODAY: (params: IAppointmentParams) => [
			'assistant',
			'appointments',
			'today',
			params,
		],
		TODAY_ATTENDED: (params: IAppointmentParams) => [
			'assistant',
			'appointments',
			'today-attended',
			params,
		],
		DETAIL: (appointmentId: number) => [
			'assistant',
			'appointments',
			'detail',
			appointmentId,
		],
		HISTORIES: (params: IAppointmentParams) => [
			'assistant',
			'appointments',
			'histories',
			params,
		],
		CALENDAR: (params: {
			clinicId?: number;
			status?: string;
			fromDate?: string;
			toDate?: string;
		}) => ['assistant', 'appointments', 'calendar', params],
	},
	PRESCRIPTIONS: {
		ALL: (params: IPrescriptionListParams) => [
			'assistant',
			'prescriptions',
			'all',
			params,
		],
		BY_APPOINTMENT: (appointmentId: number) => [
			'assistant',
			'prescriptions',
			'appointment',
			appointmentId,
		],
		DETAIL: (prescriptionId: number) => [
			'assistant',
			'prescriptions',
			'detail',
			prescriptionId,
		],
	},
	DIAGNOSTIC_REQUESTS: {
		BY_APPOINTMENT: (appointmentId: number) => [
			'assistant',
			'diagnostic-requests',
			'appointment',
			appointmentId,
		],
		DETAIL: (diagnosticRequestId: number) => [
			'assistant',
			'diagnostic-requests',
			'detail',
			diagnosticRequestId,
		],
		ALL: (params: IDiagnosticRequestListParams) => [
			'assistant',
			'diagnostic-requests',
			'all',
			params,
		],
	},
	LAB_REQUESTS: {
		ALL: (params: ILabRequestListParams) => [
			'assistant',
			'lab-requests',
			'all',
			params,
		],
		BY_APPOINTMENT: (appointmentId: number) => [
			'assistant',
			'lab-requests',
			'appointment',
			appointmentId,
		],
		DETAIL: (labRequestId: number) => [
			'assistant',
			'lab-requests',
			'detail',
			labRequestId,
		],
	},
	MEDICAL_DOCUMENTS: {
		PRESCRIPTIONS: (params: ISearchParams) => [
			'assistant',
			'prescriptions',
			'all',
			params,
		],
		APPOINTMENT_HISTORY: (profileId: number) => [
			'assistant',
			'patients',
			'appointment-history',
			profileId,
		],
		LAB_REQUESTS: (params: ISearchParams) => [
			'assistant',
			'lab-requests',
			'all',
			params,
		],
		DIAGNOSTIC_REQUESTS: (params: ISearchParams) => [
			'assistant',
			'diagnostic-requests',
			'all',
			params,
		],
		MEDICAL_CERTIFICATES: (params: ISearchParams) => [
			'assistant',
			'medical-certificates',
			'all',
			params,
		],
		REFERRALS: (params: ISearchParams) => [
			'assistant',
			'referrals',
			'all',
			params,
		],
	},
	MEDICAL_CERTIFICATES: {
		ALL: (params: IMedicalCertificateListParams) => [
			'assistant',
			'medical-certificates',
			'all',
			params,
		],
		BY_APPOINTMENT: (appointmentId: number) => [
			'assistant',
			'medical-certificates',
			'appointment',
			appointmentId,
		],
		DETAIL: (medicalCertificateId: number) => [
			'assistant',
			'medical-certificates',
			'detail',
			medicalCertificateId,
		],
	},
	REFERRALS: {
		ALL: (params: IReferralListParams) => [
			'assistant',
			'referrals',
			'all',
			params,
		],
		BY_APPOINTMENT: (appointmentId: number) => [
			'assistant',
			'referrals',
			'appointment',
			appointmentId,
		],
		DETAIL: (referralId: number) => [
			'assistant',
			'referrals',
			'detail',
			referralId,
		],
	},
} as const;
