import { isValidPhoneNumber } from 'react-phone-number-input';
import { z } from 'zod';

const MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB

export const RegisterDoctorSchema = z.object({
	firstName: z.string().min(1, 'First name is required'),
	middleName: z.string().optional(),
	lastName: z.string().min(1, 'Last name is required'),
	suffix: z.string().optional(),
	email: z.string().email('Invalid email address'),
	phone: z
		.string()
		.min(1, 'Phone number is required')
		.refine((value) => value && isValidPhoneNumber(value), {
			message: 'Invalid phone number',
		}),
	gender: z.string().min(1, 'Gender is required'),
	birthday: z
		.string()
		.regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format. Use YYYY-MM-DD'),
	prcNumber: z.string().optional(),
	prcExpiryDate: z
		.string()
		.regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format. Use YYYY-MM-DD')
		.optional(),
	prcImageFront: z
		.any()
		.optional()
		.refine((file) => {
			if (!file) return true; // Optional field
			if (typeof File !== 'undefined' && file instanceof File) {
				return file.size <= MAX_FILE_SIZE;
			}
			return true; // Skip validation in server environment
		}, 'Max file size is 20MB'),
	prcImageBack: z
		.any()
		.optional()
		.refine((file) => {
			if (!file) return true; // Optional field
			if (typeof File !== 'undefined' && file instanceof File) {
				return file.size <= MAX_FILE_SIZE;
			}
			return true; // Skip validation in server environment
		}, 'Max file size is 20MB'),

	clinicAddress: z.string().optional(),
	clinicName: z.string().optional(),
	specialty: z.string().optional(),
});

export type IRegisterDoctor = z.infer<typeof RegisterDoctorSchema>;
