import { isValidPhoneNumber } from 'react-phone-number-input';
import { z } from 'zod';

// Address Schema
export const addressSchema = z.object({
	country: z.string().optional(),
	region: z.string().min(1, 'Region is required'),
	province: z.string().min(1, 'Province is required'),
	city: z.string().min(1, 'City is required'),
	barangay: z.string().min(1, 'Barangay is required'),
	streetName: z.string().min(1, 'Street is required'),
	lotBlockPhaseStreet: z.string().optional(),
	unitRoomFloorBuilding: z.string().optional(),
	subdivisionVillageZone: z.string().optional(),
	buildingApartment: z.string().optional(),
});

export type IAddress = z.infer<typeof addressSchema>;

// Patient Registration Schema
export const PatientRegistrationSchema = z
	.object({
		// Personal Information
		title: z.string().min(1, 'Title is required'),
		firstName: z.string().min(1, 'First name is required'),
		middleName: z.string().optional(),
		lastName: z.string().min(1, 'Last name is required'),
		suffix: z.string().optional(),
		email: z.string().email('Invalid email address'),
		phone: z
			.string()
			.min(1, 'Phone number is required')
			.refine((value) => value && isValidPhoneNumber(value), {
				message: 'Invalid phone number',
			}),
		gender: z.string().min(1, 'Gender is required'),
		birthday: z
			.string()
			.regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format. Use YYYY-MM-DD'),
		occupation: z.string().min(1, 'Occupation is required'),
		civilStatus: z.string().min(1, 'Civil status is required'),
		bloodType: z.string().min(1, 'Blood type is required'),
		ethnicity: z.string().optional(),
		religion: z.string().min(1, 'Religion is required'),
		height: z
			.string()
			.min(1, 'Height is required')
			.regex(/^[0-9]*$/, 'Height must be a number')
			.refine((value) => value && parseInt(value) > 0, {
				message: 'Height must be greater than 0',
			}),
		heightType: z.enum(['cm', 'ft', 'in']),
		weight: z
			.string()
			.min(1, 'Weight is required')
			.regex(/^[0-9]*$/, 'Weight must be a number')
			.refine((value) => value && parseInt(value) > 0, {
				message: 'Weight must be greater than 0',
			}),
		weightType: z.enum(['kg', 'lbs']),
		isPwd: z.enum(['1', '0']),

		// Guardian Information (required when isPwd = '1')
		guardianFirstName: z.string().optional(),
		guardianMiddleName: z.string().optional(),
		guardianLastName: z.string().optional(),
		guardianContactNumber: z.string().optional(),
		guardianEmail: z.string().email('Invalid email address').optional(),
		guardianBirthdate: z
			.string()
			.regex(/^$|^\d{4}-\d{2}-\d{2}$/, 'Invalid date format. Use YYYY-MM-DD')
			.optional(),

		// Address Information
		currentAddress: addressSchema,
		permanentAddress: addressSchema,

		// Emergency Contact
		emergencyContactName: z
			.string()
			.min(1, 'Emergency contact name is required'),
		emergencyContactNumber: z
			.string()
			.min(1, 'Emergency contact number is required'),

		// Additional
		clinicIds: z.array(z.string()).optional(),
	})
	.superRefine((val, ctx) => {
		if (val.isPwd === '1') {
			if (!val.guardianEmail) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					path: ['guardianEmail'],
					message: 'Guardian email is required',
				});
			}
			if (!val.guardianFirstName) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					path: ['guardianFirstName'],
					message: 'Guardian first name is required',
				});
			}
			if (!val.guardianLastName) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					path: ['guardianLastName'],
					message: 'Guardian last name is required',
				});
			}
			if (
				!val.guardianBirthdate ||
				!/^\d{4}-\d{2}-\d{2}$/.test(val.guardianBirthdate)
			) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					path: ['guardianBirthdate'],
					message: 'Guardian birthdate is required (YYYY-MM-DD)',
				});
			}
		}
	});

export type IPatientRegistration = z.infer<typeof PatientRegistrationSchema>;
