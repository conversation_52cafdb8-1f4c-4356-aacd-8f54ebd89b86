import axios from '@/core/api';
import {
	IApiResponse,
	IAppointment,
	IAppointmentParams,
	IAppointmentsResponse,
	IAssignAssistantRequest,
	IAssistantsResponse,
	IClinicParams,
	IClinicsResponse,
	ICreateAppointmentRequest,
	ICreateAssistantRequest,
	ICreateClinicRequest,
	ICreateDiagnosticRequest,
	ICreateDiagnosticRequestItemRequest,
	ICreateLabRequest,
	ICreateLabRequestItemRequest,
	ICreateMedicalCertificateItemRequest,
	ICreatePatientRequest,
	ICreatePrescriptionItemRequest,
	ICreatePrescriptionRequest,
	ICreateReferralRequest,
	ICreateStandaloneDiagnosticRequestRequest,
	ICreateStandaloneLabRequestRequest,
	ICreateStandaloneMedicalCertificateRequest,
	ICreateStandalonePrescriptionRequest,
	ICreateStandaloneReferralRequest,
	IDashboardAppointmentsResponse,
	IDiagnosticRequestDetailResponse,
	IDiagnosticRequestListParams,
	IDiagnosticRequestsResponse,
	IDoctorDashboardResponse,
	ILabRequestDetailResponse,
	ILabRequestListParams,
	ILabRequestsResponse,
	IMedicalCertificateDetailResponse,
	IMedicalCertificateListParams,
	IMedicalCertificatesResponse,
	IPaginationParams,
	IPatientsResponse,
	IPrescriptionDetailResponse,
	IPrescriptionListParams,
	IPrescriptionsResponse,
	IReferralDetailResponse,
	IReferralListParams,
	IReferralsResponse,
	ISearchParams,
	ISubscribe,
	ISubscriptionListParams,
	ISubscriptionsResponse,
	IUpdateAppointmentRequest,
	IUpdateAssistantCredentialsRequest,
	IUpdateAssistantRequest,
	IUpdateClinicRequest,
	IUpdatePatientRequest,
} from '@/features/dashboard/types/doctor.types';

export const updatePrescriptionPtr = async (
	prescriptionId: number,
	data: {
		professionalTaxReceipt: string;
	}
) => {
	const response = await axios.post(
		`/doctor-assistant/prescriptions/${prescriptionId}/update-professional-tax-receipt`,
		data
	);
	return response.data;
};

export const updateLabRequestPtr = async (
	labRequestId: number,
	data: {
		professionalTaxReceipt: string;
	}
) => {
	const response = await axios.post(
		`/doctor-assistant/lab-requests/${labRequestId}/update-professional-tax-receipt`,
		data
	);
	return response.data;
};

export const updateMedicalCertificatePtr = async (
	medicalCertificateId: number,
	data: {
		professionalTaxReceipt: string;
	}
) => {
	const response = await axios.post(
		`/doctor-assistant/medical-certificates/${medicalCertificateId}/update-professional-tax-receipt`,
		data
	);
	return response.data;
};

export const updateReferralPtr = async (
	referralId: number,
	data: {
		professionalTaxReceipt: string;
	}
) => {
	const response = await axios.post(
		`/doctor-assistant/referrals/${referralId}/update-professional-tax-receipt`,
		data
	);
	return response.data;
};

export const updateDiagnosticRequestPtr = async (
	diagnosticRequestId: number,
	data: {
		professionalTaxReceipt: string;
	}
) => {
	const response = await axios.post(
		`/doctor-assistant/diagnostic-requests/${diagnosticRequestId}/update-professional-tax-receipt`,
		data
	);
	return response.data;
};

export const updateAppointmentPtr = async (
	appointmentId: number,
	data: {
		professionalTaxReceipt: string;
	}
) => {
	const response = await axios.post(
		`/doctor-assistant/appointments/${appointmentId}/update-professional-tax-receipt`,
		data
	);
	return response.data;
};

export const createMedicalDescription = async (data: {
	name: string;
	description?: string;
}) => {
	const response = await axios.post(
		'/doctor-assistant/data-management/medical-descriptions/create',
		data
	);
	return response.data;
};

export const createDiagnosticRequestType = async (data: {
	name: string;
	description?: string;
}) => {
	const response = await axios.post(
		'/doctor-assistant/data-management/diagnostic-request-types/create',
		data
	);
	return response.data;
};

export const createLabRequestType = async (data: {
	name: string;
	description?: string;
}) => {
	const response = await axios.post(
		'/doctor-assistant/data-management/lab-request-types/create',
		data
	);
	return response.data;
};

export const createPrescriptionType = async (data: {
	name: string;
	description?: string;
}) => {
	const response = await axios.post(
		'/doctor-assistant/data-management/prescription-types/create',
		data
	);
	return response.data;
};

// Doctor Dashboard Metrics
export const getDoctorDashboardMetrics =
	async (): Promise<IDoctorDashboardResponse> => {
		const response = await axios.get('/doctor-assistant/dashboard');
		return response.data;
	};

// Today's Queue Appointments (not completed)
export const getTodaysQueueAppointments = async (
	params: IAppointmentParams = {}
): Promise<IDashboardAppointmentsResponse> => {
	const response = await axios.get(
		'/doctor-assistant/dashboard/todays-queue-appointments',
		{ params }
	);
	return response.data;
};

// Today's Attended Appointments (completed)
export const getTodaysAttendedAppointments = async (
	params: IAppointmentParams = {}
): Promise<IDashboardAppointmentsResponse> => {
	const response = await axios.get(
		'/doctor-assistant/dashboard/todays-attended-appointments',
		{ params }
	);
	return response.data;
};

// Clinic Management APIs
export const getDoctorClinics = async (
	params: IClinicParams = {}
): Promise<IClinicsResponse> => {
	const response = await axios.get('/doctor-assistant/clinics', { params });
	return response.data;
};

export const getClinicById = async (clinicId: number) => {
	const response = await axios.get(`/doctor-assistant/clinics/${clinicId}`);
	return response.data.data;
};

export const createClinic = async (
	clinicData: ICreateClinicRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		'/doctor-assistant/clinics/create',
		clinicData
	);
	return response.data;
};

export const updateClinic = async (
	clinicData: IUpdateClinicRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		'/doctor-assistant/clinics/update',
		clinicData
	);
	return response.data;
};

export const enableClinic = async (clinicId: number) => {
	const response = await axios.post('/doctor-assistant/clinics/enable', {
		clinicId: clinicId,
	});
	return response.data;
};

export const disableClinic = async (clinicId: number) => {
	const response = await axios.post('/doctor-assistant/clinics/disable', {
		clinicId: clinicId,
	});
	return response.data;
};

export const archiveClinic = async (clinicId: number) => {
	const response = await axios.post('/doctor-assistant/clinics/archive', {
		clinicId: clinicId,
	});
	return response.data;
};

// Patient Management APIs
export const getDoctorPatients = async (
	params: ISearchParams = {}
): Promise<IPatientsResponse> => {
	const response = await axios.get('/doctor-assistant/patients', { params });
	return response.data;
};

export const getClinicPatients = async (
	clinicId: number,
	params: ISearchParams = {}
): Promise<IPatientsResponse> => {
	const response = await axios.get(
		`/doctor-assistant/clinics/${clinicId}/patients`,
		{
			params,
		}
	);
	return response.data;
};

export const getPatientById = async (profileId: number) => {
	const response = await axios.get(`/doctor-assistant/patients/${profileId}`);
	return response.data.data;
};

export const getPatientAppointmentHistory = async (profileId: number) => {
	const response = await axios.get(
		`/doctor-assistant/patients/${profileId}/appointment-history`
	);
	return response.data.data;
};

export const rescheduleAppointment = async (
	appointmentId: number,
	appointmentDate: string
) => {
	const response = await axios.post(
		`/doctor-assistant/appointments/${appointmentId}/reschedule`,
		{ appointmentDate }
	);
	return response.data;
};

export const createPatient = async (patientData: ICreatePatientRequest) => {
	const response = await axios.post(
		'/doctor-assistant/patients/create',
		patientData
	);
	return response.data;
};

export const updatePatient = async (
	patientData: IUpdatePatientRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		'/doctor-assistant/patients/update',
		patientData
	);
	return response.data;
};

export const enablePatient = async (profileId: number) => {
	const response = await axios.post('/doctor-assistant/patients/enable', {
		profileId: profileId,
	});
	return response.data;
};

export const disablePatient = async (profileId: number) => {
	const response = await axios.post('/doctor-assistant/patients/disable', {
		profileId: profileId,
	});
	return response.data;
};

export const archivePatient = async (profileId: number) => {
	const response = await axios.post('/doctor-assistant/patients/archive', {
		profileId: profileId,
	});
	return response.data;
};

// Patient Profile Management APIs

// Habits Management
export const addHabitToPatient = async (
	profileId: number,
	habitData: {
		name: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/habit/create`,
		habitData
	);
	return response.data;
};

export const updatePatientHabit = async (
	profileId: number,
	patientHabitId: number,
	habitData: {
		name?: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/habit/${patientHabitId}/update`,
		habitData
	);
	return response.data;
};

export const removeHabitFromPatient = async (
	profileId: number,
	patientHabitId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/habit/${patientHabitId}/remove`
	);
	return response.data;
};

// History Management
export const addHistoryToPatient = async (
	profileId: number,
	historyData: {
		name: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/history/create`,
		historyData
	);
	return response.data;
};

export const updatePatientHistory = async (
	profileId: number,
	patientHistoryId: number,
	historyData: {
		name?: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/history/${patientHistoryId}/update`,
		historyData
	);
	return response.data;
};

export const removeHistoryFromPatient = async (
	profileId: number,
	patientHistoryId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/history/${patientHistoryId}/remove`
	);
	return response.data;
};

// Allergies Management
export const addAllergyToPatient = async (
	profileId: number,
	allergyData: {
		name: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/allergy/create`,
		allergyData
	);
	return response.data;
};

export const updatePatientAllergy = async (
	profileId: number,
	patientAllergyId: number,
	allergyData: {
		name?: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/allergy/${patientAllergyId}/update`,
		allergyData
	);
	return response.data;
};

export const removeAllergyFromPatient = async (
	profileId: number,
	patientAllergyId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/allergy/${patientAllergyId}/remove`
	);
	return response.data;
};

// Illnesses Management
export const addIllnessToPatient = async (
	profileId: number,
	illnessData: {
		name: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/illness/create`,
		illnessData
	);
	return response.data;
};

export const updatePatientIllness = async (
	profileId: number,
	patientIllnessId: number,
	illnessData: {
		name?: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/illness/${patientIllnessId}/update`,
		illnessData
	);
	return response.data;
};

export const removeIllnessFromPatient = async (
	profileId: number,
	patientIllnessId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/illness/${patientIllnessId}/remove`
	);
	return response.data;
};

// Surgeries Management
export const addSurgeryToPatient = async (
	profileId: number,
	surgeryData: {
		name: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/surgery/create`,
		surgeryData
	);
	return response.data;
};

export const updatePatientSurgery = async (
	profileId: number,
	patientSurgeryId: number,
	surgeryData: {
		name?: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/surgery/${patientSurgeryId}/update`,
		surgeryData
	);
	return response.data;
};

export const removeSurgeryFromPatient = async (
	profileId: number,
	patientSurgeryId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/surgery/${patientSurgeryId}/remove`
	);
	return response.data;
};

// Diets Management
export const addDietToPatient = async (
	profileId: number,
	dietData: {
		name: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/diet/create`,
		dietData
	);
	return response.data;
};

export const updatePatientDiet = async (
	profileId: number,
	patientDietId: number,
	dietData: {
		name?: string;
		description?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/diet/${patientDietId}/update`,
		dietData
	);
	return response.data;
};

export const removeDietFromPatient = async (
	profileId: number,
	patientDietId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/diet/${patientDietId}/remove`
	);
	return response.data;
};

// HMO Management - Backend expects: { hmoId, hmoCompany, hmoDetail, hmoProvider }
export const addHmoToPatient = async (
	profileId: number,
	hmoData: {
		hmoId: string;
		hmoCompany: string;
		hmoDetail?: string;
		hmoProvider?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/hmo/create`,
		hmoData
	);
	return response.data;
};

export const updatePatientHmo = async (
	profileId: number,
	patientHmoId: number,
	hmoData: {
		hmoId?: string;
		hmoCompany?: string;
		hmoDetail?: string;
		hmoProvider?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/hmo/${patientHmoId}/update`,
		hmoData
	);
	return response.data;
};

export const removeHmoFromPatient = async (
	profileId: number,
	patientHmoId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/hmo/${patientHmoId}/remove`
	);
	return response.data;
};

// Family Management - Backend expects: { familyProfileId, relationship }
export const addFamilyToPatient = async (
	profileId: number,
	familyData: {
		familyProfileId: number;
		relationship: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/family/create`,
		familyData
	);
	return response.data;
};

export const removeFamilyFromPatient = async (
	profileId: number,
	patientFamilyId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/patients/${profileId}/family/${patientFamilyId}/remove`
	);
	return response.data;
};

// Assistant Management APIs
export const getDoctorAssistants = async (
	params: IPaginationParams = {}
): Promise<IAssistantsResponse> => {
	const response = await axios.get('/doctor-assistant/assistants', { params });
	return response.data;
};

export const getClinicAssistants = async (
	clinicId: number,
	params: ISearchParams = {}
): Promise<IAssistantsResponse> => {
	const response = await axios.get(
		`/doctor-assistant/clinics/${clinicId}/assistants`,
		{
			params,
		}
	);
	return response.data;
};

export const getAssistantById = async (assistantProfileId: number) => {
	const response = await axios.get(
		`/doctor-assistant/assistants/${assistantProfileId}`
	);
	return response.data.data;
};

export const getAssistantClinics = async (assistantProfileId: number) => {
	const response = await axios.get(
		`/doctor-assistant/assistants/${assistantProfileId}/clinics`
	);
	return response.data;
};

export const createAssistant = async (
	clinicId: number,
	assistantData: ICreateAssistantRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/clinics/${clinicId}/create-assistant`,
		assistantData
	);
	return response.data;
};

export const assignAssistant = async (
	clinicId: number,
	assistantData: IAssignAssistantRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/clinics/${clinicId}/assign-assistant`,
		assistantData
	);
	return response.data;
};

export const updateAssistant = async (
	clinicId: number,
	assistantData: IUpdateAssistantRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/clinics/${clinicId}/update-assistant`,
		assistantData
	);
	return response.data;
};

export const removeAssistant = async (
	clinicId: number,
	assistantProfileId: number
) => {
	const response = await axios.post(
		`/doctor-assistant/clinics/${clinicId}/remove-assistant`,
		{
			assistantProfileId: assistantProfileId,
		}
	);
	return response.data;
};

export const updateAssistantCredentials = async (
	assistantProfileId: number,
	credentialsData: IUpdateAssistantCredentialsRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/assistants/${assistantProfileId}/update-credentials`,
		credentialsData
	);
	return response.data;
};

export const archiveAssistant = async (assistantProfileId: number) => {
	const response = await axios.post('/doctor-assistant/assistants/archive', {
		assistantProfileId: assistantProfileId,
	});
	return response.data;
};

export const disableAssistant = async (assistantProfileId: number) => {
	const response = await axios.post('/doctor-assistant/assistants/disable', {
		assistantProfileId: assistantProfileId,
	});
	return response.data;
};

export const enableAssistant = async (assistantProfileId: number) => {
	const response = await axios.post('/doctor-assistant/assistants/enable', {
		assistantProfileId: assistantProfileId,
	});
	return response.data;
};

// Appointment Management APIs (Note: These endpoints may need to be implemented in the backend)
export const getDoctorAppointments = async (
	params: IAppointmentParams = {}
): Promise<IAppointmentsResponse> => {
	const response = await axios.get('/doctor-assistant/appointments', {
		params,
	});
	return response.data;
};

export const getTodayAppointments = async (
	params: IAppointmentParams = {}
): Promise<IAppointmentsResponse> => {
	// Since backend doesn't support date filtering, get all appointments and filter client-side
	const response = await axios.get('/doctor-assistant/appointments', {
		params,
	});
	const allAppointments = response.data;

	// Filter appointments for today's date
	const today = new Date().toISOString().split('T')[0];
	const todayAppointments = allAppointments.data.data.filter(
		(appointment: { appointment_date: string }) => {
			const appointmentDate = new Date(appointment.appointment_date)
				.toISOString()
				.split('T')[0];
			return appointmentDate === today;
		}
	);

	// Return in the same format as the original response
	return {
		...allAppointments,
		data: {
			...allAppointments.data,
			data: todayAppointments,
		},
	};
};

export const getAppointmentsByDateRange = async (
	startDate: string,
	endDate: string,
	params: Omit<IAppointmentParams, 'date'> = {}
): Promise<IAppointmentsResponse> => {
	const response = await axios.get('/doctor-assistant/appointments', {
		params: {
			...params,
			start_date: startDate,
			end_date: endDate,
			pageSize: 1000, // Get all appointments in range for calendar view
		},
	});
	return response.data;
};

export const getAppointmentById = async (
	appointmentId: number
): Promise<IAppointment> => {
	const response = await axios.get(
		`/doctor-assistant/appointments/${appointmentId}`
	);
	return response.data.data;
};

export const getAppointmentCalendar = async (
	params: {
		clinicId?: number;
		status?: string;
		fromDate?: string;
		toDate?: string;
	} = {}
): Promise<IDashboardAppointmentsResponse> => {
	const response = await axios.get('/doctor-assistant/appointments/calendar', {
		params,
	});
	return response.data;
};

export const getAppointmentHistories = async (
	params: {
		page?: number;
		pageSize?: number;
		search?: string;
		category?: string;
		clinicId?: number;
		status?: string;
		fromDate?: string;
		toDate?: string;
	} = {}
): Promise<IAppointmentsResponse> => {
	const response = await axios.get('/doctor-assistant/appointments/histories', {
		params,
	});
	return response.data;
};

export const createAppointment = async (
	appointmentData: ICreateAppointmentRequest
) => {
	const response = await axios.post(
		'/doctor-assistant/appointments/create',
		appointmentData
	);
	return response.data;
};

export const updateAppointment = async (
	appointmentData: IUpdateAppointmentRequest
): Promise<IApiResponse> => {
	const { appointment_id, ...data } = appointmentData;
	const response = await axios.post(
		`/doctor-assistant/appointments/${appointment_id}/update`,
		data
	);
	return response.data;
};

export const updateAppointmentStatus = async (
	appointmentId: number,
	status: string
) => {
	const response = await axios.post(
		`/doctor-assistant/appointments/${appointmentId}/update-status`,
		{
			status,
		}
	);
	return response.data;
};

// Update appointment details (chief complaint, diagnosis, prognosis)
export const updateAppointmentDetails = async (
	appointmentId: number,
	data: {
		chiefComplaint?: string;
		diagnosis?: string;
		prognosis?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/appointments/${appointmentId}/update`,
		data
	);
	return response.data;
};

// Add doctor note to appointment
export const addDoctorNote = async (
	appointmentId: number,
	note: string
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/appointments/${appointmentId}/add-note`,
		{ note }
	);
	return response.data;
};

// Update vital signs for appointment
export const updateVitalSigns = async (
	appointmentId: number,
	vitalSigns: {
		systolic?: string;
		diastolic?: string;
		pulseRate?: string;
		respiration?: string;
		height?: string;
		heightType?: string;
		weight?: string;
		weightType?: string;
		temperature?: string;
		temperatureType?: string;
		oxygenSaturation?: string;
		capillaryBloodGlucose?: string;
		bodyMassIndex?: string;
	}
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/appointments/${appointmentId}/update-vital-sign`,
		vitalSigns
	);
	return response.data;
};

// Prescription Management APIs
export const createPrescription = async (
	appointmentId: number,
	prescriptionData: ICreatePrescriptionRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/appointments/${appointmentId}/prescription/create`,
		prescriptionData
	);
	return response.data;
};

export const removePrescription = async (
	prescriptionId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/prescriptions/${prescriptionId}/remove`
	);
	return response.data;
};

export const createPrescriptionItem = async (
	prescriptionId: number,
	itemData: ICreatePrescriptionItemRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/prescriptions/${prescriptionId}/items/create`,
		itemData
	);
	return response.data;
};

export const updatePrescriptionItem = async (
	prescriptionId: number,
	prescriptionItemId: number,
	itemData: ICreatePrescriptionItemRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/prescriptions/${prescriptionId}/items/${prescriptionItemId}/update`,
		itemData
	);
	return response.data;
};

export const removePrescriptionItem = async (
	prescriptionId: number,
	prescriptionItemId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/prescriptions/${prescriptionId}/items/${prescriptionItemId}/remove`
	);
	return response.data;
};

// Standalone Prescription Management APIs
export const createStandalonePrescription = async (
	prescriptionData: ICreateStandalonePrescriptionRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		'/doctor-assistant/prescriptions/create',
		prescriptionData
	);
	return response.data;
};

// Standalone Medical Certificate Management APIs
export const createStandaloneMedicalCertificate = async (
	medicalCertificateData: ICreateStandaloneMedicalCertificateRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		'/doctor-assistant/medical-certificates/create',
		medicalCertificateData
	);
	return response.data;
};

export const getAllPrescriptions = async (
	params: IPrescriptionListParams = {}
): Promise<IPrescriptionsResponse> => {
	const response = await axios.get('/doctor-assistant/prescriptions', {
		params,
	});
	return response.data;
};

export const getPrescriptionById = async (
	prescriptionId: number
): Promise<IPrescriptionDetailResponse> => {
	const response = await axios.get(
		`/doctor-assistant/prescriptions/${prescriptionId}`
	);
	return response.data;
};

// Standalone Lab Request Management APIs
export const createStandaloneLabRequest = async (
	labRequestData: ICreateStandaloneLabRequestRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		'/doctor-assistant/lab-requests/create',
		labRequestData
	);
	return response.data;
};

// Standalone Diagnostic Request Management APIs
export const createStandaloneDiagnosticRequest = async (
	diagnosticRequestData: ICreateStandaloneDiagnosticRequestRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		'/doctor-assistant/diagnostic-requests/create',
		diagnosticRequestData
	);
	return response.data;
};

export const getAllLabRequests = async (
	params: ILabRequestListParams = {}
): Promise<ILabRequestsResponse> => {
	const response = await axios.get('/doctor-assistant/lab-requests', {
		params,
	});
	return response.data;
};

export const getLabRequestById = async (
	labRequestId: number
): Promise<ILabRequestDetailResponse> => {
	const response = await axios.get(
		`/doctor-assistant/lab-requests/${labRequestId}`
	);
	return response.data;
};

export const getMedicalCertificateById = async (
	medicalCertificateId: number
): Promise<IMedicalCertificateDetailResponse> => {
	const response = await axios.get(
		`/doctor-assistant/medical-certificates/${medicalCertificateId}`
	);
	return response.data;
};

export const removeStandaloneLabRequest = async (
	labRequestId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/lab-requests/${labRequestId}/remove`
	);
	return response.data;
};

export const getAllDiagnosticRequests = async (
	params: IDiagnosticRequestListParams = {}
): Promise<IDiagnosticRequestsResponse> => {
	const response = await axios.get('/doctor-assistant/diagnostic-requests', {
		params,
	});
	return response.data;
};

export const getAllMedicalCertificates = async (
	params: IMedicalCertificateListParams = {}
): Promise<IMedicalCertificatesResponse> => {
	const response = await axios.get('/doctor-assistant/medical-certificates', {
		params,
	});
	return response.data;
};

export const removeStandaloneDiagnosticRequest = async (
	diagnosticRequestId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/diagnostic-requests/${diagnosticRequestId}/remove`
	);
	return response.data;
};

export const getDiagnosticRequestById = async (
	diagnosticRequestId: number
): Promise<IDiagnosticRequestDetailResponse> => {
	const response = await axios.get(
		`/doctor-assistant/diagnostic-requests/${diagnosticRequestId}`
	);
	return response.data;
};

// Diagnostic Request Management APIs
export const createDiagnosticRequest = async (
	appointmentId: number,
	diagnosticRequestData: ICreateDiagnosticRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/appointments/${appointmentId}/diagnostic-request/create`,
		diagnosticRequestData
	);
	return response.data;
};

export const removeDiagnosticRequest = async (
	diagnosticRequestId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/diagnostic-requests/${diagnosticRequestId}/remove`
	);
	return response.data;
};

export const createDiagnosticRequestItem = async (
	diagnosticRequestId: number,
	itemData: ICreateDiagnosticRequestItemRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/diagnostic-requests/${diagnosticRequestId}/items/create`,
		itemData
	);
	return response.data;
};

export const removeDiagnosticRequestItem = async (
	diagnosticRequestId: number,
	diagnosticRequestItemId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/diagnostic-requests/${diagnosticRequestId}/items/${diagnosticRequestItemId}/remove`
	);
	return response.data;
};

export const updateDiagnosticRequestItem = async (
	diagnosticRequestId: number,
	diagnosticRequestItemId: number,
	itemData: ICreateDiagnosticRequestItemRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/diagnostic-requests/${diagnosticRequestId}/items/${diagnosticRequestItemId}/update`,
		itemData
	);
	return response.data;
};

// Laboratory Request Management APIs
export const createLabRequest = async (
	appointmentId: number,
	labRequestData: ICreateLabRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/appointments/${appointmentId}/lab-request/create`,
		labRequestData
	);
	return response.data;
};

export const removeLabRequest = async (
	labRequestId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/lab-requests/${labRequestId}/remove`
	);
	return response.data;
};

export const createLabRequestItem = async (
	labRequestId: number,
	itemData: ICreateLabRequestItemRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/lab-requests/${labRequestId}/items/create`,
		itemData
	);
	return response.data;
};

export const removeLabRequestItem = async (
	labRequestId: number,
	labRequestItemId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/lab-requests/${labRequestId}/items/${labRequestItemId}/remove`
	);
	return response.data;
};

export const updateLabRequestItem = async (
	labRequestId: number,
	labRequestItemId: number,
	itemData: ICreateLabRequestItemRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/lab-requests/${labRequestId}/items/${labRequestItemId}/update`,
		itemData
	);
	return response.data;
};

export const uploadLabResult = async (
	appointmentId: number,
	resultData: FormData
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/appointments/${appointmentId}/laboratory-result/upload`,
		resultData,
		{
			headers: {
				'Content-Type': 'multipart/form-data',
			},
		}
	);
	return response.data;
};

// Medical Certificate Management APIs
export const createMedicalCertificate = async (
	appointmentId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/appointments/${appointmentId}/medical-certificate/create`,
		{} // Backend doesn't use request body, sends empty object
	);
	return response.data;
};

export const removeMedicalCertificate = async (
	medicalCertificateId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/medical-certificates/${medicalCertificateId}/remove`
	);
	return response.data;
};

export const createMedicalCertificateItem = async (
	medicalCertificateId: number,
	itemData: ICreateMedicalCertificateItemRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/medical-certificates/${medicalCertificateId}/items/create`,
		itemData
	);
	return response.data;
};

export const removeMedicalCertificateItem = async (
	medicalCertificateId: number,
	medicalCertificateItemId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/medical-certificates/${medicalCertificateId}/items/${medicalCertificateItemId}/remove`
	);
	return response.data;
};

export const updateMedicalCertificateItem = async (
	medicalCertificateId: number,
	medicalCertificateItemId: number,
	itemData: ICreateMedicalCertificateItemRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/medical-certificates/${medicalCertificateId}/items/${medicalCertificateItemId}/update`,
		itemData
	);
	return response.data;
};

// Medical Record Management APIs
export const addMedicalRecord = async (
	appointmentId: number,
	recordFile: File
): Promise<IApiResponse> => {
	const formData = new FormData();
	formData.append('record', recordFile);

	const response = await axios.post(
		`/doctor-assistant/appointments/${appointmentId}/add-medical-record`,
		formData,
		{
			headers: {
				'Content-Type': 'multipart/form-data',
			},
		}
	);
	return response.data;
};

export const removeMedicalRecord = async (
	appointmentId: number,
	medicalRecordId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/appointments/${appointmentId}/remove-medical-record/${medicalRecordId}`
	);
	return response.data;
};

// Get all medical records for a doctor (across all appointments)
export const getAllMedicalRecords = async (
	params: { page?: number; pageSize?: number; search?: string } = {}
): Promise<IAppointmentsResponse> => {
	// Since there's no direct endpoint, we'll need to get this through appointments
	// This would require a new backend endpoint or we can work with appointment data
	const response = await axios.get('/doctor-assistant/appointments', {
		params,
	});
	return response.data;
};

// Referral Management APIs
export const createReferral = async (
	appointmentId: number,
	referralData: ICreateReferralRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/appointments/${appointmentId}/referral/create`,
		referralData
	);
	return response.data;
};

export const createStandaloneReferral = async (
	referralData: ICreateStandaloneReferralRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		'/doctor-assistant/referrals/create',
		referralData
	);
	return response.data;
};

export const getAllReferrals = async (
	params: IReferralListParams = {}
): Promise<IReferralsResponse> => {
	const response = await axios.get('/doctor-assistant/referrals', { params });
	return response.data;
};

export const getReferralById = async (
	referralId: number
): Promise<IReferralDetailResponse> => {
	const response = await axios.get(`/doctor-assistant/referrals/${referralId}`);
	return response.data;
};

export const removeReferral = async (
	referralId: number
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/referrals/${referralId}/remove`
	);
	return response.data;
};

export const updateReferral = async (
	referralId: number,
	referralData: ICreateReferralRequest
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor-assistant/referrals/${referralId}/update`,
		referralData
	);
	return response.data;
};

export const getAllSubscriptions = async (
	params: ISubscriptionListParams = {}
): Promise<ISubscriptionsResponse> => {
	const response = await axios.get('/subscriptions', { params });
	return response.data;
};

export const subscribe = async (
	subscriptionData: ISubscribe
): Promise<IApiResponse> => {
	const response = await axios.post(
		`/doctor/subscriptions/subscribe`,
		subscriptionData
	);
	return response.data;
};

// Doctor Dashboard Query Keys
export const DOCTOR_QUERY_KEYS = {
	PROFILE: {
		DETAIL: ['doctor', 'profile'],
	},
	DASHBOARD: {
		METRICS: ['doctor', 'dashboard', 'metrics'],
		TODAYS_QUEUE: (params: IAppointmentParams) => [
			'doctor',
			'dashboard',
			'todays-queue',
			params,
		],
		TODAYS_ATTENDED: (params: IAppointmentParams) => [
			'doctor',
			'dashboard',
			'todays-attended',
			params,
		],
	},
	CLINICS: {
		ALL: (params: IClinicParams) => ['doctor', 'clinics', 'all', params],
		DETAIL: (clinicId: number) => ['doctor', 'clinics', 'detail', clinicId],
		PATIENTS: (clinicId: number, params: ISearchParams) => [
			'doctor',
			'clinics',
			clinicId,
			'patients',
			params,
		],
		ASSISTANTS: (clinicId: number, params: ISearchParams) => [
			'doctor',
			'clinics',
			clinicId,
			'assistants',
			params,
		],
	},
	PATIENTS: {
		ALL: (params: ISearchParams) => ['doctor', 'patients', 'all', params],
		DETAIL: (profileId: number) => ['doctor', 'patients', 'detail', profileId],
		APPOINTMENT_HISTORY: (profileId: number) => [
			'doctor',
			'patients',
			'appointment-history',
			profileId,
		],
		HABITS: (profileId: number) => ['doctor', 'patients', profileId, 'habits'],
		HISTORY: (profileId: number) => [
			'doctor',
			'patients',
			profileId,
			'history',
		],
		ALLERGIES: (profileId: number) => [
			'doctor',
			'patients',
			profileId,
			'allergies',
		],
		ILLNESSES: (profileId: number) => [
			'doctor',
			'patients',
			profileId,
			'illnesses',
		],
		SURGERIES: (profileId: number) => [
			'doctor',
			'patients',
			profileId,
			'surgeries',
		],
		DIETS: (profileId: number) => ['doctor', 'patients', profileId, 'diets'],
		HMO: (profileId: number) => ['doctor', 'patients', profileId, 'hmo'],
		FAMILY: (profileId: number) => ['doctor', 'patients', profileId, 'family'],
	},
	ASSISTANTS: {
		ALL: (params: IPaginationParams) => ['doctor', 'assistants', 'all', params],
		DETAIL: (assistantProfileId: number) => [
			'doctor',
			'assistants',
			'detail',
			assistantProfileId,
		],
	},
	APPOINTMENTS: {
		ALL: (params: IAppointmentParams) => [
			'doctor',
			'appointments',
			'all',
			params,
		],
		TODAY: (params: IAppointmentParams) => [
			'doctor',
			'appointments',
			'today',
			params,
		],
		DATE_RANGE: (
			startDate: string,
			endDate: string,
			params: Omit<IAppointmentParams, 'date'>
		) => ['doctor', 'appointments', 'date-range', startDate, endDate, params],
		CALENDAR: (params: {
			clinicId?: number;
			status?: string;
			fromDate?: string;
			toDate?: string;
		}) => ['doctor', 'appointments', 'calendar', params],
		HISTORIES: (params: {
			page?: number;
			pageSize?: number;
			search?: string;
			category?: string;
			clinicId?: number;
			status?: string;
			fromDate?: string;
			toDate?: string;
		}) => ['doctor', 'appointments', 'histories', params],
		DETAIL: (appointmentId: number) => [
			'doctor',
			'appointments',
			'detail',
			appointmentId,
		],
	},
	PRESCRIPTIONS: {
		ALL: (params: IPrescriptionListParams) => [
			'doctor',
			'prescriptions',
			'all',
			params,
		],
		BY_APPOINTMENT: (appointmentId: number) => [
			'doctor',
			'prescriptions',
			'appointment',
			appointmentId,
		],
		DETAIL: (prescriptionId: number) => [
			'doctor',
			'prescriptions',
			'detail',
			prescriptionId,
		],
	},
	DIAGNOSTIC_REQUESTS: {
		BY_APPOINTMENT: (appointmentId: number) => [
			'doctor',
			'diagnostic-requests',
			'appointment',
			appointmentId,
		],
		DETAIL: (diagnosticRequestId: number) => [
			'doctor',
			'diagnostic-requests',
			'detail',
			diagnosticRequestId,
		],
		ALL: (params: IDiagnosticRequestListParams) => [
			'doctor',
			'diagnostic-requests',
			'all',
			params,
		],
	},
	LAB_REQUESTS: {
		ALL: (params: ILabRequestListParams) => [
			'doctor',
			'lab-requests',
			'all',
			params,
		],
		BY_APPOINTMENT: (appointmentId: number) => [
			'doctor',
			'lab-requests',
			'appointment',
			appointmentId,
		],
		DETAIL: (labRequestId: number) => [
			'doctor',
			'lab-requests',
			'detail',
			labRequestId,
		],
	},
	MEDICAL_CERTIFICATES: {
		ALL: (params: IMedicalCertificateListParams) => [
			'doctor',
			'medical-certificates',
			'all',
			params,
		],
		BY_APPOINTMENT: (appointmentId: number) => [
			'doctor',
			'medical-certificates',
			'appointment',
			appointmentId,
		],
		DETAIL: (medicalCertificateId: number) => [
			'doctor',
			'medical-certificates',
			'detail',
			medicalCertificateId,
		],
	},
	REFERRALS: {
		ALL: (params: IReferralListParams) => [
			'doctor',
			'referrals',
			'all',
			params,
		],
		BY_APPOINTMENT: (appointmentId: number) => [
			'doctor',
			'referrals',
			'appointment',
			appointmentId,
		],
		DETAIL: (referralId: number) => [
			'doctor',
			'referrals',
			'detail',
			referralId,
		],
	},
	SUBSCRIPTIONS: {
		ALL: (params: IPaginationParams) => ['doctor', 'assistants', 'all', params],
	},
} as const;
