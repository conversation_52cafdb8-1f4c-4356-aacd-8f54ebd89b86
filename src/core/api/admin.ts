import axios from '@/core/api';
import { IDoctorRegistrationRequest } from '@/core/api/auth';
import {
	IAdminDashboardResponse,
	IAllDoctorsResponse,
	IAllPatientsResponse,
	ICreateSubscriptionRequest,
	IDoctorActionRequest,
	IDoctorActionResponse,
	IPaginationParams,
	IPendingDoctorsResponse,
	ISearchParams,
	ISubscriptionResponse,
	IUpdateSubscriptionRequest,
	TDashboardPeriod,
} from '@/features/dashboard/types/admin.types';
import { IDoctorDetails } from '@/features/dashboard/types/doctor-details.type';

// Admin Dashboard Metrics
export const getAdminDashboardMetrics = async (params?: {
	type?: TDashboardPeriod;
}): Promise<IAdminDashboardResponse> => {
	const response = await axios.get('/admin/dashboard', { params });
	return response.data;
};

// Doctor Management APIs
export const getPendingDoctors = async (
	params: IPaginationParams = {}
): Promise<IPendingDoctorsResponse> => {
	const response = await axios.get('/admin/doctors/pending', { params });
	return response.data;
};

export const getAllDoctors = async (
	params: IPaginationParams = {}
): Promise<IAllDoctorsResponse> => {
	const response = await axios.get('/admin/doctors', { params });
	return response.data;
};

export const getDoctorById = async (
	profileId: number
): Promise<IDoctorDetails> => {
	const response = await axios.get(`/admin/doctors/${profileId}`);
	return response.data.data;
};

export const acceptDoctor = async (
	request: IDoctorActionRequest
): Promise<IDoctorActionResponse> => {
	const response = await axios.post('/admin/doctors/pending/accept', request);
	return response.data;
};

export const rejectDoctor = async (
	request: IDoctorActionRequest
): Promise<IDoctorActionResponse> => {
	const response = await axios.post('/admin/doctors/pending/reject', request);
	return response.data;
};

export const disableDoctor = async (
	profileId: number
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(`/admin/doctors/${profileId}/disable`);
	return response.data;
};

export const enableDoctor = async (
	profileId: number
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(`/admin/doctors/${profileId}/enable`);
	return response.data;
};

export const createDoctor = async (
	registrationData: IDoctorRegistrationRequest
): Promise<IDoctorActionResponse> => {
	const formData = new FormData();

	// Add text fields
	formData.append('firstName', registrationData.firstName);
	if (registrationData.middleName) {
		formData.append('middleName', registrationData.middleName);
	}
	formData.append('lastName', registrationData.lastName);
	if (registrationData.suffix) {
		formData.append('suffix', registrationData.suffix);
	}
	formData.append('email', registrationData.email);
	formData.append('phone', registrationData.phone);
	formData.append('gender', registrationData.gender);
	formData.append('birthday', registrationData.birthday);
	if (registrationData.prcNumber) {
		formData.append('prcNumber', registrationData.prcNumber);
	}
	if (registrationData.prcExpiryDate) {
		formData.append('prcExpiryDate', registrationData.prcExpiryDate);
	}
	if (registrationData.clinicAddress) {
		formData.append('clinicAddress', registrationData.clinicAddress);
	}
	if (registrationData.clinicName) {
		formData.append('clinicName', registrationData.clinicName);
	}
	if (registrationData.specialty) {
		formData.append('specialty', registrationData.specialty);
	}

	// Add file fields
	formData.append('prcImageFront', registrationData.prcImageFront);
	formData.append('prcImageBack', registrationData.prcImageBack);

	const response = await axios.post('/admin/doctors/create', formData, {
		headers: {
			'Content-Type': 'multipart/form-data',
		},
	});
	return response.data;
};

// Patient Management APIs
export const getAllPatients = async (
	params: ISearchParams = {}
): Promise<IAllPatientsResponse> => {
	const response = await axios.get('/admin/patients', { params });
	return response.data;
};

export const getPatientById = async (profileId: number) => {
	const response = await axios.get(`/admin/patients/${profileId}`);
	return response.data.data;
};

// Patient Profile Management APIs

// Habits Management
export const addHabitToPatient = async (
	profileId: number,
	habitData: {
		name: string;
		description?: string;
	}
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/habit/create`,
		habitData
	);
	return response.data;
};

export const updatePatientHabit = async (
	profileId: number,
	patientHabitId: number,
	habitData: {
		name?: string;
		description?: string;
	}
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/habit/${patientHabitId}/update`,
		habitData
	);
	return response.data;
};

export const removeHabitFromPatient = async (
	profileId: number,
	patientHabitId: number
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/habit/${patientHabitId}/remove`
	);
	return response.data;
};

// History Management
export const addHistoryToPatient = async (
	profileId: number,
	historyData: {
		name: string;
		description?: string;
	}
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/history/create`,
		historyData
	);
	return response.data;
};

export const updatePatientHistory = async (
	profileId: number,
	patientHistoryId: number,
	historyData: {
		name?: string;
		description?: string;
	}
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/history/${patientHistoryId}/update`,
		historyData
	);
	return response.data;
};

export const removeHistoryFromPatient = async (
	profileId: number,
	patientHistoryId: number
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/history/${patientHistoryId}/remove`
	);
	return response.data;
};

// Allergies Management
export const addAllergyToPatient = async (
	profileId: number,
	allergyData: {
		name: string;
		description?: string;
	}
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/allergy/create`,
		allergyData
	);
	return response.data;
};

export const updatePatientAllergy = async (
	profileId: number,
	patientAllergyId: number,
	allergyData: {
		name?: string;
		description?: string;
	}
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/allergy/${patientAllergyId}/update`,
		allergyData
	);
	return response.data;
};

export const removeAllergyFromPatient = async (
	profileId: number,
	patientAllergyId: number
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/allergy/${patientAllergyId}/remove`
	);
	return response.data;
};

// Illnesses Management
export const addIllnessToPatient = async (
	profileId: number,
	illnessData: {
		name: string;
		description?: string;
	}
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/illness/create`,
		illnessData
	);
	return response.data;
};

export const updatePatientIllness = async (
	profileId: number,
	patientIllnessId: number,
	illnessData: {
		name?: string;
		description?: string;
	}
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/illness/${patientIllnessId}/update`,
		illnessData
	);
	return response.data;
};

export const removeIllnessFromPatient = async (
	profileId: number,
	patientIllnessId: number
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/illness/${patientIllnessId}/remove`
	);
	return response.data;
};

// Surgeries Management
export const addSurgeryToPatient = async (
	profileId: number,
	surgeryData: {
		name: string;
		description?: string;
	}
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/surgery/create`,
		surgeryData
	);
	return response.data;
};

export const updatePatientSurgery = async (
	profileId: number,
	patientSurgeryId: number,
	surgeryData: {
		name?: string;
		description?: string;
	}
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/surgery/${patientSurgeryId}/update`,
		surgeryData
	);
	return response.data;
};

export const removeSurgeryFromPatient = async (
	profileId: number,
	patientSurgeryId: number
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/surgery/${patientSurgeryId}/remove`
	);
	return response.data;
};

// Diets Management
export const addDietToPatient = async (
	profileId: number,
	dietData: {
		name: string;
		description?: string;
	}
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/diet/create`,
		dietData
	);
	return response.data;
};

export const updatePatientDiet = async (
	profileId: number,
	patientDietId: number,
	dietData: {
		name?: string;
		description?: string;
	}
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/diet/${patientDietId}/update`,
		dietData
	);
	return response.data;
};

export const removeDietFromPatient = async (
	profileId: number,
	patientDietId: number
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/diet/${patientDietId}/remove`
	);
	return response.data;
};

// HMO Management
export const getAllHmoTypes = async () => {
	const response = await axios.get('/admin/data-management/hmo');
	return response.data;
};

export const addHmoToPatient = async (
	profileId: number,
	hmoData: {
		hmoId: string;
		hmoCompany: string;
		hmoDetail?: string;
		hmoProvider?: string;
	}
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/hmo/create`,
		hmoData
	);
	return response.data;
};

export const updatePatientHmo = async (
	profileId: number,
	patientHmoId: number,
	hmoData: {
		hmoId?: string;
		hmoCompany?: string;
		hmoDetail?: string;
		hmoProvider?: string;
	}
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/hmo/${patientHmoId}/update`,
		hmoData
	);
	return response.data;
};

export const removeHmoFromPatient = async (
	profileId: number,
	patientHmoId: number
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/hmo/${patientHmoId}/remove`
	);
	return response.data;
};

// Family Management
export const addFamilyToPatient = async (
	profileId: number,
	familyData: {
		familyProfileId: number;
		relationship: string;
	}
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/family/create`,
		familyData
	);
	return response.data;
};

export const removeFamilyFromPatient = async (
	profileId: number,
	patientFamilyId: number
): Promise<IDoctorActionResponse> => {
	const response = await axios.post(
		`/admin/patients/${profileId}/family/${patientFamilyId}/remove`
	);
	return response.data;
};

// Subscription Management APIs
export const getAllSubscriptions = async (): Promise<ISubscriptionResponse> => {
	const response = await axios.get('/admin/subscriptions');
	return response.data;
};

export const getSubscriptionById = async (
	subscriptionId: number
): Promise<ISubscriptionResponse> => {
	const response = await axios.get(`/admin/subscriptions/${subscriptionId}`);
	return response.data;
};

export const createSubscription = async (
	subscriptionData: ICreateSubscriptionRequest
): Promise<ISubscriptionResponse> => {
	const response = await axios.post(
		'/admin/subscriptions/create',
		subscriptionData
	);
	return response.data;
};

export const updateSubscription = async (
	subscriptionId: number,
	subscriptionData: IUpdateSubscriptionRequest
): Promise<ISubscriptionResponse> => {
	const response = await axios.post(
		`/admin/subscriptions/${subscriptionId}/update`,
		subscriptionData
	);
	return response.data;
};

export const removeSubscription = async (
	subscriptionId: number
): Promise<ISubscriptionResponse> => {
	const response = await axios.post(
		`/admin/subscriptions/${subscriptionId}/remove`
	);
	return response.data;
};

// Data Management APIs - Visit Reasons
export const getAllVisitReasons = async () => {
	const response = await axios.get('/admin/data-management/visit-reasons');
	return response.data;
};

export const createVisitReason = async (data: {
	name: string;
	description?: string;
}) => {
	const response = await axios.post(
		'/admin/data-management/visit-reasons/create',
		data
	);
	return response.data;
};

export const updateVisitReason = async (
	visitReasonId: number,
	data: { name?: string; description?: string }
) => {
	const response = await axios.post(
		`/admin/data-management/visit-reasons/${visitReasonId}/update`,
		data
	);
	return response.data;
};

export const removeVisitReason = async (visitReasonId: number) => {
	const response = await axios.post(
		`/admin/data-management/visit-reasons/${visitReasonId}/remove`
	);
	return response.data;
};

// Data Management APIs - Habit Types
export const getAllHabitTypes = async () => {
	const response = await axios.get('/admin/data-management/habit-types');
	return response.data;
};

// Data Management APIs - Allergy Types
export const getAllAllergyTypes = async () => {
	const response = await axios.get('/admin/data-management/allergy-types');
	return response.data;
};

export const createAllergyType = async (data: {
	name: string;
	description?: string;
}) => {
	const response = await axios.post(
		'/admin/data-management/allergy-types/create',
		data
	);
	return response.data;
};

export const updateAllergyType = async (
	allergyTypeId: number,
	data: { name?: string; description?: string }
) => {
	const response = await axios.post(
		`/admin/data-management/allergy-types/${allergyTypeId}/update`,
		data
	);
	return response.data;
};

export const removeAllergyType = async (allergyTypeId: number) => {
	const response = await axios.post(
		`/admin/data-management/allergy-types/${allergyTypeId}/remove`
	);
	return response.data;
};

// Data Management APIs - Medical Description
export const getAllMedicalDescription = async () => {
	const response = await axios.get(
		'/admin/data-management/medical-descriptions'
	);
	return response.data;
};

export const createMedicalDescription = async (data: {
	name: string;
	description?: string;
}) => {
	const response = await axios.post(
		'/admin/data-management/medical-descriptions/create',
		data
	);
	return response.data;
};

export const updateMedicalDescription = async (
	medicalCertTypeId: number,
	data: { name?: string; description?: string }
) => {
	const response = await axios.post(
		`/admin/data-management/medical-descriptions/${medicalCertTypeId}/update`,
		data
	);
	return response.data;
};

export const removeMedicalDescription = async (medicalCertTypeId: number) => {
	const response = await axios.post(
		`/admin/data-management/medical-descriptions/${medicalCertTypeId}/remove`
	);
	return response.data;
};

// Data Management APIs - Appointment Types
export const getAllAppointmentTypes = async () => {
	const response = await axios.get('/admin/data-management/appointment-types');
	return response.data;
};

export const createAppointmentType = async (data: {
	name: string;
	description?: string;
}) => {
	const response = await axios.post(
		'/admin/data-management/appointment-types/create',
		data
	);
	return response.data;
};

export const updateAppointmentType = async (
	appointmentTypeId: number,
	data: { name?: string; description?: string }
) => {
	const response = await axios.post(
		`/admin/data-management/appointment-types/${appointmentTypeId}/update`,
		data
	);
	return response.data;
};

export const removeAppointmentType = async (appointmentTypeId: number) => {
	const response = await axios.post(
		`/admin/data-management/appointment-types/${appointmentTypeId}/remove`
	);
	return response.data;
};

// Data Management APIs - Consultation Types
export const getAllConsultationTypes = async () => {
	const response = await axios.get('/admin/data-management/consultation-types');
	return response.data;
};

export const createConsultationType = async (data: {
	name: string;
	description?: string;
}) => {
	const response = await axios.post(
		'/admin/data-management/consultation-types/create',
		data
	);
	return response.data;
};

export const updateConsultationType = async (
	consultationTypeId: number,
	data: { name?: string; description?: string }
) => {
	const response = await axios.post(
		`/admin/data-management/consultation-types/${consultationTypeId}/update`,
		data
	);
	return response.data;
};

export const removeConsultationType = async (consultationTypeId: number) => {
	const response = await axios.post(
		`/admin/data-management/consultation-types/${consultationTypeId}/remove`
	);
	return response.data;
};

// Data Management APIs - Payment Types
export const getAllPaymentTypes = async () => {
	const response = await axios.get('/admin/data-management/payment-types');
	return response.data;
};

export const createPaymentType = async (data: {
	name: string;
	description?: string;
}) => {
	const response = await axios.post(
		'/admin/data-management/payment-types/create',
		data
	);
	return response.data;
};

export const updatePaymentType = async (
	paymentTypeId: number,
	data: { name?: string; description?: string }
) => {
	const response = await axios.post(
		`/admin/data-management/payment-types/${paymentTypeId}/update`,
		data
	);
	return response.data;
};

export const removePaymentType = async (paymentTypeId: number) => {
	const response = await axios.post(
		`/admin/data-management/payment-types/${paymentTypeId}/remove`
	);
	return response.data;
};

// Data Management APIs - History Types
export const getAllHistoryTypes = async () => {
	const response = await axios.get('/admin/data-management/history-types');
	return response.data;
};

export const createHistoryType = async (data: {
	name: string;
	description?: string;
}) => {
	const response = await axios.post(
		'/admin/data-management/history-types/create',
		data
	);
	return response.data;
};

export const updateHistoryType = async (
	historyTypeId: number,
	data: { name?: string; description?: string }
) => {
	const response = await axios.post(
		`/admin/data-management/history-types/${historyTypeId}/update`,
		data
	);
	return response.data;
};

export const removeHistoryType = async (historyTypeId: number) => {
	const response = await axios.post(
		`/admin/data-management/history-types/${historyTypeId}/remove`
	);
	return response.data;
};

// Data Management APIs - Diet Types
export const getAllDietTypes = async () => {
	const response = await axios.get('/admin/data-management/diet-types');
	return response.data;
};

export const createDietType = async (data: {
	name: string;
	description?: string;
}) => {
	const response = await axios.post(
		'/admin/data-management/diet-types/create',
		data
	);
	return response.data;
};

export const updateDietType = async (
	dietTypeId: number,
	data: { name?: string; description?: string }
) => {
	const response = await axios.post(
		`/admin/data-management/diet-types/${dietTypeId}/update`,
		data
	);
	return response.data;
};

export const removeDietType = async (dietTypeId: number) => {
	const response = await axios.post(
		`/admin/data-management/diet-types/${dietTypeId}/remove`
	);
	return response.data;
};

// Data Management APIs - Diagnostic Request Types
export const getAllDiagnosticRequestTypes = async () => {
	const response = await axios.get(
		'/admin/data-management/diagnostic-request-types'
	);
	return response.data;
};

export const createDiagnosticRequestType = async (data: {
	name: string;
	description?: string;
}) => {
	const response = await axios.post(
		'/admin/data-management/diagnostic-request-types/create',
		data
	);
	return response.data;
};

export const updateDiagnosticRequestType = async (
	diagnosticRequestTypeId: number,
	data: { name?: string; description?: string }
) => {
	const response = await axios.post(
		`/admin/data-management/diagnostic-request-types/${diagnosticRequestTypeId}/update`,
		data
	);
	return response.data;
};

export const removeDiagnosticRequestType = async (
	diagnosticRequestTypeId: number
) => {
	const response = await axios.post(
		`/admin/data-management/diagnostic-request-types/${diagnosticRequestTypeId}/remove`
	);
	return response.data;
};

// Data Management APIs - Illness Types
export const getAllIllnessTypes = async () => {
	const response = await axios.get('/admin/data-management/illness-types');
	return response.data;
};

export const createIllnessType = async (data: {
	name: string;
	description?: string;
}) => {
	const response = await axios.post(
		'/admin/data-management/illness-types/create',
		data
	);
	return response.data;
};

export const updateIllnessType = async (
	illnessTypeId: number,
	data: { name?: string; description?: string }
) => {
	const response = await axios.post(
		`/admin/data-management/illness-types/${illnessTypeId}/update`,
		data
	);
	return response.data;
};

export const removeIllnessType = async (illnessTypeId: number) => {
	const response = await axios.post(
		`/admin/data-management/illness-types/${illnessTypeId}/remove`
	);
	return response.data;
};

// Data Management APIs - Lab Request Types
export const getAllLabRequestTypes = async () => {
	const response = await axios.get('/admin/data-management/lab-request-types');
	return response.data;
};

export const createLabRequestType = async (data: {
	name: string;
	description?: string;
}) => {
	const response = await axios.post(
		'/admin/data-management/lab-request-types/create',
		data
	);
	return response.data;
};

export const updateLabRequestType = async (
	labRequestTypeId: number,
	data: { name?: string; description?: string }
) => {
	const response = await axios.post(
		`/admin/data-management/lab-request-types/${labRequestTypeId}/update`,
		data
	);
	return response.data;
};

export const removeLabRequestType = async (labRequestTypeId: number) => {
	const response = await axios.post(
		`/admin/data-management/lab-request-types/${labRequestTypeId}/remove`
	);
	return response.data;
};

// Data Management APIs - Prescription Types
export const getAllPrescriptionTypes = async () => {
	const response = await axios.get('/admin/data-management/prescription-types');
	return response.data;
};

export const createPrescriptionType = async (data: {
	name: string;
	description?: string;
}) => {
	const response = await axios.post(
		'/admin/data-management/prescription-types/create',
		data
	);
	return response.data;
};

export const updatePrescriptionType = async (
	prescriptionTypeId: number,
	data: { name?: string; description?: string }
) => {
	const response = await axios.post(
		`/admin/data-management/prescription-types/${prescriptionTypeId}/update`,
		data
	);
	return response.data;
};

export const removePrescriptionType = async (prescriptionTypeId: number) => {
	const response = await axios.post(
		`/admin/data-management/prescription-types/${prescriptionTypeId}/remove`
	);
	return response.data;
};

// Data Management APIs - Surgery Types
export const getAllSurgeryTypes = async () => {
	const response = await axios.get('/admin/data-management/surgery-types');
	return response.data;
};

export const createSurgeryType = async (data: {
	name: string;
	description?: string;
}) => {
	const response = await axios.post(
		'/admin/data-management/surgery-types/create',
		data
	);
	return response.data;
};

export const updateSurgeryType = async (
	surgeryTypeId: number,
	data: { name?: string; description?: string }
) => {
	const response = await axios.post(
		`/admin/data-management/surgery-types/${surgeryTypeId}/update`,
		data
	);
	return response.data;
};

export const removeSurgeryType = async (surgeryTypeId: number) => {
	const response = await axios.post(
		`/admin/data-management/surgery-types/${surgeryTypeId}/remove`
	);
	return response.data;
};

export const createHabitType = async (data: {
	name: string;
	description?: string;
}) => {
	const response = await axios.post(
		'/admin/data-management/habit-types/create',
		data
	);
	return response.data;
};

export const updateHabitType = async (
	habitTypeId: number,
	data: { name?: string; description?: string }
) => {
	const response = await axios.post(
		`/admin/data-management/habit-types/${habitTypeId}/update`,
		data
	);
	return response.data;
};

export const removeHabitType = async (habitTypeId: number) => {
	const response = await axios.post(
		`/admin/data-management/habit-types/${habitTypeId}/remove`
	);
	return response.data;
};

export const createHmoType = async (data: {
	name: string;
	description?: string;
}) => {
	const response = await axios.post('/admin/data-management/hmo/create', data);
	return response.data;
};

export const updateHmoType = async (
	habitTypeId: number,
	data: { name?: string; description?: string }
) => {
	const response = await axios.post(
		`/admin/data-management/hmo/${habitTypeId}/update`,
		data
	);
	return response.data;
};

export const removeHmoType = async (habitTypeId: number) => {
	const response = await axios.post(
		`/admin/data-management/hmo/${habitTypeId}/remove`
	);
	return response.data;
};

// Admin Dashboard Query Keys
export const ADMIN_QUERY_KEYS = {
	DASHBOARD: {
		METRICS: ['admin', 'dashboard', 'metrics'],
		METRICS_WITH_PERIOD: (type?: TDashboardPeriod) => [
			'admin',
			'dashboard',
			'metrics',
			type || 'all',
		],
	},
	DOCTORS: {
		PENDING: (params: IPaginationParams) => [
			'admin',
			'doctors',
			'pending',
			params,
		],
		ALL: (params: IPaginationParams) => ['admin', 'doctors', 'all', params],
		DETAIL: (profileId: number) => ['admin', 'doctors', 'detail', profileId],
	},
	PATIENTS: {
		ALL: (params: ISearchParams) => ['admin', 'patients', 'all', params],
		DETAIL: (profileId: number) => ['admin', 'patients', 'detail', profileId],
		HABITS: (profileId: number) => ['admin', 'patients', profileId, 'habits'],
		HISTORY: (profileId: number) => ['admin', 'patients', profileId, 'history'],
		ALLERGIES: (profileId: number) => [
			'admin',
			'patients',
			profileId,
			'allergies',
		],
		ILLNESSES: (profileId: number) => [
			'admin',
			'patients',
			profileId,
			'illnesses',
		],
		SURGERIES: (profileId: number) => [
			'admin',
			'patients',
			profileId,
			'surgeries',
		],
		DIETS: (profileId: number) => ['admin', 'patients', profileId, 'diets'],
		HMO: (profileId: number) => ['admin', 'patients', profileId, 'hmo'],
		FAMILY: (profileId: number) => ['admin', 'patients', profileId, 'family'],
	},
	SUBSCRIPTIONS: {
		ALL: ['admin', 'subscriptions', 'all'],
		DETAIL: (subscriptionId: number) => [
			'admin',
			'subscriptions',
			'detail',
			subscriptionId,
		],
	},
	DATA_MANAGEMENT: {
		VISIT_REASONS: ['admin', 'data-management', 'visit-reasons'],
		ALLERGY_TYPES: ['admin', 'data-management', 'allergy-types'],
		APPOINTMENT_TYPES: ['admin', 'data-management', 'appointment-types'],
		CONSULTATION_TYPES: ['admin', 'data-management', 'consultation-types'],
		PAYMENT_TYPES: ['admin', 'data-management', 'payment-types'],
		HABIT_TYPES: ['admin', 'data-management', 'habit-types'],
		HISTORY_TYPES: ['admin', 'data-management', 'history-types'],
		DIET_TYPES: ['admin', 'data-management', 'diet-types'],
		HMO_TYPES: ['admin', 'data-management', 'hmo-types'],
		DIAGNOSTIC_REQUEST_TYPES: [
			'admin',
			'data-management',
			'diagnostic-request-types',
		],
		ILLNESS_TYPES: ['admin', 'data-management', 'illness-types'],
		LAB_REQUEST_TYPES: ['admin', 'data-management', 'lab-request-types'],
		PRESCRIPTION_TYPES: ['admin', 'data-management', 'prescription-types'],
		MEDICAL_CERT_TYPES: ['admin', 'data-management', 'medical-description'],
		SURGERY_TYPES: ['admin', 'data-management', 'surgery-types'],
	},
} as const;
