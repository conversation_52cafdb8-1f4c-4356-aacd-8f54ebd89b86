'use client';

import axios from '@/core/api';
import { ILoginRequest, ILoginResponse } from '@/core/hooks/useSession';

// Doctor Registration Types
export interface IDoctorRegistrationRequest {
	firstName: string;
	middleName?: string;
	lastName: string;
	suffix?: string;
	email: string;
	phone: string;
	gender: string;
	birthday: string; // ISO date string
	prcNumber?: string;
	prcExpiryDate?: string; // ISO date string
	prcImageFront: File;
	prcImageBack: File;
	clinicAddress?: string;
	clinicName?: string;
	specialty?: string;
}

export interface IDoctorRegistrationResponse {
	status: number;
	message: string;
}

// Patient Registration Types
export interface IPatientAddress {
	country: string;
	province: string;
	city: string;
	barangay: string;
	streetName: string;
	buildingApartment?: string;
	subdivisionVillageZone?: string;
	unitRoomFloorBuilding?: string;
	lotBlockPhaseStreet?: string;
}

export interface IPatientRegistrationRequest {
	title?: string;
	firstName: string;
	middleName?: string;
	lastName: string;
	suffix?: string;
	email: string;
	phone: string;
	gender: string;
	birthday: string; // ISO date string
	occupation?: string;
	civilStatus: string;
	ethnicity?: string;
	currentAddress: IPatientAddress;
	permanentAddress: IPatientAddress;
	emergencyContactName?: string;
	emergencyContactNumber?: string;
	bloodType?: string;
	religion?: string;
	height: string;
	heightType: string;
	weight: string;
	weightType: string;
	clinicIds: number[];
	isPwd: number; // 0 or 1
}

export interface IPatientRegistrationResponse {
	status: number;
	message: string;
}

// Login API function
export const loginApi = async (
	credentials: ILoginRequest
): Promise<ILoginResponse> => {
	const response = await axios.post('/login', credentials);
	return response.data;
};

// Doctor Registration API function
export const doctorRegisterApi = async (
	registrationData: IDoctorRegistrationRequest
): Promise<IDoctorRegistrationResponse> => {
	const formData = new FormData();

	// Add text fields
	formData.append('firstName', registrationData.firstName);
	if (registrationData.middleName) {
		formData.append('middleName', registrationData.middleName);
	}
	formData.append('lastName', registrationData.lastName);
	if (registrationData.suffix) {
		formData.append('suffix', registrationData.suffix);
	}
	formData.append('email', registrationData.email);
	formData.append('phone', registrationData.phone);
	formData.append('gender', registrationData.gender);
	formData.append('birthday', registrationData.birthday);
	if (registrationData.prcNumber) {
		formData.append('prcNumber', registrationData.prcNumber);
	}
	if (registrationData.prcExpiryDate) {
		formData.append('prcExpiryDate', registrationData.prcExpiryDate);
	}
	if (registrationData.clinicAddress) {
		formData.append('clinicAddress', registrationData.clinicAddress);
	}
	if (registrationData.clinicName) {
		formData.append('clinicName', registrationData.clinicName);
	}
	if (registrationData.specialty) {
		formData.append('specialty', registrationData.specialty);
	}

	// Add file fields
	formData.append('prcImageFront', registrationData.prcImageFront);
	formData.append('prcImageBack', registrationData.prcImageBack);

	const response = await axios.post('/doctor/register', formData, {
		headers: {
			'Content-Type': 'multipart/form-data',
		},
	});
	return response.data;
};

// Email Verification Types
export interface IEmailVerificationRequest {
	token: string;
}

export interface IEmailVerificationResponse {
	status: number;
	message: string;
	data?: {
		email?: string;
	};
}

// Patient Registration API function
export const patientRegisterApi = async (
	registrationData: IPatientRegistrationRequest
): Promise<IPatientRegistrationResponse> => {
	const response = await axios.post('/patient/register', registrationData);
	return response.data;
};

// Email Verification API function
export const verifyEmailApi = async (
	verificationData: IEmailVerificationRequest
): Promise<IEmailVerificationResponse> => {
	const response = await axios.post('/verify', verificationData);
	return response.data;
};

// Logout Types
export interface ILogoutResponse {
	status: number;
	message: string;
}

// Change Password Types
export interface IChangePasswordRequest {
	currentPassword: string;
	newPassword: string;
	confirmPassword: string;
}

export interface IChangePasswordResponse {
	status: number;
	message: string;
}

// Logout API function
export const logoutApi = async (): Promise<ILogoutResponse> => {
	const response = await axios.post('/logout');
	return response.data;
};

// Change Password API function
export const changePasswordApi = async (
	passwordData: IChangePasswordRequest
): Promise<IChangePasswordResponse> => {
	const response = await axios.post('/change-password', passwordData);
	return response.data;
};
