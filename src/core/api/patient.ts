/*
 * Patient API functions are currently disabled as the backend endpoints are not yet implemented.
 * This file contains placeholder functions that will be replaced with actual API calls
 * once the patient backend infrastructure is available.
 *
 * When implementing the patient API:
 * 1. Create patient middleware in the backend
 * 2. Add patient route group with authentication
 * 3. Implement patient controller methods
 * 4. Replace these placeholder functions with actual API calls
 */

// Placeholder functions that return empty responses
export const getPatientProfile = async () => {
	return { data: null, status: 0, message: 'Patient API not yet implemented' };
};

export const updatePatientProfile = async () => {
	return { data: null, status: 0, message: 'Patient API not yet implemented' };
};

export const getPatientAppointments = async () => {
	return { data: null, status: 0, message: 'Patient API not yet implemented' };
};

export const getPatientAppointmentHistory = async () => {
	return { data: null, status: 0, message: 'Patient API not yet implemented' };
};

export const getPatientAppointmentById = async () => {
	return { data: null, status: 0, message: 'Patient API not yet implemented' };
};

export const getPatientMedicalDocuments = async () => {
	return { data: null, status: 0, message: 'Patient API not yet implemented' };
};

export const getPatientPrescriptions = async () => {
	return { data: null, status: 0, message: 'Patient API not yet implemented' };
};

export const getPatientLabResults = async () => {
	return { data: null, status: 0, message: 'Patient API not yet implemented' };
};

export const getPatientMedicalCertificates = async () => {
	return { data: null, status: 0, message: 'Patient API not yet implemented' };
};

export const getPatientReferrals = async () => {
	return { data: null, status: 0, message: 'Patient API not yet implemented' };
};

export const getPatientMedicalHistory = async () => {
	return { data: null, status: 0, message: 'Patient API not yet implemented' };
};

export const updatePatientMedicalHistory = async () => {
	return { data: null, status: 0, message: 'Patient API not yet implemented' };
};

export const getPatientQRCode = async () => {
	return { data: null, status: 0, message: 'Patient API not yet implemented' };
};

// Patient Dashboard Query Keys (kept for future use)
export const PATIENT_QUERY_KEYS = {
	PROFILE: ['patient', 'profile'],
	APPOINTMENTS: {
		ALL: ['patient', 'appointments', 'all'],
		HISTORY: ['patient', 'appointments', 'history'],
		DETAIL: (appointmentId: number) => [
			'patient',
			'appointments',
			'detail',
			appointmentId,
		],
	},
	MEDICAL_DOCUMENTS: {
		ALL: ['patient', 'medical-documents', 'all'],
		PRESCRIPTIONS: ['patient', 'medical-documents', 'prescriptions'],
		LAB_RESULTS: ['patient', 'medical-documents', 'lab-results'],
		CERTIFICATES: ['patient', 'medical-documents', 'certificates'],
		REFERRALS: ['patient', 'medical-documents', 'referrals'],
	},
	MEDICAL_HISTORY: ['patient', 'medical-history'],
	QR_CODE: ['patient', 'qr-code'],
} as const;
