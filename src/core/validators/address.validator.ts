/* eslint-disable @typescript-eslint/no-explicit-any */
// address.validator.ts
import { z } from 'zod';

import {
	IProfileCurrentAddress,
	IProfilePermanentAddress,
} from '@/features/dashboard/types/doctor-details.type';

/* -----------------------------------------------------------------------------
 * 1. Re-usable stringified-JSON schemas
 * ---------------------------------------------------------------------------*/

/* Region ------------------------------------------------------------------- */
const RegionSchema = z.object({
	id: z.number().int().positive(),
	psgc_code: z.string().regex(/^\d{9}$/), // 9-digit numeric string
	region_name: z.string().min(1),
	region_code: z.string().regex(/^\d{1,2}$/), // "03", "13", etc.
});

/* Province ----------------------------------------------------------------- */
const ProvinceSchema = z.object({
	province_code: z.string().regex(/^\d{4}$/), // 4-digit numeric string
	province_name: z.string().min(1),
	psgc_code: z.string().regex(/^\d{9}$/),
	region_code: z.string().regex(/^\d{1,2}$/),
});

/* City/Municipality -------------------------------------------------------- */
const CitySchema = z.object({
	city_code: z.string().regex(/^\d{6}$/), // 6-digit numeric string
	city_name: z.string().min(1),
	province_code: z.string().regex(/^\d{4}$/),
	psgc_code: z.string().regex(/^\d{9}$/),
	region_code: z.string().regex(/^\d{1,2}$/),
});

/* Barangay ----------------------------------------------------------------- */
const BarangaySchema = z.object({
	brgy_code: z.string().regex(/^\d{9}$/),
	brgy_name: z.string().min(1),
	city_code: z.string().regex(/^\d{6}$/),
	province_code: z.string().regex(/^\d{4}$/),
	region_code: z.string().regex(/^\d{1,2}$/),
});

/* -----------------------------------------------------------------------------
 * 2. Helper: validates a JSON string that must parse to a specific schema
 * ---------------------------------------------------------------------------*/
function jsonStringSchema<T extends z.ZodTypeAny>(schema: T) {
	return z
		.string()
		.transform((str, ctx) => {
			try {
				return JSON.parse(str);
			} catch {
				ctx.addIssue({ code: 'custom', message: 'Invalid JSON string' });
				return z.NEVER;
			}
		})
		.pipe(schema);
}

/* -----------------------------------------------------------------------------
 * 3. Single address validator
 * ---------------------------------------------------------------------------*/
const SingleAddressSchema = z.object({
	country: z.literal('Philippines'), // only PH for now
	region: z.optional(jsonStringSchema(RegionSchema)),
	province: jsonStringSchema(ProvinceSchema),
	city: jsonStringSchema(CitySchema),
	barangay: jsonStringSchema(BarangaySchema),

	// Optional free-text fields
	streetName: z.string(), // street_name
	lotBlockPhaseStreet: z.string().optional(), // lot_block_phase_street
	unitRoomFloorBuilding: z.string().optional(), // unit_room_floor_building
	subdivisionVillageZone: z.string().optional(), // subdivision_village_zone
	buildingApartment: z.string().optional(), // building_apartment
});

const ApiAddressSchema = z.object({
	id: z.number().int().positive(),
	profile_id: z.number().int().positive(),

	country: z.literal('Philippines'),
	region: z.optional(jsonStringSchema(RegionSchema)),
	province: jsonStringSchema(ProvinceSchema),
	city: jsonStringSchema(CitySchema),
	barangay: jsonStringSchema(BarangaySchema),

	/* snake_case keys */
	street_name: z.string().optional(),
	building_apartment: z.string().optional(),
	subdivision_village_zone: z.string().optional(),
	unit_room_floor_building: z.string().optional(),
	lot_block_phase_street: z.string().optional(),

	created_at: z.string(),
	updated_at: z.string(),
});

/* -----------------------------------------------------------------------------
 * 4. Top-level payload validator
 * ---------------------------------------------------------------------------*/
export const AddressPayloadSchema = z
	.object({
		currentAddress: SingleAddressSchema,
		permanentAddress: SingleAddressSchema,
	})
	.refine(
		(data) => {
			const { currentAddress } = data;

			// use the region_code from region if available, otherwise use the region_code from province
			const regionCode = currentAddress.region
				? currentAddress.region.region_code
				: currentAddress.province.region_code;
			const provinceCode = currentAddress.province.province_code;
			const cityCode = currentAddress.city.city_code;

			return (
				currentAddress.province.region_code === regionCode &&
				currentAddress.city.region_code === regionCode &&
				currentAddress.city.province_code === provinceCode &&
				currentAddress.barangay.region_code === regionCode &&
				currentAddress.barangay.province_code === provinceCode &&
				currentAddress.barangay.city_code === cityCode
			);
		},
		{ message: 'Address hierarchy codes are inconsistent' }
	);

export const ApiAddressPayloadSchema = z.object({
	profileCurrentAddress: ApiAddressSchema,
	profilePermanentAddress: ApiAddressSchema,
});

/* -----------------------------------------------------------------------------
 * 5. Convenience type
 * ---------------------------------------------------------------------------*/
export type AddressPayload = z.infer<typeof AddressPayloadSchema>;
export type ApiAddressPayload = z.infer<typeof ApiAddressPayloadSchema>;

export const isValidApiAddress = ({
	profileCurrentAddress,
	profilePermanentAddress,
}: {
	profileCurrentAddress: IProfileCurrentAddress;
	profilePermanentAddress: IProfilePermanentAddress;
}) => {
	try {
		const result = ApiAddressPayloadSchema.parse({
			profileCurrentAddress,
			profilePermanentAddress,
		});
		return {
			isValid: true,
			address: result,
		};
	} catch (err) {
		console.error('❌ Validation failed');
		if (err instanceof Error && 'errors' in err) {
			(err as any).errors.forEach((e: any) =>
				console.error(`- ${e.path.join('.')} : ${e.message}`)
			);
		} else {
			console.error(err);
		}
		return { isValid: false, address: null };
	}
};
