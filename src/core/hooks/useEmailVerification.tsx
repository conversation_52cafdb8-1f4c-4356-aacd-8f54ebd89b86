'use client';

import { useMutation } from '@tanstack/react-query';
import { toast } from 'sonner';

import { IEmailVerificationRequest, verifyEmailApi } from '@/core/api/auth';
import { catchError } from '@/core/lib/utils';

export const useEmailVerification = () => {
	const verificationMutation = useMutation({
		mutationFn: verifyEmailApi,
		onSuccess: (data) => {
			// Handle different response scenarios based on backend logic
			if (data.status === 1) {
				// Success - either verified or already verified
				toast.success('Email Verification', {
					description: data.message,
				});
			} else if (data.status === 2) {
				// Token expired
				toast.error('Verification Failed', {
					description: data.message,
				});
			} else {
				// Other error cases
				toast.error('Verification Failed', {
					description: data.message,
				});
			}
		},
		onError: (error) => {
			catchError(error, 'Email Verification Error', 'Verification Failed');
		},
	});

	const verifyEmail = (verificationData: IEmailVerificationRequest) => {
		verificationMutation.mutate(verificationData);
	};

	return {
		verifyEmail,
		isLoading: verificationMutation.isPending,
		isSuccess: verificationMutation.isSuccess,
		isError: verificationMutation.isError,
		data: verificationMutation.data,
		error: verificationMutation.error,
		reset: verificationMutation.reset,
	};
};
