'use client';

import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

import {
	changePasswordApi,
	IChangePasswordRequest,
	loginApi,
	logoutApi,
} from '@/core/api/auth';
import { catchError } from '@/core/lib/utils';

import { ILoginRequest, useSession } from './useSession';

// Login Hook
export const useLogin = () => {
	const { setLoginData } = useSession();
	const router = useRouter();

	const loginMutation = useMutation({
		mutationFn: loginApi,
		onSuccess: (data) => {
			// Set login data in session first
			setLoginData(data);

			// Show success message
			toast.success('Login Successful', {
				description: 'Welcome back! You have been logged in successfully.',
			});

			// Use requestAnimationFrame to ensure state update is processed
			// before navigation
			requestAnimationFrame(() => {
				router.push('/dashboard');
			});
		},
		onError: (error) => {
			catchError(error, 'Login Error', 'Login Failed');
		},
	});

	const login = (credentials: ILoginRequest) => {
		loginMutation.mutate(credentials);
	};

	return {
		login,
		isLoading: loginMutation.isPending,
		error: loginMutation.error,
		isError: loginMutation.isError,
	};
};

// Logout Hook
export const useLogout = () => {
	const { logout: clearSession } = useSession();
	const router = useRouter();

	const logoutMutation = useMutation({
		mutationFn: logoutApi,
		onSuccess: () => {
			// Clear session data
			clearSession();

			// Show success message
			toast.success('Logout Successful', {
				description: 'You have been logged out successfully.',
			});

			// Navigate to login page
			router.push('/login');
		},
		onError: (error) => {
			// Even if API call fails, clear session and redirect
			clearSession();
			router.push('/login');
			catchError(error, 'Logout Error', 'Logout completed with errors');
		},
	});

	const logout = () => {
		logoutMutation.mutate();
	};

	return {
		logout,
		isLoading: logoutMutation.isPending,
		error: logoutMutation.error,
		isError: logoutMutation.isError,
	};
};

// Change Password Hook
export const useChangePassword = () => {
	const changePasswordMutation = useMutation({
		mutationFn: changePasswordApi,
		onSuccess: () => {
			// Show success message
			toast.success('Password Changed Successfully', {
				description: 'Your password has been updated successfully.',
			});
		},
		onError: (error) => {
			catchError(error, 'Change Password Error', 'Failed to change password');
		},
	});

	const changePassword = (passwordData: IChangePasswordRequest) => {
		changePasswordMutation.mutate(passwordData);
	};

	return {
		changePassword,
		isLoading: changePasswordMutation.isPending,
		error: changePasswordMutation.error,
		isError: changePasswordMutation.isError,
		isSuccess: changePasswordMutation.isSuccess,
	};
};
