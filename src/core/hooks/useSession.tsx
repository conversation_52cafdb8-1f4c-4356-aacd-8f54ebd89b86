'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import merge from 'lodash.merge';
import { useCallback } from 'react';

import { queryClient } from '@/providers/lib/react-query';
import { localstored } from '@/store/plugins/localStored';

// Types for the login API
export interface ILoginRequest {
	username: string;
	password: string;
}

export interface IUser {
	id: number;
	email: string;
	username: string;
	first_name: string;
	last_name: string;
	last_login: string;
	is_superuser: number;
	is_active: number;
	is_staff: number;
	created_at: string;
	updated_at: string;
	profile: {
		id: number;
		user_id: number;
		profile_role_id: number;
		first_name: string;
		last_name: string;
		middle_name: string;
		suffix: string;
		phone: string;
		gender: string;
		birthday: string;
		is_verified: number;
		is_completed: number;
		is_accepted: number;
		is_rejected: number;
		is_deleted: number;
		is_active: number;
		is_new_password: number;
		created_at: string;
		updated_at: string;
		profile_role: {
			id: number;
			name: string;
		};
	};
}

export interface ILoginResponse {
	status: number;
	message: string;
	data: {
		user: IUser;
		token: string;
		isAdmin: boolean;
		isDoctor: boolean;
		isPatient: boolean;
		isAssistant: boolean;
		isLaboratory: boolean;
	};
}

// Initial State - Use consistent initial state to prevent hydration mismatch
const initialState = {
	token: null as string | null,
	isAuthenticated: false,
	user: null as IUser | null,
	userRoles: {
		is_admin: false,
		is_doctor: false,
		is_patient: false,
		is_assistant: false,
		is_laboratory: false,
	},
};

// Create global state with Hookstate
const sessionState = hookstate(
	initialState,
	localstored({
		key: 'session',
		onRestored: (s) => {
			const restored = s.get({ noproxy: true });

			if (s.value) {
				const synced = merge({}, initialState, restored);
				s.set(synced);
			}
		},
	})
);

// Session manager functions
export const useSession = () => {
	const state = useHookstate(sessionState);

	// Set login data from API response
	const setLoginData = useCallback(
		(loginResponse: ILoginResponse) => {
			const {
				user,
				token,
				isAdmin,
				isDoctor,
				isPatient,
				isAssistant,
				isLaboratory,
			} = loginResponse.data;

			// Update state with login data
			state.merge({
				token,
				isAuthenticated: true,
				user,
				userRoles: {
					is_admin: isAdmin,
					is_doctor: isDoctor,
					is_patient: isPatient,
					is_assistant: isAssistant,
					is_laboratory: isLaboratory,
				},
			});

			// Store role hint in localStorage for better role detection
			if (typeof window !== 'undefined') {
				try {
					let roleHint = null;
					if (isAssistant) roleHint = 'assistant';
					else if (isDoctor) roleHint = 'doctor';
					else if (isAdmin) roleHint = 'admin';
					else if (isPatient) roleHint = 'patient';

					if (roleHint) {
						window.localStorage.setItem('userRole', roleHint);
					}
				} catch (error) {
					console.warn('Error storing role hint:', error);
				}
			}
		},
		[state]
	);

	// Logout function
	const logout = useCallback(() => {
		state.merge({
			token: null,
			isAuthenticated: false,
			user: null,
			userRoles: {
				is_admin: false,
				is_doctor: false,
				is_patient: false,
				is_assistant: false,
				is_laboratory: false,
			},
		});

		// Clear role hint from localStorage
		if (typeof window !== 'undefined') {
			try {
				window.localStorage.removeItem('userRole');
			} catch (error) {
				console.warn('Error clearing role hint:', error);
			}
		}

		// Clear react query cache
		queryClient.clear();
	}, [state]);

	return {
		token: state.token.get(),
		isAuthenticated: state.isAuthenticated.get(),
		user: state.user.get(),
		userRoles: state.userRoles.get(),
		setLoginData,
		logout,
	};
};

// Export raw state for advanced use (optional)
export default sessionState;
