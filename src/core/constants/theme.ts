/**
 * Elena Theme Constants
 *
 * This file contains the Elena brand colors and theme-related constants
 * that can be used throughout the application.
 */

export const ELENA_COLORS = {
	// Primary Elena brand color
	PRIMARY: '#0CC3CF',

	// OKLCH values for better color manipulation
	PRIMARY_OKLCH: 'oklch(0.7448 0.1256 202.74)',
	PRIMARY_DARK_OKLCH: 'oklch(0.6448 0.1256 202.74)',
	PRIMARY_LIGHT_OKLCH: 'oklch(0.8448 0.1256 202.74)',
} as const;

export const ELENA_THEME = {
	colors: ELENA_COLORS,

	// CSS custom properties for use in components
	cssVars: {
		primary: 'var(--elena-primary)',
		primaryDark: 'var(--elena-primary-dark)',
		primaryLight: 'var(--elena-primary-light)',
	},

	// Tailwind classes for Elena colors
	tailwind: {
		primary: 'text-elena-primary',
		primaryBg: 'bg-elena-primary',
		primaryDark: 'text-elena-primary-dark',
		primaryDarkBg: 'bg-elena-primary-dark',
		primaryLight: 'text-elena-primary-light',
		primaryLightBg: 'bg-elena-primary-light',
	},
} as const;

export type ElenaColor = keyof typeof ELENA_COLORS;
export type ElenaTailwindClass = keyof typeof ELENA_THEME.tailwind;
