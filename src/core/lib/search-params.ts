import { parseAsInteger, parseAsString, parseAsStringLiteral } from 'nuqs';

// Common dashboard tabs
export const EDashboardTab = {
	// Admin Dashboard Tabs
	ADMIN_OVERVIEW: 'overview',
	ADMIN_CLINICS: 'clinics',
	ADMIN_DOCTORS: 'doctors',
	ADMIN_PATIENTS: 'patients',
	ADMIN_ASSISTANTS: 'assistants',
	ADMIN_PATIENT_DETAILS: 'patient-details',
	ADMIN_CLINIC_DETAILS: 'clinic-details',
	ADMIN_DOCTOR_DETAILS: 'doctor-details',
	ADMIN_ASSISTANT_DETAILS: 'assistant-details',

	// Doctor Dashboard Tabs
	DOCTOR_OVERVIEW: 'overview',
	DOCTOR_APPOINTMENTS: 'appointments',
	DOCTOR_PATIENTS: 'patients',
	DOCTOR_CLINICS: 'clinics',
	DOCTOR_ASSISTANTS: 'assistants',
	DOCTOR_MEDICAL_DOCUMENTS: 'medical-documents',
	DOCTOR_PATIENT_DETAILS: 'patient-details',
	DOCTOR_CLINIC_DETAILS: 'clinic-details',
	DOCTOR_ASSISTANT_DETAILS: 'assistant-details',

	// Assistant Dashboard Tabs (mirrors doctor tabs except subscription)
	ASSISTANT_OVERVIEW: 'overview',
	ASSISTANT_APPOINTMENTS: 'appointments',
	ASSISTANT_CALENDAR: 'calendar',
	ASSISTANT_PATIENTS: 'patients',
	ASSISTANT_CLINICS: 'clinics',
	ASSISTANT_MEDICAL_DOCUMENTS: 'medical-documents',
	ASSISTANT_SETTINGS: 'settings',
	ASSISTANT_PATIENT_DETAILS: 'patient-details',
} as const;

// Action types for dialogs and forms
export const EActionType = {
	CREATE: 'create',
	EDIT: 'edit',
	VIEW: 'view',
	PRINT: 'print',
} as const;

// Document types for medical documents
export const EDocumentType = {
	PRESCRIPTIONS: 'prescriptions',
	LAB_REQUESTS: 'lab-requests',
	REFERRALS: 'referrals',
	DIAGNOSTIC_REQUESTS: 'diagnostic-requests',
	MEDICAL_CERTIFICATES: 'medical-certificates',
} as const;

// Subscription view types
export const ESubscriptionView = {
	OVERVIEW: 'overview',
	PLANS: 'plans',
	HISTORY: 'history',
} as const;

// Search parameter parsers
export const searchParamParsers = {
	// Dashboard tab parser
	tab: parseAsString.withDefault(EDashboardTab.DOCTOR_OVERVIEW),

	// Action parser for dialogs/forms
	action: parseAsStringLiteral([
		EActionType.CREATE,
		EActionType.EDIT,
		EActionType.VIEW,
		EActionType.PRINT,
	]),

	// ID parser for entity selection
	id: parseAsInteger,

	// Appointment ID parser for appointment details
	appointmentId: parseAsInteger,

	// Clinic ID parser
	clinicId: parseAsInteger,

	// Subscription ID parser
	subscriptionId: parseAsInteger,

	// Document type parser
	type: parseAsStringLiteral([
		EDocumentType.PRESCRIPTIONS,
		EDocumentType.LAB_REQUESTS,
		EDocumentType.REFERRALS,
		EDocumentType.DIAGNOSTIC_REQUESTS,
		EDocumentType.MEDICAL_CERTIFICATES,
		'patients',
	]).withDefault(EDocumentType.PRESCRIPTIONS),

	// Subscription view parser
	subscriptionView: parseAsStringLiteral([
		ESubscriptionView.OVERVIEW,
		ESubscriptionView.PLANS,
		ESubscriptionView.HISTORY,
	]).withDefault(ESubscriptionView.OVERVIEW),

	// Search query parser
	search: parseAsString.withDefault(''),

	// View parser for appointments
	view: parseAsString.withDefault('upcoming'),

	// Document type parser for medical documents
	documentType: parseAsString.withDefault('all'),

	// Section parser for medical history
	section: parseAsString.withDefault('allergies'),

	// Pagination parser
	page: parseAsInteger.withDefault(1),

	// Page size parser
	pageSize: parseAsInteger.withDefault(10),

	// Email verification parsers
	token: parseAsString,
	email: parseAsString,

	// Appointment type parser
	category: parseAsString.withDefault('all'),

	// Appointment status parser
	status: parseAsString.withDefault('all'),
	bloodType: parseAsString.withDefault('all'),
	gender: parseAsString.withDefault('all'),
} as const;

// Type helpers for better TypeScript support
export type DashboardTab = (typeof EDashboardTab)[keyof typeof EDashboardTab];
export type ActionType = (typeof EActionType)[keyof typeof EActionType];
export type DocumentType = (typeof EDocumentType)[keyof typeof EDocumentType];
export type SubscriptionView =
	(typeof ESubscriptionView)[keyof typeof ESubscriptionView];
