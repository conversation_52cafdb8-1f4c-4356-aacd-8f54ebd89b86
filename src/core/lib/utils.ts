import { clsx, type ClassValue } from 'clsx';
import { toast } from 'sonner';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

export const catchError = (
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	e: any,
	label: string = 'Error',
	title: string = 'Oops! Something went wrong',
	description: string = ''
) => {
	const error = e?.response?.data?.message || e.message;
	console.log(`${label}: `, error);

	toast.error(title, {
		description: description.length > 0 ? description : error,
	});
};

export function isObjectEmpty(obj: object) {
	return Object.keys(obj).length === 0;
}

/**
 * Create a function that accepts a number and then returns it like this:
 * 1000 => 1k, 10000 => 10k, 100000 => 100k, 1000000 => 1M, 10000000 => 10M,
 * 100000000 => 100M, 1000000000 => 1B, etc
 */
export const formatNumber = (num: number) => {
	if (num >= 1000000000) return `${(num / 1000000000).toFixed(1)}B`;
	if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
	if (num >= 1000) return `${(num / 1000).toFixed(1)}k`;
	return num.toString();
};

export const uniqueIndentifier = () =>
	(
		new Date().getTime().toString(36) + Math.random().toString(36).slice(2)
	).toLowerCase();

export function bytesToSize(bytes: number) {
	const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
	if (bytes === 0) return 'n/a';
	const i = parseInt(String(Math.floor(Math.log(bytes) / Math.log(1024))), 10);
	if (i === 0) return `${bytes} ${sizes[i]}`;
	return `${(bytes / 1024 ** i).toFixed(1)} ${sizes[i]}`;
}

export function capitalize(str: string) {
	// Split the string into an array of words
	const words = str.split(' ');

	// Capitalize the first letter of each word
	const capitalizedWords = words.map((word) => {
		return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
	});

	// Join the capitalized words back into a single string
	const capitalizedStr = capitalizedWords.join(' ');

	return capitalizedStr;
}

export function pascalCase(str: string): string {
	return str
		.toLowerCase()
		.split(/[^a-zA-Z0-9]+/) // Split by non-alphanumeric characters
		.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
		.join('');
}

export const formatDate = (dateString: string) => {
	const date = new Date(dateString || new Date().toISOString());

	const dateOptions: Intl.DateTimeFormatOptions = {
		year: 'numeric',
		month: 'long',
		day: 'numeric',
	};
	const timeOptions: Intl.DateTimeFormatOptions = {
		hour: 'numeric',
		minute: 'numeric',
		// second: 'numeric',
		hour12: true,
	};

	const formattedDate = date.toLocaleDateString([], dateOptions);
	const formattedTime = date.toLocaleTimeString([], timeOptions);

	return `${formattedDate} ${formattedTime}`;
};

export const formatDateOnly = (dateString: string) => {
	const date = new Date(dateString || new Date().toISOString());

	const dateOptions: Intl.DateTimeFormatOptions = {
		year: 'numeric',
		month: 'long',
		day: 'numeric',
	};
	const formattedDate = date.toLocaleDateString([], dateOptions);

	return `${formattedDate}`;
};

export const convertTo12Hour = (time: string): string => {
	const [hour, minute] = time.split(':').map(Number);
	const period = hour >= 12 ? 'PM' : 'AM';
	const adjustedHour = hour % 12 || 12; // Convert 0 to 12 for midnight
	return `${adjustedHour.toString().padStart(2, '0')}:${minute
		.toString()
		.padStart(2, '0')} ${period}`;
};

export const mapPermissionsToRole = (permissions?: { code: string }[]) => {
	return (
		permissions?.reduce((acc: Record<string, boolean>, permission) => {
			acc[permission.code] = true;
			return acc;
		}, {}) || {}
	);
};

export const getDateOnly = (appointmentSchedule: string): string => {
	return appointmentSchedule.split(' ')[0];
};

export const getTimeOnly = (appointmentSchedule: string): string => {
	const timePart = appointmentSchedule.split(' ')[1]; // Extract time from the string
	const [hours, minutes] = timePart.split(':').map(Number); // Convert to numbers

	const date = new Date();
	date.setHours(hours);
	date.setMinutes(minutes);

	return date.toLocaleTimeString('en-US', {
		hour: 'numeric',
		minute: '2-digit',
		hour12: true,
	});
};
