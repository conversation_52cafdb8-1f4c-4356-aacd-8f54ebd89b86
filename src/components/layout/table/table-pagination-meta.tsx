import {
	ChevronLeftIcon,
	ChevronRightIcon,
	DoubleArrowLeftIcon,
	DoubleArrowRightIcon,
} from '@radix-ui/react-icons';
import type { Table } from '@tanstack/react-table';

import { Button } from '@/components/ui/button';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';

type PaginationMeta = {
	total: number;
	per_page: number;
	current_page: number;
	last_page: number;
	first_page_url: string;
	last_page_url: string;
	next_page_url: string | null;
	previous_page_url: string | null;
};

type DataTablePaginationMetaProps<TData> = {
	table: Table<TData>;
	meta: PaginationMeta;
	onChangePageSize: (size: number) => void;
	onChangePage: (page: number) => void;
	all?: number;
};

export function DataTablePaginationMeta<TData>({
	table,
	meta,
	onChangePageSize,
	onChangePage,
	all = 0,
}: DataTablePaginationMetaProps<TData>) {
	return (
		<div className="flex flex-col items-center gap-2 px-2 xl:flex-row xl:justify-between xl:gap-4">
			<div className="flex flex-1 items-center justify-between gap-4">
				<div className="text-sm text-gray-500">
					Total of {meta.total} row(s)
				</div>

				<div className="flex items-center space-x-2">
					<p className="text-sm font-medium">Rows per page</p>
					<Select
						value={`${meta.per_page}`}
						onValueChange={(value) => {
							onChangePageSize(Number(value));
							table.setPageSize(Number(value));
						}}
					>
						<SelectTrigger className="h-8 w-[70px]">
							<SelectValue placeholder={table.getState().pagination.pageSize} />
						</SelectTrigger>
						<SelectContent side="top">
							{all > 0 && ![10, 20, 30, 40, 50].includes(all) && (
								<SelectItem value={`${all}`}>All</SelectItem>
							)}
							{[10, 20, 30, 40, 50].map((pageSize) => (
								<SelectItem key={pageSize} value={`${pageSize}`}>
									{pageSize}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>
			</div>

			<div className="flex justify-between md:gap-4">
				<div className="flex w-[100px] items-center justify-center text-sm font-medium">
					Page {meta.current_page} of {meta.last_page}
				</div>

				<div className="flex items-center space-x-2">
					<Button
						variant="outline"
						className="size-8 p-0"
						onClick={() =>
							onChangePage(Number(meta.first_page_url.split('=')[1]))
						}
						disabled={!meta.previous_page_url}
					>
						<span className="sr-only">Go to first page</span>
						<DoubleArrowLeftIcon className="size-4" />
					</Button>
					<Button
						variant="outline"
						className="size-8 p-0"
						onClick={() =>
							onChangePage(Number(meta.previous_page_url?.split('=')[1]))
						}
						disabled={!meta.previous_page_url}
					>
						<span className="sr-only">Go to previous page</span>
						<ChevronLeftIcon className="size-4" />
					</Button>
					<Button
						variant="outline"
						className="size-8 p-0"
						onClick={() =>
							onChangePage(Number(meta.next_page_url?.split('=')[1]))
						}
						disabled={!meta.next_page_url}
					>
						<span className="sr-only">Go to next page</span>
						<ChevronRightIcon className="size-4" />
					</Button>
					<Button
						variant="outline"
						className="size-8 p-0"
						onClick={() =>
							onChangePage(Number(meta.last_page_url.split('=')[1]))
						}
						disabled={!meta.next_page_url}
					>
						<span className="sr-only">Go to last page</span>
						<DoubleArrowRightIcon className="size-4" />
					</Button>
				</div>
			</div>
		</div>
	);
}
