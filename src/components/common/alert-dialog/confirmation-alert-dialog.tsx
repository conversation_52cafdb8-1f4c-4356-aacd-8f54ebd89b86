'use client';

import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { cn } from '@/core/lib/utils';

import Loading from '../loading';

interface IConfirmationDialogProps {
	title?: string;
	description: string;
	onClick: () => void;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	isLoading?: boolean;
	variant?: 'default' | 'destructive';
	hideTrigger?: boolean;
	children?: React.ReactNode;
}

export function ConfirmationDialog({
	title = 'Are you absolutely sure?',
	description,
	onClick,
	open,
	onOpenChange,
	isLoading = false,
	variant = 'default',
	hideTrigger = true,
	children,
}: IConfirmationDialogProps) {
	return (
		<AlertDialog
			open={open}
			onOpenChange={(v) => {
				onOpenChange(v);
			}}
		>
			{!hideTrigger && (
				<AlertDialogTrigger asChild>
					{children ? (
						children
					) : (
						<Button variant="outline">Alert Dialog</Button>
					)}
				</AlertDialogTrigger>
			)}
			<AlertDialogContent className="font-sans">
				<AlertDialogHeader>
					<AlertDialogTitle>{title}</AlertDialogTitle>
					<AlertDialogDescription>{description}</AlertDialogDescription>
				</AlertDialogHeader>
				<AlertDialogFooter>
					<AlertDialogCancel>Cancel</AlertDialogCancel>
					<AlertDialogAction
						className={cn({
							'bg-red-500 hover:bg-red-600': variant === 'destructive',
						})}
						onClick={onClick}
						disabled={isLoading}
					>
						{isLoading ? (
							<div className="flex items-center justify-center gap-2">
								<Loading />
								<span>Please wait</span>
							</div>
						) : (
							'Continue'
						)}
					</AlertDialogAction>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	);
}
