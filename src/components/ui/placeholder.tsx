'use client';

import { Construction, Info } from 'lucide-react';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface IPlaceholderProps {
	title: string;
	description?: string;
	icon?: React.ReactNode;
	variant?: 'default' | 'info' | 'warning';
	className?: string;
}

export function Placeholder({
	title,
	description = 'This feature is currently under development and will be available soon.',
	icon,
	variant = 'info',
	className = '',
}: IPlaceholderProps) {
	const getVariantStyles = () => {
		switch (variant) {
			case 'warning':
				return 'border-yellow-200 bg-yellow-50';
			case 'info':
				return 'border-blue-200 bg-blue-50';
			default:
				return 'border-gray-200 bg-gray-50';
		}
	};

	const getIcon = () => {
		if (icon) return icon;
		switch (variant) {
			case 'warning':
				return <Construction className="h-5 w-5 text-yellow-600" />;
			case 'info':
			default:
				return <Info className="h-5 w-5 text-blue-600" />;
		}
	};

	return (
		<Card className={`${getVariantStyles()} ${className}`}>
			<CardHeader className="pb-3">
				<CardTitle className="flex items-center gap-2 text-lg">
					{getIcon()}
					{title}
				</CardTitle>
			</CardHeader>
			<CardContent>
				<p className="text-muted-foreground text-sm">{description}</p>
			</CardContent>
		</Card>
	);
}

interface IFeaturePlaceholderProps {
	feature: string;
	description?: string;
	className?: string;
}

export function FeaturePlaceholder({
	feature,
	description,
	className = '',
}: IFeaturePlaceholderProps) {
	return (
		<Alert className={`border-dashed ${className}`}>
			<Construction className="h-4 w-4" />
			<AlertTitle>{feature} - Coming Soon</AlertTitle>
			<AlertDescription>
				{description ||
					`The ${feature.toLowerCase()} feature is currently under development. Please check back later for updates.`}
			</AlertDescription>
		</Alert>
	);
}
