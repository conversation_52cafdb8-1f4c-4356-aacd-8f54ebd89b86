'use client';

import { ScrollAreaProps } from '@radix-ui/react-scroll-area';
import * as React from 'react';

import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { cn } from '@/core/lib/utils';

export default function XScroll({
	children,
	className,
	...props
}: ScrollAreaProps) {
	return (
		<div className="flex">
			<ScrollArea className={cn('w-1 flex-1', className)} {...props}>
				{children}
				<ScrollBar orientation="horizontal" />
			</ScrollArea>
		</div>
	);
}
