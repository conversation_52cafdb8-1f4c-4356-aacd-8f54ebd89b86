'use client';

import {
	parseAsInteger,
	parseAsString,
	parseAsStringLiteral,
	useQueryState,
	useQueryStates,
} from 'nuqs';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';

// Example of using individual query states
export function NuqsBasicExample() {
	// Single query state with default value
	const [name, setName] = useQueryState('name', parseAsString.withDefault(''));

	// Integer query state with default
	const [age, setAge] = useQueryState('age', parseAsInteger.withDefault(18));

	// String literal (enum-like) query state
	const [status, setStatus] = useQueryState(
		'status',
		parseAsStringLiteral(['active', 'inactive', 'pending']).withDefault(
			'active'
		)
	);

	return (
		<div className="space-y-4 rounded-lg border p-4">
			<h3 className="text-lg font-semibold">Basic nuqs Example</h3>

			<div className="space-y-2">
				<Label htmlFor="name">Name</Label>
				<Input
					id="name"
					value={name}
					onChange={(e) => setName(e.target.value)}
					placeholder="Enter your name"
				/>
			</div>

			<div className="space-y-2">
				<Label htmlFor="age">Age</Label>
				<Input
					id="age"
					type="number"
					value={age}
					onChange={(e) => setAge(parseInt(e.target.value) || 18)}
					placeholder="Enter your age"
				/>
			</div>

			<div className="space-y-2">
				<Label htmlFor="status">Status</Label>
				<Select
					value={status}
					onValueChange={(value) =>
						setStatus(value as 'active' | 'inactive' | 'pending')
					}
				>
					<SelectTrigger>
						<SelectValue placeholder="Select status" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="active">Active</SelectItem>
						<SelectItem value="inactive">Inactive</SelectItem>
						<SelectItem value="pending">Pending</SelectItem>
					</SelectContent>
				</Select>
			</div>

			<div className="flex gap-2">
				<Button onClick={() => setName('')}>Clear Name</Button>
				<Button onClick={() => setAge(18)}>Reset Age</Button>
				<Button onClick={() => setStatus('active')}>Reset Status</Button>
			</div>

			<div className="rounded bg-gray-100 p-2 text-sm">
				<strong>Current URL params:</strong>
				<br />
				name: {name || 'empty'}
				<br />
				age: {age}
				<br />
				status: {status}
			</div>
		</div>
	);
}

// Example of using grouped query states
export function NuqsGroupedExample() {
	// Multiple related query states managed together
	const [filters, setFilters] = useQueryStates({
		search: parseAsString.withDefault(''),
		category: parseAsStringLiteral([
			'all',
			'electronics',
			'clothing',
			'books',
		]).withDefault('all'),
		minPrice: parseAsInteger.withDefault(0),
		maxPrice: parseAsInteger.withDefault(1000),
		page: parseAsInteger.withDefault(1),
	});

	// Update multiple parameters at once
	const handleReset = () => {
		setFilters({
			search: '',
			category: 'all',
			minPrice: 0,
			maxPrice: 1000,
			page: 1,
		});
	};

	// Update search and reset page
	const handleSearch = (searchValue: string) => {
		setFilters({
			search: searchValue,
			page: 1, // Reset to first page when searching
		});
	};

	return (
		<div className="space-y-4 rounded-lg border p-4">
			<h3 className="text-lg font-semibold">Grouped nuqs Example</h3>

			<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
				<div className="space-y-2">
					<Label htmlFor="search">Search</Label>
					<Input
						id="search"
						value={filters.search}
						onChange={(e) => handleSearch(e.target.value)}
						placeholder="Search products..."
					/>
				</div>

				<div className="space-y-2">
					<Label htmlFor="category">Category</Label>
					<Select
						value={filters.category}
						onValueChange={(value) =>
							setFilters({
								category: value as 'all' | 'electronics' | 'clothing' | 'books',
								page: 1,
							})
						}
					>
						<SelectTrigger>
							<SelectValue placeholder="Select category" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="all">All Categories</SelectItem>
							<SelectItem value="electronics">Electronics</SelectItem>
							<SelectItem value="clothing">Clothing</SelectItem>
							<SelectItem value="books">Books</SelectItem>
						</SelectContent>
					</Select>
				</div>

				<div className="space-y-2">
					<Label htmlFor="minPrice">Min Price</Label>
					<Input
						id="minPrice"
						type="number"
						value={filters.minPrice}
						onChange={(e) =>
							setFilters({ minPrice: parseInt(e.target.value) || 0 })
						}
						placeholder="Min price"
					/>
				</div>

				<div className="space-y-2">
					<Label htmlFor="maxPrice">Max Price</Label>
					<Input
						id="maxPrice"
						type="number"
						value={filters.maxPrice}
						onChange={(e) =>
							setFilters({ maxPrice: parseInt(e.target.value) || 1000 })
						}
						placeholder="Max price"
					/>
				</div>
			</div>

			<div className="flex gap-2">
				<Button onClick={handleReset}>Reset All Filters</Button>
				<Button onClick={() => setFilters({ page: filters.page + 1 })}>
					Next Page ({filters.page + 1})
				</Button>
				<Button
					onClick={() => setFilters({ page: Math.max(1, filters.page - 1) })}
					disabled={filters.page <= 1}
				>
					Previous Page
				</Button>
			</div>

			<div className="rounded bg-gray-100 p-2 text-sm">
				<strong>Current filters:</strong>
				<pre className="mt-1">{JSON.stringify(filters, null, 2)}</pre>
			</div>
		</div>
	);
}
