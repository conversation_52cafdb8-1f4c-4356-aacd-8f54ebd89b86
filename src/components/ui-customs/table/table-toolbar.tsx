'use client';

import { Cross2Icon } from '@radix-ui/react-icons';
import { Table } from '@tanstack/react-table';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { IMeta } from '@/core/types/index.type';

import { DataTableViewOptions } from './table-view-option';

interface DataTableToolbarProps<TData> {
	id?: string;
	table: Table<TData>;
	meta?: IMeta | null;
}

export function DataTableToolbar<TData>({
	id = '',
	table,
}: DataTableToolbarProps<TData>) {
	const isFiltered = table.getState().columnFilters.length > 0;

	return (
		<div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
			<div className="flex flex-1 flex-wrap items-center gap-2">
				{id !== 'marketplace-reports' && (
					<Input
						placeholder="Search..."
						onChange={(event) => table.setGlobalFilter(event.target.value)}
						className="h-8 w-full sm:w-[150px] lg:w-[250px]"
					/>
				)}

				{isFiltered && (
					<Button
						variant="destructive"
						onClick={() => table.resetColumnFilters()}
						className="h-8 px-2 lg:px-3"
					>
						Reset
						<Cross2Icon className="ml-2 size-4" />
					</Button>
				)}
			</div>

			<div className="flex gap-2">
				<DataTableViewOptions table={table} />
			</div>
		</div>
	);
}
