'use client';

import LoadingDots from '@/components/common/loading/loading-dots';
import { AlertDialogDemo } from '@/components/samples/AlertDialogSample';
import { DialogCloseButton } from '@/components/samples/DialogSample';
import ReactAlertSample from '@/components/samples/ReactAlertSample';
import { DrawerDialogDemo } from '@/components/samples/responsive-dialog';
import { AdvancedDateRangePicker } from '@/components/ui-customs/advance-range-date-picker';

import Loading from '../common/loading';
import { LoadingButton } from '../common/loading/loading-button';
import LoadingFull from '../common/loading/loading-full';
import { Badge } from '../ui-customs/badge';
import { Button } from '../ui/button';
import { ScrollArea } from '../ui/scroll-area';

export default function Template() {
	return (
		<ScrollArea className="h-screen">
			<div className="flex min-h-screen flex-col items-center justify-center gap-4 px-6 py-12">
				{/* <PERSON> */}
				<div className="mb-6 flex items-center gap-4">
					<img
						src="/elena-banner.png"
						alt="Elena Logo"
						className="h-16 w-auto"
					/>
				</div>

				<h1 className="text-elena-primary text-center text-4xl font-bold">
					Elena Web Application
				</h1>
				<p className="text-muted-foreground text-center text-lg">
					Powered by Next.js 15 & Tailwind CSS 4
				</p>

				<div className="mt-4 rounded-full bg-gray-300 px-3 py-1 text-xs">
					Elena Theme Colors
				</div>
				<div className="flex gap-4">
					<div className="flex flex-col items-center gap-2">
						<div className="bg-elena-primary h-16 w-16 rounded-lg shadow-lg"></div>
						<span className="text-xs font-medium">Primary</span>
					</div>
					<div className="flex flex-col items-center gap-2">
						<div className="bg-elena-primary-dark h-16 w-16 rounded-lg shadow-lg"></div>
						<span className="text-xs font-medium">Primary Dark</span>
					</div>
					<div className="flex flex-col items-center gap-2">
						<div className="bg-elena-primary-light h-16 w-16 rounded-lg shadow-lg"></div>
						<span className="text-xs font-medium">Primary Light</span>
					</div>
				</div>

				<div className="mt-4 rounded-full bg-gray-300 px-3 py-1 text-xs">
					Available Fonts
				</div>
				<div className="flex gap-4">
					<h1 className="text-elena-primary text-center font-sans text-4xl font-bold">
						Geist Font
					</h1>
					<h1 className="font-inter text-elena-primary-dark text-center text-4xl font-bold">
						Inter Font
					</h1>
					<h1 className="font-poppins text-elena-primary-light text-center text-4xl font-bold">
						Poppins Font
					</h1>
				</div>

				<div className="mt-4 rounded-full bg-gray-300 px-3 py-1 text-xs">
					Elena Theme Buttons
				</div>
				<div className="flex gap-4">
					<Button>Primary Button</Button>
					<Button variant="secondary">Secondary</Button>
					<Button variant="outline">Outline</Button>
					<Button variant="ghost">Ghost</Button>
				</div>

				<div className="mt-4 rounded-full bg-gray-300 px-3 py-1 text-xs">
					Toast Example
				</div>
				<ReactAlertSample />

				<div className="mt-4 rounded-full bg-gray-300 px-3 py-1 text-xs">
					Dialog Example
				</div>
				<div className="flex gap-4">
					<AlertDialogDemo />
					<DialogCloseButton />
					<DrawerDialogDemo />
				</div>

				<div className="mt-4 rounded-full bg-gray-300 px-3 py-1 text-xs">
					Badge Example
				</div>
				<div className="flex gap-4">
					<Badge variant="success">Success</Badge>
					<Badge variant="warning">Warning</Badge>
					<Badge variant="info">Info</Badge>
					<Badge variant="error">Error</Badge>
					<Badge variant="gray">Gray</Badge>
					<Badge variant="orange">Orange</Badge>
				</div>

				<div className="mt-4 rounded-full bg-gray-300 px-3 py-1 text-xs">
					Advance Date Picker
				</div>
				<div className="flex gap-4">
					<AdvancedDateRangePicker />
				</div>

				<div className="mt-4 rounded-full bg-gray-300 px-3 py-1 text-xs">
					Loading Example
				</div>
				<div className="flex items-center gap-4">
					<Loading />
					<LoadingButton />
					<LoadingDots />
					<LoadingDots message="Please wait" dots="." />
				</div>

				<LoadingFull className="mt-4" />
			</div>
		</ScrollArea>
	);
}
