import { IPaginationMeta } from './pagination.types';

// Admin Dashboard Types
export interface IAdminDashboardMetrics {
	total_doctors: number;
	total_sales: number;
	total_active_subscriptions: number;
	pending_doctor_applications: number;
	// Backend response fields (snake_case from /admin/dashboard endpoint)
	totalDoctors?: number;
	totalActiveSubscriptions?: number;
	totalSales?: number;
	totalPendingDoctors?: number;
}

// Doctor Management Types
export interface IDoctorProfile {
	id: number;
	user_id?: number;
	profile_role_id?: number;
	first_name: string;
	last_name: string;
	middle_name: string | null;
	suffix: string | null;
	phone?: string;
	gender?: string;
	birthday?: string;
	is_verified?: number;
	is_completed?: number;
	is_accepted?: number;
	is_rejected?: number;
	is_deleted?: number;
	is_active?: number;
	created_at: string;
	updated_at?: string;
	doctor?: {
		id: number;
		specialty: string | null;
		profile_id: number;
		prc_number?: string | null;
		prc_expiry_date?: string | null;
		prc_image_front?: string | null;
		prc_image_back?: string | null;
		sub_specialty?: string | null;
		sub_specialization?: string | null;
		awards?: string | null;
	};
	user?: {
		id: number;
		email: string;
		username: string;
		first_name: string;
		last_name: string;
		last_login: string;
		is_superuser: number;
		is_active: number;
		is_staff: number;
		created_at: string;
		updated_at: string;
	};
	profile_role?: {
		id: number;
		name: string;
	};
	clinics?: IClinic[];
}

// Clinic Types
export interface IClinic {
	id: number;
	name: string | null;
	clinic_type?: string | null;
	address: string | null;
	phone: string | null;
	email: string | null;
	website: string | null;
	start_time: string | null;
	end_time: string | null;
	date_established: string | null;
	status: string;
	logo?: string | null;
	doctor_profile_id: number;
	longitude: string | null;
	latitude: string | null;
	is_deleted: number;
	created_at: string;
	updated_at: string;
}

export interface IPendingDoctorsResponse {
	status: number;
	message?: string;
	data: {
		data: IPendingDoctorItem[];
		meta: IPaginationMeta;
	};
}

export interface IPendingDoctorItem {
	id: number;
	first_name: string;
	last_name: string;
	middle_name: null;
	suffix: null;
	created_at: string;
	doctor: IPendingDoctor;
}

export interface IPendingDoctor {
	id: number;
	specialty: string;
	profile_id: number;
}

export interface IAllDoctorsResponse {
	status: number;
	message?: string;
	data: {
		data: IDoctorProfile[];
		meta: {
			total: number;
			per_page: number;
			current_page: number;
			last_page: number;
			first_page: number;
			first_page_url: string;
			last_page_url: string;
			next_page_url: string | null;
			previous_page_url: string | null;
		};
	};
}

// Patient Management Types
export interface IPatientProfile {
	id: number;
	user_id: number;
	profile_role_id: number;
	first_name: string;
	last_name: string;
	middle_name: string;
	suffix: string;
	phone: string;
	gender: string;
	birthday: string;
	occupation: string;
	civil_status: string;
	blood_type: string;
	height: string;
	height_type: string;
	weight: string;
	weight_type: string;
	religion: string;
	ethnicity: string;
	nationality: string;
	is_verified: number;
	is_completed: number;
	is_accepted: number;
	is_rejected: number;
	is_deleted: number;
	is_active: number;
	title: string;
	doctor_profile_id: number;
	created_at: string;
	updated_at: string;
	user: {
		id: number;
		email: string;
		username: string;
		first_name: string;
		last_name: string;
		last_login: string;
		is_superuser: number;
		is_active: number;
		is_staff: number;
		created_at: string;
		updated_at: string;
	};
	profile_role: {
		id: number;
		name: string;
	};
}

export interface IAllPatientsResponse {
	status: number;
	message?: string;
	data: {
		data: IPatientProfile[];
		meta: {
			total: number;
			per_page: number;
			current_page: number;
			last_page: number;
			first_page: number;
			first_page_url: string;
			last_page_url: string;
			next_page_url: string | null;
			previous_page_url: string | null;
		};
	};
}

// Patient Detail Types (for individual patient view)
export interface IPatientFamily {
	id: number;
	patient_id: number;
	family_member: string;
	medical_condition: string;
	created_at: string;
	updated_at: string;
}

export interface IPatientAllergy {
	id: number;
	patient_id: number;
	name: string;
	description: string;
	created_at: string;
	updated_at: string;
}

export interface IPatientIllness {
	id: number;
	patient_id: number;
	name: string;
	description: string;
	created_at: string;
	updated_at: string;
}

export interface IPatientHmo {
	id: number;
	patient_id: number;
	hmo_id: string;
	hmo_company: string;
	hmo_detail: string;
	hmo_provider: string;
	created_at: string;
	updated_at: string;
}

export interface IPatientHabit {
	id: number;
	patient_id: number;
	name: string;
	description: string;
	created_at: string;
	updated_at: string;
}

export interface IPatientDiet {
	id: number;
	patient_id: number;
	name: string;
	description: string;
	created_at: string;
	updated_at: string;
}

export interface IPatientSurgery {
	id: number;
	patient_id: number;
	name: string;
	description: string;
	created_at: string;
	updated_at: string;
}

export interface IPatientMedicalRecord {
	id: number;
	patient_id: number;
	record_type: string;
	description: string;
	date_recorded: string;
	doctor_name: string;
	created_at: string;
	updated_at: string;
}

export interface IPatientHistory {
	id: number;
	patient_id: number;
	name: string;
	description: string;
	created_at: string;
	updated_at: string;
}

export interface IPatientAddress {
	id: number;
	profile_id: number;
	address: string;
	city: string;
	province: string;
	country: string;
	postal_code: string;
	created_at: string;
	updated_at: string;
}

export interface IVitalSign {
	id: number;
	appointment_id: number;
	systolic: string;
	diastolic: string;
	pulse_rate: string;
	respiration: string;
	height: string;
	height_type: string;
	weight: string;
	weight_type: string;
	temperature: string;
	temperature_type: string;
	oxygen_saturation: string;
	capillary_blood_glucose: string;
	body_mass_index: string;
	created_at: string;
	updated_at: string;
}

export interface IPatientData {
	id: number;
	profile_id: number;
	is_deleted: number;
	is_active: number;
	is_pwd: number;
	emergency_contact_name: string | null;
	emergency_contact_number: string | null;
	created_at: string;
	updated_at: string;
	patient_family: IPatientFamily[];
	patient_allergy: IPatientAllergy[];
	patient_illness: IPatientIllness[];
	patient_hmo: IPatientHmo[];
	patient_habit: IPatientHabit[];
	patient_diet: IPatientDiet[];
	patient_surgery: IPatientSurgery[];
	patient_medical_record: IPatientMedicalRecord[];
	patient_history: IPatientHistory[];
	clinic_patients?: {
		id: number;
		clinic_id: number;
		patient_id: number;
		created_at: string;
		updated_at: string;
		clinic?: {
			id: number;
			name: string;
			address: string | null;
			phone: string | null;
			email: string | null;
		};
	}[];
}

export interface IPatientDetailProfile extends IPatientProfile {
	profile_current_address: IPatientAddress;
	profile_permanent_address: IPatientAddress;
	patient: IPatientData;
	recent_vital_sign: IVitalSign | null;
}

// Doctor Actions Types
export interface IDoctorActionRequest {
	profileId: number;
	reason?: string;
}

export interface IDoctorActionResponse {
	status: number;
	message: string;
}

// Admin Dashboard API Response Types
export interface IAdminDashboardResponse {
	status: number;
	message: string;
	data: IAdminDashboardMetrics;
}

// Pagination Parameters
export interface IPaginationParams {
	page?: number;
	pageSize?: number;
}

// Search Parameters
export interface ISearchParams extends IPaginationParams {
	search?: string;
}

// Admin Navigation Types
export interface IAdminNavItem {
	title: string;
	url: string;
	icon: React.ComponentType;
	isActive?: boolean;
	items?: IAdminNavSubItem[];
}

export interface IAdminNavSubItem {
	title: string;
	url: string;
}

export interface IUpdateSubscriptionRequest {
	name?: string;
	description?: string;
	price?: number;
	discountedPrice?: number;
	coverage?: number;
	coverageType?: string;
	isActive?: number;
	isDiscountEnabled?: number;
	// Patient settings
	patientEnabled?: number;
	patientCount?: number;
	patientLimitType?: string;
	patientCoverageType?: string;
	patientQrEnabled?: number;
	// Appointment settings
	appointmentEnabled?: number;
	appointmentCount?: number;
	appointmentLimitType?: string;
	appointmentCoverageType?: string;
	appointmentQrEnabled?: number;
	// Clinic settings
	clinicEnabled?: number;
	clinicCount?: number;
	clinicLimitType?: string;
	// Clinic assistant settings
	clinicAssistantEnabled?: number;
	clinicAssistantCount?: number;
	clinicAssistantLimitType?: string;
	// Medical document settings
	medicalDocumentEnabled?: number;
}

export interface ISubscriptionResponse {
	status: number;
	message?: string;
	data?: Subscription[];
}

// Data Management Types
export interface IDataManagementItem {
	id: number;
	name: string;
	description?: string;
	is_deleted: number;
	created_at: string;
	updated_at: string;
}

export interface ICreateDataManagementRequest {
	name: string;
	description?: string;
}

export interface IUpdateDataManagementRequest {
	name?: string;
	description?: string;
}

export interface IDataManagementResponse {
	status: number;
	message?: string;
	data?: IDataManagementItem[];
}

// Admin Dashboard Tab Types
export enum EAdminDashboardTab {
	OVERVIEW = 'overview',
	PENDING_DOCTORS = 'pending-doctors',
	ALL_DOCTORS = 'all-doctors',
	DOCTOR_DETAILS = 'doctor-details',
	ALL_PATIENTS = 'all-patients',
	PATIENT_DETAILS = 'patient-details',
	AUDIT_LOGS = 'audit-logs',
	SUBSCRIPTIONS = 'subscriptions',
	ACTIVE_SUBSCRIPTIONS = 'active-subscriptions',
	SUBSCRIPTION_PLANS = 'view-subscription-plan',
	GIFT_CODES = 'gift-codes',
	PLATFORM_DATA = 'platform-data',
	VISIT_REASONS = 'visit-reasons',
	CONSULTATION_TYPES = 'consultation-types',
	APPOINTMENT_TYPES = 'appointment-types',
	PAYMENT_TYPES = 'payment-types',
	MEDICAL_DESCRIPTIONS = 'medical-descriptions',
	HABIT_TYPES = 'habit-types',
	ALLERGY_TYPES = 'allergy-types',
	HISTORY_TYPES = 'history-types',
	DIET_TYPES = 'diet-types',
	DIAGNOSTIC_REQUEST_TYPES = 'diagnostic-request-types',
	ILLNESS_TYPES = 'illness-types',
	LAB_REQUEST_TYPES = 'lab-request-types',
	PRESCRIPTION_TYPES = 'prescription-types',
	SURGERY_TYPES = 'surgery-types',
	LAB_MANAGEMENT = 'lab-management',
	USER_ROLES = 'user-roles',
	SYSTEM_CONFIG = 'system-config',
	HMO_TYPES = 'hmo-types',
}

export type TDashboardPeriod = 'all' | 'day' | 'week' | 'month' | 'year';

// Doctor Status Types
export enum EDoctorStatus {
	PENDING = 'pending',
	ACCEPTED = 'accepted',
	REJECTED = 'rejected',
	ACTIVE = 'active',
	INACTIVE = 'inactive',
}

// Patient Status Types
export enum EPatientStatus {
	ACTIVE = 'active',
	INACTIVE = 'inactive',
	ARCHIVED = 'archived',
}

// Subscription Types
export type Subscription = {
	id: number;
	name: string;
	description: string | null;
	price: number;
	discounted_price: number;
	is_discount_enabled: number;
	coverage: number;
	coverage_type: 'YEAR' | 'MONTH' | string;
	is_active: number;
	is_deleted: number;
	patient_enabled: number;
	patient_count: number;
	patient_limit_type: 'UNLIMITED' | 'LIMITED' | string;
	patient_coverage_type: 'MONTHLY' | 'YEARLY' | string;
	patient_qr_enabled: number;
	appointment_enabled: number;
	appointment_count: number;
	appointment_limit_type: 'UNLIMITED' | 'LIMITED' | string;
	appointment_coverage_type: 'MONTHLY' | 'YEARLY' | string;
	appointment_qr_enabled: number;
	clinic_enabled: number;
	clinic_count: number;
	clinic_limit_type: 'UNLIMITED' | 'LIMITED' | string;
	clinic_assistant_enabled: number;
	clinic_assistant_count: number;
	clinic_assistant_limit_type: 'UNLIMITED' | 'LIMITED' | string;
	medical_document_enabled: number;
	created_at: string;
	updated_at: string;
};

export interface ICreateSubscriptionRequest {
	name: string;
	description?: string;
	price: string;
	discountedPrice?: string;
	coverage: string;
	coverageType: string;
}
