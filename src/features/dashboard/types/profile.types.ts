export interface IDoctorProfileResponse {
	status: number;
	data: IDoctorProfile;
}

export interface IDoctorProfile {
	id: number;
	user_id: number;
	profile_role_id: number;
	first_name: string;
	last_name: string;
	middle_name: null;
	suffix: null;
	bio: null;
	avatar: null;
	phone: string;
	gender: string;
	birthday: string;
	occupation: null;
	civil_status: null;
	blood_type: null;
	height: null;
	height_type: null;
	weight: null;
	weight_type: null;
	religion: null;
	ethnicity: null;
	nationality: null;
	is_verified: number;
	is_completed: number;
	is_accepted: number;
	is_rejected: number;
	is_deleted: number;
	is_active: number;
	is_new_password: number;
	title: null;
	doctor_profile_id: null;
	created_at: string;
	updated_at: string;
	profileRole: IProfileRole;
	profileSocialMediaAccount: null;
	profileCurrentAddress: IProfileCurrentAddress;
	profilePermanentAddress: IProfilePermanentAddress;
	user: IUser;
	doctor: IDoctor;
}

export interface IProfileRole {
	id: number;
	name: string;
	description: string;
	created_at: string;
	updated_at: string;
}

export interface IProfileCurrentAddress {
	id: number;
	profile_id: number;
	country: null;
	province: null;
	city: null;
	barangay: null;
	street_name: null;
	building_apartment: null;
	subdivision_village_zone: null;
	unit_room_floor_building: null;
	lot_block_phase_street: null;
	created_at: string;
	updated_at: string;
}

export interface IProfilePermanentAddress {
	id: number;
	profile_id: number;
	country: null;
	province: null;
	city: null;
	barangay: null;
	street_name: null;
	building_apartment: null;
	subdivision_village_zone: null;
	unit_room_floor_building: null;
	lot_block_phase_street: null;
	created_at: string;
	updated_at: string;
}

export interface IUser {
	id: number;
	email: string;
	username: string;
	first_name: string;
	last_name: string;
	last_login: string;
	is_superuser: number;
	is_active: number;
	is_staff: number;
	created_at: string;
	updated_at: string;
}

export interface IDoctor {
	id: number;
	profile_id: number;
	specialty: null | string;
	sub_specialty: null;
	sub_specialization: null;
	awards: null;
	prc_number: null;
	prc_expiry_date: null;
	prc_image_front: string;
	prc_image_back: string;
	e_signature: null;
	subscription_start: null;
	subscription_end: null;
	is_subscribed: number;
	is_free_trial_done: number;
	created_at: string;
	updated_at: string;
	doctorSettings: null;
}

export interface IAssistantProfile {
	id: number;
	user_id: number;
	profile_role_id: number;
	first_name: string;
	last_name: string;
	middle_name: null;
	suffix: null;
	bio: null;
	avatar: null;
	phone: string;
	gender: string;
	birthday: string;
	occupation: null;
	civil_status: null;
	blood_type: null;
	height: null;
	height_type: null;
	weight: null;
	weight_type: null;
	religion: null;
	ethnicity: null;
	nationality: null;
	is_verified: number;
	is_completed: number;
	is_accepted: number;
	is_rejected: number;
	is_deleted: number;
	is_active: number;
	is_new_password: number;
	title: null;
	doctor_profile_id: null;
	created_at: string;
	updated_at: string;
	profileRole: IProfileRole;
	profileSocialMediaAccount: null;
	profileCurrentAddress: IProfileCurrentAddress;
	profilePermanentAddress: IProfilePermanentAddress;
	user: IUser;
	doctor: IDoctor;
}
