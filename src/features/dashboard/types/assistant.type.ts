// Doctor Dashboard Types

import { IVitalSign } from './admin.types';
import { IAssistantProfile } from './profile.types';

export interface IAssistant {
	id: number;
	profile_id: number;
	clinic_id: number;
	created_at: string;
	updated_at: string;
	profile: IProfile;
}

export interface IProfile {
	id: number;
	user_id: number;
	profile_role_id: number;
	first_name: string;
	last_name: string;
	middle_name: null;
	suffix: string;
	bio: null;
	avatar: null;
	phone: null;
	gender: null;
	birthday: null;
	occupation: null;
	civil_status: null;
	blood_type: null;
	height: null;
	height_type: null;
	weight: null;
	weight_type: null;
	religion: null;
	ethnicity: null;
	nationality: null;
	is_verified: number;
	is_completed: number;
	is_accepted: number;
	is_rejected: number;
	is_deleted: number;
	is_active: number;
	is_new_password: number;
	title: null;
	doctor_profile_id: number;
	created_at: string;
	updated_at: string;
	user: IUser;
}

export interface IUser {
	id: number;
	email: string;
	username: string;
	first_name: string;
	last_name: string;
	last_login: string;
	is_superuser: number;
	is_active: number;
	is_staff: number;
	created_at: string;
	updated_at: string;
}

// Dashboard Metrics
export interface IAssistantDashboardMetrics {
	total_appointments: number;
	total_attended: number;
	registered_patients: number;
	today_appointments: number;
	pending_appointments: number;
	completed_appointments: number;
	// Backend response fields (from /doctor/dashboard endpoint)
	totalPatients?: number;
	todaysTotalAppointments?: number;
	todaysTotalAttendedAppointments?: number;
}

// Clinic Types
export interface IClinic {
	id: number;
	name: string;
	address: string;
	phone: string;
	email: string;
	website: string;
	date_established: string;
	longitude: string;
	latitude: string;
	start_time: string;
	end_time: string;
	status: string;
	logo: string;
	doctor_profile_id: number;
	is_deleted: number;
	created_at: string;
	updated_at: string;
	clinicSchedules?: IClinicSchedule[];
	assistantProfile: IAssistantProfile;
}

// Patient Types
export interface IPatient {
	id: number;
	user_id: number;
	profile_role_id: number;
	first_name: string;
	last_name: string;
	middle_name: string | null;
	suffix: string | null;
	bio: string | null;
	avatar: string | null;
	phone: string;
	gender: string;
	birthday: string;
	occupation: string;
	civil_status: string;
	blood_type: string | null;
	height: string | null;
	height_type: string | null;
	weight: string | null;
	weight_type: string | null;
	religion: string | null;
	ethnicity: string | null;
	nationality: string | null;
	is_verified: number;
	is_completed: number;
	is_accepted: number;
	is_rejected: number;
	is_deleted: number;
	is_active: number;
	is_new_password: number;
	title: string;
	doctor_profile_id: number;
	created_at: string;
	updated_at: string;
	user: {
		id: number;
		email: string;
		username: string;
		first_name: string;
		last_name: string;
		last_login: string | null;
		is_superuser: number;
		is_active: number;
		is_staff: number;
		created_at: string;
		updated_at: string;
	};
	patient: {
		id: number;
		profile_id: number;
		is_deleted: number;
		is_active: number;
		is_pwd: number;
		emergency_contact_name: string | null;
		emergency_contact_number: string | null;
		created_at: string;
		updated_at: string;
		patient_hmo: {
			id: number;
			patient_id: number;
			hmo_id: string;
			hmo_company: string;
			hmo_detail: string;
			hmo_provider: string;
			created_at: string;
			updated_at: string;
		}[];
		clinicPatients?: {
			id: number;
			clinic_id: number;
			patient_id: number;
			created_at: string;
			updated_at: string;
			clinic?: {
				id: number;
				name: string;
				address: string | null;
				phone: string | null;
				email: string | null;
			};
		}[];
	};
	profileCurrentAddress: {
		id: number;
		profile_id: number;
		country: string | null;
		province: string | null;
		city: string | null;
		barangay: string | null;
		street_name: string | null;
		building_apartment: string | null;
		subdivision_village_zone: string | null;
		unit_room_floor_building: string | null;
		lot_block_phase_street: string | null;
		created_at: string;
		updated_at: string;
	};
	profilePermanentAddress: {
		id: number;
		profile_id: number;
		country: string | null;
		province: string | null;
		city: string | null;
		barangay: string | null;
		street_name: string | null;
		building_apartment: string | null;
		subdivision_village_zone: string | null;
		unit_room_floor_building: string | null;
		lot_block_phase_street: string | null;
		created_at: string;
		updated_at: string;
	};
}

// Appointment Patient Types (for appointment API responses)
export interface IAppointmentPatient {
	id: number;
	profile_id: number;
	is_deleted: number;
	is_active: number;
	is_pwd: number;
	emergency_contact_name: string | null;
	emergency_contact_number: string | null;
	created_at: string;
	updated_at: string;
	profile?: {
		avatar: string;
		id: number;
		user_id: number;
		profile_role_id: number;
		first_name: string;
		last_name: string;
		middle_name: string | null;
		suffix: string | null;
		phone: string;
		gender: string;
		birthday: string;
		title: string;
		user?: {
			id: number;
			email: string;
			username: string;
		};
		profileCurrentAddress?: {
			id: number;
			profile_id: number;
			address: string;
			city: string;
			province: string;
			country: string;
			postal_code: string;
			created_at: string;
			updated_at: string;
		};
		profilePermanentAddress?: {
			id: number;
			profile_id: number;
			address: string;
			city: string;
			province: string;
			country: string;
			postal_code: string;
			created_at: string;
			updated_at: string;
		};
	};
}

// Appointment Types
export interface IAppointment {
	id: number;
	consultation_type: string;
	medical_certificate: number;
	appointment_date: string;
	status: string;
	clinic_id: number;
	doctor_id: number;
	patient_hmo_id: number;
	patient_id: number;
	appointment_reason: string;
	appointment_type: string;
	payment_type: string;
	chief_complaint: string;
	diagnosis: string;
	prognosis: string;
	appointment_reference_number: string;
	queue_number: number;
	created_at: string;
	updated_at: string;
	// Patient information fields
	patient_name?: string;
	patient_address?: string;
	patient_phone?: string;
	patient_gender?: string;
	patient_birthdate?: string;
	professional_tax_receipt?: string;
	clinic?: IClinic;
	patient?: IAppointmentPatient;
	visitReason?: {
		id: number;
		name: string;
		description?: string;
	};
	consultationType?: {
		id: number;
		name: string;
		description?: string;
	};
	paymentType?: {
		id: number;
		name: string;
		description?: string;
	};
	appointmentType?: {
		id: number;
		name: string;
		description?: string;
	};
	prescriptions?: IPrescription[];
	diagnosticRequests?: AssistantDiagnosticRequest[];
	labRequests?: AssistantLabRequest[];
	laboratoryResults?: ILaboratoryResult[];
	medicalCertificates?: AssistantMedicalCertificate[];
	medicalRecords?: IMedicalRecord[];
	referrals?: AssistantReferral[];
	appointmentDoctorNotes?: IDoctorNote[];
	appointmentAssistantNotes?: IAssistantNote[];
	// Alternative field names that might be returned by the API
	doctor_notes?: IDoctorNote[];
	assistant_notes?: IAssistantNote[];
	vitalSign?: {
		id?: number;
		systolic?: string;
		diastolic?: string;
		pulse_rate?: string;
		respiration?: string;
		height?: string;
		height_type?: string;
		weight?: string;
		weight_type?: string;
		temperature?: string;
		temperature_type?: string;
		oxygen_saturation?: string;
		capillary_blood_glucose?: string;
		body_mass_index?: string;
		created_at?: string;
		updated_at?: string;
	};
}

// Assistant Types
export interface IAssistant {
	id: number;
	profile_id: number;
	first_name: string;
	last_name: string;
	middle_name: string;
	suffix: string;
	phone: string;
	gender: string;
	is_active: number;
	created_at: string;
	updated_at: string;
	user?: {
		id: number;
		email: string;
		username: string;
	};
}

// API Response Types
export interface IAssistantDashboardResponse {
	status: number;
	message: string;
	data: IAssistantDashboardMetrics;
}

export interface IClinicsResponse {
	status: number;
	message: string;
	data: {
		data: IClinic[];
		meta: {
			total: number;
			per_page: number;
			current_page: number;
			last_page: number;
			first_page: number;
			first_page_url: string;
			last_page_url: string;
			next_page_url: string | null;
			previous_page_url: string | null;
		};
	};
}

export interface IPatientsResponse {
	status: number;
	message: string;
	data: {
		data: IPatient[];
		meta: {
			total: number;
			per_page: number;
			current_page: number;
			last_page: number;
			first_page: number;
			first_page_url: string;
			last_page_url: string;
			next_page_url: string | null;
			previous_page_url: string | null;
		};
	};
}

export interface IAppointmentsResponse {
	status: number;
	message: string;
	data: {
		data: IAppointment[];
		meta: {
			total: number;
			per_page: number;
			current_page: number;
			last_page: number;
			first_page: number;
			first_page_url: string;
			last_page_url: string;
			next_page_url: string | null;
			previous_page_url: string | null;
		};
	};
}

// Dashboard appointments response (simple array, not paginated)
export interface IDashboardAppointmentsResponse {
	status: number;
	message?: string;
	data: IAppointment[];
}

export interface IAssistantsResponse {
	status: number;
	message: string;
	data: {
		data: IAssistant[];
		meta: {
			total: number;
			per_page: number;
			current_page: number;
			last_page: number;
			first_page: number;
			first_page_url: string;
			last_page_url: string;
			next_page_url: string | null;
			previous_page_url: string | null;
		};
	};
}

// Request Types
export interface IPaginationParams {
	page?: number;
	pageSize?: number;
}

export interface ISearchParams extends IPaginationParams {
	search?: string;
}

export interface IClinicParams extends ISearchParams {
	status?: string;
}

export interface IAppointmentParams extends ISearchParams {
	clinicId?: number;
	status?: string;
	date?: string;
	category?: string;
}

// Appointment Creation Types
export interface ICreateAppointmentRequest {
	clinicId: number;
	patientProfileId: number;
	visitReasonId: number;
	consultationTypeId: number;
	appointmentTypeId: number;
	patientHmoId?: number;
	paymentTypeId: number;
	medicalCertificate: number; // 0 or 1
	appointmentDate: string;
}

// Patient Creation Types
export interface ICreatePatientRequest {
	clinicId: number;
	title?: string;
	firstName: string;
	middleName?: string;
	lastName: string;
	suffix?: string;
	gender: string;
	phone: string;
	birthday: string;
	occupation?: string;
	civilStatus: string;
	ethnicity: string;
	emergencyContactName?: string;
	emergencyContactNumber?: string;
	email?: string;
	currentAddress?: string;
	permanentAddress?: string;
	bloodType?: string;
}

// Frontend schedule interface (snake_case for display)
export interface IClinicSchedule {
	day: string;
	start_time: string;
	end_time: string;
	is_active: number;
}

// Backend schedule interface (camelCase for API)
export interface IBackendClinicSchedule {
	day: string;
	startTime: string;
	endTime: string;
	isActive: number;
}

// Clinic Creation Types
export interface ICreateClinicRequest {
	name: string;
	assistantProfileIds?: number[];
	address: string;
	longitude?: string;
	latitude?: string;
	phone?: string;
	email?: string;
	website?: string;
	startTime?: string;
	endTime?: string;
	dateEstablished?: Date;
	schedule?: IBackendClinicSchedule[];
}

// Clinic Update Types
export interface IUpdateClinicRequest {
	clinicId: number;
	status?: string;
	name?: string;
	address?: string;
	longitude?: string;
	latitude?: string;
	phone?: string;
	email?: string;
	website?: string;
	startTime?: string;
	endTime?: string;
	dateEstablished?: string;
	schedule?: IBackendClinicSchedule[];
}

// Patient Update Types
export interface IUpdatePatientRequest {
	profileId: number;
	title?: string;
	firstName?: string;
	middleName?: string;
	lastName?: string;
	suffix?: string;
	phone?: string;
	occupation?: string;
	emergencyContactName?: string;
	emergencyContactNumber?: string;
	bloodType?: string;
}

// Patient Profile Management Types

// Habit Management Types
export interface IAddHabitRequest {
	habitTypeId: number;
	description?: string;
	frequency?: string;
	duration?: string;
}

export interface IUpdateHabitRequest {
	habitTypeId?: number;
	description?: string;
	frequency?: string;
	duration?: string;
}

// History Management Types
export interface IAddHistoryRequest {
	name: string;
	description?: string;
}

export interface IUpdateHistoryRequest {
	name?: string;
	description?: string;
}

// Allergy Management Types
export interface IAddAllergyRequest {
	name: string;
	description?: string;
}

export interface IUpdateAllergyRequest {
	name?: string;
	description?: string;
}

// Illness Management Types
export interface IAddIllnessRequest {
	name: string;
	description?: string;
}

export interface IUpdateIllnessRequest {
	name?: string;
	description?: string;
}

// Surgery Management Types
export interface IAddSurgeryRequest {
	name: string;
	description?: string;
}

export interface IUpdateSurgeryRequest {
	name?: string;
	description?: string;
}

// Diet Management Types
export interface IAddDietRequest {
	name: string;
	description?: string;
}

export interface IUpdateDietRequest {
	name?: string;
	description?: string;
}

// HMO Management Types
export interface IAddHmoRequest {
	hmoId: string;
	hmoCompany: string;
	hmoDetail?: string;
	hmoProvider?: string;
}

export interface IUpdateHmoRequest {
	hmoId?: string;
	hmoCompany?: string;
	hmoDetail?: string;
	hmoProvider?: string;
}

// Family Management Types
export interface IAddFamilyRequest {
	familyProfileId: number;
	relationship: string;
}

// Assistant Creation Types
export interface ICreateAssistantRequest {
	firstName: string;
	middleName?: string;
	lastName: string;
	suffix?: string;
	email?: string;
	username: string;
	password: string;
	password_confirmation: string;
}

// Assistant Assignment Types
export interface IAssignAssistantRequest {
	assistantProfileId: number;
}

// Assistant Update Types
export interface IUpdateAssistantRequest {
	assistantProfileId: number;
	firstName?: string;
	middleName?: string;
	lastName?: string;
	suffix?: string;
	email?: string;
	gender?: string;
	phone?: string;
}

// Assistant Credentials Update Types
export interface IUpdateAssistantCredentialsRequest {
	username?: string;
	password?: string;
}

// Appointment Update Types
export interface IUpdateAppointmentRequest {
	appointment_id: number;
	consultation_type?: string;
	medical_certificate?: boolean;
	appointment_date?: string;
	appointment_time?: string;
	status?: string;
	clinic_id?: number;
	patient_id?: number;
	appointment_reason?: string;
	appointment_type?: string;
	payment_type?: string;
	chief_complaint?: string;
	diagnosis?: string;
	prognosis?: string;
}

// Generic API Response Types
export interface IApiResponse<T = unknown> {
	status: number;
	message: string;
	data?: T;
}

// Prescription Types
export interface IPrescription {
	id: number;
	appointment_id: number | null;
	clinic_id: number;
	patient_id: number | null;
	is_deleted: number;
	patient_name: string;
	patient_address: string;
	patient_phone: string;
	patient_gender: string;
	patient_birthdate: string;
	professional_tax_receipt?: string;
	created_at: string;
	updated_at: string;
	prescriptionItems?: IPrescriptionItem[];
	clinic?: IClinic;
}

export interface IPrescriptionItem {
	id: number;
	prescription_id: number;
	generic: string;
	brand: string;
	dosage_form: string;
	dosage: string;
	frequency: string;
	quantity: number;
	instruction: string;
	is_deleted: number;
	created_at: string;
	updated_at: string;
}

// Prescription Request Types
export interface ICreatePrescriptionRequest {
	patient_name: string;
	patient_address: string;
	patient_phone: string;
	patient_gender: string;
	patient_birthdate: string;
	professional_tax_receipt?: string;
}

export interface ICreatePrescriptionItemRequest {
	generic: string;
	brand: string;
	dosageForm: string;
	dosage: string;
	frequency: string;
	quantity: number;
	instruction: string;
}

// Standalone Medical Certificate Types
export interface ICreateStandaloneMedicalCertificateRequest {
	clinicId: number;
	patientProfileId?: number;
	patientName: string;
	patientAddress: string;
	patientPhone: string;
	patientGender: string;
	patientBirthdate: string;
}

// Standalone Prescription Types
export interface ICreateStandalonePrescriptionRequest {
	clinicId: number;
	patientProfileId?: number;
	patientName: string;
	patientAddress: string;
	patientPhone: string;
	patientGender: string;
	patientBirthdate: string;
}

export interface IPrescriptionListParams extends IPaginationParams {
	search?: string;
	isArchived?: boolean;
}

// Standalone Lab Request Types
export interface ICreateStandaloneLabRequestRequest {
	clinicId: number;
	patientProfileId?: number;
	patientName: string;
	patientAddress: string;
	patientPhone: string;
	patientGender: string;
	patientBirthdate: string;
}

// Standalone Diagnostic Request Types
export interface ICreateStandaloneDiagnosticRequestRequest {
	clinicId: number;
	patientProfileId?: number;
	patientName: string;
	patientAddress: string;
	patientPhone: string;
	patientGender: string;
	patientBirthdate: string;
}

export interface ILabRequestListParams extends IPaginationParams {
	search?: string;
	isArchived?: boolean;
}

export interface IMedicalCertificateListParams extends IPaginationParams {
	search?: string;
	isArchived?: boolean;
}

export interface IMedicalCertificateListParams extends IPaginationParams {
	search?: string;
	isArchived?: boolean;
}

export interface ICreateStandaloneDiagnosticRequestRequest {
	clinicId: number;
	patientProfileId?: number;
	patientName: string;
	patientAddress: string;
	patientPhone: string;
	patientGender: string;
	patientBirthdate: string;
}

export interface IDiagnosticRequestListParams extends IPaginationParams {
	search?: string;
	isArchived?: boolean;
}

export interface IPrescriptionsResponse {
	status: number;
	message: string;
	data: {
		data: IPrescription[];
		meta: {
			total: number;
			per_page: number;
			current_page: number;
			last_page: number;
			first_page: number;
			first_page_url: string;
			last_page_url: string;
			next_page_url: string | null;
			previous_page_url: string | null;
		};
	};
}

export interface IPrescriptionDetailResponse {
	status: number;
	message: string;
	data: IPrescription;
}

export interface ILabRequestsResponse {
	status: number;
	message: string;
	data: {
		data: AssistantLabRequest[];
		meta: {
			total: number;
			per_page: number;
			current_page: number;
			last_page: number;
			first_page: number;
			first_page_url: string;
			last_page_url: string;
			next_page_url: string | null;
			previous_page_url: string | null;
		};
	};
}

export interface IMedicalCertificatesResponse {
	status: number;
	message: string;
	data: {
		data: AssistantMedicalCertificate[];
		meta: {
			total: number;
			per_page: number;
			current_page: number;
			last_page: number;
			first_page: number;
			first_page_url: string;
			last_page_url: string;
			next_page_url: string | null;
			previous_page_url: string | null;
		};
	};
}

export interface IDiagnosticRequestsResponse {
	status: number;
	message: string;
	data: {
		data: AssistantDiagnosticRequest[];
		meta: {
			total: number;
			per_page: number;
			current_page: number;
			last_page: number;
			first_page: number;
			first_page_url: string;
			last_page_url: string;
			next_page_url: string | null;
			previous_page_url: string | null;
		};
	};
}

export interface ILabRequestDetailResponse {
	status: number;
	message: string;
	data: AssistantLabRequest;
}

export interface IMedicalCertificateDetailResponse {
	status: number;
	message: string;
	data: AssistantMedicalCertificate;
}

export interface IDiagnosticRequestDetailResponse {
	status: number;
	message: string;
	data: AssistantDiagnosticRequest;
}

// Diagnostic Request Types
export interface AssistantDiagnosticRequest {
	id: number;
	appointment_id: number;
	clinic_id: number;
	patient_id: number;
	is_deleted: number;
	patient_name: string;
	patient_address: string;
	patient_phone: string;
	patient_gender: string;
	patient_birthdate: string;
	created_at: string;
	updated_at: string;
	diagnosticRequestItems?: IDiagnosticRequestItem[];
	clinic?: IClinic;
}

export interface IDiagnosticRequestItem {
	id: number;
	diagnostic_request_id: number;
	name: string;
	created_at: string;
	updated_at: string;
}

// Diagnostic Request Request Types
export interface ICreateDiagnosticRequest {
	patient_name: string;
	patient_address: string;
	patient_phone: string;
	patient_gender: string;
	patient_birthdate: string;
}

export interface ICreateDiagnosticRequestItemRequest {
	name: string;
}

// Laboratory Request Types
export interface AssistantLabRequest {
	id: number;
	appointment_id: number | null;
	clinic_id: number;
	patient_id: number | null;
	is_deleted: number;
	patient_name: string;
	patient_address: string;
	patient_phone: string;
	patient_gender: string;
	patient_birthdate: string;
	reference_number: string;
	professional_tax_receipt: string | null;
	created_at: string;
	updated_at: string;
	labRequestItems?: ILabRequestItem[];
	clinic?: IClinic;
}

export interface ILabRequestItem {
	id: number;
	lab_request_id: number;
	laboratory_id: number | null;
	name: string | null;
	created_at: string;
	updated_at: string;
	laboratory: ILaboratory | null;
}

export interface ILaboratory {
	id: number;
	name: string;
	// Add other laboratory fields as needed
}

// Laboratory Request Request Types
export interface ICreateLabRequest {
	patient_name: string;
	patient_address: string;
	patient_phone: string;
	patient_gender: string;
	patient_birthdate: string;
}

export interface ICreateLabRequestItemRequest {
	name: string;
}

export interface IUploadLabResultRequest {
	record: File;
	notes?: string;
}

export interface ILaboratoryResult {
	id: number;
	laboratory_id: number | null;
	appointment_id: number;
	record: string;
	reference_number: string | null;
	patient_name: string;
	created_at: string;
	updated_at: string;
}

// Doctor Note Types
export interface IDoctorNote {
	id: number;
	appointment_id: number;
	note: string;
	doctor_id: number;
	created_at: string;
	updated_at: string;
}

// Assistant Note Types
export interface IAssistantNote {
	id: number;
	appointment_id: number;
	note: string;
	assistant_id: number;
	created_at: string;
	updated_at: string;
	assistant?: IAssistant;
}

// Medical Certificate Types
export interface AssistantMedicalCertificate {
	id: number;
	appointment_id: number;
	clinic_id: number;
	patient_id: number;
	is_deleted: number;
	patient_name: string;
	patient_address: string;
	patient_phone: string;
	patient_gender: string;
	patient_birthdate: string;
	professional_tax_receipt: string;
	medical_number: string;
	created_at: string;
	updated_at: string;
	medicalCertificateItems?: IMedicalCertificateItem[];
	clinic?: IClinic;
}

export interface IMedicalCertificateItem {
	id: number;
	medical_certificate_id: number;
	description: string;
	name: string;
	is_deleted: number;
	created_at: string;
	updated_at: string;
}

// Medical Certificate Request Types
export interface ICreateMedicalCertificateRequest {
	patient_name: string;
	patient_address: string;
	patient_phone: string;
	patient_gender: string;
	patient_birthdate: string;
	professional_tax_receipt?: string;
	medical_number?: string;
}

export interface ICreateMedicalCertificateItemRequest {
	description: string;
}

// Medical Record Types
export interface IMedicalRecord {
	id: number;
	appointment_id: number;
	record: string;
	created_at: string;
	updated_at: string;
}

// Referral Types
export interface AssistantReferral {
	id: number;
	appointment_id: number | null; // null for standalone referrals
	clinic_id: number;
	patient_id: number | null;
	is_deleted: number;
	patient_name: string;
	patient_address: string;
	patient_phone: string;
	patient_gender: string;
	patient_birthdate: string;
	patient_occupation?: string;
	professional_tax_receipt?: string;
	doctor_name: string;
	purpose: string;
	qr_code?: string; // For medical history sharing
	created_at: string;
	updated_at: string;
	clinic?: IClinic;
	vitalSign?: IVitalSign;
}

// Referral Request Types
export interface ICreateReferralRequest {
	patient_name: string;
	patient_address: string;
	patient_phone: string;
	patient_gender: string;
	patient_birthdate: string;
	professional_tax_receipt?: string;
	doctorName: string;
	purpose: string;
}

// Standalone Referral Types
export interface ICreateStandaloneReferralRequest {
	clinicId: number;
	patientProfileId?: number;
	patientName: string;
	patientAddress: string;
	patientPhone: string;
	patientGender: string;
	patientBirthdate: string;
	doctorName: string;
	purpose: string;
}

export interface IReferralListParams extends IPaginationParams {
	search?: string;
	isArchived?: boolean;
}

export interface AssistantReferralsResponse {
	status: number;
	message: string;
	data: {
		data: AssistantReferral[];
		meta: {
			total: number;
			per_page: number;
			current_page: number;
			last_page: number;
			from: number;
			to: number;
			first_page_url: string;
			last_page_url: string;
			next_page_url: string | null;
			previous_page_url: string | null;
		};
	};
}

export interface IReferralDetailResponse {
	status: number;
	message: string;
	data: AssistantReferral;
}

// Enums
export enum EAppointmentStatus {
	CREATED = 'created',
	CONFIRMED = 'confirmed',
	DECLINED = 'declined',
	WAITING = 'waiting',
	ONGOING = 'ongoing',
	COMPLETED = 'completed',
	FOLLOW_UP = 'follow-up',
	NO_SHOW = 'no-show',
	RESCHEDULED = 'rescheduled',
	CANCELLED = 'cancelled',
}

export enum EAssistantDashboardTab {
	OVERVIEW = 'overview',
	APPOINTMENTS = 'appointments',
	PATIENTS = 'patients',
	CLINICS = 'clinics',
	ASSISTANTS = 'assistants',
	MEDICAL_DOCUMENTS = 'medical-documents',
	HISTORY = 'history',
	CALENDAR = 'calendar',
	ARCHIVE = 'archive',
	SUBSCRIPTION = 'subscription',
	SETTINGS = 'settings',
}
