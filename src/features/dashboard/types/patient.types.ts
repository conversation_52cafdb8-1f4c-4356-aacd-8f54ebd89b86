// Patient Dashboard Tab Enum
export enum EPatientDashboardTab {
	OVERVIEW = 'overview',
	APPOINTMENTS = 'appointments',
	MEDICAL_DOCUMENTS = 'medical-documents',
	PROFILE = 'profile',
	MEDICAL_HISTORY = 'medical-history',
	QR_CODE = 'qr-code',
}

// Patient Profile Interface
export interface IPatientProfile {
	id: number;
	patient_id: string;
	first_name: string;
	middle_name?: string;
	last_name: string;
	suffix?: string;
	email: string;
	phone_number: string;
	date_of_birth: string;
	gender: string;
	civil_status: string;
	nationality: string;
	religion?: string;
	occupation?: string;
	emergency_contact_name?: string;
	emergency_contact_phone?: string;
	emergency_contact_relationship?: string;
	current_address?: IPatientAddress;
	permanent_address?: IPatientAddress;
	avatar?: string;
	is_active: number;
	created_at: string;
	updated_at: string;
}

// Patient Address Interface
export interface IPatientAddress {
	id: number;
	street_address: string;
	barangay: string;
	city: string;
	province: string;
	postal_code: string;
	country: string;
}

// Patient Appointment Interface
export interface IPatientAppointment {
	id: number;
	appointment_date: string;
	appointment_time: string;
	status: string;
	visit_reason: string;
	consultation_type: string;
	doctor_name: string;
	clinic_name: string;
	clinic_address: string;
	notes?: string;
	vital_signs?: IVitalSigns;
	created_at: string;
	updated_at: string;
}

// Vital Signs Interface
export interface IVitalSigns {
	blood_pressure?: string;
	heart_rate?: number;
	temperature?: number;
	weight?: number;
	height?: number;
	respiratory_rate?: number;
	oxygen_saturation?: number;
}

// Medical Document Interface
export interface IMedicalDocument {
	id: number;
	type: 'prescription' | 'lab_result' | 'medical_certificate' | 'referral';
	title: string;
	description?: string;
	file_url?: string;
	doctor_name: string;
	clinic_name: string;
	issued_date: string;
	created_at: string;
}

// Prescription Interface
export interface IPrescription {
	id: number;
	appointment_id: number;
	medications: IMedication[];
	instructions?: string;
	doctor_name: string;
	clinic_name: string;
	issued_date: string;
	created_at: string;
}

// Medication Interface
export interface IMedication {
	id: number;
	name: string;
	dosage: string;
	frequency: string;
	duration: string;
	instructions?: string;
}

// Lab Result Interface
export interface ILabResult {
	id: number;
	appointment_id: number;
	test_name: string;
	test_type: string;
	results: ILabTestResult[];
	doctor_name: string;
	clinic_name: string;
	test_date: string;
	created_at: string;
}

// Lab Test Result Interface
export interface ILabTestResult {
	parameter: string;
	value: string;
	unit: string;
	reference_range: string;
	status: 'normal' | 'high' | 'low' | 'critical';
}

// Medical Certificate Interface
export interface IMedicalCertificate {
	id: number;
	appointment_id: number;
	certificate_type: string;
	purpose: string;
	diagnosis?: string;
	recommendations?: string;
	fitness_status?: string;
	valid_from: string;
	valid_until?: string;
	doctor_name: string;
	clinic_name: string;
	issued_date: string;
	created_at: string;
}

// Referral Interface
export interface IReferral {
	id: number;
	appointment_id: number;
	referred_to_doctor: string;
	referred_to_clinic: string;
	specialty: string;
	reason: string;
	urgency: 'routine' | 'urgent' | 'emergency';
	notes?: string;
	doctor_name: string;
	clinic_name: string;
	referral_date: string;
	created_at: string;
}

// Medical History Interface
export interface IPatientMedicalHistory {
	allergies: IAllergy[];
	illnesses: IIllness[];
	surgeries: ISurgery[];
	medications: ICurrentMedication[];
	family_history: IFamilyHistory[];
	lifestyle: ILifestyle;
}

// Allergy Interface
export interface IAllergy {
	id: number;
	allergen: string;
	reaction: string;
	severity: 'mild' | 'moderate' | 'severe';
	notes?: string;
}

// Illness Interface
export interface IIllness {
	id: number;
	condition: string;
	diagnosed_date: string;
	status: 'active' | 'resolved' | 'chronic';
	notes?: string;
}

// Surgery Interface
export interface ISurgery {
	id: number;
	procedure: string;
	surgery_date: string;
	hospital: string;
	surgeon: string;
	notes?: string;
}

// Current Medication Interface
export interface ICurrentMedication {
	id: number;
	medication_name: string;
	dosage: string;
	frequency: string;
	started_date: string;
	prescribing_doctor: string;
	notes?: string;
}

// Family History Interface
export interface IFamilyHistory {
	id: number;
	relationship: string;
	condition: string;
	age_of_onset?: number;
	notes?: string;
}

// Lifestyle Interface
export interface ILifestyle {
	smoking_status: 'never' | 'former' | 'current';
	alcohol_consumption: 'none' | 'occasional' | 'moderate' | 'heavy';
	exercise_frequency: 'none' | 'rarely' | 'weekly' | 'daily';
	diet_type: 'regular' | 'vegetarian' | 'vegan' | 'special';
	sleep_hours: number;
	stress_level: 'low' | 'moderate' | 'high';
}

// Patient Dashboard Overview Interface
export interface IPatientDashboardOverview {
	upcoming_appointments: IPatientAppointment[];
	recent_documents: IMedicalDocument[];
	vital_signs_summary: IVitalSigns;
	health_alerts: IHealthAlert[];
}

// Health Alert Interface
export interface IHealthAlert {
	id: number;
	type:
		| 'medication_reminder'
		| 'appointment_reminder'
		| 'health_tip'
		| 'urgent';
	title: string;
	message: string;
	priority: 'low' | 'medium' | 'high';
	created_at: string;
}
