export interface IDoctorDetails {
	id: number;
	user_id: number;
	profile_role_id: number;
	first_name: string;
	last_name: string;
	middle_name: string;
	suffix: string;
	bio: null;
	avatar: null;
	phone: string;
	gender: string;
	birthday: string;
	occupation: null;
	civil_status: null;
	blood_type: null;
	height: null;
	height_type: null;
	weight: null;
	weight_type: null;
	religion: null;
	ethnicity: null;
	nationality: null;
	is_verified: number;
	is_completed: number;
	is_accepted: number;
	is_rejected: number;
	is_deleted: number;
	is_active: number;
	is_new_password: number;
	title: null;
	doctor_profile_id: null;
	created_at: string;
	updated_at: string;
	profilePermanentAddress: IProfilePermanentAddress;
	profileCurrentAddress: IProfileCurrentAddress;
	clinics: IClinic[];
	user: IUser;
	profileRole: IProfileRole;
	profileSocialMediaAccount: null;
	doctor: IDoctor;
}

export interface IProfilePermanentAddress {
	id: number;
	profile_id: number;
	country: null | string;
	province: null | string;
	city: null | string;
	barangay: null | string;
	street_name: null | string;
	building_apartment: null | string;
	subdivision_village_zone: null | string;
	unit_room_floor_building: null | string;
	lot_block_phase_street: null | string;
	created_at: string;
	updated_at: string;
}

export interface IProfileCurrentAddress {
	id: number;
	profile_id: number;
	country: null | string;
	province: null | string;
	city: null | string;
	barangay: null | string;
	street_name: null | string;
	building_apartment: null | string;
	subdivision_village_zone: null | string;
	unit_room_floor_building: null | string;
	lot_block_phase_street: null | string;
	created_at: string;
	updated_at: string;
}

export interface IUser {
	id: number;
	email: string;
	username: string;
	first_name: string;
	last_name: string;
	last_login: null;
	is_superuser: number;
	is_active: number;
	is_staff: number;
	created_at: string;
	updated_at: string;
}

export interface IProfileRole {
	id: number;
	name: string;
	description: string;
	created_at: string;
	updated_at: string;
}

export interface IDoctor {
	id: number;
	profile_id: number;
	specialty: string;
	sub_specialty: null;
	sub_specialization: null;
	awards: null;
	prc_number: string;
	prc_expiry_date: string;
	prc_image_front: string;
	prc_image_back: string;
	e_signature: null;
	subscription_start: null;
	subscription_end: null;
	is_subscribed: number;
	is_free_trial_done: number;
	created_at: string;
	updated_at: string;
	doctorSettings: null;
}

export interface IClinic {
	id: number;
	name: string | null;
	clinic_type: string | null;
	address: string | null;
	phone: string | null;
	email: string | null;
	website: string | null;
	start_time: string | null;
	end_time: string | null;
	date_established: string | null;
	status: string | null;
	logo: string | null;
	doctor_profile_id: number;
	longitude: string | null;
	latitude: string | null;
	is_deleted: number;
	created_at: string;
	updated_at: string;
}
