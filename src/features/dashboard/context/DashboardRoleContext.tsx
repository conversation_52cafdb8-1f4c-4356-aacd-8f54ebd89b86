'use client';

import React, { createContext, ReactNode, useContext } from 'react';

import {
	useNavigation,
	usePermissions,
	useUserRole,
} from '../hooks/useRoleAwareDashboard';

interface IDashboardRoleContextValue {
	role: 'doctor' | 'assistant' | 'admin' | 'patient' | null;
	navigation: {
		basePath: string;
		availableTabs: string[];
		canAccessTab: (tab: string) => boolean;
	};
	permissions: {
		canManageAssistants: boolean;
		canManageSubscription: boolean;
		canCreateClinics: boolean;
		canArchiveData: boolean;
		canViewSettings: boolean;
		canManageAppointments: boolean;
		canManagePatients: boolean;
		canViewMedicalDocuments: boolean;
		canViewCalendar: boolean;
	};
}

const DashboardRoleContext = createContext<IDashboardRoleContextValue | null>(
	null
);

interface IDashboardRoleProviderProps {
	children: ReactNode;
}

export function DashboardRoleProvider({
	children,
}: IDashboardRoleProviderProps) {
	const role = useUserRole();
	const navigation = useNavigation();
	const permissions = usePermissions();

	const value: IDashboardRoleContextValue = {
		role,
		navigation,
		permissions,
	};

	return (
		<DashboardRoleContext.Provider value={value}>
			{children}
		</DashboardRoleContext.Provider>
	);
}

export function useDashboardRole() {
	const context = useContext(DashboardRoleContext);
	if (!context) {
		throw new Error(
			'useDashboardRole must be used within a DashboardRoleProvider'
		);
	}
	return context;
}

// Convenience hooks for specific aspects
export function useDashboardNavigation() {
	const { navigation } = useDashboardRole();
	return navigation;
}

export function useDashboardPermissions() {
	const { permissions } = useDashboardRole();
	return permissions;
}

export function useCurrentRole() {
	const { role } = useDashboardRole();
	return role;
}
