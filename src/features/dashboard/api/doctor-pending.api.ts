import axios from '@/core/api';
import { TUserRole } from '@/core/types/role.type';

import { IPendingDoctorsResponse } from './doctor-pending.type';

export const getPendingDoctors = async (
	page = 1,
	pageSize = 10,
	role: TUserRole = 'admin'
) => {
	const { data } = await axios.get(`/${role}/doctors/pending`, {
		params: {
			page,
			pageSize,
		},
	});
	return data.data as IPendingDoctorsResponse;
};

export const acceptDoctor = async (
	profileId: number,
	role: TUserRole = 'admin'
) => {
	const { data } = await axios.post(`/${role}/doctors/pending/accept`, {
		profileId,
	});
	return data;
};

export const rejectDoctor = async (
	profileId: number,
	role: TUserRole = 'admin'
) => {
	const { data } = await axios.post(`/${role}/doctors/pending/reject`, {
		profileId,
	});
	return data;
};
