import { IMeta } from '@/core/types/index.type';

export interface IDoctorsRequest {
	search: string;
	page: number;
	pageSize: number;
}

export interface IDoctorsResponse {
	meta: IMeta;
	data: IDoctorData[];
}

export interface IDoctorData {
	id: number;
	user_id: number;
	profile_role_id: number;
	first_name: string;
	last_name: string;
	middle_name: string | null;
	suffix: string | null;
	bio: null;
	avatar: null;
	phone: string;
	gender: string;
	birthday: string;
	occupation: null;
	civil_status: null;
	blood_type: null;
	height: null;
	height_type: null;
	weight: null;
	weight_type: null;
	religion: null;
	ethnicity: null;
	nationality: null;
	is_verified: number;
	is_completed: number;
	is_accepted: number;
	is_rejected: number;
	is_deleted: number;
	is_active: number;
	is_new_password: number;
	title: null;
	doctor_profile_id: null;
	created_at: string;
	updated_at: string;
	doctor: IDoctor;
	clinics: unknown[] | IClinics[];
	user: IUser;
}

export interface IDoctor {
	id: number;
	profile_id: number;
	specialty: string | null;
	sub_specialty: null;
	sub_specialization: null;
	awards: null;
	prc_number: string | null;
	prc_expiry_date: string | null;
	prc_image_front: string;
	prc_image_back: string;
	e_signature: null;
	subscription_start: null;
	subscription_end: null;
	is_subscribed: number;
	is_free_trial_done: number;
	created_at: string;
	updated_at: string;
}

export interface IUser {
	id: number;
	email: string;
	username: string;
	first_name: string;
	last_name: string;
	last_login: null | string;
	is_superuser: number;
	is_active: number;
	is_staff: number;
	created_at: string;
	updated_at: string;
}

export interface IClinics {
	id: number;
	name: string;
	clinic_type: string | null;
	address: string | null;
	phone: string | null;
	email: string | null;
	website: null | string;
	start_time: null;
	end_time: null;
	date_established: string | null;
	status: string | null;
	logo: null;
	doctor_profile_id: number;
	longitude: null;
	latitude: null;
	is_deleted: number;
	created_at: string;
	updated_at: string;
}
