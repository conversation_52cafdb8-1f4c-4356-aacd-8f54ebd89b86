import { IMeta } from '@/core/types/index.type';

export interface IPendingDoctorsResponse {
	meta: IMeta;
	data: IPendingDoctorData[];
}

export interface IPendingDoctorData {
	id: number;
	first_name: string;
	last_name: string;
	middle_name: string | null;
	suffix: string | null;
	created_at: string;
	doctor: IPendingDoctor;
}

export interface IPendingDoctor {
	id: number;
	specialty: string | null;
	profile_id: number;
}
