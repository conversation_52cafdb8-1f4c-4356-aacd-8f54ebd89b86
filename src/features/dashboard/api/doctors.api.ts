'use client';

import axios from '@/core/api';
import { IRegisterDoctor } from '@/core/api/registration/doctor.type';
import { TUserRole } from '@/core/types/role.type';

import { IDoctorsRequest, IDoctorsResponse } from './doctors.type';

export const getDoctors = async (
	userRole: TUserRole = 'admin',
	params: IDoctorsRequest
): Promise<IDoctorsResponse> => {
	const { data } = await axios.get(`/${userRole}/doctors`, { params });
	return data.data as IDoctorsResponse;
};

export const disableDoctor = async (
	profileId: number,
	userRole: TUserRole = 'admin'
) => {
	const { data } = await axios.post(
		`/${userRole}/doctors/${profileId}/disable`
	);
	return data;
};

export const enableDoctor = async (
	profileId: number,
	userRole: TUserRole = 'admin'
) => {
	const { data } = await axios.post(`/${userRole}/doctors/${profileId}/enable`);
	return data;
};

export const createDoctor = async (
	doctorData: IRegisterDoctor,
	userRole: TUserRole = 'admin'
) => {
	const { data } = await axios.post(`/${userRole}/doctors/create`, doctorData, {
		headers: {
			'Content-Type': 'multipart/form-data',
		},
	});
	return data;
};
