'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import {
	completeOnboarding,
	DOCTOR_QUERY_KEYS,
	getDoctorProfile,
	updateDoctorAddress,
	updateDoctorBasicInfo,
	updateDoctorESignature,
	updateDoctorSocialMedia,
	updateDoctorSpecialization,
} from '@/core/api/doctor';
import { catchError } from '@/core/lib/utils';

// Doctor Profile Management Hooks
export const useDoctorProfile = () => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.PROFILE.DETAIL,
		queryFn: getDoctorProfile,
		staleTime: 10 * 60 * 1000, // 10 minutes
	});
};

export const useUpdateDoctorBasicInfo = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: updateDoctorBasicInfo,
		onSuccess: () => {
			// Invalidate and refetch doctor profile
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PROFILE.DETAIL,
			});

			toast.success('Profile Updated', {
				description: 'Basic information has been updated successfully.',
			});
		},
		onError: (error) => {
			catchError(error, 'Update Error', 'Failed to update basic information');
		},
	});
};

export const useUpdateDoctorSpecialization = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: updateDoctorSpecialization,
		onSuccess: () => {
			// Invalidate and refetch doctor profile
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PROFILE.DETAIL,
			});

			toast.success('Specialization Updated', {
				description: 'Professional information has been updated successfully.',
			});
		},
		onError: (error) => {
			catchError(error, 'Update Error', 'Failed to update specialization');
		},
	});
};

export const useUpdateDoctorAddress = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: updateDoctorAddress,
		onSuccess: () => {
			// Invalidate and refetch doctor profile
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PROFILE.DETAIL,
			});

			toast.success('Address Updated', {
				description: 'Address information has been updated successfully.',
			});
		},
		onError: (error) => {
			catchError(error, 'Update Error', 'Failed to update address');
		},
	});
};

export const useUpdateDoctorESignature = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: updateDoctorESignature,
		onSuccess: () => {
			// Invalidate and refetch doctor profile
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PROFILE.DETAIL,
			});

			toast.success('E-Signature Updated', {
				description: 'Electronic signature has been updated successfully.',
			});
		},
		onError: (error) => {
			catchError(error, 'Update Error', 'Failed to update e-signature');
		},
	});
};

export const useUpdateDoctorSocialMedia = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: updateDoctorSocialMedia,
		onSuccess: () => {
			// Invalidate and refetch doctor profile
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PROFILE.DETAIL,
			});

			toast.success('Social Media Updated', {
				description: 'Social media information has been updated successfully.',
			});
		},
		onError: (error) => {
			catchError(error, 'Update Error', 'Failed to update social media');
		},
	});
};

export const useCompleteOnboarding = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: completeOnboarding,
		onSuccess: () => {
			// Invalidate and refetch doctor profile
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PROFILE.DETAIL,
			});

			toast.success('Onboarding Complete!', {
				description: 'Your profile setup has been completed successfully.',
			});
		},
		onError: (error) => {
			catchError(error, 'Onboarding Error', 'Failed to complete onboarding');
		},
	});
};
