'use client';

// Patient hooks are currently disabled as the backend API is not yet implemented
// These hooks will be re-enabled once the patient API endpoints are available

export const usePatientProfile = () => {
	return {
		data: null,
		isLoading: false,
		error: null,
		isError: false,
	};
};

export const useUpdatePatientProfile = () => {
	return {
		mutate: () => {},
		isPending: false,
		error: null,
		isError: false,
	};
};

// Additional patient hooks - all disabled until backend API is implemented
export const usePatientAppointments = () => {
	return {
		data: null,
		isLoading: false,
		error: null,
		isError: false,
	};
};

export const usePatientAppointmentHistory = () => {
	return {
		data: null,
		isLoading: false,
		error: null,
		isError: false,
	};
};

export const usePatientAppointmentById = () => {
	return {
		data: null,
		isLoading: false,
		error: null,
		isError: false,
	};
};

export const usePatientAppointmentDetail = () => {
	return {
		data: null,
		isLoading: false,
		error: null,
		isError: false,
	};
};

export const usePatientMedicalDocuments = () => {
	return {
		data: null,
		isLoading: false,
		error: null,
		isError: false,
	};
};

export const usePatientPrescriptions = () => {
	return {
		data: null,
		isLoading: false,
		error: null,
		isError: false,
	};
};

export const usePatientLabResults = () => {
	return {
		data: null,
		isLoading: false,
		error: null,
		isError: false,
	};
};

export const usePatientMedicalCertificates = () => {
	return {
		data: null,
		isLoading: false,
		error: null,
		isError: false,
	};
};

export const usePatientReferrals = () => {
	return {
		data: null,
		isLoading: false,
		error: null,
		isError: false,
	};
};

export const usePatientMedicalHistory = () => {
	return {
		data: null,
		isLoading: false,
		error: null,
		isError: false,
	};
};

export const useUpdatePatientMedicalHistory = () => {
	return {
		mutate: () => {},
		isPending: false,
		error: null,
		isError: false,
	};
};

export const usePatientQRCode = () => {
	return {
		data: null,
		isLoading: false,
		error: null,
		isError: false,
	};
};
