'use client';

import { usePathname } from 'next/navigation';
import { useMemo } from 'react';

import { useSession } from '@/core/hooks/useSession';

// Role detection hook
export const useUserRole = () => {
	const { userRoles, isAuthenticated, setLoginData } = useSession();
	const pathname = usePathname();

	const role = useMemo(() => {
		// First, try to get role from session system
		if (isAuthenticated && userRoles) {
			if (userRoles.is_doctor) return 'doctor';
			if (userRoles.is_assistant) return 'assistant';
			if (userRoles.is_admin) return 'admin';
			if (userRoles.is_patient) return 'patient';
		}

		// Enhanced fallback: Check for role indicators in URL and localStorage
		if (pathname) {
			// Check for explicit role paths
			if (pathname.includes('/assistant/')) {
				// Auto-populate session for assistant
				if (!isAuthenticated || !userRoles?.is_assistant) {
					const mockLoginData = {
						status: 1,
						message: 'Auto-detected assistant from route',
						data: {
							user: {
								id: 0,
								email: '',
								username: 'assistant-user',
								first_name: 'Assistant',
								last_name: 'User',
								last_login: new Date().toISOString(),
								is_superuser: 0,
								is_active: 1,
								is_staff: 0,
								created_at: new Date().toISOString(),
								updated_at: new Date().toISOString(),
								profile: {
									id: 0,
									user_id: 0,
									profile_role_id: 4,
									first_name: 'Assistant',
									last_name: 'User',
									middle_name: '',
									suffix: '',
									phone: '',
									gender: '',
									birthday: '',
									is_verified: 1,
									is_completed: 1,
									is_accepted: 1,
									is_rejected: 0,
									is_deleted: 0,
									is_active: 1,
									is_new_password: 0,
									created_at: new Date().toISOString(),
									updated_at: new Date().toISOString(),
									profile_role: {
										id: 4,
										name: 'Assistant',
									},
								},
							},
							token: 'route-detected-token',
							isAdmin: false,
							isDoctor: false,
							isPatient: false,
							isAssistant: true,
							isLaboratory: false,
						},
					};
					setLoginData(mockLoginData);
				}
				return 'assistant';
			}

			if (pathname.includes('/doctor/')) {
				return 'doctor';
			}

			if (pathname.includes('/admin/')) {
				return 'admin';
			}

			// For /dashboard path, try to detect role from other indicators
			if (pathname.includes('/dashboard')) {
				// Check localStorage for any role indicators
				if (typeof window !== 'undefined') {
					try {
						const sessionData = window.localStorage.getItem('session');
						if (sessionData) {
							const session = JSON.parse(sessionData);
							if (session.userRoles) {
								if (session.userRoles.is_assistant) return 'assistant';
								if (session.userRoles.is_doctor) return 'doctor';
								if (session.userRoles.is_admin) return 'admin';
								if (session.userRoles.is_patient) return 'patient';
							}
						}

						// Check for any role-specific data in localStorage
						const roleHint = window.localStorage.getItem('userRole');
						if (roleHint) {
							return roleHint as 'doctor' | 'assistant' | 'admin' | 'patient';
						}
					} catch (error) {
						console.warn('Error reading role from localStorage:', error);
					}
				}
			}
		}

		return null;
	}, [userRoles, isAuthenticated, pathname, setLoginData]);

	// Debug logging for role detection issues
	if (process.env.NODE_ENV === 'development') {
		console.log('Role Detection Debug:', {
			role,
			pathname,
			isAuthenticated,
			userRoles,
		});
	}

	return role;
};

// Role-aware permissions
export const usePermissions = () => {
	const role = useUserRole();

	return {
		canManageAssistants: role === 'doctor',
		canManageSubscription: role === 'doctor',
		canCreateClinics: role === 'doctor',
		canArchiveData: role === 'doctor',
		canViewSettings: role === 'doctor',
		canManageAppointments: true, // Both roles
		canManagePatients: true, // Both roles
		canViewMedicalDocuments: true, // Both roles
		canViewCalendar: true, // Both roles
	};
};

// Role-aware permissions
export const useRolePermissions = () => {
	const role = useUserRole();

	const permissions = [] as {
		code: string;
		name: string;
		title: string;
	}[];

	return {
		canViewAppointment:
			role !== 'assistant'
				? true
				: permissions.some((item) => item.code === 'VW_PPNTMNT'),
		canAddAppointment: permissions.some((item) => item.code === 'DD_PPNTMNT'),
		canEditAppointment: permissions.some((item) => item.code === 'DT_PPNTMNT'),
		canDeleteAppointment: permissions.some(
			(item) => item.code === 'DLT_PPNTMNT'
		),
		canArchiveAppointment: permissions.some(
			(item) => item.code === 'ACHV_PPNTMNT'
		),
		canRescheduleAppointment: permissions.some(
			(item) => item.code === 'RSCHDL_PPNTMNT'
		),
		canEditChiefComplaint: permissions.some(
			(item) => item.code === 'DT_CHFCMPLNT'
		),
		canEditDiagnosis: permissions.some((item) => item.code === 'DT_DGNSS'),
		canEditPrognosis: permissions.some((item) => item.code === 'DT_PRGNSS'),
		canEditVitalSigns: permissions.some((item) => item.code === 'DT_VTLSGNS'),

		canViewMedicalCertificateAppointment: permissions.some(
			(item) => item.code === 'VW_MDCLCRTFCT_PPNTMNT'
		),
		canAddMedicalCertificateAppointment: permissions.some(
			(item) => item.code === 'DD_MDCLCRTFCT_PPNTMNT'
		),
		canEditMedicalCertificateAppointment: permissions.some(
			(item) => item.code === 'DT_MDCLCRTFCT_PPNTMNT'
		),
		canDeleteMedicalCertificateAppointment: permissions.some(
			(item) => item.code === 'DLT_MDCLCRTFCT_PPNTMNT'
		),
		canArchiveMedicalCertificateAppointment: permissions.some(
			(item) => item.code === 'ACHV_MDCLCRTFCT_PPNTMNT'
		),
		canPrintMedicalCertificateAppointment: permissions.some(
			(item) => item.code === 'PRNT_MDCLCRTFCT_PPNTMNT'
		),

		canViewPrescriptionAppointment: permissions.some(
			(item) => item.code === 'VW_PRSCRPTN_PPNTMNT'
		),
		canAddPrescriptionAppointment: permissions.some(
			(item) => item.code === 'DD_PRSCRPTN_PPNTMNT'
		),
		canEditPrescriptionAppointment: permissions.some(
			(item) => item.code === 'DT_PRSCRPTN_PPNTMNT'
		),
		canDeletePrescriptionAppointment: permissions.some(
			(item) => item.code === 'DLT_PRSCRPTN_PPNTMNT'
		),
		canArchivePrescriptionAppointment: permissions.some(
			(item) => item.code === 'ACHV_PRSCRPTN_PPNTMNT'
		),
		canPrintPrescriptionAppointment: permissions.some(
			(item) => item.code === 'PRNT_PRSCRPTN_PPNTMNT'
		),

		canViewLabRequestAppointment: permissions.some(
			(item) => item.code === 'VW_LBRQST_PPNTMNT'
		),
		canAddLabRequestAppointment: permissions.some(
			(item) => item.code === 'DD_LBRQST_PPNTMNT'
		),
		canEditLabRequestAppointment: permissions.some(
			(item) => item.code === 'DT_LBRQST_PPNTMNT'
		),
		canDeleteLabRequestAppointment: permissions.some(
			(item) => item.code === 'DLT_LBRQST_PPNTMNT'
		),
		canArchiveLabRequestAppointment: permissions.some(
			(item) => item.code === 'ACHV_LBRQST_PPNTMNT'
		),
		canPrintLabRequestAppointment: permissions.some(
			(item) => item.code === 'PRNT_LBRQST_PPNTMNT'
		),

		canViewDiagnosticRequestAppointment: permissions.some(
			(item) => item.code === 'VW_DGNSTCRQST_PPNTMNT'
		),
		canAddDiagnosticRequestAppointment: permissions.some(
			(item) => item.code === 'DD_DGNSTCRQST_PPNTMNT'
		),
		canEditDiagnosticRequestAppointment: permissions.some(
			(item) => item.code === 'DT_DGNSTCRQST_PPNTMNT'
		),
		canDeleteDiagnosticRequestAppointment: permissions.some(
			(item) => item.code === 'DLT_DGNSTCRQST_PPNTMNT'
		),
		canArchiveDiagnosticRequestAppointment: permissions.some(
			(item) => item.code === 'ACHV_DGNSTCRQST_PPNTMNT'
		),
		canPrintDiagnosticRequestAppointment: permissions.some(
			(item) => item.code === 'PRNT_DGNSTCRQST_PPNTMNT'
		),

		canViewLabResultAppointment: permissions.some(
			(item) => item.code === 'VW_LBRSLT_PPNTMNT'
		),
		canAddLabResultAppointment: permissions.some(
			(item) => item.code === 'DD_LBRSLT_PPNTMNT'
		),
		canEditLabResultAppointment: permissions.some(
			(item) => item.code === 'DT_LBRSLT_PPNTMNT'
		),
		canDeleteLabResultAppointment: permissions.some(
			(item) => item.code === 'DLT_LBRSLT_PPNTMNT'
		),
		canArchiveLabResultAppointment: permissions.some(
			(item) => item.code === 'ACHV_LBRSLT_PPNTMNT'
		),
		canDownloadLabResultAppointment: permissions.some(
			(item) => item.code === 'DWNLD_LBRSLT_PPNTMNT'
		),

		canViewReferralAppointment: permissions.some(
			(item) => item.code === 'VW_RFRRL_PPNTMNT'
		),
		canAddReferralAppointment: permissions.some(
			(item) => item.code === 'DD_RFRRL_PPNTMNT'
		),
		canEditReferralAppointment: permissions.some(
			(item) => item.code === 'DT_RFRRL_PPNTMNT'
		),
		canDeleteReferralAppointment: permissions.some(
			(item) => item.code === 'DLT_RFRRL_PPNTMNT'
		),
		canArchiveReferralAppointment: permissions.some(
			(item) => item.code === 'ACHV_RFRRL_PPNTMNT'
		),
		canPrintReferralAppointment: permissions.some(
			(item) => item.code === 'PRNT_RFRRL_PPNTMNT'
		),

		canViewMedicalRecordAppointment: permissions.some(
			(item) => item.code === 'VW_MDCLRCRD_PPNTMNT'
		),
		canAddMedicalRecordAppointment: permissions.some(
			(item) => item.code === 'DD_MDCLRCRD_PPNTMNT'
		),
		canDeleteMedicalRecordAppointment: permissions.some(
			(item) => item.code === 'DLT_MDCLRCRD_PPNTMNT'
		),
		canArchiveMedicalRecordAppointment: permissions.some(
			(item) => item.code === 'ACHV_MDCLRCRD_PPNTMNT'
		),

		canViewCheckupHistory: permissions.some(
			(item) => item.code === 'VW_CHCKPHSTR'
		),

		canViewMedicalDocument: permissions.some(
			(item) => item.code === 'VW_MDCLDCMNT'
		),

		canViewPrescription: permissions.some(
			(item) => item.code === 'VW_PRSCRPTN'
		),
		canAddPrescription: permissions.some((item) => item.code === 'DD_PRSCRPTN'),
		canEditPrescription: permissions.some(
			(item) => item.code === 'DT_PRSCRPTN'
		),
		canDeletePrescription: permissions.some(
			(item) => item.code === 'DLT_PRSCRPTN'
		),
		canArchivePrescription: permissions.some(
			(item) => item.code === 'ACHV_PRSCRPTN'
		),
		canPrintPrescription: permissions.some(
			(item) => item.code === 'PRNT_PRSCRPTN'
		),

		canViewLabRequest: permissions.some((item) => item.code === 'VW_LBRQST'),
		canAddLabRequest: permissions.some((item) => item.code === 'DD_LBRQST'),
		canEditLabRequest: permissions.some((item) => item.code === 'DT_LBRQST'),
		canDeleteLabRequest: permissions.some((item) => item.code === 'DLT_LBRQST'),
		canArchiveLabRequest: permissions.some(
			(item) => item.code === 'ACHV_LBRQST'
		),
		canPrintLabRequest: permissions.some((item) => item.code === 'PRNT_LBRQST'),

		canViewDiagnosticRequest: permissions.some(
			(item) => item.code === 'VW_DGNSTCRQST'
		),
		canAddDiagnosticRequest: permissions.some(
			(item) => item.code === 'DD_DGNSTCRQST'
		),
		canEditDiagnosticRequest: permissions.some(
			(item) => item.code === 'DT_DGNSTCRQST'
		),
		canDeleteDiagnosticRequest: permissions.some(
			(item) => item.code === 'DLT_DGNSTCRQST'
		),
		canArchiveDiagnosticRequest: permissions.some(
			(item) => item.code === 'ACHV_DGNSTCRQST'
		),
		canPrintDiagnosticRequest: permissions.some(
			(item) => item.code === 'PRNT_DGNSTCRQST'
		),

		canViewReferral: permissions.some((item) => item.code === 'VW_RFRRL'),
		canAddReferral: permissions.some((item) => item.code === 'DD_RFRRL'),
		canEditReferral: permissions.some((item) => item.code === 'DT_RFRRL'),
		canDeleteReferral: permissions.some((item) => item.code === 'DLT_RFRRL'),
		canArchiveReferral: permissions.some((item) => item.code === 'ACHV_RFRRL'),
		canPrintReferral: permissions.some((item) => item.code === 'PRNT_RFRRL'),

		canViewMedicalCertificate: permissions.some(
			(item) => item.code === 'VW_MDCLCRTFCT'
		),
		canAddMedicalCertificate: permissions.some(
			(item) => item.code === 'DD_MDCLCRTFCT'
		),
		canEditMedicalCertificate: permissions.some(
			(item) => item.code === 'DT_MDCLCRTFCT'
		),
		canDeleteMedicalCertificate: permissions.some(
			(item) => item.code === 'DLT_MDCLCRTFCT'
		),
		canArchiveMedicalCertificate: permissions.some(
			(item) => item.code === 'ACHV_MDCLCRTFCT'
		),
		canPrintMedicalCertificate: permissions.some(
			(item) => item.code === 'PRNT_MDCLCRTFCT'
		),

		canViewHistory: permissions.some((item) => item.code === 'VW_HSTRY'),

		canViewPrescriptionHistory: permissions.some(
			(item) => item.code === 'VW_PRSCRPTN_HSTRY'
		),
		canPrintPrescriptionHistory: permissions.some(
			(item) => item.code === 'PRNT_PRSCRPTN_HSTRY'
		),

		canViewLabRequestHistory: permissions.some(
			(item) => item.code === 'VW_LBRQST_HSTRY'
		),
		canPrintLabRequestHistory: permissions.some(
			(item) => item.code === 'PRNT_LBRQST_HSTRY'
		),

		canViewLabResultHistory: permissions.some(
			(item) => item.code === 'VW_LBRSLT_HSTRY'
		),
		canPrintLabResultHistory: permissions.some(
			(item) => item.code === 'PRNT_LBRSLT_HSTRY'
		),

		canViewMedicalCertificateHistory: permissions.some(
			(item) => item.code === 'VW_MDCLCRTFCT_HSTRY'
		),
		canPrintMedicalCertificateHistory: permissions.some(
			(item) => item.code === 'PRNT_MDCLCRTFCT_HSTRY'
		),

		canViewReferralHistory: permissions.some(
			(item) => item.code === 'VW_RFRRL_HSTRY'
		),
		canPrintReferralHistory: permissions.some(
			(item) => item.code === 'PRNT_RFRRL_HSTRY'
		),

		canViewCalendar: permissions.some((item) => item.code === 'VW_CLNDR'),
		canViewArchive: permissions.some((item) => item.code === 'VW_ACHV'),

		canViewPatient: permissions.some((item) => item.code === 'VW_PTNT'),
		canAddPatient: permissions.some((item) => item.code === 'DD_PTNT'),
		canEditPatient: permissions.some((item) => item.code === 'DT_PTNT'),
		canEditPatientName: permissions.some((item) => item.code === 'DT_PTNTNM'),
		canDeletePatient: permissions.some((item) => item.code === 'DLT_PTNT'),
		canArchivePatient: permissions.some((item) => item.code === 'ACHV_PTNT'),

		canAddHabit: permissions.some((item) => item.code === 'DD_HBT'),
		canEditHabit: permissions.some((item) => item.code === 'DT_HBT'),
		canDeleteHabit: permissions.some((item) => item.code === 'DLT_HBT'),
		canArchiveHabit: permissions.some((item) => item.code === 'ACHV_HBT'),

		canAddHistory: permissions.some((item) => item.code === 'DD_HSTRY'),
		canEditHistory: permissions.some((item) => item.code === 'DT_HSTRY'),
		canDeleteHistory: permissions.some((item) => item.code === 'DLT_HSTRY'),
		canArchiveHistory: permissions.some((item) => item.code === 'ACHV_HSTRY'),

		canAddAllergy: permissions.some((item) => item.code === 'DD_LLRGY'),
		canEditAllergy: permissions.some((item) => item.code === 'DT_LLRGY'),
		canDeleteAllergy: permissions.some((item) => item.code === 'DLT_LLRGY'),
		canArchiveAllergy: permissions.some((item) => item.code === 'ACHV_LLRGY'),

		canAddIllness: permissions.some((item) => item.code === 'DD_LLNSS'),
		canEditIllness: permissions.some((item) => item.code === 'DT_LLNSS'),
		canDeleteIllness: permissions.some((item) => item.code === 'DLT_LLNSS'),
		canArchiveIllness: permissions.some((item) => item.code === 'ACHV_LLNSS'),

		canAddSurgery: permissions.some((item) => item.code === 'DD_SRGRY'),
		canEditSurgery: permissions.some((item) => item.code === 'DT_SRGRY'),
		canDeleteSurgery: permissions.some((item) => item.code === 'DLT_SRGRY'),
		canArchiveSurgery: permissions.some((item) => item.code === 'ACHV_SRGRY'),

		canAddDiet: permissions.some((item) => item.code === 'DD_DT'),
		canEditDiet: permissions.some((item) => item.code === 'DT_DT'),
		canDeleteDiet: permissions.some((item) => item.code === 'DLT_DT'),
		canArchiveDiet: permissions.some((item) => item.code === 'ACHV_DT'),

		canAddHmo: permissions.some((item) => item.code === 'DD_HM'),
		canEditHmo: permissions.some((item) => item.code === 'DT_HM'),
		canDeleteHmo: permissions.some((item) => item.code === 'DLT_HM'),
		canArchiveHmo: permissions.some((item) => item.code === 'ACHV_HM'),

		canAddFamily: permissions.some((item) => item.code === 'DD_FMLY'),
		canDeleteFamily: permissions.some((item) => item.code === 'DLT_FMLY'),
		canArchiveFamily: permissions.some((item) => item.code === 'ACHV_FMLY'),

		canViewClinic: permissions.some((item) => item.code === 'VW_CLNC'),
		canAddClinic: permissions.some((item) => item.code === 'DD_CLNC'),
		canEditClinic: permissions.some((item) => item.code === 'DT_CLNC'),
		canDeleteClinic: permissions.some((item) => item.code === 'DLT_CLNC'),
		canArchiveClinic: permissions.some((item) => item.code === 'ACHV_CLNC'),

		canViewAssistant: permissions.some((item) => item.code === 'VW_SSSTNT'),
		canAddAssistant: permissions.some((item) => item.code === 'DD_SSSTNT'),
		canAssignClinicAssistant: permissions.some(
			(item) => item.code === 'SSGN_CLNC'
		),
		canEditAssistant: permissions.some((item) => item.code === 'DT_SSSTNT'),
		canDeleteAssistant: permissions.some((item) => item.code === 'DLT_SSSTNT'),
		canArchiveAssistant: permissions.some(
			(item) => item.code === 'ACHV_SSSTNT'
		),

		canViewSubscription: permissions.some(
			(item) => item.code === 'VW_SBSCRPTN'
		),
		canViewSetting: permissions.some((item) => item.code === 'VW_STTNG'),
	};
};

// Role-aware navigation and permissions
export const useNavigation = () => {
	const role = useUserRole();

	const getBasePath = () => {
		return '/dashboard'; // Both roles use the same dashboard path
	};

	const getAvailableTabs = () => {
		const commonTabs = [
			'overview',
			'appointments',
			'calendar',
			'patients',
			'clinics',
			'medical-documents',
			'history',
			'archive',
		];

		const doctorOnlyTabs = [
			'assistants',
			'subscription',
			'settings',
			'profile',
		];

		return role === 'doctor' ? [...commonTabs, ...doctorOnlyTabs] : commonTabs;
	};

	const canAccessTab = (tab: string) => {
		return getAvailableTabs().includes(tab);
	};

	return {
		role,
		basePath: getBasePath(),
		availableTabs: getAvailableTabs(),
		canAccessTab,
	};
};
