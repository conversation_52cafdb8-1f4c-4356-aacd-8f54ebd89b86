'use client';

import { useMutation } from '@tanstack/react-query';
import { toast } from 'sonner';

import { IRegisterDoctor } from '@/core/api/registration/doctor.type';
import { catchError } from '@/core/lib/utils';
import { TUserRole } from '@/core/types/role.type';

import { createDoctor, disableDoctor, enableDoctor } from '../api/doctors.api';

export const useDoctorActions = (userRole: TUserRole = 'admin') => {
	const { mutateAsync: disableAction, isPending: isDisabling } = useMutation({
		mutationFn: (profileId: number) => disableDoctor(profileId, userRole),
		onSuccess: () => {
			toast.success('Doctor Disabled', {
				description: 'Doctor has been disabled successfully',
			});
		},
		onError: (error) => {
			catchError(error, 'Disable Doctor Error', 'Failed to disable doctor');
		},
	});

	const { mutateAsync: enableAction, isPending: isEnabling } = useMutation({
		mutationFn: (profileId: number) => enableDoctor(profileId, userRole),
		onSuccess: () => {
			toast.success('Doctor Enabled', {
				description: 'Doctor has been enabled successfully',
			});
		},
		onError: (error) => {
			catchError(error, 'Enable Doctor Error', 'Failed to enable doctor');
		},
	});

	const { mutateAsync: createAction, isPending: isCreating } = useMutation({
		mutationFn: (doctorData: IRegisterDoctor) =>
			createDoctor(doctorData, userRole),
		onSuccess: () => {
			toast.success('Doctor Created', {
				description: 'Doctor has been created successfully',
			});
		},
		onError: (error) => {
			catchError(error, 'Create Doctor Error', 'Failed to create doctor');
		},
	});

	return {
		disableAction,
		enableAction,
		createAction,
		isDisabling,
		isEnabling,
		isCreating,
	};
};
