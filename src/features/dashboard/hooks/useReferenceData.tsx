'use client';

import { useQuery } from '@tanstack/react-query';

import {
	DATA_QUERY_KEYS,
	getAppointmentTypes,
	getConsultationTypes,
	getDiagnosticRequestTypes,
	getHmos,
	getLabRequestTypes,
	getMedicalDescriptions,
	getPaymentTypes,
	getPrescriptionTypes,
	getVisitReasons,
} from '@/core/api/data';

// Medical Descriptions Hook
export const useMedicalDescriptions = () => {
	return useQuery({
		queryKey: DATA_QUERY_KEYS.MEDICAL_DESCRIPTIONS,
		queryFn: getMedicalDescriptions,
		staleTime: 10 * 60 * 1000, // 10 minutes - reference data doesn't change often
	});
};

// Prescription Types Hook
export const usePrescriptionTypes = () => {
	return useQuery({
		queryKey: DATA_QUERY_KEYS.PRESCRIPTION_TYPES,
		queryFn: getPrescriptionTypes,
		staleTime: 10 * 60 * 1000, // 10 minutes - reference data doesn't change often
	});
};

// Lab Request Types Hook
export const useLabRequestTypes = () => {
	return useQuery({
		queryKey: DATA_QUERY_KEYS.LAB_REQUEST_TYPES,
		queryFn: getLabRequestTypes,
		staleTime: 10 * 60 * 1000, // 10 minutes - reference data doesn't change often
	});
};

export const useDiagnosticRequestTypes = () => {
	return useQuery({
		queryKey: DATA_QUERY_KEYS.DIAGNOSTIC_REQUEST_TYPES,
		queryFn: getDiagnosticRequestTypes,
		staleTime: 10 * 60 * 1000, // 10 minutes - reference data doesn't change often
	});
};

// Visit Reasons Hook
export const useVisitReasons = () => {
	return useQuery({
		queryKey: DATA_QUERY_KEYS.VISIT_REASONS,
		queryFn: getVisitReasons,
		staleTime: 10 * 60 * 1000, // 10 minutes - reference data doesn't change often
	});
};

// Consultation Types Hook
export const useConsultationTypes = () => {
	return useQuery({
		queryKey: DATA_QUERY_KEYS.CONSULTATION_TYPES,
		queryFn: getConsultationTypes,
		staleTime: 10 * 60 * 1000, // 10 minutes
	});
};

// Appointment Types Hook
export const useAppointmentTypes = () => {
	return useQuery({
		queryKey: DATA_QUERY_KEYS.APPOINTMENT_TYPES,
		queryFn: getAppointmentTypes,
		staleTime: 10 * 60 * 1000, // 10 minutes
	});
};

// Payment Types Hook
export const usePaymentTypes = () => {
	return useQuery({
		queryKey: DATA_QUERY_KEYS.PAYMENT_TYPES,
		queryFn: getPaymentTypes,
		staleTime: 10 * 60 * 1000, // 10 minutes
	});
};

// HMOs Hook
export const useHmos = () => {
	return useQuery({
		queryKey: DATA_QUERY_KEYS.HMOS,
		queryFn: getHmos,
		staleTime: 10 * 60 * 1000, // 10 minutes
	});
};
