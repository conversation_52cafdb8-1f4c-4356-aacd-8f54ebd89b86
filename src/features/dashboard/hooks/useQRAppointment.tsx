'use client';

import { useMutation } from '@tanstack/react-query';
import axios from 'axios';

import { useSession } from '@/core/hooks/useSession';

// Types
interface IQRCodeVerificationResponse {
	success: boolean;
	message: string;
	data?: {
		patient: string;
	};
}

// API Functions
const verifyQRCodeApi = async (
	qrCode: string,
	token: string
): Promise<IQRCodeVerificationResponse> => {
	const response = await axios.get(`/api/qrcode/verify-qrcode?code=${qrCode}`, {
		headers: {
			Authorization: `Bearer ${token}`,
		},
	});
	return response.data;
};

// Hooks
export const useQRCodeVerification = () => {
	const { token } = useSession();

	return useMutation({
		mutationFn: (qrCode: string) => verifyQRCodeApi(qrCode, token || ''),
		onError: (error: unknown) => {
			console.error('QR Code verification failed:', error);
		},
	});
};
