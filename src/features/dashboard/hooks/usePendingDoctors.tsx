'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { useMutation, useQuery } from '@tanstack/react-query';
import { toast } from 'sonner';

import { catchError } from '@/core/lib/utils';
import { TUserRole } from '@/core/types/role.type';

import {
	acceptDoctor,
	getPendingDoctors,
	rejectDoctor,
} from '../api/doctor-pending.api';

// State
const initialState = {
	page: 1,
	pageSize: 10,
};
export const pendingDoctorsState = hookstate(initialState);

export const usePendingDoctors = (role: TUserRole = 'admin') => {
	const state = useHookstate(pendingDoctorsState);

	const pendingDoctorsQuery = useQuery({
		queryKey: [
			role,
			'doctors',
			'pending',
			state.page.value,
			state.pageSize.value,
		],
		queryFn: () =>
			getPendingDoctors(state.page.value, state.pageSize.value, role),
		staleTime: 1000 * 60 * 10, // 10 mins
	});

	const { mutateAsync: acceptAction, isPending: isAccepting } = useMutation({
		mutationFn: acceptDoctor,
		onSuccess: () => {
			pendingDoctorsQuery.refetch();
			toast.success('Accept Application', {
				description: 'Doctor application accepted successfully',
			});
		},
		onError: (error) => {
			catchError(error, 'Accept Doctor Error', 'Accept Application Failed');
		},
	});

	const { mutateAsync: rejectAction, isPending: isRejecting } = useMutation({
		mutationFn: rejectDoctor,
		onSuccess: () => {
			pendingDoctorsQuery.refetch();
			toast.success('Decline Application', {
				description: 'Doctor application declined successfully',
			});
		},
		onError: (error) => {
			catchError(error, 'Decline Doctor Error', 'Decline Application Failed');
		},
	});

	return {
		state,
		pendingDoctors: pendingDoctorsQuery.data,
		isSuccess: pendingDoctorsQuery.isSuccess,
		isLoading:
			pendingDoctorsQuery.isLoading ||
			pendingDoctorsQuery.isFetching ||
			pendingDoctorsQuery.isRefetching,
		refetch: pendingDoctorsQuery.refetch,
		error: pendingDoctorsQuery.error,
		acceptAction,
		rejectAction,
		isAccepting,
		isRejecting,
	};
};
