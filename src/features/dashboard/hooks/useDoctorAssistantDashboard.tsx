'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import { DATA_QUERY_KEYS } from '@/core/api/data';
import {
	addAllergyToPatient,
	addDietToPatient,
	addDoctorNote,
	addFamilyToPatient,
	addHabitToPatient,
	addHistoryToPatient,
	addHmoToPatient,
	addIllnessToPatient,
	addMedicalRecord,
	addSurgeryToPatient,
	archiveAssistant,
	archiveClinic,
	archivePatient,
	assignAssistant,
	createAppointment,
	createAssistant,
	createClinic,
	createDiagnosticRequest,
	createDiagnosticRequestItem,
	createDiagnosticRequestType,
	createLabRequest,
	createLabRequestItem,
	createLabRequestType,
	createMedicalCertificate,
	createMedicalCertificateItem,
	createMedicalDescription,
	createPatient,
	createPrescription,
	createPrescriptionItem,
	createPrescriptionType,
	createReferral,
	createStandaloneDiagnosticRequest,
	createStandaloneLabRequest,
	createStandaloneMedicalCertificate,
	createStandalonePrescription,
	createStandaloneReferral,
	disableAssistant,
	disableClinic,
	disablePatient,
	DOCTOR_QUERY_KEYS,
	enableAssistant,
	enableClinic,
	enablePatient,
	getAllDiagnosticRequests,
	getAllLabRequests,
	getAllMedicalCertificates,
	getAllPrescriptions,
	getAllReferrals,
	getAllSubscriptions,
	getAppointmentById,
	getAppointmentCalendar,
	getAppointmentHistories,
	getAppointmentsByDateRange,
	getAssistantById,
	getAssistantClinics,
	getClinicAssistants,
	getClinicById,
	getClinicPatients,
	getDiagnosticRequestById,
	getDoctorAppointments,
	getDoctorAssistants,
	getDoctorClinics,
	getDoctorDashboardMetrics,
	getDoctorPatients,
	getLabRequestById,
	getMedicalCertificateById,
	getPatientAppointmentHistory,
	getPatientById,
	getPrescriptionById,
	getReferralById,
	getTodayAppointments,
	getTodaysAttendedAppointments,
	getTodaysQueueAppointments,
	removeAllergyFromPatient,
	removeAssistant,
	removeDiagnosticRequest,
	removeDiagnosticRequestItem,
	removeDietFromPatient,
	removeFamilyFromPatient,
	removeHabitFromPatient,
	removeHistoryFromPatient,
	removeHmoFromPatient,
	removeIllnessFromPatient,
	removeLabRequest,
	removeLabRequestItem,
	removeMedicalCertificate,
	removeMedicalCertificateItem,
	removeMedicalRecord,
	removePrescription,
	removePrescriptionItem,
	removeReferral,
	removeStandaloneDiagnosticRequest,
	removeStandaloneLabRequest,
	removeSurgeryFromPatient,
	rescheduleAppointment,
	subscribe,
	updateAppointment,
	updateAppointmentDetails,
	updateAppointmentPtr,
	updateAppointmentStatus,
	updateAssistant,
	updateAssistantCredentials,
	updateClinic,
	updateDiagnosticRequestItem,
	updateDiagnosticRequestPtr,
	updateLabRequestItem,
	updateLabRequestPtr,
	updateMedicalCertificateItem,
	updateMedicalCertificatePtr,
	updatePatient,
	updatePatientAllergy,
	updatePatientDiet,
	updatePatientHabit,
	updatePatientHistory,
	updatePatientHmo,
	updatePatientIllness,
	updatePatientSurgery,
	updatePrescriptionItem,
	updatePrescriptionPtr,
	updateReferral,
	updateReferralPtr,
	updateVitalSigns,
	uploadLabResult,
} from '@/core/api/doctor-assistant';
import { catchError } from '@/core/lib/utils';
import {
	IAddAllergyRequest,
	IAddDietRequest,
	IAddFamilyRequest,
	IAddHistoryRequest,
	IAddHmoRequest,
	IAddIllnessRequest,
	IAddSurgeryRequest,
	IAppointmentParams,
	IAssignAssistantRequest,
	IClinicParams,
	ICreateAssistantRequest,
	ICreateReferralRequest,
	ICreateStandaloneDiagnosticRequestRequest,
	ICreateStandaloneLabRequestRequest,
	ICreateStandaloneMedicalCertificateRequest,
	ICreateStandalonePrescriptionRequest,
	ICreateStandaloneReferralRequest,
	IDiagnosticRequestListParams,
	ILabRequestListParams,
	IMedicalCertificateListParams,
	IPaginationParams,
	IPrescriptionListParams,
	IReferralListParams,
	ISearchParams,
	ISubscribe,
	ISubscriptionListParams,
	ISubscriptionsResponse,
	IUpdateAllergyRequest,
	IUpdateAssistantCredentialsRequest,
	IUpdateAssistantRequest,
	IUpdateDietRequest,
	IUpdateHabitRequest,
	IUpdateHistoryRequest,
	IUpdateHmoRequest,
	IUpdateIllnessRequest,
	IUpdateSurgeryRequest,
} from '@/features/dashboard/types/doctor.types';

export const useUpdatePrescriptionPtr = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			prescriptionId,
			data,
		}: {
			prescriptionId: number;
			data: {
				professionalTaxReceipt: string;
			};
		}) => updatePrescriptionPtr(prescriptionId, data),
		onSuccess: () => {
			toast.success('PTR Updated', {
				description:
					'The professional tax receipt has been successfully updated.',
			});

			// Invalidate all prescription-related queries
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'prescriptions'],
			});
		},
		onError: (error) => {
			catchError(error, 'Update PTR Error', 'Failed to update PTR');
		},
	});
};

export const useUpdateLabRequestPtr = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			labRequestId,
			data,
		}: {
			labRequestId: number;
			data: {
				professionalTaxReceipt: string;
			};
		}) => updateLabRequestPtr(labRequestId, data),
		onSuccess: () => {
			toast.success('PTR Updated', {
				description:
					'The professional tax receipt has been successfully updated.',
			});

			// Invalidate all lab request-related queries
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'lab-requests'],
			});
		},
		onError: (error) => {
			catchError(error, 'Update PTR Error', 'Failed to update PTR');
		},
	});
};

export const useUpdateDiagnosticRequestPtr = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			diagnosticRequestId,
			data,
		}: {
			diagnosticRequestId: number;
			data: {
				professionalTaxReceipt: string;
			};
		}) => updateDiagnosticRequestPtr(diagnosticRequestId, data),
		onSuccess: () => {
			toast.success('PTR Updated', {
				description:
					'The professional tax receipt has been successfully updated.',
			});

			// Invalidate all diagnostic request-related queries
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'diagnostic-requests'],
			});
		},
		onError: (error) => {
			catchError(error, 'Update PTR Error', 'Failed to update PTR');
		},
	});
};

export const useUpdateMedicalCertificatePtr = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			medicalCertificateId,
			data,
		}: {
			medicalCertificateId: number;
			data: {
				professionalTaxReceipt: string;
			};
		}) => updateMedicalCertificatePtr(medicalCertificateId, data),
		onSuccess: () => {
			toast.success('PTR Updated', {
				description:
					'The professional tax receipt has been successfully updated.',
			});

			// Invalidate all medical certificate-related queries
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'medical-certificates'],
			});
		},
		onError: (error) => {
			catchError(error, 'Update PTR Error', 'Failed to update PTR');
		},
	});
};

export const useUpdateReferralPtr = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			referralId,
			data,
		}: {
			referralId: number;
			data: {
				professionalTaxReceipt: string;
			};
		}) => updateReferralPtr(referralId, data),
		onSuccess: () => {
			toast.success('PTR Updated', {
				description:
					'The professional tax receipt has been successfully updated.',
			});

			// Invalidate all referral-related queries
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'referrals'],
			});
		},
		onError: (error) => {
			catchError(error, 'Update PTR Error', 'Failed to update PTR');
		},
	});
};

export const useCreateDiagnosticRequestType = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createDiagnosticRequestType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: DATA_QUERY_KEYS.DIAGNOSTIC_REQUEST_TYPES,
			});
			toast.success('Diagnostic Request Type Created', {
				description: 'Diagnostic request type has been created successfully.',
			});
		},
		onError: (error) =>
			catchError(
				error,
				'Create Error',
				'Failed to create diagnostic request type'
			),
	});
};

export const useCreateMedicalDescription = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createMedicalDescription,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: DATA_QUERY_KEYS.MEDICAL_DESCRIPTIONS,
			});
			toast.success('Medical Description Created', {
				description: 'Medical Description has been created successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Create Error', 'Failed to create medical description'),
	});
};

export const useCreateLabRequestType = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createLabRequestType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: DATA_QUERY_KEYS.LAB_REQUEST_TYPES,
			});
			toast.success('Lab Request Type Created', {
				description: 'Lab Request type has been created successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Create Error', 'Failed to create lab request type'),
	});
};

export const useCreatePrescriptionType = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createPrescriptionType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: DATA_QUERY_KEYS.PRESCRIPTION_TYPES,
			});
			toast.success('Prescription Type Created', {
				description: 'Prescription type has been created successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Create Error', 'Failed to create prescription type'),
	});
};

// Dashboard Metrics Hook
export const useDashboardMetrics = (options?: { enabled?: boolean }) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.DASHBOARD.METRICS,
		queryFn: getDoctorDashboardMetrics,
		staleTime: 5 * 60 * 1000, // 5 minutes
		enabled: options?.enabled ?? true, // Default to enabled if not specified
	});
};

// Today's Queue Appointments Hook
export const useTodaysQueueAppointments = (
	params: IAppointmentParams = {},
	options?: { enabled?: boolean }
) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.DASHBOARD.TODAYS_QUEUE(params),
		queryFn: () => getTodaysQueueAppointments(params),
		staleTime: 1 * 60 * 1000, // 1 minute
		enabled: options?.enabled ?? true,
	});
};

// Today's Attended Appointments Hook
export const useTodaysAttendedAppointments = (
	params: IAppointmentParams = {},
	options?: { enabled?: boolean }
) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.DASHBOARD.TODAYS_ATTENDED(params),
		queryFn: () => getTodaysAttendedAppointments(params),
		staleTime: 2 * 60 * 1000, // 2 minutes
		enabled: options?.enabled ?? true,
	});
};

// Clinic Hooks
export const useClinics = (
	params: IClinicParams = {},
	options?: { enabled?: boolean }
) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.CLINICS.ALL(params),
		queryFn: () => getDoctorClinics(params),
		staleTime: 5 * 60 * 1000, // 5 minutes
		enabled: options?.enabled ?? true,
	});
};

export const useClinicDetail = (clinicId: number) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.CLINICS.DETAIL(clinicId),
		queryFn: () => getClinicById(clinicId),
		enabled: !!clinicId,
		staleTime: 10 * 60 * 1000, // 10 minutes
	});
};

// Patient Hooks
export const usePatients = (
	params: ISearchParams = {},
	options?: { enabled?: boolean }
) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.PATIENTS.ALL(params),
		queryFn: () => getDoctorPatients(params),
		staleTime: 5 * 60 * 1000, // 5 minutes
		enabled: options?.enabled ?? true,
	});
};

export const useClinicPatients = (
	clinicId: number,
	params: ISearchParams = {}
) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.CLINICS.PATIENTS(clinicId, params),
		queryFn: () => getClinicPatients(clinicId, params),
		enabled: !!clinicId,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const usePatientDetail = (profileId: number) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
		queryFn: () => getPatientById(profileId),
		enabled: !!profileId,
		staleTime: 10 * 60 * 1000, // 10 minutes
	});
};

export const usePatientAppointmentHistory = (profileId: number) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.PATIENTS.APPOINTMENT_HISTORY(profileId),
		queryFn: () => getPatientAppointmentHistory(profileId),
		enabled: !!profileId,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

// Appointment Hooks
export const useAppointments = (
	params: IAppointmentParams = {},
	options?: { enabled?: boolean }
) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.ALL(params),
		queryFn: () => getDoctorAppointments(params),
		staleTime: 2 * 60 * 1000, // 2 minutes
		enabled: options?.enabled ?? true,
	});
};

export const useTodayAppointments = (params: IAppointmentParams = {}) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.TODAY(params),
		queryFn: () => getTodayAppointments(params),
		staleTime: 1 * 60 * 1000, // 1 minute
	});
};

export const useAppointmentsByDateRange = (
	startDate: string,
	endDate: string,
	params: Omit<IAppointmentParams, 'date'> = {}
) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DATE_RANGE(
			startDate,
			endDate,
			params
		),
		queryFn: () => getAppointmentsByDateRange(startDate, endDate, params),
		enabled: !!startDate && !!endDate,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useAppointmentDetail = (
	appointmentId: number,
	options?: { enabled?: boolean }
) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(appointmentId),
		queryFn: () => getAppointmentById(appointmentId),
		enabled: !!appointmentId && options?.enabled !== false,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useAppointmentCalendar = (
	params: {
		clinicId?: number;
		status?: string;
		fromDate?: string;
		toDate?: string;
	} = {}
) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.CALENDAR(params),
		queryFn: () => getAppointmentCalendar(params),
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useAppointmentHistories = (
	params: {
		page?: number;
		pageSize?: number;
		search?: string;
		category?: string;
		clinicId?: number;
		status?: string;
		fromDate?: string;
		toDate?: string;
	} = {},
	options?: { enabled?: boolean }
) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.HISTORIES(params),
		queryFn: () => getAppointmentHistories(params),
		staleTime: 5 * 60 * 1000, // 5 minutes
		enabled: options?.enabled ?? true,
	});
};

// Assistant Hooks
export const useAssistants = (params: IPaginationParams = {}) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.ASSISTANTS.ALL(params),
		queryFn: () => getDoctorAssistants(params),
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useClinicAssistants = (
	clinicId: number,
	params: ISearchParams = {}
) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.CLINICS.ASSISTANTS(clinicId, params),
		queryFn: () => getClinicAssistants(clinicId, params),
		enabled: !!clinicId,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useAssistantDetail = (assistantProfileId: number) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.ASSISTANTS.DETAIL(assistantProfileId),
		queryFn: () => getAssistantById(assistantProfileId),
		enabled: !!assistantProfileId,
		staleTime: 10 * 60 * 1000, // 10 minutes
	});
};

export const useAssistantClinics = (assistantProfileId: number) => {
	return useQuery({
		queryKey: ['doctor', 'assistants', 'clinics', assistantProfileId],
		queryFn: () => getAssistantClinics(assistantProfileId),
		enabled: !!assistantProfileId,
		staleTime: 10 * 60 * 1000, // 10 minutes
	});
};

// Mutation Hooks
export const useCreateClinic = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createClinic,
		onSuccess: () => {
			toast.success('Clinic Created', {
				description: 'The clinic has been successfully created.',
			});

			// Invalidate and refetch clinics
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'clinics'],
			});

			// Invalidate dashboard metrics
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.DASHBOARD.METRICS,
			});
		},
		onError: (error) => {
			catchError(error, 'Create Clinic Error', 'Failed to create clinic');
		},
	});
};

export const useUpdateClinic = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: updateClinic,
		onSuccess: () => {
			toast.success('Clinic Updated', {
				description: 'The clinic has been successfully updated.',
			});

			// Invalidate and refetch clinics
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'clinics'],
			});
		},
		onError: (error) => {
			catchError(error, 'Update Clinic Error', 'Failed to update clinic');
		},
	});
};

export const useEnableClinic = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: enableClinic,
		onSuccess: () => {
			toast.success('Clinic Enabled', {
				description: 'The clinic has been successfully enabled.',
			});

			// Invalidate and refetch clinics
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'clinics'],
			});

			// Invalidate dashboard metrics
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.DASHBOARD.METRICS,
			});
		},
		onError: (error) => {
			catchError(error, 'Enable Clinic Error', 'Failed to enable clinic');
		},
	});
};

export const useDisableClinic = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: disableClinic,
		onSuccess: () => {
			toast.success('Clinic Disabled', {
				description: 'The clinic has been successfully disabled.',
			});

			// Invalidate and refetch clinics
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'clinics'],
			});

			// Invalidate dashboard metrics
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.DASHBOARD.METRICS,
			});
		},
		onError: (error) => {
			catchError(error, 'Disable Clinic Error', 'Failed to disable clinic');
		},
	});
};

export const useArchiveClinic = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: archiveClinic,
		onSuccess: () => {
			toast.success('Clinic Archived', {
				description: 'The clinic has been successfully archived.',
			});

			// Invalidate and refetch clinics
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'clinics'],
			});

			// Invalidate dashboard metrics
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.DASHBOARD.METRICS,
			});
		},
		onError: (error) => {
			catchError(error, 'Archive Clinic Error', 'Failed to archive clinic');
		},
	});
};

export const useCreatePatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createPatient,
		onSuccess: () => {
			toast.success('Patient Created', {
				description: 'The patient has been successfully created.',
			});

			// Invalidate and refetch patients
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'patients'],
			});

			// Invalidate dashboard metrics
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.DASHBOARD.METRICS,
			});
		},
		onError: (error) => {
			catchError(error, 'Create Patient Error', 'Failed to create patient');
		},
	});
};

export const useUpdatePatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: updatePatient,
		onSuccess: () => {
			toast.success('Patient Updated', {
				description: 'The patient has been successfully updated.',
			});

			// Invalidate and refetch patients
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'patients'],
			});
		},
		onError: (error) => {
			catchError(error, 'Update Patient Error', 'Failed to update patient');
		},
	});
};

export const useEnablePatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: enablePatient,
		onSuccess: () => {
			toast.success('Patient Enabled', {
				description: 'The patient has been successfully enabled.',
			});

			// Invalidate and refetch patients
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'patients'],
			});

			// Invalidate dashboard metrics
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.DASHBOARD.METRICS,
			});
		},
		onError: (error) => {
			catchError(error, 'Enable Patient Error', 'Failed to enable patient');
		},
	});
};

export const useDisablePatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: disablePatient,
		onSuccess: () => {
			toast.success('Patient Disabled', {
				description: 'The patient has been successfully disabled.',
			});

			// Invalidate and refetch patients
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'patients'],
			});

			// Invalidate dashboard metrics
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.DASHBOARD.METRICS,
			});
		},
		onError: (error) => {
			catchError(error, 'Disable Patient Error', 'Failed to disable patient');
		},
	});
};

export const useArchivePatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: archivePatient,
		onSuccess: () => {
			toast.success('Patient Archived', {
				description: 'The patient has been successfully archived.',
			});

			// Invalidate and refetch patients
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'patients'],
			});

			// Invalidate dashboard metrics
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.DASHBOARD.METRICS,
			});
		},
		onError: (error) => {
			catchError(error, 'Archive Patient Error', 'Failed to archive patient');
		},
	});
};

// Patient Profile Management Hooks

// Habits Management Hooks
export const useAddHabitToPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			habitData,
		}: {
			profileId: number;
			habitData: {
				name: string;
				description?: string;
			};
		}) => addHabitToPatient(profileId, habitData),
		onSuccess: (_, { profileId }) => {
			toast.success('Habit Added', {
				description: 'The habit has been successfully added to the patient.',
			});

			// Invalidate patient detail and habits
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.HABITS(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Add Habit Error', 'Failed to add habit');
		},
	});
};

export const useUpdatePatientHabit = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientHabitId,
			habitData,
		}: {
			profileId: number;
			patientHabitId: number;
			habitData: IUpdateHabitRequest;
		}) => updatePatientHabit(profileId, patientHabitId, habitData),
		onSuccess: (_, { profileId }) => {
			toast.success('Habit Updated', {
				description: 'The habit has been successfully updated.',
			});

			// Invalidate patient detail and habits
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.HABITS(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Update Habit Error', 'Failed to update habit');
		},
	});
};

export const useRemoveHabitFromPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientHabitId,
		}: {
			profileId: number;
			patientHabitId: number;
		}) => removeHabitFromPatient(profileId, patientHabitId),
		onSuccess: (_, { profileId }) => {
			toast.success('Habit Removed', {
				description:
					'The habit has been successfully removed from the patient.',
			});

			// Invalidate patient detail and habits
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.HABITS(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Habit Error', 'Failed to remove habit');
		},
	});
};

// History Management Hooks
export const useAddHistoryToPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			historyData,
		}: {
			profileId: number;
			historyData: IAddHistoryRequest;
		}) => addHistoryToPatient(profileId, historyData),
		onSuccess: (_, { profileId }) => {
			toast.success('History Added', {
				description: 'The history has been successfully added to the patient.',
			});

			// Invalidate patient detail and history
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.HISTORY(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Add History Error', 'Failed to add history');
		},
	});
};

export const useUpdatePatientHistory = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientHistoryId,
			historyData,
		}: {
			profileId: number;
			patientHistoryId: number;
			historyData: IUpdateHistoryRequest;
		}) => updatePatientHistory(profileId, patientHistoryId, historyData),
		onSuccess: (_, { profileId }) => {
			toast.success('History Updated', {
				description: 'The history has been successfully updated.',
			});

			// Invalidate patient detail and history
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.HISTORY(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Update History Error', 'Failed to update history');
		},
	});
};

export const useRemoveHistoryFromPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientHistoryId,
		}: {
			profileId: number;
			patientHistoryId: number;
		}) => removeHistoryFromPatient(profileId, patientHistoryId),
		onSuccess: (_, { profileId }) => {
			toast.success('History Removed', {
				description:
					'The history has been successfully removed from the patient.',
			});

			// Invalidate patient detail and history
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.HISTORY(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove History Error', 'Failed to remove history');
		},
	});
};

// Allergies Management Hooks
export const useAddAllergyToPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			allergyData,
		}: {
			profileId: number;
			allergyData: IAddAllergyRequest;
		}) => addAllergyToPatient(profileId, allergyData),
		onSuccess: (_, { profileId }) => {
			toast.success('Allergy Added', {
				description: 'The allergy has been successfully added to the patient.',
			});

			// Invalidate patient detail and allergies
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.ALLERGIES(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Add Allergy Error', 'Failed to add allergy');
		},
	});
};

export const useUpdatePatientAllergy = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientAllergyId,
			allergyData,
		}: {
			profileId: number;
			patientAllergyId: number;
			allergyData: IUpdateAllergyRequest;
		}) => updatePatientAllergy(profileId, patientAllergyId, allergyData),
		onSuccess: (_, { profileId }) => {
			toast.success('Allergy Updated', {
				description: 'The allergy has been successfully updated.',
			});

			// Invalidate patient detail and allergies
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.ALLERGIES(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Update Allergy Error', 'Failed to update allergy');
		},
	});
};

export const useRemoveAllergyFromPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientAllergyId,
		}: {
			profileId: number;
			patientAllergyId: number;
		}) => removeAllergyFromPatient(profileId, patientAllergyId),
		onSuccess: (_, { profileId }) => {
			toast.success('Allergy Removed', {
				description:
					'The allergy has been successfully removed from the patient.',
			});

			// Invalidate patient detail and allergies
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.ALLERGIES(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Allergy Error', 'Failed to remove allergy');
		},
	});
};

// Illnesses Management Hooks
export const useAddIllnessToPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			illnessData,
		}: {
			profileId: number;
			illnessData: IAddIllnessRequest;
		}) => addIllnessToPatient(profileId, illnessData),
		onSuccess: (_, { profileId }) => {
			toast.success('Illness Added', {
				description: 'The illness has been successfully added to the patient.',
			});

			// Invalidate patient detail and illnesses
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.ILLNESSES(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Add Illness Error', 'Failed to add illness');
		},
	});
};

export const useUpdatePatientIllness = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientIllnessId,
			illnessData,
		}: {
			profileId: number;
			patientIllnessId: number;
			illnessData: IUpdateIllnessRequest;
		}) => updatePatientIllness(profileId, patientIllnessId, illnessData),
		onSuccess: (_, { profileId }) => {
			toast.success('Illness Updated', {
				description: 'The illness has been successfully updated.',
			});

			// Invalidate patient detail and illnesses
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.ILLNESSES(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Update Illness Error', 'Failed to update illness');
		},
	});
};

export const useRemoveIllnessFromPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientIllnessId,
		}: {
			profileId: number;
			patientIllnessId: number;
		}) => removeIllnessFromPatient(profileId, patientIllnessId),
		onSuccess: (_, { profileId }) => {
			toast.success('Illness Removed', {
				description:
					'The illness has been successfully removed from the patient.',
			});

			// Invalidate patient detail and illnesses
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.ILLNESSES(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Illness Error', 'Failed to remove illness');
		},
	});
};

// Surgeries Management Hooks
export const useAddSurgeryToPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			surgeryData,
		}: {
			profileId: number;
			surgeryData: IAddSurgeryRequest;
		}) => addSurgeryToPatient(profileId, surgeryData),
		onSuccess: (_, { profileId }) => {
			toast.success('Surgery Added', {
				description: 'The surgery has been successfully added to the patient.',
			});

			// Invalidate patient detail and surgeries
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.SURGERIES(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Add Surgery Error', 'Failed to add surgery');
		},
	});
};

export const useUpdatePatientSurgery = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientSurgeryId,
			surgeryData,
		}: {
			profileId: number;
			patientSurgeryId: number;
			surgeryData: IUpdateSurgeryRequest;
		}) => updatePatientSurgery(profileId, patientSurgeryId, surgeryData),
		onSuccess: (_, { profileId }) => {
			toast.success('Surgery Updated', {
				description: 'The surgery has been successfully updated.',
			});

			// Invalidate patient detail and surgeries
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.SURGERIES(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Update Surgery Error', 'Failed to update surgery');
		},
	});
};

export const useRemoveSurgeryFromPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientSurgeryId,
		}: {
			profileId: number;
			patientSurgeryId: number;
		}) => removeSurgeryFromPatient(profileId, patientSurgeryId),
		onSuccess: (_, { profileId }) => {
			toast.success('Surgery Removed', {
				description:
					'The surgery has been successfully removed from the patient.',
			});

			// Invalidate patient detail and surgeries
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.SURGERIES(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Surgery Error', 'Failed to remove surgery');
		},
	});
};

// Diets Management Hooks
export const useAddDietToPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			dietData,
		}: {
			profileId: number;
			dietData: IAddDietRequest;
		}) => addDietToPatient(profileId, dietData),
		onSuccess: (_, { profileId }) => {
			toast.success('Diet Added', {
				description: 'The diet has been successfully added to the patient.',
			});

			// Invalidate patient detail and diets
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DIETS(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Add Diet Error', 'Failed to add diet');
		},
	});
};

export const useUpdatePatientDiet = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientDietId,
			dietData,
		}: {
			profileId: number;
			patientDietId: number;
			dietData: IUpdateDietRequest;
		}) => updatePatientDiet(profileId, patientDietId, dietData),
		onSuccess: (_, { profileId }) => {
			toast.success('Diet Updated', {
				description: 'The diet has been successfully updated.',
			});

			// Invalidate patient detail and diets
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DIETS(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Update Diet Error', 'Failed to update diet');
		},
	});
};

export const useRemoveDietFromPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientDietId,
		}: {
			profileId: number;
			patientDietId: number;
		}) => removeDietFromPatient(profileId, patientDietId),
		onSuccess: (_, { profileId }) => {
			toast.success('Diet Removed', {
				description: 'The diet has been successfully removed from the patient.',
			});

			// Invalidate patient detail and diets
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DIETS(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Diet Error', 'Failed to remove diet');
		},
	});
};

// HMO Management Hooks
export const useAddHmoToPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			hmoData,
		}: {
			profileId: number;
			hmoData: IAddHmoRequest;
		}) => addHmoToPatient(profileId, hmoData),
		onSuccess: (_, { profileId }) => {
			toast.success('HMO Added', {
				description: 'The HMO has been successfully added to the patient.',
			});

			// Invalidate patient detail and HMO
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.HMO(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Add HMO Error', 'Failed to add HMO');
		},
	});
};

export const useUpdatePatientHmo = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientHmoId,
			hmoData,
		}: {
			profileId: number;
			patientHmoId: number;
			hmoData: IUpdateHmoRequest;
		}) => updatePatientHmo(profileId, patientHmoId, hmoData),
		onSuccess: (_, { profileId }) => {
			toast.success('HMO Updated', {
				description: 'The HMO has been successfully updated.',
			});

			// Invalidate patient detail and HMO
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.HMO(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Update HMO Error', 'Failed to update HMO');
		},
	});
};

export const useRemoveHmoFromPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientHmoId,
		}: {
			profileId: number;
			patientHmoId: number;
		}) => removeHmoFromPatient(profileId, patientHmoId),
		onSuccess: (_, { profileId }) => {
			toast.success('HMO Removed', {
				description: 'The HMO has been successfully removed from the patient.',
			});

			// Invalidate patient detail and HMO
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.HMO(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove HMO Error', 'Failed to remove HMO');
		},
	});
};

// Family Management Hooks
export const useAddFamilyToPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			familyData,
		}: {
			profileId: number;
			familyData: IAddFamilyRequest;
		}) => addFamilyToPatient(profileId, familyData),
		onSuccess: (_, { profileId }) => {
			toast.success('Family Member Added', {
				description:
					'The family member has been successfully added to the patient.',
			});

			// Invalidate patient detail and family
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.FAMILY(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Add Family Error', 'Failed to add family member');
		},
	});
};

export const useRemoveFamilyFromPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientFamilyId,
		}: {
			profileId: number;
			patientFamilyId: number;
		}) => removeFamilyFromPatient(profileId, patientFamilyId),
		onSuccess: (_, { profileId }) => {
			toast.success('Family Member Removed', {
				description:
					'The family member has been successfully removed from the patient.',
			});

			// Invalidate patient detail and family
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PATIENTS.FAMILY(profileId),
			});
		},
		onError: (error) => {
			catchError(
				error,
				'Remove Family Error',
				'Failed to remove family member'
			);
		},
	});
};

export const useCreateAppointment = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createAppointment,
		onSuccess: () => {
			toast.success('Appointment Created', {
				description: 'The appointment has been successfully created.',
			});

			// Invalidate all appointment-related queries
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'appointments'],
			});

			// Invalidate all dashboard queries (includes queue and attended)
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'dashboard'],
			});

			// Invalidate patient appointment history
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'patients'],
				predicate: (query) => {
					return query.queryKey.includes('appointment-history');
				},
			});
		},
		onError: (error) => {
			catchError(
				error,
				'Create Appointment Error',
				'Failed to create appointment'
			);
		},
	});
};

export const useUpdateAppointment = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: updateAppointment,
		onSuccess: () => {
			toast.success('Appointment Updated', {
				description: 'The appointment has been successfully updated.',
			});

			// Invalidate all appointment-related queries
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'appointments'],
			});

			// Invalidate all dashboard queries (includes queue and attended)
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'dashboard'],
			});

			// Invalidate patient appointment history
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'patients'],
				predicate: (query) => {
					return query.queryKey.includes('appointment-history');
				},
			});
		},
		onError: (error) => {
			catchError(
				error,
				'Update Appointment Error',
				'Failed to update appointment'
			);
		},
	});
};

export const useUpdateAppointmentStatus = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			appointmentId,
			status,
		}: {
			appointmentId: number;
			status: string;
		}) => updateAppointmentStatus(appointmentId, status),
		onSuccess: () => {
			toast.success('Appointment Status Updated', {
				description: 'The appointment status has been successfully updated.',
			});

			// Invalidate all appointment-related queries
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'appointments'],
			});

			// Invalidate all dashboard queries (includes queue and attended)
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'dashboard'],
			});

			// Invalidate patient appointment history
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'patients'],
				predicate: (query) => {
					return query.queryKey.includes('appointment-history');
				},
			});
		},
		onError: (error) => {
			catchError(
				error,
				'Update Status Error',
				'Failed to update appointment status'
			);
		},
	});
};

export const useUpdateAppointmentPtr = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			appointmentId,
			data,
		}: {
			appointmentId: number;
			data: {
				professionalTaxReceipt: string;
			};
		}) => updateAppointmentPtr(appointmentId, data),
		onSuccess: () => {
			toast.success('PTR Updated', {
				description:
					'The professional tax receipt has been successfully updated.',
			});

			// Invalidate all appointment-related queries
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'appointments'],
			});
		},
		onError: (error) => {
			catchError(error, 'Update PTR Error', 'Failed to update PTR');
		},
	});
};

// Assistant Mutation Hooks
export const useCreateAssistant = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			clinicId,
			assistantData,
		}: {
			clinicId: number;
			assistantData: ICreateAssistantRequest;
		}) => createAssistant(clinicId, assistantData),
		onSuccess: () => {
			toast.success('Assistant Created', {
				description: 'The clinic assistant has been successfully created.',
			});

			// Invalidate and refetch assistants
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'assistants'],
			});

			// Invalidate clinic assistants
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'clinics'],
			});
		},
		onError: (error) => {
			catchError(error, 'Create Assistant Error', 'Failed to create assistant');
		},
	});
};

export const useAssignAssistant = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			clinicId,
			assistantData,
		}: {
			clinicId: number;
			assistantData: IAssignAssistantRequest;
		}) => assignAssistant(clinicId, assistantData),
		onSuccess: () => {
			toast.success('Assistant Assigned', {
				description:
					'The assistant has been successfully assigned to the clinic.',
			});

			// Invalidate and refetch assistants
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'assistants'],
			});

			// Invalidate clinic assistants
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'clinics'],
			});
		},
		onError: (error) => {
			catchError(error, 'Assign Assistant Error', 'Failed to assign assistant');
		},
	});
};

export const useUpdateAssistant = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			clinicId,
			assistantData,
		}: {
			clinicId: number;
			assistantData: IUpdateAssistantRequest;
		}) => updateAssistant(clinicId, assistantData),
		onSuccess: () => {
			toast.success('Assistant Updated', {
				description: 'The assistant has been successfully updated.',
			});

			// Invalidate and refetch assistants
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'assistants'],
			});
		},
		onError: (error) => {
			catchError(error, 'Update Assistant Error', 'Failed to update assistant');
		},
	});
};

export const useRemoveAssistant = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			clinicId,
			assistantProfileId,
		}: {
			clinicId: number;
			assistantProfileId: number;
		}) => removeAssistant(clinicId, assistantProfileId),
		onSuccess: () => {
			toast.success('Assistant Removed', {
				description:
					'The assistant has been successfully removed from the clinic.',
			});

			// Invalidate and refetch assistants
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'assistants'],
			});

			// Invalidate clinic assistants
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'clinics'],
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Assistant Error', 'Failed to remove assistant');
		},
	});
};

export const useArchiveAssistant = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (assistantProfileId: number) =>
			archiveAssistant(assistantProfileId),
		onSuccess: () => {
			toast.success('Assistant Deleted', {
				description: 'The assistant has been successfully deleted.',
			});

			// Invalidate and refetch assistants
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'assistants'],
			});

			// Invalidate clinic assistants
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'clinics'],
			});
		},
		onError: (error) => {
			catchError(error, 'Delete Assistant Error', 'Failed to delete assistant');
		},
	});
};

export const useDisableAssistant = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (assistantProfileId: number) =>
			disableAssistant(assistantProfileId),
		onSuccess: () => {
			toast.success('Assistant Disabled', {
				description: 'The assistant has been successfully disabled.',
			});

			// Invalidate and refetch assistants
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'assistants'],
			});
		},
		onError: (error) => {
			catchError(
				error,
				'Disable Assistant Error',
				'Failed to disable assistant'
			);
		},
	});
};

export const useEnableAssistant = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (assistantProfileId: number) =>
			enableAssistant(assistantProfileId),
		onSuccess: () => {
			toast.success('Assistant Enabled', {
				description: 'The assistant has been successfully enabled.',
			});

			// Invalidate and refetch assistants
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'assistants'],
			});
		},
		onError: (error) => {
			catchError(error, 'Enable Assistant Error', 'Failed to enable assistant');
		},
	});
};

export const useUpdateAssistantCredentials = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			assistantProfileId,
			credentialsData,
		}: {
			assistantProfileId: number;
			credentialsData: IUpdateAssistantCredentialsRequest;
		}) => updateAssistantCredentials(assistantProfileId, credentialsData),
		onSuccess: () => {
			toast.success('Credentials Updated', {
				description:
					'The assistant credentials have been successfully updated.',
			});

			// Invalidate and refetch assistants
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'assistants'],
			});
		},
		onError: (error) => {
			catchError(
				error,
				'Update Credentials Error',
				'Failed to update assistant credentials'
			);
		},
	});
};

// Update appointment details hook
export const useUpdateAppointmentDetails = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			appointmentId,
			data,
		}: {
			appointmentId: number;
			data: {
				chiefComplaint?: string;
				diagnosis?: string;
				prognosis?: string;
			};
		}) => updateAppointmentDetails(appointmentId, data),
		onSuccess: () => {
			toast.success('Appointment Updated', {
				description: 'Appointment details have been successfully updated.',
			});

			// Invalidate all appointment-related queries
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'appointments'],
			});

			// Invalidate all dashboard queries (includes queue and attended)
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'dashboard'],
			});

			// Invalidate patient appointment history
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'patients'],
				predicate: (query) => {
					return query.queryKey.includes('appointment-history');
				},
			});
		},
		onError: (error) => {
			catchError(error, 'Update Error', 'Failed to update appointment details');
		},
	});
};

// Add doctor note hook
export const useAddDoctorNote = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			appointmentId,
			note,
		}: {
			appointmentId: number;
			note: string;
		}) => addDoctorNote(appointmentId, note),
		onSuccess: (_, variables) => {
			toast.success('Note Added', {
				description: 'Doctor note has been successfully added.',
			});

			// Invalidate specific appointment detail
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
					variables.appointmentId
				),
			});

			// Invalidate all appointment-related queries
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'appointments'],
			});

			// Invalidate dashboard queries
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'dashboard'],
			});
		},
		onError: (error) => {
			catchError(error, 'Add Note Error', 'Failed to add doctor note');
		},
	});
};

// Update vital signs hook
export const useUpdateVitalSigns = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			appointmentId,
			vitalSigns,
		}: {
			appointmentId: number;
			vitalSigns: {
				systolic?: string;
				diastolic?: string;
				pulseRate?: string;
				respiration?: string;
				height?: string;
				heightType?: string;
				weight?: string;
				weightType?: string;
				temperature?: string;
				temperatureType?: string;
				oxygenSaturation?: string;
				capillaryBloodGlucose?: string;
				bodyMassIndex?: string;
			};
		}) => updateVitalSigns(appointmentId, vitalSigns),
		onSuccess: (_, variables) => {
			toast.success('Vital Signs Updated', {
				description: 'Vital signs have been successfully updated.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
					variables.appointmentId
				),
			});
		},
		onError: (error) => {
			catchError(error, 'Update Error', 'Failed to update vital signs');
		},
	});
};

// Prescription Management Hooks
export const useCreatePrescription = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			appointmentId,
			prescriptionData,
		}: {
			appointmentId: number;
			prescriptionData: {
				patient_name: string;
				patient_address: string;
				patient_phone: string;
				patient_gender: string;
				patient_birthdate: string;
				professional_tax_receipt?: string;
			};
		}) => createPrescription(appointmentId, prescriptionData),
		onSuccess: (_, variables) => {
			toast.success('Prescription Created', {
				description: 'Prescription has been successfully created.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
					variables.appointmentId
				),
			});
		},
		onError: (error) => {
			catchError(error, 'Create Error', 'Failed to create prescription');
		},
	});
};

export const useRemovePrescription = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			prescriptionId,
			appointmentId, // eslint-disable-line @typescript-eslint/no-unused-vars
		}: {
			prescriptionId: number;
			appointmentId: number;
		}) => removePrescription(prescriptionId),
		onSuccess: (_, variables) => {
			toast.success('Prescription Removed', {
				description: 'Prescription has been successfully removed.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
					variables.appointmentId
				),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Error', 'Failed to remove prescription');
		},
	});
};

export const useCreatePrescriptionItem = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			prescriptionId,
			appointmentId, // eslint-disable-line @typescript-eslint/no-unused-vars
			itemData,
		}: {
			prescriptionId: number;
			appointmentId: number;
			itemData: {
				generic: string;
				brand: string;
				dosageForm: string;
				dosage: string;
				frequency: string;
				quantity: number;
				instruction: string;
			};
		}) => createPrescriptionItem(prescriptionId, itemData),
		onSuccess: (_, variables) => {
			toast.success('Medication Added', {
				description: 'Medication has been successfully added to prescription.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
					variables.appointmentId
				),
			});
		},
		onError: (error) => {
			catchError(error, 'Add Error', 'Failed to add medication');
		},
	});
};

export const useRemovePrescriptionItem = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			prescriptionId,
			prescriptionItemId,
			appointmentId, // eslint-disable-line @typescript-eslint/no-unused-vars
		}: {
			prescriptionId: number;
			prescriptionItemId: number;
			appointmentId: number;
		}) => removePrescriptionItem(prescriptionId, prescriptionItemId),
		onSuccess: (_, variables) => {
			toast.success('Medication Removed', {
				description:
					'Medication has been successfully removed from prescription.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
					variables.appointmentId
				),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Error', 'Failed to remove medication');
		},
	});
};

// Standalone Prescription Management Hooks
export const useAllPrescriptions = (
	params: IPrescriptionListParams = {},
	options?: { enabled?: boolean }
) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.PRESCRIPTIONS.ALL(params),
		queryFn: () => getAllPrescriptions(params),
		enabled: options?.enabled !== false,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const usePrescriptionDetail = (
	prescriptionId: number,
	options?: { enabled?: boolean }
) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.PRESCRIPTIONS.DETAIL(prescriptionId),
		queryFn: () => getPrescriptionById(prescriptionId),
		enabled: !!prescriptionId && options?.enabled !== false,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useCreateStandalonePrescription = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (prescriptionData: ICreateStandalonePrescriptionRequest) =>
			createStandalonePrescription(prescriptionData),
		onSuccess: () => {
			toast.success('Prescription Created', {
				description: 'Standalone prescription has been successfully created.',
			});

			queryClient.invalidateQueries({
				queryKey: ['doctor', 'prescriptions', 'all'],
			});
		},
		onError: (error) => {
			catchError(error, 'Create Error', 'Failed to create prescription');
		},
	});
};

export const useCreateStandaloneMedicalCertificate = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (
			medicalCertificateData: ICreateStandaloneMedicalCertificateRequest
		) => createStandaloneMedicalCertificate(medicalCertificateData),
		onSuccess: () => {
			toast.success('Medical Certificate Created', {
				description:
					'Standalone Medical Certificate has been successfully created.',
			});

			queryClient.invalidateQueries({
				queryKey: ['doctor', 'medical-certificates', 'all'],
			});
		},
		onError: (error) => {
			catchError(error, 'Create Error', 'Failed to create medical certificate');
		},
	});
};

export const useRemoveStandalonePrescription = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (prescriptionId: number) => removePrescription(prescriptionId),
		onSuccess: () => {
			toast.success('Prescription Removed', {
				description: 'Prescription has been successfully removed.',
			});

			queryClient.invalidateQueries({
				queryKey: ['doctor', 'prescriptions', 'all'],
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Error', 'Failed to remove prescription');
		},
	});
};

export const useCreateStandalonePrescriptionItem = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			prescriptionId,
			itemData,
		}: {
			prescriptionId: number;
			itemData: {
				generic: string;
				brand: string;
				dosageForm: string;
				dosage: string;
				frequency: string;
				quantity: number;
				instruction: string;
			};
		}) => createPrescriptionItem(prescriptionId, itemData),
		onSuccess: (_, variables) => {
			toast.success('Medication Added', {
				description: 'Medication has been successfully added to prescription.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PRESCRIPTIONS.DETAIL(
					variables.prescriptionId
				),
			});
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'prescriptions', 'all'],
			});
		},
		onError: (error) => {
			catchError(error, 'Add Error', 'Failed to add medication');
		},
	});
};

export const useRemoveStandalonePrescriptionItem = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			prescriptionId,
			prescriptionItemId,
		}: {
			prescriptionId: number;
			prescriptionItemId: number;
		}) => removePrescriptionItem(prescriptionId, prescriptionItemId),
		onSuccess: (_, variables) => {
			toast.success('Medication Removed', {
				description:
					'Medication has been successfully removed from prescription.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PRESCRIPTIONS.DETAIL(
					variables.prescriptionId
				),
			});
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'prescriptions', 'all'],
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Error', 'Failed to remove medication');
		},
	});
};

export const useUpdateStandalonePrescriptionItem = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			prescriptionId,
			prescriptionItemId,
			itemData,
		}: {
			prescriptionId: number;
			prescriptionItemId: number;
			itemData: {
				generic: string;
				brand: string;
				dosageForm: string;
				dosage: string;
				frequency: string;
				quantity: number;
				instruction: string;
			};
		}) => updatePrescriptionItem(prescriptionId, prescriptionItemId, itemData),
		onSuccess: (_, variables) => {
			toast.success('Medication Updated', {
				description:
					'Medication has been successfully updated from prescription.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.PRESCRIPTIONS.DETAIL(
					variables.prescriptionId
				),
			});
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'prescriptions', 'all'],
			});
		},
		onError: (error) => {
			catchError(error, 'Update Error', 'Failed to update medication');
		},
	});
};

export const useUpdateLabRequestItem = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			labRequestId,
			labRequestItemId,
			itemData,
		}: {
			labRequestId: number;
			labRequestItemId: number;
			itemData: {
				name: string;
			};
		}) => updateLabRequestItem(labRequestId, labRequestItemId, itemData),
		onSuccess: (_, variables) => {
			toast.success('Lab Test Updated', {
				description: 'Lab test has been successfully updated from request.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.LAB_REQUESTS.DETAIL(variables.labRequestId),
			});
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'lab-requests'],
			});
		},
		onError: (error) => {
			catchError(error, 'Update Error', 'Failed to update lab test');
		},
	});
};

// Referral Management Hooks
export const useAllReferrals = (
	params: IReferralListParams = {},
	options?: { enabled?: boolean }
) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.REFERRALS.ALL(params),
		queryFn: () => getAllReferrals(params),
		enabled: options?.enabled !== false,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useReferralDetail = (
	referralId: number | null,
	options?: { enabled?: boolean }
) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.REFERRALS.DETAIL(referralId!),
		queryFn: () => getReferralById(referralId!),
		enabled: !!referralId && options?.enabled !== false,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useCreateStandaloneReferral = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (referralData: ICreateStandaloneReferralRequest) =>
			createStandaloneReferral(referralData),
		onSuccess: () => {
			toast.success('Referral Created', {
				description: 'Referral has been successfully created.',
			});

			queryClient.invalidateQueries({
				queryKey: ['doctor', 'referrals', 'all'],
			});
		},
		onError: (error) => {
			catchError(error, 'Create Error', 'Failed to create referral');
		},
	});
};

export const useCreateReferral = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			appointmentId,
			referralData,
		}: {
			appointmentId: number;
			referralData: ICreateReferralRequest;
		}) => createReferral(appointmentId, referralData),
		onSuccess: (_, variables) => {
			toast.success('Referral Created', {
				description: 'Referral has been successfully created.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
					variables.appointmentId
				),
			});
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'referrals', 'all'],
			});
		},
		onError: (error) => {
			catchError(error, 'Create Error', 'Failed to create referral');
		},
	});
};

export const useRemoveReferral = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({ referralId }: { referralId: number }) =>
			removeReferral(referralId),
		onSuccess: () => {
			toast.success('Referral Removed', {
				description: 'Referral has been successfully removed.',
			});

			queryClient.invalidateQueries({
				queryKey: ['doctor', 'referrals', 'all'],
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Error', 'Failed to remove referral');
		},
	});
};

export const useUpdateReferral = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			referralId,
			referralData,
		}: {
			referralId: number;
			referralData: ICreateReferralRequest;
		}) => updateReferral(referralId, referralData),
		onSuccess: () => {
			toast.success('Referral Updated', {
				description: 'Referral has been successfully updated.',
			});

			queryClient.invalidateQueries({
				queryKey: ['doctor', 'referrals', 'all'],
			});
		},
		onError: (error) => {
			catchError(error, 'Update Error', 'Failed to update referral');
		},
	});
};

// Standalone Lab Request Management Hooks
export const useAllLabRequests = (
	params: ILabRequestListParams = {},
	options?: { enabled?: boolean }
) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.LAB_REQUESTS.ALL(params),
		queryFn: () => getAllLabRequests(params),
		enabled: options?.enabled !== false,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useLabRequestDetail = (
	labRequestId: number,
	options?: { enabled?: boolean }
) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.LAB_REQUESTS.DETAIL(labRequestId),
		queryFn: () => getLabRequestById(labRequestId),
		enabled: !!labRequestId && options?.enabled !== false,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useMedicalCertificateDetail = (
	medicalCertificateId: number,
	options?: { enabled?: boolean }
) => {
	return useQuery({
		queryKey:
			DOCTOR_QUERY_KEYS.MEDICAL_CERTIFICATES.DETAIL(medicalCertificateId),
		queryFn: () => getMedicalCertificateById(medicalCertificateId),
		enabled: !!medicalCertificateId && options?.enabled !== false,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

// Standalone Diagnostic Request Management Hooks
export const useAllMedicalCertificates = (
	params: IMedicalCertificateListParams = {},
	options?: { enabled?: boolean }
) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.MEDICAL_CERTIFICATES.ALL(params),
		queryFn: () => getAllMedicalCertificates(params),
		enabled: options?.enabled !== false,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

// Standalone Diagnostic Request Management Hooks
export const useAllDiagnosticRequests = (
	params: IDiagnosticRequestListParams = {},
	options?: { enabled?: boolean }
) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.DIAGNOSTIC_REQUESTS.ALL(params),
		queryFn: () => getAllDiagnosticRequests(params),
		enabled: options?.enabled !== false,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useDiagnosticRequestDetail = (
	diagnosticRequestId: number,
	options?: { enabled?: boolean }
) => {
	return useQuery({
		queryKey: DOCTOR_QUERY_KEYS.DIAGNOSTIC_REQUESTS.DETAIL(diagnosticRequestId),
		queryFn: () => getDiagnosticRequestById(diagnosticRequestId),
		enabled: !!diagnosticRequestId && options?.enabled !== false,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useCreateStandaloneLabRequest = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (labRequestData: ICreateStandaloneLabRequestRequest) =>
			createStandaloneLabRequest(labRequestData),
		onSuccess: () => {
			toast.success('Lab Request Created', {
				description: 'Laboratory request has been successfully created.',
			});

			// Invalidate all lab request queries (with any parameters)
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'lab-requests'],
			});
		},
		onError: (error) => {
			catchError(error, 'Create Error', 'Failed to create lab request');
		},
	});
};

export const useRemoveStandaloneLabRequest = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (labRequestId: number) =>
			removeStandaloneLabRequest(labRequestId),
		onSuccess: () => {
			toast.success('Lab Request Removed', {
				description: 'Laboratory request has been successfully removed.',
			});

			// Invalidate all lab request queries (with any parameters)
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'lab-requests'],
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Error', 'Failed to remove lab request');
		},
	});
};

// Diagnostic Request Management Hooks
export const useCreateDiagnosticRequest = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			appointmentId,
			diagnosticRequestData,
		}: {
			appointmentId: number;
			diagnosticRequestData: {
				patient_name: string;
				patient_address: string;
				patient_phone: string;
				patient_gender: string;
				patient_birthdate: string;
			};
		}) => createDiagnosticRequest(appointmentId, diagnosticRequestData),
		onSuccess: (_, variables) => {
			toast.success('Diagnostic Request Created', {
				description: 'Diagnostic request has been successfully created.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
					variables.appointmentId
				),
			});
		},
		onError: (error) => {
			catchError(error, 'Create Error', 'Failed to create diagnostic request');
		},
	});
};

export const useRemoveDiagnosticRequest = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			diagnosticRequestId,
			appointmentId, // eslint-disable-line @typescript-eslint/no-unused-vars
		}: {
			diagnosticRequestId: number;
			appointmentId: number;
		}) => removeDiagnosticRequest(diagnosticRequestId),
		onSuccess: (_, variables) => {
			toast.success('Diagnostic Request Removed', {
				description: 'Diagnostic request has been successfully removed.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
					variables.appointmentId
				),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Error', 'Failed to remove diagnostic request');
		},
	});
};

export const useCreateDiagnosticRequestItem = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			diagnosticRequestId,
			appointmentId, // eslint-disable-line @typescript-eslint/no-unused-vars
			itemData,
		}: {
			diagnosticRequestId: number;
			appointmentId: number;
			itemData: {
				name: string;
			};
		}) => createDiagnosticRequestItem(diagnosticRequestId, itemData),
		onSuccess: (_, variables) => {
			toast.success('Test Added', {
				description: 'Test has been successfully added to diagnostic request.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
					variables.appointmentId
				),
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.DIAGNOSTIC_REQUESTS.DETAIL(
					variables.diagnosticRequestId
				),
			});
		},
		onError: (error) => {
			catchError(error, 'Add Error', 'Failed to add test');
		},
	});
};

export const useRemoveDiagnosticRequestItem = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			diagnosticRequestId,
			diagnosticRequestItemId,
			appointmentId, // eslint-disable-line @typescript-eslint/no-unused-vars
		}: {
			diagnosticRequestId: number;
			diagnosticRequestItemId: number;
			appointmentId: number;
		}) =>
			removeDiagnosticRequestItem(diagnosticRequestId, diagnosticRequestItemId),
		onSuccess: (_, variables) => {
			toast.success('Test Removed', {
				description:
					'Test has been successfully removed from diagnostic request.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
					variables.appointmentId
				),
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.DIAGNOSTIC_REQUESTS.DETAIL(
					variables.diagnosticRequestId
				),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Error', 'Failed to remove test');
		},
	});
};

export const useUpdateDiagnosticRequestItem = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			diagnosticRequestId,
			diagnosticRequestItemId,
			itemData,
		}: {
			diagnosticRequestId: number;
			diagnosticRequestItemId: number;
			itemData: {
				name: string;
			};
		}) =>
			updateDiagnosticRequestItem(
				diagnosticRequestId,
				diagnosticRequestItemId,
				itemData
			),
		onSuccess: (_, variables) => {
			toast.success('Test Updated', {
				description: 'Test has been successfully updated from request.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.DIAGNOSTIC_REQUESTS.DETAIL(
					variables.diagnosticRequestId
				),
			});
		},
		onError: (error) => {
			catchError(error, 'Update Error', 'Failed to update test');
		},
	});
};

export const useCreateStandaloneDiagnosticRequest = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (diagnosticData: ICreateStandaloneDiagnosticRequestRequest) =>
			createStandaloneDiagnosticRequest(diagnosticData),
		onSuccess: () => {
			toast.success('Diagnostic Request Created', {
				description: 'Diagnostic request has been successfully created.',
			});

			// Invalidate all lab request queries (with any parameters)
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'diagnostic-requests'],
			});
		},
		onError: (error) => {
			catchError(error, 'Create Error', 'Failed to create diagnostic request');
		},
	});
};

export const useRemoveStandaloneDiagnosticRequest = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (diagnosticRequestId: number) =>
			removeStandaloneDiagnosticRequest(diagnosticRequestId),
		onSuccess: () => {
			toast.success('Diagnostic Request Removed', {
				description: 'Diagnostic request has been successfully removed.',
			});

			// Invalidate all diagnostic request queries (with any parameters)
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'diagnostic-requests'],
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Error', 'Failed to remove diagnostic request');
		},
	});
};

// Laboratory Request Management Hooks
export const useCreateLabRequest = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			appointmentId,
			labRequestData,
		}: {
			appointmentId: number;
			labRequestData: {
				patient_name: string;
				patient_address: string;
				patient_phone: string;
				patient_gender: string;
				patient_birthdate: string;
			};
		}) => createLabRequest(appointmentId, labRequestData),
		onSuccess: (_, variables) => {
			toast.success('Lab Request Created', {
				description: 'Laboratory request has been successfully created.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
					variables.appointmentId
				),
			});
		},
		onError: (error) => {
			catchError(error, 'Create Error', 'Failed to create lab request');
		},
	});
};

export const useRemoveLabRequest = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			labRequestId,
			appointmentId, // eslint-disable-line @typescript-eslint/no-unused-vars
		}: {
			labRequestId: number;
			appointmentId: number;
		}) => removeLabRequest(labRequestId),
		onSuccess: (_, variables) => {
			toast.success('Lab Request Removed', {
				description: 'Laboratory request has been successfully removed.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
					variables.appointmentId
				),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Error', 'Failed to remove lab request');
		},
	});
};

export const useCreateLabRequestItem = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			labRequestId,
			appointmentId, // eslint-disable-line @typescript-eslint/no-unused-vars
			itemData,
		}: {
			labRequestId: number;
			appointmentId: number;
			itemData: {
				name: string;
			};
		}) => createLabRequestItem(labRequestId, itemData),
		onSuccess: (_, variables) => {
			toast.success('Lab Test Added', {
				description: 'Lab test has been successfully added to request.',
			});

			// Invalidate appointment detail if it's an appointment-based lab request
			if (variables.appointmentId && variables.appointmentId > 0) {
				queryClient.invalidateQueries({
					queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
						variables.appointmentId
					),
				});
			}

			// Always invalidate lab request detail for standalone lab requests
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.LAB_REQUESTS.DETAIL(variables.labRequestId),
			});

			// Also invalidate the lab requests list
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'lab-requests'],
			});
		},
		onError: (error) => {
			catchError(error, 'Add Error', 'Failed to add lab test');
		},
	});
};

export const useRemoveLabRequestItem = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			labRequestId,
			labRequestItemId,
			appointmentId, // eslint-disable-line @typescript-eslint/no-unused-vars
		}: {
			labRequestId: number;
			labRequestItemId: number;
			appointmentId: number;
		}) => removeLabRequestItem(labRequestId, labRequestItemId),
		onSuccess: (_, variables) => {
			toast.success('Lab Test Removed', {
				description: 'Lab test has been successfully removed from request.',
			});

			// Invalidate appointment detail if it's an appointment-based lab request
			if (variables.appointmentId && variables.appointmentId > 0) {
				queryClient.invalidateQueries({
					queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
						variables.appointmentId
					),
				});
			}

			// Always invalidate lab request detail for standalone lab requests
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.LAB_REQUESTS.DETAIL(variables.labRequestId),
			});

			// Also invalidate the lab requests list
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'lab-requests'],
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Error', 'Failed to remove lab test');
		},
	});
};

export const useUploadLabResult = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			appointmentId,
			resultData,
		}: {
			appointmentId: number;
			resultData: FormData;
		}) => uploadLabResult(appointmentId, resultData),
		onSuccess: (_, variables) => {
			toast.success('Lab Result Uploaded', {
				description: 'Laboratory result has been successfully uploaded.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
					variables.appointmentId
				),
			});
		},
		onError: (error) => {
			catchError(error, 'Upload Error', 'Failed to upload lab result');
		},
	});
};

// Medical Certificate Management Hooks
export const useCreateMedicalCertificate = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (appointmentId: number) =>
			createMedicalCertificate(appointmentId),
		onSuccess: () => {
			toast.success('Medical Certificate Created', {
				description: 'Medical certificate has been successfully created.',
			});

			// Invalidate all appointment-related queries
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'appointments'],
			});
		},
		onError: (error) => {
			catchError(error, 'Create Error', 'Failed to create medical certificate');
		},
	});
};

export const useRemoveMedicalCertificate = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			medicalCertificateId,
			appointmentId, // eslint-disable-line @typescript-eslint/no-unused-vars
		}: {
			medicalCertificateId: number;
			appointmentId: number;
		}) => removeMedicalCertificate(medicalCertificateId),
		onSuccess: (_, variables) => {
			toast.success('Medical Certificate Removed', {
				description: 'Medical certificate has been successfully removed.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
					variables.appointmentId
				),
			});

			queryClient.invalidateQueries({
				queryKey: ['doctor', 'medical-certificates'],
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Error', 'Failed to remove medical certificate');
		},
	});
};

export const useUpdateMedicalCertificateItem = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			medicalCertificateId,
			medicalCertificateItemId,
			itemData,
		}: {
			medicalCertificateId: number;
			medicalCertificateItemId: number;
			itemData: {
				description: string;
			};
		}) =>
			updateMedicalCertificateItem(
				medicalCertificateId,
				medicalCertificateItemId,
				itemData
			),
		onSuccess: (_, variables) => {
			toast.success('Condition Updated', {
				description:
					'Condition has been successfully updated from medical certificate.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.MEDICAL_CERTIFICATES.DETAIL(
					variables.medicalCertificateId
				),
			});
		},
		onError: (error) => {
			catchError(error, 'Update Error', 'Failed to update condition');
		},
	});
};

export const useCreateMedicalCertificateItem = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			medicalCertificateId,
			appointmentId, // eslint-disable-line @typescript-eslint/no-unused-vars
			itemData,
		}: {
			medicalCertificateId: number;
			appointmentId: number;
			itemData: {
				description: string;
			};
		}) => createMedicalCertificateItem(medicalCertificateId, itemData),
		onSuccess: (_, variables) => {
			toast.success('Condition Added', {
				description:
					'Condition has been successfully added to medical certificate.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
					variables.appointmentId
				),
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.MEDICAL_CERTIFICATES.DETAIL(
					variables.medicalCertificateId
				),
			});
		},
		onError: (error) => {
			catchError(error, 'Add Error', 'Failed to add condition');
		},
	});
};

export const useRemoveMedicalCertificateItem = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			medicalCertificateId,
			medicalCertificateItemId,
			appointmentId, // eslint-disable-line @typescript-eslint/no-unused-vars
		}: {
			medicalCertificateId: number;
			medicalCertificateItemId: number;
			appointmentId: number;
		}) =>
			removeMedicalCertificateItem(
				medicalCertificateId,
				medicalCertificateItemId
			),
		onSuccess: (_, variables) => {
			toast.success('Condition Removed', {
				description:
					'Condition has been successfully removed from medical certificate.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
					variables.appointmentId
				),
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.MEDICAL_CERTIFICATES.DETAIL(
					variables.medicalCertificateId
				),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Error', 'Failed to remove condition');
		},
	});
};

// Medical Record Management Hooks
export const useAddMedicalRecord = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			appointmentId,
			recordFile,
		}: {
			appointmentId: number;
			recordFile: File;
		}) => addMedicalRecord(appointmentId, recordFile),
		onSuccess: (_, variables) => {
			toast.success('Medical Record Added', {
				description: 'Medical record has been successfully uploaded.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
					variables.appointmentId
				),
			});
		},
		onError: (error) => {
			catchError(error, 'Upload Error', 'Failed to upload medical record');
		},
	});
};

export const useRemoveMedicalRecord = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			appointmentId,
			medicalRecordId,
		}: {
			appointmentId: number;
			medicalRecordId: number;
		}) => removeMedicalRecord(appointmentId, medicalRecordId),
		onSuccess: (_, variables) => {
			toast.success('Medical Record Removed', {
				description: 'Medical record has been successfully removed.',
			});

			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.APPOINTMENTS.DETAIL(
					variables.appointmentId
				),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Error', 'Failed to remove medical record');
		},
	});
};

// Appointment Rescheduling Hook
export const useRescheduleAppointment = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			appointmentId,
			appointmentDate,
		}: {
			appointmentId: number;
			appointmentDate: string;
		}) => rescheduleAppointment(appointmentId, appointmentDate),
		onSuccess: () => {
			toast.success('Appointment Rescheduled', {
				description: 'Appointment has been successfully rescheduled.',
			});

			// Invalidate all appointment-related queries
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'appointments'],
			});

			// Invalidate all dashboard queries (includes queue and attended)
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'dashboard'],
			});

			// Invalidate patient appointment history
			queryClient.invalidateQueries({
				queryKey: ['doctor', 'patients'],
				predicate: (query) => {
					return query.queryKey.includes('appointment-history');
				},
			});
		},
		onError: (error) => {
			catchError(error, 'Reschedule Error', 'Failed to reschedule appointment');
		},
	});
};

export const useAllSubscription = (
	params: ISubscriptionListParams = {},
	options?: { enabled?: boolean }
) => {
	return useQuery<ISubscriptionsResponse>({
		queryKey: DOCTOR_QUERY_KEYS.SUBSCRIPTIONS.ALL(params),
		queryFn: () => getAllSubscriptions(params),
		enabled: options?.enabled !== false,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useSubscribe = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({ id }: ISubscribe) => subscribe({ id }),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: DOCTOR_QUERY_KEYS.SUBSCRIPTIONS.ALL({}),
			});

			toast.success('Subscription Successful', {
				description: 'You have successfully subscribed to this plan.',
			});
		},
		onError: (error) => {
			catchError(
				error,
				'Subscription Error',
				'Failed to subscribe to the plan'
			);
		},
	});
};
