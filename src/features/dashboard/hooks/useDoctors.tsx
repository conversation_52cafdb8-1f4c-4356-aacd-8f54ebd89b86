'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { useQuery } from '@tanstack/react-query';

import useHookstateDebounce from '@/core/hooks/utils/useHookstateDebounce';
import { TUserRole } from '@/core/types/role.type';

import { getDoctors } from '../api/doctors.api';

// State
const initialState = {
	params: {
		search: '',
		page: 1,
		pageSize: 10,
	},
};
export const doctorsState = hookstate(initialState);

export const useDoctors = (userRole: TUserRole = 'admin') => {
	const state = useHookstate(doctorsState);
	const searchDebounce = useHookstateDebounce(state.params.search, 500);

	const doctorsQuery = useQuery({
		queryKey: [
			userRole,
			'doctors',
			state.params.page.value,
			state.params.pageSize.value,
			searchDebounce.value,
		],
		queryFn: () =>
			getDoctors(userRole, {
				search: searchDebounce.value,
				page: state.params.page.value,
				pageSize: state.params.pageSize.value,
			}),
		staleTime: 1000 * 60 * 60 * 24, // 1 day
		refetchOnWindowFocus: false,
	});

	return {
		state,
		doctors: doctorsQuery.data,
		isSuccess: doctorsQuery.isSuccess,
		isLoading:
			doctorsQuery.isLoading ||
			doctorsQuery.isFetching ||
			doctorsQuery.isRefetching,
		refetch: doctorsQuery.refetch,
		error: doctorsQuery.error,
		isError: doctorsQuery.isError,
	};
};
