'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import {
	acceptDoctor,
	addAllergyToPatient,
	addDietToPatient,
	addFamilyToPatient,
	addHabitToPatient,
	addHistoryToPatient,
	addHmoToPatient,
	addIllnessToPatient,
	addSurgeryToPatient,
	ADMIN_QUERY_KEYS,
	createAllergyType,
	createAppointmentType,
	createConsultationType,
	createDiagnosticRequestType,
	createDietType,
	createDoctor,
	createHabitType,
	createHistoryType,
	createHmoType,
	createIllnessType,
	createLabRequestType,
	createMedicalDescription,
	createPaymentType,
	createPrescriptionType,
	createSubscription,
	createSurgeryType,
	createVisitReason,
	disableDoctor,
	enableDoctor,
	getAdminDashboardMetrics,
	getAllAllergyTypes,
	getAllAppointmentTypes,
	getAllConsultationTypes,
	getAllDiagnosticRequestTypes,
	getAllDietTypes,
	getAllDoctors,
	getAllHabitTypes,
	getAllHistoryTypes,
	getAllHmoTypes,
	getAllIllnessTypes,
	getAllLabRequestTypes,
	getAllMedicalDescription,
	getAllPatients,
	getAllPaymentTypes,
	getAllPrescriptionTypes,
	getAllSubscriptions,
	getAllSurgeryTypes,
	getAllVisitReasons,
	getDoctorById,
	getPatientById,
	getPendingDoctors,
	getSubscriptionById,
	rejectDoctor,
	removeAllergyFromPatient,
	removeAllergyType,
	removeAppointmentType,
	removeConsultationType,
	removeDiagnosticRequestType,
	removeDietFromPatient,
	removeDietType,
	removeFamilyFromPatient,
	removeHabitFromPatient,
	removeHabitType,
	removeHistoryFromPatient,
	removeHistoryType,
	removeHmoFromPatient,
	removeHmoType,
	removeIllnessFromPatient,
	removeIllnessType,
	removeLabRequestType,
	removeMedicalDescription,
	removePaymentType,
	removePrescriptionType,
	removeSubscription,
	removeSurgeryFromPatient,
	removeSurgeryType,
	removeVisitReason,
	updateAllergyType,
	updateAppointmentType,
	updateConsultationType,
	updateDiagnosticRequestType,
	updateDietType,
	updateHabitType,
	updateHistoryType,
	updateHmoType,
	updateIllnessType,
	updateLabRequestType,
	updateMedicalDescription,
	updatePatientAllergy,
	updatePatientDiet,
	updatePatientHabit,
	updatePatientHistory,
	updatePatientHmo,
	updatePatientIllness,
	updatePatientSurgery,
	updatePaymentType,
	updatePrescriptionType,
	updateSubscription,
	updateSurgeryType,
	updateVisitReason,
} from '@/core/api/admin';
import { catchError } from '@/core/lib/utils';
import {
	IDoctorActionRequest,
	IPaginationParams,
	ISearchParams,
	IUpdateSubscriptionRequest,
	TDashboardPeriod,
} from '@/features/dashboard/types/admin.types';

// Dashboard Metrics Hook
export const useAdminDashboardMetrics = (type?: TDashboardPeriod) => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.DASHBOARD.METRICS_WITH_PERIOD(type),
		queryFn: () => getAdminDashboardMetrics({ type }),
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

// Pending Doctors Hook
export const usePendingDoctors = (params: IPaginationParams = {}) => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.DOCTORS.PENDING(params),
		queryFn: () => getPendingDoctors(params),
		staleTime: 2 * 60 * 1000, // 2 minutes
	});
};

// All Doctors Hook
export const useAllDoctors = (params: IPaginationParams = {}) => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.DOCTORS.ALL(params),
		queryFn: () => getAllDoctors(params),
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

// Doctor Detail Hook
export const useDoctorDetail = (profileId: number) => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.DOCTORS.DETAIL(profileId),
		queryFn: () => getDoctorById(profileId),
		enabled: !!profileId,
		staleTime: 10 * 60 * 1000, // 10 minutes
	});
};

// All Patients Hook
export const useAllPatients = (params: ISearchParams = {}) => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.PATIENTS.ALL(params),
		queryFn: () => getAllPatients(params),
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

// Patient Detail Hook
export const usePatientDetail = (profileId: number) => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
		queryFn: () => getPatientById(profileId),
		enabled: !!profileId,
		staleTime: 10 * 60 * 1000, // 10 minutes
	});
};

// Accept Doctor Mutation
export const useAcceptDoctor = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: acceptDoctor,
		onSuccess: () => {
			toast.success('Doctor Accepted', {
				description: 'The doctor has been successfully accepted.',
			});

			// Invalidate and refetch pending doctors
			queryClient.invalidateQueries({
				queryKey: ['admin', 'doctors', 'pending'],
			});

			// Invalidate and refetch all doctors
			queryClient.invalidateQueries({
				queryKey: ['admin', 'doctors', 'all'],
			});

			// Invalidate dashboard metrics
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DASHBOARD.METRICS,
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DASHBOARD.METRICS_WITH_PERIOD(),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DASHBOARD.METRICS_WITH_PERIOD(),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DASHBOARD.METRICS_WITH_PERIOD(),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DASHBOARD.METRICS_WITH_PERIOD(),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DASHBOARD.METRICS_WITH_PERIOD(),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DASHBOARD.METRICS_WITH_PERIOD(),
			});
		},
		onError: (error) => {
			catchError(error, 'Accept Doctor Error', 'Failed to accept doctor');
		},
	});
};

// Reject Doctor Mutation
export const useRejectDoctor = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: rejectDoctor,
		onSuccess: () => {
			toast.success('Doctor Rejected', {
				description: 'The doctor has been successfully rejected.',
			});

			// Invalidate and refetch pending doctors
			queryClient.invalidateQueries({
				queryKey: ['admin', 'doctors', 'pending'],
			});

			// Invalidate dashboard metrics
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DASHBOARD.METRICS,
			});
		},
		onError: (error) => {
			catchError(error, 'Reject Doctor Error', 'Failed to reject doctor');
		},
	});
};

// Create Doctor Mutation
export const useCreateDoctor = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createDoctor,
		onSuccess: () => {
			toast.success('Doctor Created', {
				description: 'The doctor has been successfully created and activated.',
			});

			// Invalidate and refetch all doctors
			queryClient.invalidateQueries({
				queryKey: ['admin', 'doctors', 'all'],
			});

			// Invalidate dashboard metrics
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DASHBOARD.METRICS,
			});
		},
		onError: (error) => {
			catchError(error, 'Create Doctor Error', 'Failed to create doctor');
		},
	});
};

// Combined hook for doctor actions
export const useDoctorActions = () => {
	const acceptMutation = useAcceptDoctor();
	const rejectMutation = useRejectDoctor();

	const handleAcceptDoctor = (request: IDoctorActionRequest) => {
		acceptMutation.mutate(request);
	};

	const handleRejectDoctor = (request: IDoctorActionRequest) => {
		rejectMutation.mutate(request);
	};

	return {
		acceptDoctor: handleAcceptDoctor,
		rejectDoctor: handleRejectDoctor,
		isAccepting: acceptMutation.isPending,
		isRejecting: rejectMutation.isPending,
		isLoading: acceptMutation.isPending || rejectMutation.isPending,
	};
};

// Doctor Account Management Hooks
export const useDisableDoctor = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: disableDoctor,
		onSuccess: () => {
			// Invalidate and refetch doctor lists
			queryClient.invalidateQueries({
				predicate: (query) =>
					query.queryKey[0] === 'admin' && query.queryKey[1] === 'doctors',
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DASHBOARD.METRICS,
			});

			toast.success('Doctor Disabled', {
				description: 'Doctor account has been disabled successfully.',
			});
		},
		onError: (error) => {
			catchError(error, 'Disable Error', 'Failed to disable doctor account');
		},
	});
};

export const useEnableDoctor = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: enableDoctor,
		onSuccess: () => {
			// Invalidate and refetch doctor lists
			queryClient.invalidateQueries({
				predicate: (query) =>
					query.queryKey[0] === 'admin' && query.queryKey[1] === 'doctors',
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DASHBOARD.METRICS,
			});

			toast.success('Doctor Enabled', {
				description: 'Doctor account has been enabled successfully.',
			});
		},
		onError: (error) => {
			catchError(error, 'Enable Error', 'Failed to enable doctor account');
		},
	});
};

// Subscription Management Hooks
export const useAllSubscriptions = () => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.SUBSCRIPTIONS.ALL,
		queryFn: getAllSubscriptions,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

// Subscription Management Hooks
export const useSubscriptionsById = ({
	subscriptionId,
}: {
	subscriptionId: number;
}) => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.SUBSCRIPTIONS.DETAIL(subscriptionId),
		queryFn: () => getSubscriptionById(subscriptionId),
		enabled: !!subscriptionId,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useCreateSubscription = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createSubscription,
		onSuccess: () => {
			// Invalidate and refetch subscriptions
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.SUBSCRIPTIONS.ALL,
			});

			toast.success('Subscription Created', {
				description: 'Subscription plan has been created successfully.',
			});
		},
		onError: (error) => {
			catchError(error, 'Create Error', 'Failed to create subscription plan');
		},
	});
};

export const useUpdateSubscription = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			subscriptionId,
			subscriptionData,
		}: {
			subscriptionId: number;
			subscriptionData: IUpdateSubscriptionRequest;
		}) => {
			return updateSubscription(subscriptionId, subscriptionData);
		},
		onSuccess: () => {
			// Invalidate and refetch subscriptions
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.SUBSCRIPTIONS.ALL,
			});

			toast.success('Subscription Updated', {
				description: 'Subscription plan has been updated successfully.',
			});
		},
		onError: (error) => {
			catchError(error, 'Update Error', 'Failed to update subscription plan');
		},
	});
};

export const useRemoveSubscription = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: removeSubscription,
		onSuccess: () => {
			// Invalidate and refetch subscriptions
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.SUBSCRIPTIONS.ALL,
			});

			toast.success('Subscription Removed', {
				description: 'Subscription plan has been removed successfully.',
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Error', 'Failed to remove subscription plan');
		},
	});
};

// Data Management Hooks - Visit Reasons
export const useAllVisitReasons = () => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.VISIT_REASONS,
		queryFn: getAllVisitReasons,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useCreateVisitReason = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createVisitReason,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.VISIT_REASONS,
			});
			toast.success('Visit Reason Created', {
				description: 'Visit reason has been created successfully.',
			});
		},
		onError: (error) => {
			catchError(error, 'Create Error', 'Failed to create visit reason');
		},
	});
};

export const useUpdateVisitReason = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			visitReasonId,
			data,
		}: {
			visitReasonId: number;
			data: { name?: string; description?: string };
		}) => updateVisitReason(visitReasonId, data),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.VISIT_REASONS,
			});
			toast.success('Visit Reason Updated', {
				description: 'Visit reason has been updated successfully.',
			});
		},
		onError: (error) => {
			catchError(error, 'Update Error', 'Failed to update visit reason');
		},
	});
};

export const useRemoveVisitReason = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: removeVisitReason,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.VISIT_REASONS,
			});
			toast.success('Visit Reason Removed', {
				description: 'Visit reason has been removed successfully.',
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Error', 'Failed to remove visit reason');
		},
	});
};

// Data Management Hooks - Allergy Types
export const useAllAllergyTypes = () => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.ALLERGY_TYPES,
		queryFn: getAllAllergyTypes,
		staleTime: 5 * 60 * 1000,
	});
};

export const useCreateAllergyType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: createAllergyType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.ALLERGY_TYPES,
			});
			toast.success('Allergy Type Created', {
				description: 'Allergy type has been created successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Create Error', 'Failed to create allergy type'),
	});
};

export const useUpdateAllergyType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: ({
			allergyTypeId,
			data,
		}: {
			allergyTypeId: number;
			data: { name?: string; description?: string };
		}) => updateAllergyType(allergyTypeId, data),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.ALLERGY_TYPES,
			});
			toast.success('Allergy Type Updated', {
				description: 'Allergy type has been updated successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Update Error', 'Failed to update allergy type'),
	});
};

export const useRemoveAllergyType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: removeAllergyType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.ALLERGY_TYPES,
			});
			toast.success('Allergy Type Removed', {
				description: 'Allergy type has been removed successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Remove Error', 'Failed to remove allergy type'),
	});
};

// Data Management Hooks - Medical Cert Types
export const useAllMedicalTypes = () => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.MEDICAL_CERT_TYPES,
		queryFn: getAllMedicalDescription,
		staleTime: 5 * 60 * 1000,
	});
};

export const useCreateMedicalType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: createMedicalDescription,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.MEDICAL_CERT_TYPES,
			});
			toast.success('Medical Description Created', {
				description: 'Medical Description has been created successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Create Error', 'Failed to create medical description'),
	});
};

export const useUpdateMedicalType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: ({
			medicalTypeId,
			data,
		}: {
			medicalTypeId: number;
			data: { name?: string; description?: string };
		}) => updateMedicalDescription(medicalTypeId, data),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.MEDICAL_CERT_TYPES,
			});
			toast.success('Medical Description Updated', {
				description: 'Medical Description has been updated successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Update Error', 'Failed to update medical description'),
	});
};

export const useRemoveMedicalType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: removeMedicalDescription,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.MEDICAL_CERT_TYPES,
			});
			toast.success('Medical Description Removed', {
				description: 'Medical Description has been removed successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Remove Error', 'Failed to remove medical description'),
	});
};

// Data Management Hooks - Appointment Types
export const useAllAppointmentTypes = () => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.APPOINTMENT_TYPES,
		queryFn: getAllAppointmentTypes,
		staleTime: 5 * 60 * 1000,
	});
};

export const useCreateAppointmentType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: createAppointmentType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.APPOINTMENT_TYPES,
			});
			toast.success('Appointment Type Created', {
				description: 'Appointment type has been created successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Create Error', 'Failed to create appointment type'),
	});
};

export const useUpdateAppointmentType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: ({
			appointmentTypeId,
			data,
		}: {
			appointmentTypeId: number;
			data: { name?: string; description?: string };
		}) => updateAppointmentType(appointmentTypeId, data),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.APPOINTMENT_TYPES,
			});
			toast.success('Appointment Type Updated', {
				description: 'Appointment type has been updated successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Update Error', 'Failed to update appointment type'),
	});
};

export const useRemoveAppointmentType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: removeAppointmentType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.APPOINTMENT_TYPES,
			});
			toast.success('Appointment Type Removed', {
				description: 'Appointment type has been removed successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Remove Error', 'Failed to remove appointment type'),
	});
};

// Data Management Hooks - Consultation Types
export const useAllConsultationTypes = () => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.CONSULTATION_TYPES,
		queryFn: getAllConsultationTypes,
		staleTime: 5 * 60 * 1000,
	});
};

export const useCreateConsultationType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: createConsultationType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.CONSULTATION_TYPES,
			});
			toast.success('Consultation Type Created', {
				description: 'Consultation type has been created successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Create Error', 'Failed to create consultation type'),
	});
};

export const useUpdateConsultationType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: ({
			consultationTypeId,
			data,
		}: {
			consultationTypeId: number;
			data: { name?: string; description?: string };
		}) => updateConsultationType(consultationTypeId, data),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.CONSULTATION_TYPES,
			});
			toast.success('Consultation Type Updated', {
				description: 'Consultation type has been updated successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Update Error', 'Failed to update consultation type'),
	});
};

export const useRemoveConsultationType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: removeConsultationType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.CONSULTATION_TYPES,
			});
			toast.success('Consultation Type Removed', {
				description: 'Consultation type has been removed successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Remove Error', 'Failed to remove consultation type'),
	});
};

// Data Management Hooks - Payment Types
export const useAllPaymentTypes = () => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.PAYMENT_TYPES,
		queryFn: getAllPaymentTypes,
		staleTime: 5 * 60 * 1000,
	});
};

export const useCreatePaymentType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: createPaymentType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.PAYMENT_TYPES,
			});
			toast.success('Payment Type Created', {
				description: 'Payment type has been created successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Create Error', 'Failed to create payment type'),
	});
};

export const useUpdatePaymentType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: ({
			paymentTypeId,
			data,
		}: {
			paymentTypeId: number;
			data: { name?: string; description?: string };
		}) => updatePaymentType(paymentTypeId, data),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.PAYMENT_TYPES,
			});
			toast.success('Payment Type Updated', {
				description: 'Payment type has been updated successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Update Error', 'Failed to update payment type'),
	});
};

export const useRemovePaymentType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: removePaymentType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.PAYMENT_TYPES,
			});
			toast.success('Payment Type Removed', {
				description: 'Payment type has been removed successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Remove Error', 'Failed to remove payment type'),
	});
};

// Data Management Hooks - History Types
export const useAllHistoryTypes = () => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.HISTORY_TYPES,
		queryFn: getAllHistoryTypes,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useCreateHistoryType = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createHistoryType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.HISTORY_TYPES,
			});
			toast.success('History Type Created', {
				description: 'History type has been created successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Create Error', 'Failed to create history type'),
	});
};

export const useUpdateHistoryType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: ({
			historyTypeId,
			data,
		}: {
			historyTypeId: number;
			data: { name?: string; description?: string };
		}) => updateHistoryType(historyTypeId, data),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.HISTORY_TYPES,
			});
			toast.success('History Type Updated', {
				description: 'History type has been updated successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Update Error', 'Failed to update history type'),
	});
};

export const useRemoveHistoryType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: removeHistoryType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.HISTORY_TYPES,
			});
			toast.success('History Type Removed', {
				description: 'History type has been removed successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Remove Error', 'Failed to remove history type'),
	});
};

// Data Management Hooks - Diet Types
export const useAllDietTypes = () => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.DIET_TYPES,
		queryFn: getAllDietTypes,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useCreateDietType = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createDietType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.DIET_TYPES,
			});
			toast.success('Diet Type Created', {
				description: 'Diet type has been created successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Create Error', 'Failed to create diet type'),
	});
};

export const useUpdateDietType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: ({
			dietTypeId,
			data,
		}: {
			dietTypeId: number;
			data: { name?: string; description?: string };
		}) => updateDietType(dietTypeId, data),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.DIET_TYPES,
			});
			toast.success('Diet Type Updated', {
				description: 'Diet type has been updated successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Update Error', 'Failed to update diet type'),
	});
};

export const useRemoveDietType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: removeDietType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.DIET_TYPES,
			});
			toast.success('Diet Type Removed', {
				description: 'Diet type has been removed successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Remove Error', 'Failed to remove diet type'),
	});
};

// Data Management Hooks - Habit Types
export const useAllHabitTypes = () => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.HABIT_TYPES,
		queryFn: getAllHabitTypes,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useCreateHabitType = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createHabitType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.HABIT_TYPES,
			});
			toast.success('Habit Type Created', {
				description: 'Habit type has been created successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Create Error', 'Failed to create habit type'),
	});
};

export const useUpdateHabitType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: ({
			habitTypeId,
			data,
		}: {
			habitTypeId: number;
			data: { name?: string; description?: string };
		}) => updateHabitType(habitTypeId, data),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.HABIT_TYPES,
			});
			toast.success('Habit Type Updated', {
				description: 'Habit type has been updated successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Update Error', 'Failed to update habit type'),
	});
};

export const useRemoveHabitType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: removeHabitType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.HABIT_TYPES,
			});
			toast.success('Habit Type Removed', {
				description: 'Habit type has been removed successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Remove Error', 'Failed to remove habit type'),
	});
};

// Data Management Hooks - Diagnostic Request Types
export const useAllDiagnosticRequestTypes = () => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.DIAGNOSTIC_REQUEST_TYPES,
		queryFn: getAllDiagnosticRequestTypes,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useCreateDiagnosticRequestType = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createDiagnosticRequestType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.DIAGNOSTIC_REQUEST_TYPES,
			});
			toast.success('Diagnostic Request Type Created', {
				description: 'Diagnostic request type has been created successfully.',
			});
		},
		onError: (error) =>
			catchError(
				error,
				'Create Error',
				'Failed to create diagnostic request type'
			),
	});
};

export const useUpdateDiagnosticRequestType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: ({
			diagnosticRequestTypeId,
			data,
		}: {
			diagnosticRequestTypeId: number;
			data: { name?: string; description?: string };
		}) => updateDiagnosticRequestType(diagnosticRequestTypeId, data),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.DIAGNOSTIC_REQUEST_TYPES,
			});
			toast.success('Diagnostic Request Type Updated', {
				description: 'Diagnostic request type has been updated successfully.',
			});
		},
		onError: (error) =>
			catchError(
				error,
				'Update Error',
				'Failed to update diagnostic request type'
			),
	});
};

export const useRemoveDiagnosticRequestType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: removeDiagnosticRequestType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.DIAGNOSTIC_REQUEST_TYPES,
			});
			toast.success('Diagnostic Request Type Removed', {
				description: 'Diagnostic request type has been removed successfully.',
			});
		},
		onError: (error) =>
			catchError(
				error,
				'Remove Error',
				'Failed to remove diagnostic request type'
			),
	});
};

// Data Management Hooks - Illness Types
export const useAllIllnessTypes = () => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.ILLNESS_TYPES,
		queryFn: getAllIllnessTypes,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useCreateIllnessType = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createIllnessType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.ILLNESS_TYPES,
			});
			toast.success('Illness Type Created', {
				description: 'Illness type has been created successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Create Error', 'Failed to create illness type'),
	});
};

export const useUpdateIllnessType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: ({
			illnessTypeId,
			data,
		}: {
			illnessTypeId: number;
			data: { name?: string; description?: string };
		}) => updateIllnessType(illnessTypeId, data),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.ILLNESS_TYPES,
			});
			toast.success('Illness Type Updated', {
				description: 'Illness type has been updated successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Update Error', 'Failed to update illness type'),
	});
};

export const useRemoveIllnessType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: removeIllnessType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.ILLNESS_TYPES,
			});
			toast.success('Illness Type Removed', {
				description: 'Illness type has been removed successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Remove Error', 'Failed to remove illness type'),
	});
};

// Data Management Hooks - Lab Request Types
export const useAllLabRequestTypes = () => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.LAB_REQUEST_TYPES,
		queryFn: getAllLabRequestTypes,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useCreateLabRequestType = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createLabRequestType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.LAB_REQUEST_TYPES,
			});
			toast.success('Lab Request Type Created', {
				description: 'Lab request type has been created successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Create Error', 'Failed to create lab request type'),
	});
};

export const useUpdateLabRequestType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: ({
			labRequestTypeId,
			data,
		}: {
			labRequestTypeId: number;
			data: { name?: string; description?: string };
		}) => updateLabRequestType(labRequestTypeId, data),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.LAB_REQUEST_TYPES,
			});
			toast.success('Lab Request Type Updated', {
				description: 'Lab request type has been updated successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Update Error', 'Failed to update lab request type'),
	});
};

export const useRemoveLabRequestType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: removeLabRequestType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.LAB_REQUEST_TYPES,
			});
			toast.success('Lab Request Type Removed', {
				description: 'Lab request type has been removed successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Remove Error', 'Failed to remove lab request type'),
	});
};

// Data Management Hooks - Prescription Types
export const useAllPrescriptionTypes = () => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.PRESCRIPTION_TYPES,
		queryFn: getAllPrescriptionTypes,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useCreatePrescriptionType = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createPrescriptionType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.PRESCRIPTION_TYPES,
			});
			toast.success('Prescription Type Created', {
				description: 'Prescription type has been created successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Create Error', 'Failed to create prescription type'),
	});
};

export const useUpdatePrescriptionType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: ({
			prescriptionTypeId,
			data,
		}: {
			prescriptionTypeId: number;
			data: { name?: string; description?: string };
		}) => updatePrescriptionType(prescriptionTypeId, data),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.PRESCRIPTION_TYPES,
			});
			toast.success('Prescription Type Updated', {
				description: 'Prescription type has been updated successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Update Error', 'Failed to update prescription type'),
	});
};

export const useRemovePrescriptionType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: removePrescriptionType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.PRESCRIPTION_TYPES,
			});
			toast.success('Prescription Type Removed', {
				description: 'Prescription type has been removed successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Remove Error', 'Failed to remove prescription type'),
	});
};

// Data Management Hooks - Surgery Types
export const useAllSurgeryTypes = () => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.SURGERY_TYPES,
		queryFn: getAllSurgeryTypes,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useCreateSurgeryType = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createSurgeryType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.SURGERY_TYPES,
			});
			toast.success('Surgery Type Created', {
				description: 'Surgery type has been created successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Create Error', 'Failed to create surgery type'),
	});
};

export const useUpdateSurgeryType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: ({
			surgeryTypeId,
			data,
		}: {
			surgeryTypeId: number;
			data: { name?: string; description?: string };
		}) => updateSurgeryType(surgeryTypeId, data),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.SURGERY_TYPES,
			});
			toast.success('Surgery Type Updated', {
				description: 'Surgery type has been updated successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Update Error', 'Failed to update surgery type'),
	});
};

export const useRemoveSurgeryType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: removeSurgeryType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.SURGERY_TYPES,
			});
			toast.success('Surgery Type Removed', {
				description: 'Surgery type has been removed successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Remove Error', 'Failed to remove surgery type'),
	});
};

// Admin Patient Profile Management Hooks

// Habits Management Hooks
export const useAdminAddHabitToPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			habitData,
		}: {
			profileId: number;
			habitData: {
				name: string;
				description?: string;
			};
		}) => addHabitToPatient(profileId, habitData),
		onSuccess: (_, { profileId }) => {
			toast.success('Habit Added', {
				description: 'The habit has been successfully added to the patient.',
			});

			// Invalidate patient detail and habits
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.HABITS(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Add Habit Error', 'Failed to add habit');
		},
	});
};

export const useAdminUpdatePatientHabit = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientHabitId,
			habitData,
		}: {
			profileId: number;
			patientHabitId: number;
			habitData: {
				habitTypeId?: number;
				description?: string;
				frequency?: string;
				duration?: string;
			};
		}) => updatePatientHabit(profileId, patientHabitId, habitData),
		onSuccess: (_, { profileId }) => {
			toast.success('Habit Updated', {
				description: 'The habit has been successfully updated.',
			});

			// Invalidate patient detail and habits
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.HABITS(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Update Habit Error', 'Failed to update habit');
		},
	});
};

export const useAdminRemoveHabitFromPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientHabitId,
		}: {
			profileId: number;
			patientHabitId: number;
		}) => removeHabitFromPatient(profileId, patientHabitId),
		onSuccess: (_, { profileId }) => {
			toast.success('Habit Removed', {
				description:
					'The habit has been successfully removed from the patient.',
			});

			// Invalidate patient detail and habits
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.HABITS(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Habit Error', 'Failed to remove habit');
		},
	});
};

// Allergies Management Hooks
export const useAdminAddAllergyToPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			allergyData,
		}: {
			profileId: number;
			allergyData: {
				name: string;
				description?: string;
			};
		}) => addAllergyToPatient(profileId, allergyData),
		onSuccess: (_, { profileId }) => {
			toast.success('Allergy Added', {
				description: 'The allergy has been successfully added to the patient.',
			});

			// Invalidate patient detail and allergies
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.ALLERGIES(profileId),
			});
		},
		onError: (error: unknown) => {
			console.error('Add allergy mutation error:', error);
			if (error && typeof error === 'object' && 'response' in error) {
				console.error(
					'Error response:',
					(error as { response?: { data?: unknown; status?: number } }).response
				);
			}
			catchError(error, 'Add Allergy Error', 'Failed to add allergy');
		},
	});
};

export const useAdminUpdatePatientAllergy = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientAllergyId,
			allergyData,
		}: {
			profileId: number;
			patientAllergyId: number;
			allergyData: {
				name?: string;
				description?: string;
			};
		}) => updatePatientAllergy(profileId, patientAllergyId, allergyData),
		onSuccess: (_, { profileId }) => {
			toast.success('Allergy Updated', {
				description: 'The allergy has been successfully updated.',
			});

			// Invalidate patient detail and allergies
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.ALLERGIES(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Update Allergy Error', 'Failed to update allergy');
		},
	});
};

export const useAdminRemoveAllergyFromPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientAllergyId,
		}: {
			profileId: number;
			patientAllergyId: number;
		}) => removeAllergyFromPatient(profileId, patientAllergyId),
		onSuccess: (_, { profileId }) => {
			toast.success('Allergy Removed', {
				description:
					'The allergy has been successfully removed from the patient.',
			});

			// Invalidate patient detail and allergies
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.ALLERGIES(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Allergy Error', 'Failed to remove allergy');
		},
	});
};

// Illness Management Hooks
export const useAdminAddIllnessToPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			illnessData,
		}: {
			profileId: number;
			illnessData: {
				name: string;
				description?: string;
			};
		}) => addIllnessToPatient(profileId, illnessData),
		onSuccess: (_, { profileId }) => {
			toast.success('Illness Added', {
				description: 'The illness has been successfully added to the patient.',
			});

			// Invalidate patient detail and illnesses
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.ILLNESSES(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Add Illness Error', 'Failed to add illness');
		},
	});
};

export const useAdminUpdatePatientIllness = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientIllnessId,
			illnessData,
		}: {
			profileId: number;
			patientIllnessId: number;
			illnessData: {
				name?: string;
				description?: string;
			};
		}) => updatePatientIllness(profileId, patientIllnessId, illnessData),
		onSuccess: (_, { profileId }) => {
			toast.success('Illness Updated', {
				description: 'The illness has been successfully updated.',
			});

			// Invalidate patient detail and illnesses
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.ILLNESSES(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Update Illness Error', 'Failed to update illness');
		},
	});
};

export const useAdminRemoveIllnessFromPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientIllnessId,
		}: {
			profileId: number;
			patientIllnessId: number;
		}) => removeIllnessFromPatient(profileId, patientIllnessId),
		onSuccess: (_, { profileId }) => {
			toast.success('Illness Removed', {
				description:
					'The illness has been successfully removed from the patient.',
			});

			// Invalidate patient detail and illnesses
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.ILLNESSES(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Illness Error', 'Failed to remove illness');
		},
	});
};

// History Management Hooks
export const useAdminAddHistoryToPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			historyData,
		}: {
			profileId: number;
			historyData: {
				name: string;
				description?: string;
			};
		}) => addHistoryToPatient(profileId, historyData),
		onSuccess: (_, { profileId }) => {
			toast.success('History Added', {
				description: 'The history has been successfully added to the patient.',
			});

			// Invalidate patient detail and history
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.HISTORY(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Add History Error', 'Failed to add history');
		},
	});
};

export const useAdminUpdatePatientHistory = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientHistoryId,
			historyData,
		}: {
			profileId: number;
			patientHistoryId: number;
			historyData: {
				historyTypeId?: number;
				description?: string;
				dateOccurred?: string;
			};
		}) => updatePatientHistory(profileId, patientHistoryId, historyData),
		onSuccess: (_, { profileId }) => {
			toast.success('History Updated', {
				description: 'The history has been successfully updated.',
			});

			// Invalidate patient detail and history
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.HISTORY(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Update History Error', 'Failed to update history');
		},
	});
};

export const useAdminRemoveHistoryFromPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientHistoryId,
		}: {
			profileId: number;
			patientHistoryId: number;
		}) => removeHistoryFromPatient(profileId, patientHistoryId),
		onSuccess: (_, { profileId }) => {
			toast.success('History Removed', {
				description:
					'The history has been successfully removed from the patient.',
			});

			// Invalidate patient detail and history
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.HISTORY(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove History Error', 'Failed to remove history');
		},
	});
};

// Surgery Management Hooks
export const useAdminAddSurgeryToPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			surgeryData,
		}: {
			profileId: number;
			surgeryData: {
				name: string;
				description?: string;
			};
		}) => addSurgeryToPatient(profileId, surgeryData),
		onSuccess: (_, { profileId }) => {
			toast.success('Surgery Added', {
				description: 'The surgery has been successfully added to the patient.',
			});

			// Invalidate patient detail and surgeries
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.SURGERIES(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Add Surgery Error', 'Failed to add surgery');
		},
	});
};

export const useAdminUpdatePatientSurgery = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientSurgeryId,
			surgeryData,
		}: {
			profileId: number;
			patientSurgeryId: number;
			surgeryData: {
				name?: string;
				description?: string;
			};
		}) => updatePatientSurgery(profileId, patientSurgeryId, surgeryData),
		onSuccess: (_, { profileId }) => {
			toast.success('Surgery Updated', {
				description: 'The surgery has been successfully updated.',
			});

			// Invalidate patient detail and surgeries
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.SURGERIES(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Update Surgery Error', 'Failed to update surgery');
		},
	});
};

export const useAdminRemoveSurgeryFromPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientSurgeryId,
		}: {
			profileId: number;
			patientSurgeryId: number;
		}) => removeSurgeryFromPatient(profileId, patientSurgeryId),
		onSuccess: (_, { profileId }) => {
			toast.success('Surgery Removed', {
				description:
					'The surgery has been successfully removed from the patient.',
			});

			// Invalidate patient detail and surgeries
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.SURGERIES(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Surgery Error', 'Failed to remove surgery');
		},
	});
};

// Diet Management Hooks
export const useAdminAddDietToPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			dietData,
		}: {
			profileId: number;
			dietData: {
				name: string;
				description?: string;
			};
		}) => addDietToPatient(profileId, dietData),
		onSuccess: (_, { profileId }) => {
			toast.success('Diet Added', {
				description: 'The diet has been successfully added to the patient.',
			});

			// Invalidate patient detail and diets
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DIETS(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Add Diet Error', 'Failed to add diet');
		},
	});
};

export const useAdminUpdatePatientDiet = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientDietId,
			dietData,
		}: {
			profileId: number;
			patientDietId: number;
			dietData: {
				dietTypeId?: number;
				description?: string;
				restrictions?: string;
			};
		}) => updatePatientDiet(profileId, patientDietId, dietData),
		onSuccess: (_, { profileId }) => {
			toast.success('Diet Updated', {
				description: 'The diet has been successfully updated.',
			});

			// Invalidate patient detail and diets
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DIETS(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Update Diet Error', 'Failed to update diet');
		},
	});
};

export const useAdminRemoveDietFromPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientDietId,
		}: {
			profileId: number;
			patientDietId: number;
		}) => removeDietFromPatient(profileId, patientDietId),
		onSuccess: (_, { profileId }) => {
			toast.success('Diet Removed', {
				description: 'The diet has been successfully removed from the patient.',
			});

			// Invalidate patient detail and diets
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DIETS(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove Diet Error', 'Failed to remove diet');
		},
	});
};

// HMO Management Hooks
export const useAllHmoTypes = () => {
	return useQuery({
		queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.HMO_TYPES,
		queryFn: getAllHmoTypes,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

export const useCreateHmoType = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createHmoType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.HMO_TYPES,
			});
			toast.success('HMO Type Created', {
				description: 'HMO type has been created successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Create Error', 'Failed to create HMO type'),
	});
};

export const useUpdateHmoType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: ({
			habitTypeId,
			data,
		}: {
			habitTypeId: number;
			data: { name?: string; description?: string };
		}) => updateHmoType(habitTypeId, data),
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.HMO_TYPES,
			});
			toast.success('HMO Type Updated', {
				description: 'HMO type has been updated successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Update Error', 'Failed to update HMO type'),
	});
};

export const useRemoveHmoType = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: removeHmoType,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.DATA_MANAGEMENT.HMO_TYPES,
			});
			toast.success('HMO Type Removed', {
				description: 'HMO type has been removed successfully.',
			});
		},
		onError: (error) =>
			catchError(error, 'Remove Error', 'Failed to remove hmo type'),
	});
};

export const useAdminAddHmoToPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			hmoData,
		}: {
			profileId: number;
			hmoData: {
				hmoId: string;
				hmoCompany: string;
				hmoDetail: string;
				hmoProvider: string;
			};
		}) => addHmoToPatient(profileId, hmoData),
		onSuccess: (_, { profileId }) => {
			toast.success('HMO Added', {
				description: 'The HMO has been successfully added to the patient.',
			});

			// Invalidate patient detail and HMOs
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.HMO(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Add HMO Error', 'Failed to add HMO');
		},
	});
};

export const useAdminUpdatePatientHmo = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientHmoId,
			hmoData,
		}: {
			profileId: number;
			patientHmoId: number;
			hmoData: {
				hmoId?: string;
				hmoCompany?: string;
				hmoDetail?: string;
				hmoProvider?: string;
			};
		}) => updatePatientHmo(profileId, patientHmoId, hmoData),
		onSuccess: (_, { profileId }) => {
			toast.success('HMO Updated', {
				description: 'The HMO has been successfully updated.',
			});

			// Invalidate patient detail and HMOs
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.HMO(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Update HMO Error', 'Failed to update HMO');
		},
	});
};

export const useAdminRemoveHmoFromPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientHmoId,
		}: {
			profileId: number;
			patientHmoId: number;
		}) => removeHmoFromPatient(profileId, patientHmoId),
		onSuccess: (_, { profileId }) => {
			toast.success('HMO Removed', {
				description: 'The HMO has been successfully removed from the patient.',
			});

			// Invalidate patient detail and HMOs
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.HMO(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Remove HMO Error', 'Failed to remove HMO');
		},
	});
};

// Family Management Hooks
export const useAdminAddFamilyToPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			familyData,
		}: {
			profileId: number;
			familyData: {
				familyProfileId: number;
				relationship: string;
			};
		}) => addFamilyToPatient(profileId, familyData),
		onSuccess: (_, { profileId }) => {
			toast.success('Family Member Added', {
				description:
					'The family member has been successfully added to the patient.',
			});

			// Invalidate patient detail and family
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.FAMILY(profileId),
			});
		},
		onError: (error) => {
			catchError(error, 'Add Family Error', 'Failed to add family member');
		},
	});
};

export const useAdminRemoveFamilyFromPatient = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			profileId,
			patientFamilyId,
		}: {
			profileId: number;
			patientFamilyId: number;
		}) => removeFamilyFromPatient(profileId, patientFamilyId),
		onSuccess: (_, { profileId }) => {
			toast.success('Family Member Removed', {
				description:
					'The family member has been successfully removed from the patient.',
			});

			// Invalidate patient detail and family
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.DETAIL(profileId),
			});
			queryClient.invalidateQueries({
				queryKey: ADMIN_QUERY_KEYS.PATIENTS.FAMILY(profileId),
			});
		},
		onError: (error) => {
			catchError(
				error,
				'Remove Family Error',
				'Failed to remove family member'
			);
		},
	});
};
