'use client';

import { ReactNode } from 'react';

import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { cn } from '@/core/lib/utils';

type HorizontalScrollBarProps = {
	children: ReactNode;
	scrollClassName?: string;
};

export default function HorizontalScrollBar({
	children,
	scrollClassName = '',
}: HorizontalScrollBarProps) {
	return (
		<div className="flex">
			<ScrollArea className={cn('w-1 flex-1', scrollClassName)}>
				{children}
				<ScrollBar orientation="horizontal" />
			</ScrollArea>
		</div>
	);
}
