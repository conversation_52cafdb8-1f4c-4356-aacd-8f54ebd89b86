'use client';

import { <PERSON>ert<PERSON>ircle, Camera, QrCode, Upload } from 'lucide-react';
import { useState } from 'react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
	Card,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { useQRCodeVerification } from '@/features/dashboard/hooks/useQRAppointment';

import { QRScannerDialog } from './qr-scanner-dialog';
import { QRUploadDialog } from './qr-upload-dialog';

interface IQRAppointmentDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onPatientVerified: (patientId: string) => void;
}

export function QRAppointmentDialog({
	open,
	onOpenChange,
	onPatientVerified,
}: IQRAppointmentDialogProps) {
	const [scannerOpen, setScannerOpen] = useState(false);
	const [uploadOpen, setUploadOpen] = useState(false);
	const [verificationError, setVerificationError] = useState<string | null>(
		null
	);

	const { mutate: verifyQRCode, isPending: isVerifying } =
		useQRCodeVerification();

	const handleQRCodeScanned = (qrCode: string) => {
		setVerificationError(null);

		verifyQRCode(qrCode, {
			onSuccess: (response) => {
				if (response.success && response.data?.patient) {
					onPatientVerified(response.data.patient);
					onOpenChange(false);
				} else {
					setVerificationError(
						response.message || 'Invalid QR code. Please try again.'
					);
				}
			},
			onError: (error: unknown) => {
				const errorMessage =
					(error as { response?: { data?: { message?: string } } })?.response
						?.data?.message || 'Failed to verify QR code. Please try again.';
				setVerificationError(errorMessage);
			},
		});
	};

	const handleOpenScanner = () => {
		setVerificationError(null);
		setScannerOpen(true);
	};

	const handleOpenUpload = () => {
		setVerificationError(null);
		setUploadOpen(true);
	};

	return (
		<>
			<Dialog open={open} onOpenChange={onOpenChange}>
				<DialogContent className="max-w-md">
					<DialogHeader>
						<DialogTitle className="flex items-center gap-2">
							<QrCode className="h-5 w-5 text-[oklch(0.7448_0.1256_202.74)]" />
							Create Appointment via QR Code
						</DialogTitle>
						<DialogDescription>
							Scan or upload a patient&apos;s QR code to quickly create an
							appointment.
						</DialogDescription>
					</DialogHeader>

					<div className="space-y-4">
						{verificationError && (
							<Alert variant="destructive">
								<AlertCircle className="h-4 w-4" />
								<AlertDescription>{verificationError}</AlertDescription>
							</Alert>
						)}

						{isVerifying && (
							<div className="py-4 text-center">
								<p className="text-muted-foreground text-sm">
									Verifying QR code...
								</p>
							</div>
						)}

						<div className="grid gap-3">
							<Card
								className="hover:bg-muted/50 cursor-pointer transition-colors"
								onClick={handleOpenScanner}
							>
								<CardHeader className="pb-3">
									<CardTitle className="flex items-center gap-3 text-base">
										<div className="flex h-10 w-10 items-center justify-center rounded-lg bg-[oklch(0.7448_0.1256_202.74)]/10">
											<Camera className="h-5 w-5 text-[oklch(0.7448_0.1256_202.74)]" />
										</div>
										Scan QR Code
									</CardTitle>
									<CardDescription>
										Use your camera to scan the patient&apos;s QR code
									</CardDescription>
								</CardHeader>
							</Card>

							<Card
								className="hover:bg-muted/50 cursor-pointer transition-colors"
								onClick={handleOpenUpload}
							>
								<CardHeader className="pb-3">
									<CardTitle className="flex items-center gap-3 text-base">
										<div className="flex h-10 w-10 items-center justify-center rounded-lg bg-[oklch(0.7448_0.1256_202.74)]/10">
											<Upload className="h-5 w-5 text-[oklch(0.7448_0.1256_202.74)]" />
										</div>
										Upload QR Code
									</CardTitle>
									<CardDescription>
										Upload an image containing the patient&apos;s QR code
									</CardDescription>
								</CardHeader>
							</Card>
						</div>

						<div className="pt-2">
							<Button
								variant="outline"
								onClick={() => onOpenChange(false)}
								className="w-full"
							>
								Cancel
							</Button>
						</div>
					</div>
				</DialogContent>
			</Dialog>

			{/* QR Scanner Dialog */}
			<QRScannerDialog
				open={scannerOpen}
				onOpenChange={setScannerOpen}
				onQRCodeScanned={handleQRCodeScanned}
			/>

			{/* QR Upload Dialog */}
			<QRUploadDialog
				open={uploadOpen}
				onOpenChange={setUploadOpen}
				onQRCodeScanned={handleQRCodeScanned}
			/>
		</>
	);
}
