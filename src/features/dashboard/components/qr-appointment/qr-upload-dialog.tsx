'use client';

import { AlertCircle, Upload, X } from 'lucide-react';
import Image from 'next/image';
import QrScanner from 'qr-scanner';
import { useState } from 'react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { But<PERSON> } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface IQRUploadDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onQRCodeScanned: (qrCode: string) => void;
}

export function QRUploadDialog({
	open,
	onOpenChange,
	onQRCodeScanned,
}: IQRUploadDialogProps) {
	const [selectedFile, setSelectedFile] = useState<File | null>(null);
	const [previewUrl, setPreviewUrl] = useState<string | null>(null);
	const [isProcessing, setIsProcessing] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const handleFileSelect = async (
		event: React.ChangeEvent<HTMLInputElement>
	) => {
		const file = event.target.files?.[0];
		if (!file) return;

		// Validate file type
		if (!file.type.startsWith('image/')) {
			setError('Please select a valid image file.');
			return;
		}

		// Validate file size (max 5MB)
		if (file.size > 5 * 1024 * 1024) {
			setError('File size must be less than 5MB.');
			return;
		}

		setSelectedFile(file);
		setError(null);

		// Create preview URL
		const url = URL.createObjectURL(file);
		setPreviewUrl(url);

		// Process QR code
		await processQRCode(file);
	};

	const processQRCode = async (file: File) => {
		setIsProcessing(true);
		setError(null);

		try {
			const result = await QrScanner.scanImage(file, {
				returnDetailedScanResult: true,
			});

			if (result?.data) {
				onQRCodeScanned(result.data);
				onOpenChange(false);
			} else {
				setError('No QR code found in the image. Please try another image.');
			}
		} catch (err) {
			console.error('QR Code processing error:', err);
			setError(
				'Failed to read QR code from image. Please ensure the image contains a valid QR code.'
			);
		} finally {
			setIsProcessing(false);
		}
	};

	const handleClose = () => {
		// Clean up preview URL
		if (previewUrl) {
			URL.revokeObjectURL(previewUrl);
		}
		setSelectedFile(null);
		setPreviewUrl(null);
		setError(null);
		setIsProcessing(false);
		onOpenChange(false);
	};

	const handleRetry = () => {
		setError(null);
		setSelectedFile(null);
		if (previewUrl) {
			URL.revokeObjectURL(previewUrl);
		}
		setPreviewUrl(null);
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-md">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<Upload className="h-5 w-5 text-[oklch(0.7448_0.1256_202.74)]" />
						Upload Patient QR Code
					</DialogTitle>
					<DialogDescription>
						Upload an image containing the patient&apos;s QR code to create an
						appointment.
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-4">
					{!selectedFile ? (
						<div className="space-y-4">
							<div className="border-muted-foreground/25 flex flex-col items-center justify-center rounded-lg border-2 border-dashed p-8 text-center">
								<Upload className="text-muted-foreground mb-2 h-8 w-8" />
								<Label htmlFor="qr-upload" className="cursor-pointer">
									<span className="text-sm font-medium text-[oklch(0.7448_0.1256_202.74)]">
										Click to upload
									</span>
									<span className="text-muted-foreground text-sm">
										{' '}
										or drag and drop
									</span>
								</Label>
								<p className="text-muted-foreground mt-1 text-xs">
									PNG, JPG, JPEG up to 5MB
								</p>
							</div>
							<Input
								id="qr-upload"
								type="file"
								accept="image/*"
								onChange={handleFileSelect}
								className="hidden"
							/>
						</div>
					) : (
						<div className="space-y-4">
							{previewUrl && (
								<div className="relative aspect-square overflow-hidden rounded-lg border">
									<Image
										src={previewUrl}
										alt="QR Code Preview"
										fill
										className="object-contain"
									/>
								</div>
							)}

							{error && (
								<Alert variant="destructive">
									<AlertCircle className="h-4 w-4" />
									<AlertDescription>{error}</AlertDescription>
								</Alert>
							)}

							{isProcessing && (
								<div className="text-center">
									<p className="text-muted-foreground text-sm">
										Processing QR code...
									</p>
								</div>
							)}

							<div className="flex gap-2">
								{error && (
									<Button onClick={handleRetry} className="flex-1">
										Try Another Image
									</Button>
								)}
								<Button
									variant="outline"
									onClick={handleClose}
									className="flex-1"
								>
									<X className="mr-2 h-4 w-4" />
									Cancel
								</Button>
							</div>
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}
