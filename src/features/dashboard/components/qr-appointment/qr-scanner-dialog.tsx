'use client';

import { <PERSON>anner } from '@yudiel/react-qr-scanner';
import { AlertCircle, Camera, X } from 'lucide-react';
import { useEffect, useState } from 'react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { But<PERSON> } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';

interface IQRScannerDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onQRCodeScanned: (qrCode: string) => void;
}

export function QRScannerDialog({
	open,
	onOpenChange,
	onQRCodeScanned,
}: IQRScannerDialogProps) {
	const [scanning, setScanning] = useState(true);
	const [error, setError] = useState<string | null>(null);

	// Reset state when dialog opens
	useEffect(() => {
		if (open) {
			setScanning(true);
			setError(null);
		}
	}, [open]);

	const handleScan = (result: unknown) => {
		if (!scanning) return;

		try {
			const resultArray = result as { rawValue: string }[];
			if (resultArray?.[0]?.rawValue) {
				const qrCode = resultArray[0].rawValue;
				setScanning(false);
				onQRCodeScanned(qrCode);
				onOpenChange(false);
			}
		} catch {
			setError('Failed to read QR code. Please try again.');
			setScanning(false);
		}
	};

	const handleError = (error: unknown) => {
		console.error('QR Scanner Error:', error);
		setError(
			'Camera access denied or not available. Please check your camera permissions.'
		);
		setScanning(false);
	};

	const handleClose = () => {
		setScanning(false);
		onOpenChange(false);
	};

	const handleRetry = () => {
		setError(null);
		setScanning(true);
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-md">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<Camera className="h-5 w-5 text-[oklch(0.7448_0.1256_202.74)]" />
						Scan Patient QR Code
					</DialogTitle>
					<DialogDescription>
						Position the patient&apos;s QR code within the camera frame to
						create an appointment.
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-4">
					{error ? (
						<div className="space-y-4">
							<Alert variant="destructive">
								<AlertCircle className="h-4 w-4" />
								<AlertDescription>{error}</AlertDescription>
							</Alert>
							<div className="flex gap-2">
								<Button onClick={handleRetry} className="flex-1">
									Try Again
								</Button>
								<Button variant="outline" onClick={handleClose}>
									Cancel
								</Button>
							</div>
						</div>
					) : (
						<>
							<div className="relative aspect-square overflow-hidden rounded-lg border bg-black">
								{scanning && (
									<Scanner
										allowMultiple={false}
										components={{
											finder: true,
										}}
										onScan={handleScan}
										onError={handleError}
										styles={{
											container: {
												width: '100%',
												height: '100%',
											},
										}}
									/>
								)}

								{/* Scanning overlay */}
								<div className="absolute inset-0 flex items-center justify-center">
									<div className="h-48 w-48 rounded-lg border-2 border-[oklch(0.7448_0.1256_202.74)]">
										<div className="absolute -top-1 -left-1 h-6 w-6 border-t-4 border-l-4 border-[oklch(0.7448_0.1256_202.74)]"></div>
										<div className="absolute -top-1 -right-1 h-6 w-6 border-t-4 border-r-4 border-[oklch(0.7448_0.1256_202.74)]"></div>
										<div className="absolute -bottom-1 -left-1 h-6 w-6 border-b-4 border-l-4 border-[oklch(0.7448_0.1256_202.74)]"></div>
										<div className="absolute -right-1 -bottom-1 h-6 w-6 border-r-4 border-b-4 border-[oklch(0.7448_0.1256_202.74)]"></div>
									</div>
								</div>
							</div>

							<div className="text-center">
								<p className="text-muted-foreground text-sm">
									{scanning ? 'Scanning for QR code...' : 'QR code detected!'}
								</p>
							</div>

							<Button
								variant="outline"
								onClick={handleClose}
								className="w-full"
							>
								<X className="mr-2 h-4 w-4" />
								Cancel
							</Button>
						</>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}
