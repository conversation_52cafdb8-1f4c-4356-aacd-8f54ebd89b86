'use client';

import { Clock, Eye, Plus, QrCode } from 'lucide-react';
import { useQueryState, useQueryStates } from 'nuqs';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import { searchParamParsers } from '@/core/lib/search-params';
import { useDashboardPermissions } from '@/features/dashboard/context/DashboardRoleContext';
import {
	useAppointments,
	useClinics,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import {
	EAppointmentStatus,
	IAppointment,
} from '@/features/dashboard/types/doctor.types';

import HorizontalScrollBar from '../HorizontalScrollBar';

interface IAppointmentQueueProps {
	onViewAppointment?: (appointmentId: number) => void;
	onCreateAppointment?: () => void;
	onQRAppointment?: () => void;
}

interface IAppointmentRowProps {
	appointment: IAppointment;
	onView: (appointmentId: number) => void;
	canUpdateStatus: boolean;
}

const AppointmentRow = ({ appointment, onView }: IAppointmentRowProps) => {
	const getStatusBadge = (status: string) => {
		const statusConfig: Record<
			string,
			{
				variant: 'secondary' | 'default' | 'outline' | 'destructive';
				label: string;
			}
		> = {
			[EAppointmentStatus.CREATED]: {
				variant: 'secondary' as const,
				label: 'Created',
			},
			[EAppointmentStatus.CONFIRMED]: {
				variant: 'default' as const,
				label: 'Confirmed',
			},
			[EAppointmentStatus.DECLINED]: {
				variant: 'destructive' as const,
				label: 'Declined',
			},
			[EAppointmentStatus.WAITING]: {
				variant: 'outline' as const,
				label: 'Waiting',
			},
			[EAppointmentStatus.ONGOING]: {
				variant: 'default' as const,
				label: 'Ongoing',
			},
			[EAppointmentStatus.COMPLETED]: {
				variant: 'secondary' as const,
				label: 'Completed',
			},
			[EAppointmentStatus.CANCELLED]: {
				variant: 'destructive' as const,
				label: 'Cancelled',
			},
			[EAppointmentStatus.NO_SHOW]: {
				variant: 'destructive' as const,
				label: 'No Show',
			},
			[EAppointmentStatus.FOLLOW_UP]: {
				variant: 'outline' as const,
				label: 'Follow-up',
			},
			[EAppointmentStatus.RESCHEDULED]: {
				variant: 'outline' as const,
				label: 'Rescheduled',
			},
		};

		const config = statusConfig[status] || {
			variant: 'secondary' as const,
			label: status,
		};

		return (
			<Badge className="text-xxs font-bold" variant={config.variant}>
				{config.label}
			</Badge>
		);
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
		});
	};

	console.log('Data: ', appointment);

	return (
		<TableRow>
			<TableCell>
				<div className="flex items-center gap-3">
					<img
						className="bg-muted flex h-8 w-8 rounded-full"
						alt="patient avatar"
						src={
							appointment.patient?.profile?.avatar || '/placeholder-profile.jpg'
						}
					/>
					<div>
						<p className="w-[150px] font-medium">
							{appointment.patient?.profile?.first_name}{' '}
							{appointment.patient?.profile?.last_name}
						</p>
						<p className="text-muted-foreground text-sm">
							{appointment.patient?.profile?.gender},{' '}
							{appointment.patient?.profile?.birthday &&
								new Date().getFullYear() -
									new Date(
										appointment.patient?.profile?.birthday
									).getFullYear() +
									' years'}
						</p>
					</div>
				</div>
			</TableCell>
			<TableCell>
				<div>
					<p className="font-medium">{appointment.clinic?.name}</p>
					<p className="text-muted-foreground max-w-48 overflow-hidden text-sm text-ellipsis">
						{appointment.clinic?.address}
					</p>
				</div>
			</TableCell>
			<TableCell>
				<div className="flex items-center gap-2">
					<Clock className="h-4 w-4" />
					<span>{formatDate(appointment.appointment_date)}</span>
				</div>
			</TableCell>
			<TableCell>
				<div>
					<p className="font-medium">
						{appointment.visitReason?.name || 'N/A'}
					</p>
					<p className="text-muted-foreground text-sm">
						{appointment.consultationType?.name || 'N/A'}
					</p>
				</div>
			</TableCell>
			<TableCell>{appointment.medical_certificate ? 'Yes' : 'No'}</TableCell>
			<TableCell className="uppercase">
				{getStatusBadge(appointment.status)}
			</TableCell>
			<TableCell className="flex items-center justify-center">
				<Button
					onClick={() => onView(appointment.id)}
					variant="outline"
					className="h-8 w-8 cursor-pointer p-0"
				>
					<Eye className="h-4 w-4" />
				</Button>
			</TableCell>
		</TableRow>
	);
};

const AppointmentColumn = ({ appointment, onView }: IAppointmentRowProps) => {
	const getStatusBadge = (status: string) => {
		const statusConfig: Record<
			string,
			{
				variant: 'secondary' | 'default' | 'outline' | 'destructive';
				label: string;
			}
		> = {
			[EAppointmentStatus.CREATED]: {
				variant: 'secondary' as const,
				label: 'Created',
			},
			[EAppointmentStatus.CONFIRMED]: {
				variant: 'default' as const,
				label: 'Confirmed',
			},
			[EAppointmentStatus.DECLINED]: {
				variant: 'destructive' as const,
				label: 'Declined',
			},
			[EAppointmentStatus.WAITING]: {
				variant: 'outline' as const,
				label: 'Waiting',
			},
			[EAppointmentStatus.ONGOING]: {
				variant: 'default' as const,
				label: 'Ongoing',
			},
			[EAppointmentStatus.COMPLETED]: {
				variant: 'secondary' as const,
				label: 'Completed',
			},
			[EAppointmentStatus.CANCELLED]: {
				variant: 'destructive' as const,
				label: 'Cancelled',
			},
			[EAppointmentStatus.NO_SHOW]: {
				variant: 'destructive' as const,
				label: 'No Show',
			},
			[EAppointmentStatus.FOLLOW_UP]: {
				variant: 'outline' as const,
				label: 'Follow-up',
			},
			[EAppointmentStatus.RESCHEDULED]: {
				variant: 'outline' as const,
				label: 'Rescheduled',
			},
		};

		const config = statusConfig[status] || {
			variant: 'secondary' as const,
			label: status,
		};

		return (
			<Badge className="text-xs font-bold" variant={config.variant}>
				{config.label}
			</Badge>
		);
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
		});
	};

	return (
		<div className="flex flex-col gap-2 rounded-lg border p-4">
			<div className="flex items-center gap-3">
				<img
					className="bg-muted flex h-8 w-8 rounded-full"
					alt="patient avatar"
					src={
						appointment.patient?.profile?.avatar || '/placeholder-profile.jpg'
					}
				/>
				<div>
					<p className="font-medium">
						{appointment.patient?.profile?.first_name}{' '}
						{appointment.patient?.profile?.last_name}
					</p>
					<p className="text-muted-foreground text-sm">
						{appointment.patient?.profile?.gender},{' '}
						{appointment.patient?.profile?.birthday &&
							new Date().getFullYear() -
								new Date(appointment.patient?.profile?.birthday).getFullYear() +
								' years'}
					</p>
				</div>
			</div>
			<div className="flex items-center gap-2">
				<Clock className="h-4 w-4" />
				<span>{formatDate(appointment.appointment_date)}</span>
			</div>
			<div>
				<p className="font-medium">{appointment.clinic?.name}</p>
				<p className="text-muted-foreground text-sm">
					{appointment.clinic?.address}
				</p>
			</div>

			<div>
				<p className="font-medium">{appointment.visitReason?.name || 'N/A'}</p>
				<p className="text-muted-foreground text-sm">
					{appointment.consultationType?.name || 'N/A'}
				</p>
			</div>
			{getStatusBadge(appointment.status)}
			<Button onClick={() => onView(appointment.id)}>
				<Eye className="mr-2 h-4 w-4" />
				View Details
			</Button>
		</div>
	);
};

export function AppointmentList({
	onViewAppointment,
	onCreateAppointment,
	onQRAppointment,
}: IAppointmentQueueProps) {
	const permissions = useDashboardPermissions();
	const [searchTerm, setSearchTerm] = useState('');
	// const [selectedClinic, setSelectedClinic] = useState<string>();
	const [, setAppointmentId] = useQueryState(
		'appointmentId',
		searchParamParsers.appointmentId
	);

	const [{ page, clinicId = 0, category }, setSearchParams] = useQueryStates({
		page: searchParamParsers.page,
		clinicId: searchParamParsers.clinicId,
		category: searchParamParsers.category,
	});

	const pageSize = 10;

	// Use role-aware hooks
	const { data: appointmentsData } = useAppointments({
		...(searchTerm ? { search: searchTerm } : {}),
		...(Number(clinicId) ? { clinicId: Number(clinicId) } : {}),
		category,
		page: page || 1,
		pageSize,
	});

	const meta = appointmentsData?.data?.meta;

	const currentPage = page || 1;

	const { data: clinicsData } = useClinics();

	const handleViewAppointment = (id: number) => {
		if (onViewAppointment) {
			onViewAppointment(id);
		} else {
			setAppointmentId(id);
		}
	};

	const handlePageChange = (newPage: number) => {
		setSearchParams({ page: newPage });
	};

	const handleClinicChange = (value: string) => {
		setSearchParams({ page: 1, clinicId: Number(value) });
	};

	const handleAppointmentTypeChange = (value: string) => {
		setSearchParams({ page: 1, category: value });
	};

	return (
		<div className="flex flex-col gap-4 px-4">
			<div className="mb-4 flex flex-col items-start justify-between gap-4 lg:flex-row">
				<div>
					<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
						Appointments
					</h1>
					<p className="text-muted-foreground">Appointment List</p>
				</div>
				<div className="flex items-center justify-between">
					<div className="flex gap-2">
						{permissions.canManageAppointments && (
							<Button className="gap-2" onClick={onCreateAppointment}>
								<Plus className="h-4 w-4" />
								New Appointment
							</Button>
						)}
						<Button
							variant="outline"
							className="gap-2"
							onClick={onQRAppointment}
						>
							<QrCode className="h-4 w-4" />
							QR Appointment
						</Button>
					</div>
				</div>
			</div>
			{/* Filters */}
			<div className="flex flex-col gap-4 lg:flex-row">
				<div className="flex-1">
					<Input
						placeholder="Search patients..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="max-w-sm"
					/>
				</div>
				<Select
					value={`${category}`}
					onValueChange={handleAppointmentTypeChange}
				>
					<SelectTrigger className="w-full lg:w-48">
						<SelectValue placeholder="All Appointment" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all">All Appointment</SelectItem>
						<SelectItem value="old">Previous Appointments</SelectItem>
						<SelectItem value="new">Upcoming Appointments</SelectItem>
					</SelectContent>
				</Select>
				<Select value={`${clinicId || 0}`} onValueChange={handleClinicChange}>
					<SelectTrigger className="w-full lg:w-48">
						<SelectValue placeholder="All Clinics" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="0">All Clinics</SelectItem>
						{clinicsData?.data?.data?.map((clinic) => (
							<SelectItem key={clinic.id} value={clinic.id.toString()}>
								{clinic.name}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
			</div>
			{/* Content */}
			<div className="space-y-4">
				<div className="hidden rounded-md border lg:block">
					<HorizontalScrollBar>
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Patient</TableHead>
									<TableHead>Clinic</TableHead>
									<TableHead>Schedule</TableHead>
									<TableHead>Visit Reason</TableHead>
									<TableHead>Medical Certificate</TableHead>
									<TableHead>Status</TableHead>
									<TableHead>Actions</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{appointmentsData?.data?.data?.map((appointment) => (
									<AppointmentRow
										key={appointment.id}
										appointment={appointment}
										onView={handleViewAppointment}
										canUpdateStatus={permissions.canManageAppointments}
									/>
								))}
							</TableBody>
						</Table>
					</HorizontalScrollBar>
				</div>

				<div className="space-y-2 lg:hidden">
					{appointmentsData?.data?.data?.map((appointment) => (
						<AppointmentColumn
							key={appointment.id}
							appointment={appointment}
							onView={handleViewAppointment}
							canUpdateStatus={permissions.canManageAppointments}
						/>
					))}
				</div>

				{appointmentsData?.data?.data?.length === 0 && (
					<div className="flex flex-col items-center">
						<img alt="" src={'/no_patient.webp'} className="h-96" />
						<p className="text-lg font-bold text-gray-700">
							No appointments found.
						</p>
						<p className="text-xs">
							Make sure the names are spelled correctly, check for typos, or try
							a different name.
						</p>
					</div>
				)}

				{/* Pagination */}
				{meta && meta.last_page > 1 && (
					<div className="flex items-center justify-between">
						<p className="text-sm text-gray-700">
							Showing {(currentPage - 1) * meta.per_page + 1} to{' '}
							{Math.min(currentPage * meta.per_page, meta.total)} of{' '}
							{meta.total} patients
						</p>
						<div className="flex items-center gap-2">
							<Button
								variant="outline"
								size="sm"
								onClick={() => handlePageChange(currentPage - 1)}
								disabled={currentPage <= 1}
							>
								Previous
							</Button>
							<span className="text-sm">
								Page {currentPage} of {meta.last_page}
							</span>
							<Button
								variant="outline"
								size="sm"
								onClick={() => handlePageChange(currentPage + 1)}
								disabled={currentPage >= meta.last_page}
							>
								Next
							</Button>
						</div>
					</div>
				)}
			</div>
		</div>
	);
}
