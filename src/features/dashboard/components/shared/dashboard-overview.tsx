'use client';

import { Calendar, CalendarCheck, Stethoscope, Users } from 'lucide-react';

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useCurrentRole } from '@/features/dashboard/context/DashboardRoleContext';
import { useDashboardMetrics } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';

import AppointAttendedList from './appointment-attended-list';
import AppointQueueList from './appointment-queue-list';

interface IMetricCardProps {
	title: string;
	value: number;
	icon: React.ComponentType<{ className?: string }>;
	description: string;
	isLoading?: boolean;
	trend?: {
		value: number;
		isPositive: boolean;
	};
}

const MetricCard = ({
	title,
	value,
	icon: Icon,
	description,
	isLoading,
	trend,
}: IMetricCardProps) => {
	if (isLoading) {
		return (
			<Card>
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle className="text-sm font-medium">
						<Skeleton className="h-4 w-24" />
					</CardTitle>
					<Skeleton className="h-4 w-4" />
				</CardHeader>
				<CardContent>
					<Skeleton className="mb-1 h-8 w-16" />
					<Skeleton className="h-3 w-32" />
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle className="text-sm font-medium">{title}</CardTitle>
				<Icon className="text-muted-foreground h-4 w-4" />
			</CardHeader>
			<CardContent>
				<div className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
					{value.toLocaleString()}
				</div>
				<p className="text-muted-foreground text-xs">{description}</p>
				{trend && (
					<div className="mt-2 flex items-center text-xs">
						<span
							className={trend.isPositive ? 'text-green-600' : 'text-red-600'}
						>
							{trend.isPositive ? '+' : '-'}
							{Math.abs(trend.value)}%
						</span>
						<span className="text-muted-foreground ml-1">from last month</span>
					</div>
				)}
			</CardContent>
		</Card>
	);
};

export function DashboardOverview() {
	const role = useCurrentRole();
	const { data: metricsResponse, isLoading, error } = useDashboardMetrics();

	const metricsData = metricsResponse?.data;

	// Role-specific welcome messages
	const getWelcomeMessage = () => {
		switch (role) {
			case 'doctor':
				return {
					title: 'Welcome to Your Dashboard',
					subtitle: "Here's an overview of your practice today",
				};
			case 'assistant':
				return {
					title: 'Assistant Dashboard',
					subtitle: 'Manage appointments and assist with patient care',
				};
			default:
				return {
					title: 'Dashboard',
					subtitle: 'Overview of your activities',
				};
		}
	};

	const getEmptyStateMessage = () => {
		switch (role) {
			case 'doctor':
				return {
					title: 'Welcome to Elena!',
					description:
						"You're all set up and ready to go! Create your first appointment now with Elena's comprehensive practice management system.",
					features: [
						'✓ Profile Setup Complete',
						'✓ Clinic Configuration Ready',
						'✓ Ready for Patients',
					],
				};
			case 'assistant':
				return {
					title: 'Ready to Assist!',
					description:
						"You're ready to help manage the practice! Start by viewing today's appointments or helping patients with their needs.",
					features: [
						'✓ Assistant Access Configured',
						'✓ Clinic Access Ready',
						'✓ Ready to Help Patients',
					],
				};
			default:
				return {
					title: 'Welcome!',
					description: 'Get started with your dashboard.',
					features: [],
				};
		}
	};

	const welcomeMessage = getWelcomeMessage();
	const emptyStateMessage = getEmptyStateMessage();

	if (error) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
				<Card className="border-destructive">
					<CardContent className="p-6">
						<div className="text-center">
							<Stethoscope className="text-destructive mx-auto mb-2 h-8 w-8" />
							<p className="text-destructive text-sm">
								Failed to load dashboard metrics
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			{/* Debug Component - Remove in production */}
			{/* {process.env.NODE_ENV === 'development' && <RoleDebug />} */}

			{/* Welcome Message */}
			<div className="mb-4">
				<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
					{welcomeMessage.title}
				</h1>
				<p className="text-muted-foreground">{welcomeMessage.subtitle}</p>
			</div>

			{/* Welcome Message for New Users */}
			{!isLoading &&
				(metricsData?.todaysTotalAppointments ||
					metricsData?.total_appointments ||
					0) === 0 &&
				(metricsData?.totalPatients ||
					metricsData?.registered_patients ||
					0) === 0 && (
					<Card className="relative border-[oklch(0.7448_0.1256_202.74)] bg-gradient-to-r from-[oklch(0.7448_0.1256_202.74)]/5 to-[oklch(0.7448_0.1256_202.74)]/10">
						<CardContent className="p-6">
							<div className="text-center">
								<Stethoscope className="mx-auto mb-4 h-12 w-12 text-[oklch(0.7448_0.1256_202.74)]" />
								<h3 className="mb-2 text-lg font-semibold text-[oklch(0.7448_0.1256_202.74)]">
									{emptyStateMessage.title}
								</h3>
								<p className="text-muted-foreground mb-4">
									{emptyStateMessage.description}
								</p>
								<div className="flex flex-wrap justify-center gap-2 text-sm">
									{emptyStateMessage.features.map((feature, index) => (
										<span
											key={index}
											className="bg-muted rounded-full px-3 py-1"
										>
											{feature}
										</span>
									))}
								</div>
							</div>
						</CardContent>
					</Card>
				)}

			{/* Metrics Cards */}
			<div className="grid auto-rows-min gap-4 lg:grid-cols-4">
				<MetricCard
					title="Today's Appointments"
					value={metricsData?.todaysTotalAppointments || 0}
					icon={Calendar}
					description="Scheduled for today"
					isLoading={isLoading}
				/>
				<MetricCard
					title="Today's Attended"
					value={metricsData?.todaysTotalAttendedAppointments || 0}
					icon={CalendarCheck}
					description="Completed consultations today"
					isLoading={isLoading}
				/>
				<MetricCard
					title="Total Appointments"
					value={metricsData?.allTotalAppointments || 0}
					icon={Calendar}
					description="Total scheduled appointments"
					isLoading={isLoading}
				/>
				{/* <MetricCard
					title="Total Attended"
					value={metricsData?.allTotalAttendedAppointments || 0}
					icon={CalendarCheck}
					description="Completed consultations"
					isLoading={isLoading}
				/> */}
				<MetricCard
					title="Total Patients"
					value={metricsData?.totalPatients || 0}
					icon={Users}
					description="Total patients accessible to you"
					isLoading={isLoading}
				/>
			</div>

			<div className="flex flex-col gap-8 lg:flex-row">
				<div className="flex flex-col gap-4 overflow-hidden lg:w-1/2">
					<h1 className="text-xl font-bold text-[oklch(0.7448_0.1256_202.74)] uppercase">
						Appointment Queue
					</h1>
					<AppointQueueList />
				</div>
				<div className="flex flex-col gap-4 overflow-hidden lg:w-1/2">
					<h1 className="text-xl font-bold text-[oklch(0.7448_0.1256_202.74)] uppercase">
						Attended Consultation
					</h1>
					<AppointAttendedList />
				</div>
			</div>
		</div>
	);
}
