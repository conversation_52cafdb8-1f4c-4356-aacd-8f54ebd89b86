import React from 'react';

import { poppins } from '@/components/common/font';
import { formatDate } from '@/core/lib/utils';

import { AssistantDiagnosticRequest } from '../../types/assistant.type';
import {
	IDiagnosticRequest,
	IDiagnosticRequestItem,
} from '../../types/doctor.types';

type DiagnosticRequestModalProps = {
	data: IDiagnosticRequest | AssistantDiagnosticRequest;
	componentRef: React.RefObject<HTMLDivElement | null>;
};

export const DiagnosticRequestPrint: React.FC<DiagnosticRequestModalProps> = ({
	data,
	componentRef,
}) => {
	// Type guard to check if this is a doctor prescription
	const isDoctorDiagnose = (
		diagnoticRequest: IDiagnosticRequest | AssistantDiagnosticRequest
	): diagnoticRequest is IDiagnosticRequest => {
		return 'doctorProfile' in (diagnoticRequest.clinic || {});
	};

	// Get the appropriate profile based on prescription type
	const doctorProfile = isDoctorDiagnose(data)
		? data?.clinic?.doctorProfile
		: null;
	const assistantProfile = !isDoctorDiagnose(data)
		? (data as AssistantDiagnosticRequest)?.clinic?.assistantProfile
		: null;
	const clinicInfo = data?.clinic;

	return (
		<div
			className={`flex w-full flex-col space-y-1 p-5 ${poppins.className}`}
			ref={componentRef}
		>
			<div className="w-full">
				<div className="flex items-center justify-between">
					<div className="w-3/4"></div>
					<div className="flex w-1/2 flex-col text-sm">
						{/* Clinic Details */}
						<p className="font-bold">
							{doctorProfile
								? [
										doctorProfile?.first_name,
										doctorProfile?.middle_name,
										doctorProfile?.last_name,
									]
										.filter((item) => item)
										.join(' ')
								: assistantProfile
									? [
											assistantProfile?.first_name,
											assistantProfile?.middle_name,
											assistantProfile?.last_name,
										]
											.filter((item) => item)
											.join(' ')
									: 'Name not set'}
						</p>
						<p className="font-semibold">
							{doctorProfile?.doctor?.specialty || 'Specialty not set'}
						</p>
						<p className="text-xs">Clinic Name: {clinicInfo?.name}</p>
						<p className="text-xs">Clinic Address: {clinicInfo?.address}</p>
						<p className="text-xs">Contact No: {clinicInfo?.phone}</p>
					</div>
				</div>
			</div>

			{/* Patient Information */}
			<div className="flex flex-row space-x-5 pb-2">
				<div className="text-xs">
					<p>Patient Name: {data?.patient_name?.toUpperCase()}</p>
					<p>Gender: {data?.patient_gender}</p>
					<p>
						Age:{' '}
						{data?.patient_birthdate &&
							new Date().getFullYear() -
								new Date(data?.patient_birthdate).getFullYear() +
								' YEARS'}
					</p>
					<p>Address: {data?.patient_address?.toUpperCase()}</p>
					<p>Contact: {data?.patient_phone}</p>
					<p>Date: {formatDate(data?.created_at)}</p>
				</div>
			</div>

			<div className="flex flex-col space-y-2">
				<div className="min-w-[350px] space-y-1 text-xs font-medium">
					<div className="w-full">
						<p className="text-center text-3xl font-bold">DIAGNOSTIC REQUEST</p>
					</div>
				</div>
				<div className="flex min-h-[250px] flex-col space-y-2 pt-5">
					{/* <div className="flex-grow space-y-2 min-h-[250px] text-sm bg-[url('/images/prescription/PrescriptionLogoMarkBackground.png')] bg-center bg-no-repeat bg-contain"> */}
					{data?.diagnosticRequestItems?.map(
						(value: IDiagnosticRequestItem, index: number) => (
							<div key={index} className="flex w-full flex-col">
								<div className="mb-2 flex items-center justify-between space-x-5">
									<div className="flex w-full items-center justify-between">
										<div className="flex w-full flex-row items-center space-x-1">
											<p className="text-lg font-bold">
												{index + 1}. {value?.name?.toUpperCase()}
											</p>
										</div>
									</div>
								</div>
							</div>
						)
					)}
				</div>
				<div className="flex w-full flex-col items-end">
					<div className="w-2/5">
						{doctorProfile?.doctor?.e_signature && (
							<div className="flex w-full items-center justify-center">
								<img
									src={doctorProfile?.doctor?.e_signature}
									width={100}
									height={100}
									alt="signature"
									className="w-1/2"
								/>
							</div>
						)}
						<div className="border-1" />
						<p className="font-bold">
							{doctorProfile ? 'Dr. ' : ''}
							{doctorProfile
								? [
										doctorProfile?.first_name,
										doctorProfile?.middle_name,
										doctorProfile?.last_name,
									]
										.filter((item) => item)
										.join(' ')
								: assistantProfile
									? [
											assistantProfile?.first_name,
											assistantProfile?.middle_name,
											assistantProfile?.last_name,
										]
											.filter((item) => item)
											.join(' ') + ' (Assistant)'
									: '-- --'}
						</p>
						<p className="text-xs">
							License Number: {doctorProfile?.doctor?.prc_number || '-- --'}
						</p>
						<p className="mt-5 text-xs font-semibold">
							This diagnostic request is electronically generated. Should you
							wish to verify authenticity kindly call the doctor’s clinic or
							email us at <br /> <EMAIL>
						</p>
					</div>
				</div>
			</div>
			<div className="relative">
				<div className="flex items-center justify-between">
					<div className="w-1/4">
						<img
							src="/elena-logo-black.webp"
							width={130}
							height={200}
							alt="header"
							className="w-3/4"
						/>
					</div>
				</div>
			</div>
		</div>
	);
};
