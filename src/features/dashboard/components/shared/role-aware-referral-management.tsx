'use client';

import { Plus, Search } from 'lucide-react';
import { useQueryStates } from 'nuqs';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { searchParamParsers } from '@/core/lib/search-params';
import { useAllReferrals } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import {
	IReferralListParams,
	IReferralsResponse,
} from '@/features/dashboard/types/doctor.types';

// Import doctor components for doctors
import { CreateReferralDialog } from '../doctor-dashboard/referral/create-referral-dialog';
import { ReferralDetailDialog } from '../doctor-dashboard/referral/referral-detail-dialog';
import { ReferralList } from '../doctor-dashboard/referral/referral-list';
import { ReferralPrintDialog } from '../doctor-dashboard/referral/referral-print-dialog';

export function RoleAwareReferralManagement({
	isArchived = false,
}: {
	isArchived?: boolean;
}) {
	// URL search parameters using nuqs
	const [{ search, page, action, id }, setSearchParams] = useQueryStates({
		search: searchParamParsers.search,
		page: searchParamParsers.page,
		action: searchParamParsers.action,
		id: searchParamParsers.id,
	});

	const [createDialogOpen, setCreateDialogOpen] = useState(action === 'create');
	const [detailDialogOpen, setDetailDialogOpen] = useState(!!id);
	const [printDialogOpen, setPrintDialogOpen] = useState(!!id);
	const [selectedReferralId, setSelectedReferralId] = useState<number | null>(
		id ? Number(id) : null
	);

	// Build query parameters
	const queryParams: IReferralListParams = {
		page,
		pageSize: 10,
		...(search && { search }),
	};

	// Use role-aware hook
	const {
		data: referralsResponse,
		isLoading,
		error,
	} = useAllReferrals({ ...queryParams, isArchived });

	const referrals = (referralsResponse as IReferralsResponse)?.data?.data || [];

	const meta = (referralsResponse as IReferralsResponse)?.data?.meta;

	// Handle search
	const handleSearch = (query: string) => {
		setSearchParams({ search: query, page: 1 }); // Reset to first page when searching
	};

	// Handle navigation
	const navigateToList = () => {
		setSearchParams({ action: null, id: null });
	};

	const handleCreateReferral = () => {
		setCreateDialogOpen(true);
		setSearchParams({ action: 'create' });
	};

	const handleViewReferral = (referralId: number) => {
		setSelectedReferralId(referralId);
		setDetailDialogOpen(true);
		setSearchParams({ id: referralId, action: 'view' });
	};

	const handleCloseCreateDialog = () => {
		setCreateDialogOpen(false);
		navigateToList();
	};

	const handlePrintReferral = (referralId: number) => {
		setSelectedReferralId(referralId);
		setPrintDialogOpen(true);
		setSearchParams({ id: referralId, action: 'print' });
	};

	return (
		<div className="space-y-4">
			{/* Search and Filters */}
			<div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
				<div className="relative max-w-sm flex-1">
					<Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
					<Input
						placeholder="Search referrals by patient name or specialist..."
						value={search}
						onChange={(e) => handleSearch(e.target.value)}
						className="pl-10"
					/>
				</div>
				{!isArchived && (
					<Button
						onClick={handleCreateReferral}
						className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
					>
						<Plus className="mr-2 h-4 w-4" />
						Create Referral
					</Button>
				)}
			</div>

			{/* Referral List */}
			<div className="space-y-4">
				<ReferralList
					referrals={referrals}
					isLoading={isLoading}
					error={error}
					currentPage={page}
					totalPages={meta?.last_page || 1}
					onPageChange={(newPage) => setSearchParams({ page: newPage })}
					onViewReferral={handleViewReferral}
					onPrintReferral={handlePrintReferral}
				/>
			</div>

			{/* Dialogs - Only for doctors */}
			<CreateReferralDialog
				open={createDialogOpen}
				onOpenChange={setCreateDialogOpen}
				onClose={handleCloseCreateDialog}
			/>

			{selectedReferralId !== null && (
				<ReferralDetailDialog
					onPrint={handlePrintReferral}
					referralId={selectedReferralId}
					open={detailDialogOpen && action === 'view'}
					onOpenChange={(open) => {
						if (!open) {
							setDetailDialogOpen(false);
							setSearchParams({ action: null, id: null });
						} else {
							setDetailDialogOpen(true);
						}
					}}
				/>
			)}

			{/* Prescription Print Dialog */}
			{selectedReferralId !== null && (
				<ReferralPrintDialog
					referralId={selectedReferralId}
					open={printDialogOpen && action === 'print'}
					onOpenChange={(open) => {
						if (!open) {
							setPrintDialogOpen(false);
							setSearchParams({ action: null, id: null });
						} else {
							setPrintDialogOpen(true);
						}
					}}
				/>
			)}
		</div>
	);
}
