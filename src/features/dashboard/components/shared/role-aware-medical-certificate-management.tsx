'use client';

import { Plus, Search } from 'lucide-react';
import { useQueryStates } from 'nuqs';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { searchParamParsers } from '@/core/lib/search-params';
import { useAllMedicalCertificates } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import {
	IMedicalCertificateListParams,
	IMedicalCertificatesResponse,
} from '@/features/dashboard/types/doctor.types';

import { CreateMedicalCertificateDialog } from '../doctor-dashboard/medical-certificate/create-medical-certificate-dialog';
import { MedicalCertificateDetailDialog } from '../doctor-dashboard/medical-certificate/medical-certificate-detail-dialog';
import { MedicalCertificateList } from '../doctor-dashboard/medical-certificate/medical-certificate-list';
import { MedicalCertificatePrintDialog } from '../doctor-dashboard/medical-certificate/medical-certificate-print-dialog';

// import { MedicalCertificatePrintDialog } from '../doctor-dashboard/medical-certificate/medical-certificate-print-dialog';

export function RoleAwareMedicalCertificateManagement({
	isArchived = false,
}: {
	isArchived?: boolean;
}) {
	// URL search parameters using nuqs
	const [{ search, page, action, id }, setSearchParams] = useQueryStates({
		search: searchParamParsers.search,
		page: searchParamParsers.page,
		action: searchParamParsers.action,
		id: searchParamParsers.id,
	});

	const [createDialogOpen, setCreateDialogOpen] = useState(action === 'create');
	const [detailDialogOpen, setDetailDialogOpen] = useState(!!id);
	const [printDialogOpen, setPrintDialogOpen] = useState(!!id);
	const [selectedMedicalCertificateId, setSelectedMedicalCertificateId] =
		useState<number | null>(id ? Number(id) : null);

	// Build query parameters
	const queryParams: IMedicalCertificateListParams = {
		page,
		pageSize: 10,
		...(search && { search }),
	};

	// Use role-aware hook
	const {
		data: medicalCertificatesResponse,
		isLoading,
		error,
	} = useAllMedicalCertificates({ ...queryParams, isArchived });

	const medicalCertificates =
		(medicalCertificatesResponse as IMedicalCertificatesResponse)?.data?.data ||
		[];
	const meta = (medicalCertificatesResponse as IMedicalCertificatesResponse)
		?.data?.meta;

	// Handle search
	const handleSearch = (query: string) => {
		setSearchParams({ search: query, page: 1 }); // Reset to first page when searching
	};

	// Handle navigation
	const navigateToList = () => {
		setSearchParams({ action: null, id: null });
	};

	const handleCreateMedicalCertificate = () => {
		setCreateDialogOpen(true);
		setSearchParams({ action: 'create' });
	};

	const handleViewMedicalCertificate = (medicalCertificateId: number) => {
		setSelectedMedicalCertificateId(medicalCertificateId);
		setDetailDialogOpen(true);
		setSearchParams({ action: 'view', id: medicalCertificateId });
	};

	const handleCloseCreateDialog = () => {
		setCreateDialogOpen(false);
		navigateToList();
	};

	const handlePrintMedicalCertificate = (medicalCertificateId: number) => {
		setSelectedMedicalCertificateId(medicalCertificateId);
		setPrintDialogOpen(true);
		setSearchParams({ action: 'print', id: medicalCertificateId });
	};

	return (
		<div className="space-y-4">
			{/* Search and Filters */}
			<div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
				<div className="relative max-w-sm flex-1">
					<Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
					<Input
						placeholder="Search medical certificates by patient name or address..."
						value={search}
						onChange={(e) => handleSearch(e.target.value)}
						className="pl-10"
					/>
				</div>
				{!isArchived && (
					<Button
						onClick={handleCreateMedicalCertificate}
						className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
					>
						<Plus className="mr-2 h-4 w-4" />
						Create Medical Certificate
					</Button>
				)}
			</div>

			{/* Medical Certificate List */}
			<MedicalCertificateList
				medicalCertificates={medicalCertificates}
				isLoading={isLoading}
				error={error}
				currentPage={page}
				totalPages={meta?.last_page || 1}
				onPageChange={(newPage) => setSearchParams({ page: newPage })}
				onViewMedicalCertificate={handleViewMedicalCertificate}
				onPrintMedicalCertificate={handlePrintMedicalCertificate}
			/>

			{/* Dialogs - Only for doctors */}
			<CreateMedicalCertificateDialog
				open={createDialogOpen}
				onClose={handleCloseCreateDialog}
			/>

			{selectedMedicalCertificateId !== null && (
				<MedicalCertificateDetailDialog
					onPrint={handlePrintMedicalCertificate}
					medicalCertificateId={selectedMedicalCertificateId}
					open={detailDialogOpen && action === 'view'}
					onOpenChange={(open) => {
						if (!open) {
							setDetailDialogOpen(false);
							setSearchParams({ action: null, id: null });
						} else {
							setDetailDialogOpen(true);
						}
					}}
					appointmentId={0}
				/>
			)}

			{selectedMedicalCertificateId !== null && (
				<MedicalCertificatePrintDialog
					medicalCertificateId={selectedMedicalCertificateId}
					open={printDialogOpen && action === 'print'}
					onOpenChange={(open) => {
						if (!open) {
							setPrintDialogOpen(false);
							setSearchParams({ action: null, id: null });
						} else {
							setPrintDialogOpen(true);
						}
					}}
				/>
			)}
		</div>
	);
}
