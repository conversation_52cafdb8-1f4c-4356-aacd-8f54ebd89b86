import { useRouter } from 'next/navigation';
import React, { useState } from 'react';

import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import useDebounce from '@/core/hooks/utils/useDebounce';

import {
	useClinics,
	useTodaysAttendedAppointments,
} from '../../hooks/useDoctorAssistantDashboard';
import { IAppointment } from '../../types/doctor.types';

const AppointAttendedList = () => {
	const [search, setSearch] = useState('');
	const [clinicId, setClinicId] = useState<string>();
	const { debouncedValue } = useDebounce(search, 500);

	const { data: queueData, isLoading: isLoadingAttended } =
		useTodaysAttendedAppointments({
			...(debouncedValue ? { search: debouncedValue } : {}),
			...(Number(clinicId) ? { clinicId: Number(clinicId) } : {}),
		});

	const { data: clinicData, isLoading: isLoadingClinic } = useClinics({
		pageSize: 100,
	});

	const handleClinicChange = (value: string) => {
		setClinicId(value);
	};

	return (
		<div className="flex flex-col gap-4">
			<div className="flex flex-col gap-2 lg:flex-row">
				<Input
					className="lg:w-2/3"
					placeholder="Search by patient name..."
					value={search}
					onChange={(e) => setSearch(e.target.value)}
				/>
				<Select onValueChange={handleClinicChange} defaultValue={clinicId}>
					<SelectTrigger className="focus:border-elena-primary focus:ring-elena-primary w-full lg:w-1/3">
						<SelectValue placeholder="Select Clinic" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="0">All Clinics</SelectItem>
						{clinicData?.data?.data.map((clinic) => (
							<SelectItem key={clinic.id} value={clinic.id.toString()}>
								{clinic.name}
							</SelectItem>
						))}
						{isLoadingClinic && (
							<SelectItem value="0">Loading Clinics...</SelectItem>
						)}
					</SelectContent>
				</Select>
			</div>

			{isLoadingAttended ? (
				<div className="flex flex-col gap-4 text-center">Loading...</div>
			) : (
				<div className="flex flex-col gap-4">
					{queueData?.data.map((appointment: IAppointment, index: number) => (
						<AppointAttendedItem
							order={index + 1}
							key={appointment.id}
							appointment={appointment}
						/>
					))}
				</div>
			)}
			{queueData?.data.length === 0 && !debouncedValue && (
				<div className="text-muted-foreground flex flex-col items-center gap-4 text-center font-bold">
					<img className="h-48" alt="" src={'/no_history.png'} />
					<p>There are no recent visit yet.</p>
				</div>
			)}
			{queueData?.data.length === 0 && debouncedValue && (
				<div className="text-muted-foreground flex flex-col items-center gap-4 text-center font-bold">
					<img className="h-48" alt="" src={'/no_history.png'} />
					<p>No recent visit found.</p>
				</div>
			)}
		</div>
	);
};

const AppointAttendedItem = ({
	appointment,
	order,
}: {
	appointment: IAppointment;
	order: number;
}) => {
	const router = useRouter();

	const handleView = () => {
		router.push(`/dashboard/?tab=appointments&appointmentId=${appointment.id}`);
	};

	return (
		<div
			onClick={handleView}
			className="flex cursor-pointer flex-row items-center gap-4 rounded-lg border p-4 hover:bg-gray-50"
		>
			<p className="text-base font-bold">{order}</p>
			<img
				className="h-8 w-8 rounded-full"
				src={appointment.patient?.profile?.avatar || '/placeholder-profile.jpg'}
				alt="Profile"
			/>
			<div className="flex w-full flex-col overflow-hidden">
				<p className="text-sm font-bold">
					{[
						appointment.patient?.profile?.first_name,
						appointment.patient?.profile?.middle_name,
						appointment.patient?.profile?.last_name,
					]
						.filter((item) => item)
						.join(' ')}
				</p>
				<div className="flex w-full flex-col gap-2 lg:flex-row lg:items-center">
					<p className="shrink-0 text-xs">
						{new Date(appointment.appointment_date).toLocaleString()}
					</p>
					<p className="text-xxs w-fit truncate rounded-full bg-[oklch(0.7448_0.1256_202.74)] px-2 font-bold uppercase">
						{appointment.clinic?.name}
					</p>
					<p className="text-xxs w-fit rounded-full bg-[oklch(0.7448_0.1256_202.74)] px-2 font-bold uppercase">
						{appointment.status}
					</p>
				</div>
			</div>
		</div>
	);
};

export default AppointAttendedList;
