'use client';

import { Plus, Search } from 'lucide-react';
import { useQueryStates } from 'nuqs';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { searchParamParsers } from '@/core/lib/search-params';
import { useAllLabRequests } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import {
	ILabRequestListParams,
	ILabRequestsResponse,
} from '@/features/dashboard/types/doctor.types';

// Import doctor components for doctors
import { CreateLabRequestDialog } from '../doctor-dashboard/lab-request/create-lab-request-dialog';
import { LabRequestDetailDialog } from '../doctor-dashboard/lab-request/lab-request-detail-dialog';
import { LabRequestList } from '../doctor-dashboard/lab-request/lab-request-list';
import { LabRequestPrintDialog } from '../doctor-dashboard/lab-request/lab-request-print-dialog';

export function RoleAwareLabRequestManagement({
	isArchived = false,
}: {
	isArchived?: boolean;
}) {
	// URL search parameters using nuqs
	const [{ search, page, action, id }, setSearchParams] = useQueryStates({
		search: searchParamParsers.search,
		page: searchParamParsers.page,
		action: searchParamParsers.action,
		id: searchParamParsers.id,
	});

	const [createDialogOpen, setCreateDialogOpen] = useState(action === 'create');
	const [detailDialogOpen, setDetailDialogOpen] = useState(!!id);
	const [printDialogOpen, setPrintDialogOpen] = useState(!!id);
	const [selectedLabRequestId, setSelectedLabRequestId] = useState<
		number | null
	>(id ? Number(id) : null);

	// Build query parameters
	const queryParams: ILabRequestListParams = {
		page,
		pageSize: 10,
		...(search && { search }),
	};

	// Use role-aware hook
	const {
		data: labRequestsResponse,
		isLoading,
		error,
	} = useAllLabRequests({ ...queryParams, isArchived });

	const labRequests =
		(labRequestsResponse as ILabRequestsResponse)?.data?.data || [];
	const meta = (labRequestsResponse as ILabRequestsResponse)?.data?.meta;

	// Handle search
	const handleSearch = (query: string) => {
		setSearchParams({ search: query, page: 1 }); // Reset to first page when searching
	};

	// Handle navigation
	const navigateToList = () => {
		setSearchParams({ action: null, id: null });
	};

	const handleCreateLabRequest = () => {
		setCreateDialogOpen(true);
		setSearchParams({ action: 'create' });
	};

	const handleViewLabRequest = (labRequestId: number) => {
		setSelectedLabRequestId(labRequestId);
		setDetailDialogOpen(true);
		setSearchParams({ action: 'view', id: labRequestId });
	};

	const handleCloseCreateDialog = () => {
		setCreateDialogOpen(false);
		navigateToList();
	};

	const handlePrintLabRequest = (labRequestId: number) => {
		setSelectedLabRequestId(labRequestId);
		setPrintDialogOpen(true);
		setSearchParams({ action: 'print', id: labRequestId });
	};

	return (
		<div className="space-y-4">
			{/* Search and Filters */}
			<div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
				<div className="relative max-w-sm flex-1">
					<Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
					<Input
						placeholder="Search lab requests by patient name or address..."
						value={search}
						onChange={(e) => handleSearch(e.target.value)}
						className="pl-10"
					/>
				</div>
				{!isArchived && (
					<Button
						onClick={handleCreateLabRequest}
						className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
					>
						<Plus className="mr-2 h-4 w-4" />
						Create Lab Request
					</Button>
				)}
			</div>

			{/* Lab Request List */}
			<LabRequestList
				labRequests={labRequests}
				isLoading={isLoading}
				error={error}
				currentPage={page}
				totalPages={meta?.last_page || 1}
				onPageChange={(newPage) => setSearchParams({ page: newPage })}
				onViewLabRequest={handleViewLabRequest}
				onPrintLabRequest={handlePrintLabRequest}
			/>

			{/* Dialogs - Only for doctors */}
			<CreateLabRequestDialog
				open={createDialogOpen}
				onClose={handleCloseCreateDialog}
			/>

			{selectedLabRequestId !== null && (
				<LabRequestDetailDialog
					onPrint={handlePrintLabRequest}
					labRequestId={selectedLabRequestId}
					open={detailDialogOpen && action === 'view'}
					onOpenChange={(open) => {
						if (!open) {
							setDetailDialogOpen(false);
							setSearchParams({ action: null, id: null });
						} else {
							setDetailDialogOpen(true);
						}
					}}
				/>
			)}

			{selectedLabRequestId !== null && (
				<LabRequestPrintDialog
					labRequestId={selectedLabRequestId}
					open={printDialogOpen && action === 'print'}
					onOpenChange={(open) => {
						if (!open) {
							setPrintDialogOpen(false);
							setSearchParams({ action: null, id: null });
						} else {
							setPrintDialogOpen(true);
						}
					}}
				/>
			)}
		</div>
	);
}
