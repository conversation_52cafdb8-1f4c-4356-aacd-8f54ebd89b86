'use client';

import { useQueryStates } from 'nuqs';
import { useState } from 'react';

import { searchParamParsers } from '@/core/lib/search-params';
import { useDashboardPermissions } from '@/features/dashboard/context/DashboardRoleContext';
import { IClinic } from '@/features/dashboard/types/doctor.types';

// Import existing clinic components (these will be reused)
import { ClinicDetail } from '../doctor-dashboard/clinic/clinic-detail';
import { ClinicFormDialog } from '../doctor-dashboard/clinic/clinic-form-dialog';
import { ClinicList } from '../doctor-dashboard/clinic/clinic-list';

export function ClinicManagement() {
	const permissions = useDashboardPermissions();

	// URL search parameters using nuqs
	const [{ action, id }, setSearchParams] = useQueryStates({
		action: searchParamParsers.action,
		id: searchParamParsers.id,
	});

	// Local state for dialog management
	const [formDialog, setFormDialog] = useState({
		open: false,
		mode: 'create' as 'create' | 'edit',
		clinic: undefined as IClinic | undefined,
	});

	// Determine current view based on query parameters
	const currentView = id && !action ? 'detail' : 'list';

	// Handle navigation
	const navigateToList = () => {
		setSearchParams({ action: null, id: null });
	};

	const navigateToDetail = (clinic: IClinic) => {
		setSearchParams({ id: clinic.id });
	};

	const navigateToCreate = () => {
		if (!permissions.canCreateClinics) return;

		setFormDialog({
			open: true,
			mode: 'create',
			clinic: undefined,
		});
		setSearchParams({ action: 'create' });
	};

	const navigateToEdit = (clinic: IClinic) => {
		if (!permissions.canCreateClinics) return; // Using same permission for edit

		setFormDialog({
			open: true,
			mode: 'edit',
			clinic,
		});
		setSearchParams({ action: 'edit' });
	};

	const handleDialogClose = () => {
		setFormDialog({
			open: false,
			mode: 'create',
			clinic: undefined,
		});
		setSearchParams({ action: null });
	};

	// Render current view
	const renderCurrentView = () => {
		switch (currentView) {
			case 'detail':
				if (id) {
					return (
						<ClinicDetail
							clinicId={parseInt(id.toString())}
							onBack={navigateToList}
							onEdit={permissions.canCreateClinics ? navigateToEdit : undefined}
						/>
					);
				}
				break;
			default:
				return (
					<ClinicList
						onCreateClinic={
							permissions.canCreateClinics ? navigateToCreate : undefined
						}
						onViewClinic={navigateToDetail}
						onEditClinic={
							permissions.canCreateClinics ? navigateToEdit : undefined
						}
					/>
				);
		}

		// Fallback to list view
		return (
			<ClinicList
				onCreateClinic={
					permissions.canCreateClinics ? navigateToCreate : undefined
				}
				onViewClinic={navigateToDetail}
				onEditClinic={permissions.canCreateClinics ? navigateToEdit : undefined}
			/>
		);
	};

	return (
		<div className="flex flex-1 flex-col gap-4 pt-0">
			{/* Main Content */}
			{renderCurrentView()}

			{/* Form Dialog */}
			{permissions.canCreateClinics && (
				<ClinicFormDialog
					open={formDialog.open}
					mode={formDialog.mode}
					clinic={formDialog.clinic}
					onOpenChange={(open) => {
						if (!open) {
							handleDialogClose();
						}
					}}
				/>
			)}
		</div>
	);
}
