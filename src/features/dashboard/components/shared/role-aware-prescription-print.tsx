/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';

import { poppins } from '@/components/common/font';
import { formatDate } from '@/core/lib/utils';

import { IPrescription as AssistantPrescription } from '../../types/assistant.type';
import { IPrescription as DoctorPrescription } from '../../types/doctor.types';

type PrescriptionModalProps = {
	data: DoctorPrescription | AssistantPrescription;
	componentRef: React.RefObject<HTMLDivElement | null>;
};

export const PrescriptionPrint: React.FC<PrescriptionModalProps> = ({
	data,
	componentRef,
}) => {
	// Type guard to check if this is a doctor prescription
	const isDoctorPrescription = (
		prescription: DoctorPrescription | AssistantPrescription
	): prescription is DoctorPrescription => {
		return 'doctorProfile' in (prescription.clinic || {});
	};

	// Get the appropriate profile based on prescription type
	const doctorProfile = isDoctorPrescription(data)
		? data?.clinic?.doctorProfile
		: null;
	const assistantProfile = !isDoctorPrescription(data)
		? (data as AssistantPrescription)?.clinic?.assistantProfile
		: null;

	// For assistant prescriptions, we need to get doctor info from the clinic's doctor_profile_id
	// This might require an additional API call or the clinic should include doctor info
	const clinicInfo = data?.clinic;

	return (
		<div
			className={`flex w-full flex-col space-y-1 p-5 ${poppins.className}`}
			ref={componentRef}
		>
			{/* Header Section */}
			<div className="w-full">
				<div className="flex items-center justify-between">
					<div className="w-3/4"></div>
					<div className="flex w-1/2 flex-col text-sm">
						{/* Clinic Details */}
						<p className="font-bold">
							{doctorProfile
								? [
										doctorProfile?.first_name,
										doctorProfile?.middle_name,
										doctorProfile?.last_name,
									]
										.filter((item) => item)
										.join(' ')
								: assistantProfile
									? [
											assistantProfile?.first_name,
											assistantProfile?.middle_name,
											assistantProfile?.last_name,
										]
											.filter((item) => item)
											.join(' ')
									: 'Name not set'}
						</p>
						<p className="font-semibold">
							{doctorProfile?.doctor?.specialty ||
								(assistantProfile ? 'Assistant' : 'Specialty not set')}
						</p>
						<p className="text-xs">Clinic Name: {clinicInfo?.name}</p>
						<p className="text-xs">Clinic Address: {clinicInfo?.address}</p>
						<p className="text-xs">Contact No: {clinicInfo?.phone}</p>
					</div>
				</div>
			</div>

			{/* Patient Information */}
			<div className="flex flex-row space-x-5 pb-2">
				<div className="text-xs">
					<p>Patient Name: {data?.patient_name?.toUpperCase()}</p>
					<p>Gender: {data?.patient_gender}</p>
					<p>
						Age:{' '}
						{data?.patient_birthdate &&
							new Date().getFullYear() -
								new Date(data?.patient_birthdate).getFullYear() +
								' YEARS'}
					</p>
					<p>Address: {data?.patient_address?.toUpperCase()}</p>
					<p>Contact: {data?.patient_phone}</p>
					<p>Date: {formatDate(data?.created_at)}</p>
				</div>
			</div>

			<img src="/rx.webp" width={50} height={100} alt="header" />

			{/* Prescription Items */}
			{/* <div className="flex-grow space-y-2 min-h-[250px] text-sm bg-[url('/images/prescription/PrescriptionLogoMarkBackground.png')] bg-center bg-no-repeat bg-contain"> */}
			<div className="flex min-h-[250px] flex-col space-y-5">
				<div className="w-full text-right">
					<p className="text-xs font-bold">Quantity</p>
				</div>
				{data?.prescriptionItems?.map((value: any, index: number) => (
					<div key={index} className="">
						<div className="flex justify-between">
							<div>
								<div className="flex flex-row space-x-1">
									<p className="text-xs font-bold">{value?.generic} - </p>
									<p className="text-xs font-semibold">{value?.brand}</p>
									<p className="text-xs font-semibold">{value?.dosage}</p>
									<p className="text-xs font-semibold">{value?.dosage_form}</p>
								</div>
								<div className="flex flex-row space-x-1">
									<p className="text-xs font-semibold">{value?.frequency} - </p>
									<p className="text-xs font-semibold">{value?.instruction}</p>
								</div>
							</div>
							<div className="text-right">
								<p className="text-xs font-bold">#{value?.quantity} </p>
							</div>
						</div>
					</div>
				))}

				{/* Footer Section */}
				<div className="flex w-full flex-col items-end">
					<div className="w-2/5">
						{doctorProfile?.doctor?.e_signature && (
							<div className="flex w-full items-center justify-center">
								<img
									src={doctorProfile?.doctor?.e_signature}
									width={100}
									height={100}
									alt="signature"
									className="w-1/2"
								/>
							</div>
						)}
						<div className="border-1" />
						<p className="font-bold">
							{doctorProfile ? 'Dr. ' : ''}
							{doctorProfile
								? [
										doctorProfile?.first_name,
										doctorProfile?.middle_name,
										doctorProfile?.last_name,
									]
										.filter((item) => item)
										.join(' ')
								: assistantProfile
									? [
											assistantProfile?.first_name,
											assistantProfile?.middle_name,
											assistantProfile?.last_name,
										]
											.filter((item) => item)
											.join(' ') + ' (Assistant)'
									: '-- --'}
						</p>
						{doctorProfile && (
							<p className="text-xs">
								License Number: {doctorProfile?.doctor?.prc_number || '-- --'}
							</p>
						)}
						{data?.professional_tax_receipt && (
							<p className="text-xs">PTR: {data?.professional_tax_receipt}</p>
						)}
						<p className="mt-5 text-xs font-semibold">
							This prescription is electronically generated. Should you wish to
							verify authenticity kindly call the doctor’s clinic or email us at{' '}
							<br /> <EMAIL>
						</p>
					</div>
				</div>
			</div>
			<div className="flex items-center justify-between">
				<div className="w-1/4">
					<img
						src="/elena-logo-black.webp"
						width={130}
						height={200}
						alt="header"
						className="w-3/4"
					/>
				</div>
			</div>
		</div>
	);
};
