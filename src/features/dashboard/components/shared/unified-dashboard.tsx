'use client';

import { useRouter } from 'next/navigation';
import { useQueryState } from 'nuqs';
import { useState } from 'react';

import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import {
	SidebarInset,
	SidebarProvider,
	SidebarTrigger,
} from '@/components/ui/sidebar';
import { searchParamParsers } from '@/core/lib/search-params';
import {
	DashboardRoleProvider,
	useCurrentRole,
	useDashboardNavigation,
} from '@/features/dashboard/context/DashboardRoleContext';

// AssistantClinicManagement is now unified
// AssistantMedicalDocumentsManagement is now unified
// AssistantSidebar is now unified
import { AppointmentDetailsPage } from '../doctor-dashboard/appointment-details-page';
import { AssistantManagement } from '../doctor-dashboard/assistant/assistant-management';
// ClinicManagement is now unified
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ie<PERSON> } from '../doctor-dashboard/doctor-calendar-view';
import { CreateAppointmentDialog } from '../doctor-dashboard/doctor-create-appointment-dialog';
import { DoctorSettingsManagement } from '../doctor-dashboard/doctor-settings-management';
// Import role-specific components that haven't been unified yet
// DoctorSidebar is now unified
import { DoctorSubscriptionManagement } from '../doctor-dashboard/doctor-subscription-management';
import DoctorProfilePage from '../doctor-dashboard/doctor/doctor-profile';
import { DoctorOnboardingGuard } from '../doctor-dashboard/onboarding/doctor-onboarding-guard';
import { DoctorPasswordChangeGuard } from '../doctor-dashboard/onboarding/doctor-password-change-guard';
import { QRAppointmentDialog } from '../qr-appointment/qr-appointment-dialog';
import { AppointmentHistoryList } from './appointment-history-list';
import { AppointmentList } from './appointment-list';
import { ArchivesManagement } from './archive-management';
// PatientManagement is now unified
// MedicalDocumentsManagement is now unified
import { ClinicManagement } from './clinic-management';
// Import shared components
import { DashboardOverview } from './dashboard-overview';
import { MedicalDocumentsManagement } from './medical-documents-management';
import { PatientManagement } from './patient-management';
import { UnifiedSidebar } from './unified-sidebar';

// Breadcrumb configuration
const getBreadcrumbData = (
	tab: string | null,
	role: 'doctor' | 'assistant' | 'admin' | 'patient' | null
) => {
	const roleLabel =
		role === 'doctor'
			? 'Doctor'
			: role === 'assistant'
				? 'Assistant'
				: role === 'admin'
					? 'Admin'
					: role === 'patient'
						? 'Patient'
						: 'Dashboard';

	const tabLabels: Record<string, string> = {
		overview: 'Overview',
		appointments: 'Appointments',
		calendar: 'Calendar',
		patients: 'Patients',
		clinics: 'Clinics',
		assistants: 'Assistants',
		'medical-documents': 'Medical Documents',
		history: 'History',
		archive: 'Archive',
		subscription: 'Subscription',
		settings: 'Settings',
		profile: 'Profile',
	};

	return {
		title: tab ? tabLabels[tab] || tab : 'Dashboard',
		items: [
			{ label: roleLabel + ' Dashboard', href: '/dashboard' },
			...(tab && tab !== 'overview' ? [{ label: tabLabels[tab] || tab }] : []),
		],
	};
};

// Role-aware sidebar component
function RoleAwareSidebar() {
	return <UnifiedSidebar />;
}

// Calendar wrapper components with dialog handling
function DoctorCalendarWrapper() {
	const [appointmentId, setAppointmentId] = useQueryState(
		'appointmentId',
		searchParamParsers.appointmentId
	);
	const [createDialogOpen, setCreateDialogOpen] = useState(false);
	const [qrAppointmentOpen, setQrAppointmentOpen] = useState(false);
	const [selectedDate, setSelectedDate] = useState<Date | null>(null);

	const handleViewAppointment = (id: number) => {
		setAppointmentId(id);
	};

	const handleCreateAppointment = (date: Date) => {
		setSelectedDate(date);
		setCreateDialogOpen(true);
	};

	const handleQRAppointment = () => {
		setQrAppointmentOpen(true);
	};

	const handlePatientVerified = () => {
		setCreateDialogOpen(true);
	};

	// If appointmentId is present, show appointment details page
	if (appointmentId) {
		return <AppointmentDetailsPage appointmentId={appointmentId} />;
	}

	return (
		<>
			<DoctorCalendarView
				onViewAppointment={handleViewAppointment}
				onCreateAppointment={handleCreateAppointment}
				onQRAppointment={handleQRAppointment}
			/>
			<CreateAppointmentDialog
				open={createDialogOpen}
				onOpenChange={setCreateDialogOpen}
				defaultDate={selectedDate}
			/>
			<QRAppointmentDialog
				open={qrAppointmentOpen}
				onOpenChange={setQrAppointmentOpen}
				onPatientVerified={handlePatientVerified}
			/>
		</>
	);
}

// Appointment List wrapper with dialog handling
function AppointmentListWrapper() {
	const [appointmentId, setAppointmentId] = useQueryState(
		'appointmentId',
		searchParamParsers.appointmentId
	);
	const [createDialogOpen, setCreateDialogOpen] = useState(false);
	const [qrAppointmentOpen, setQrAppointmentOpen] = useState(false);
	const [, setSelectedPatientId] = useState<string | null>(null);

	const handleViewAppointment = (id: number) => {
		setAppointmentId(id);
	};

	const handleCreateAppointment = () => {
		setCreateDialogOpen(true);
	};

	const handleQRAppointment = () => {
		setQrAppointmentOpen(true);
	};

	const handlePatientVerified = (patientId: string) => {
		setSelectedPatientId(patientId);
		setCreateDialogOpen(true);
	};

	// If appointmentId is present, show appointment details page
	if (appointmentId) {
		return <AppointmentDetailsPage appointmentId={appointmentId} />;
	}

	return (
		<>
			<AppointmentList
				onViewAppointment={handleViewAppointment}
				onCreateAppointment={handleCreateAppointment}
				onQRAppointment={handleQRAppointment}
			/>

			{/* Create Appointment Dialog */}
			<CreateAppointmentDialog
				open={createDialogOpen}
				onOpenChange={setCreateDialogOpen}
			/>

			{/* QR Appointment Dialog */}
			<QRAppointmentDialog
				open={qrAppointmentOpen}
				onOpenChange={setQrAppointmentOpen}
				onPatientVerified={handlePatientVerified}
			/>
		</>
	);
}

// Main dashboard content renderer
function DashboardContent() {
	const role = useCurrentRole();
	const navigation = useDashboardNavigation();
	const [currentTab] = useQueryState('tab', searchParamParsers.tab);
	const [appointmentId] = useQueryState(
		'appointmentId',
		searchParamParsers.appointmentId
	);

	// If viewing appointment details, show appointment details page
	if (appointmentId) {
		return <AppointmentDetailsPage appointmentId={appointmentId} />;
	}

	// Render tab content based on current tab and role
	const renderTabContent = (tab: string | null) => {
		// Check if user can access this tab
		if (tab && !navigation.canAccessTab(tab)) {
			return (
				<div className="py-8 text-center">
					<p className="text-muted-foreground">Access denied to this section</p>
				</div>
			);
		}

		switch (tab) {
			case 'appointments':
				return <AppointmentListWrapper />;

			case 'calendar':
				return <DoctorCalendarWrapper />;

			case 'patients':
				return <PatientManagement />;

			case 'clinics':
				return <ClinicManagement />;

			case 'medical-documents':
				return <MedicalDocumentsManagement />;

			case 'history':
				return <AppointmentHistoryList />;

			case 'archive':
				return <ArchivesManagement />;

			// Doctor-only tabs
			case 'assistants':
				return role === 'doctor' ? <AssistantManagement /> : null;

			case 'subscription':
				return role === 'doctor' ? <DoctorSubscriptionManagement /> : null;

			case 'settings':
				return role === 'doctor' ? (
					<DoctorSettingsManagement />
				) : (
					<div className="py-8 text-center">
						<p className="text-muted-foreground">Settings coming soon</p>
					</div>
				);
			case 'profile': {
				if (role === 'doctor') {
					return <DoctorProfilePage />;
				}
				return (
					<div className="py-8 text-center">
						<p className="text-muted-foreground">Profile coming soon</p>
					</div>
				);
			}

			case 'overview':
			default:
				return <DashboardOverview />;
		}
	};

	return renderTabContent(currentTab);
}

// Main unified dashboard component
export function UnifiedDashboard() {
	return (
		<DashboardRoleProvider>
			<DashboardInner />
		</DashboardRoleProvider>
	);
}

function DashboardInner() {
	const role = useCurrentRole();
	const [currentTab] = useQueryState('tab', searchParamParsers.tab);
	const router = useRouter();

	const breadcrumbData = getBreadcrumbData(currentTab, role);

	return (
		<DoctorPasswordChangeGuard>
			<DoctorOnboardingGuard>
				<SidebarProvider>
					<RoleAwareSidebar />
					<SidebarInset>
						<header className="flex h-16 shrink-0 items-center justify-between gap-2">
							<div className="flex items-center gap-2 px-4">
								<SidebarTrigger className="-ml-1" />
								<Separator
									orientation="vertical"
									className="mr-2 data-[orientation=vertical]:h-4"
								/>
								<Breadcrumb>
									<BreadcrumbList>
										<BreadcrumbItem className="hidden md:block">
											<BreadcrumbLink
												href="/dashboard"
												onClick={(e) => {
													e.preventDefault();
													router.push('/dashboard');
												}}
											>
												{breadcrumbData.items[0]?.label}
											</BreadcrumbLink>
										</BreadcrumbItem>
										{breadcrumbData.items.length > 1 && (
											<>
												<BreadcrumbSeparator className="hidden md:block" />
												<BreadcrumbItem>
													<BreadcrumbPage>
														{breadcrumbData.title}
													</BreadcrumbPage>
												</BreadcrumbItem>
											</>
										)}
									</BreadcrumbList>
								</Breadcrumb>
							</div>
						</header>
						<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
							<DashboardContent />
						</div>
					</SidebarInset>
				</SidebarProvider>
			</DoctorOnboardingGuard>
		</DoctorPasswordChangeGuard>
	);
}
