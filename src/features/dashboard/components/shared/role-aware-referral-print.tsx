import React from 'react';

import { poppins } from '@/components/common/font';
import { formatDate } from '@/core/lib/utils';

import { AssistantReferral } from '../../types/assistant.type';
import { IReferral } from '../../types/doctor.types';

type ReferralModalProps = {
	data: IReferral | AssistantReferral;
	componentRef: React.RefObject<HTMLDivElement | null>;
};

export const ReferralPrint: React.FC<ReferralModalProps> = ({
	data,
	componentRef,
}) => {
	// Type guard to check if this is a doctor referral
	const isDoctorReferral = (
		referral: IReferral | AssistantReferral
	): referral is IReferral => {
		return 'doctorProfile' in (referral.clinic || {});
	};

	// Get the appropriate profile based on referral type
	const doctorProfile = isDoctorReferral(data)
		? data?.clinic?.doctorProfile
		: null;
	const assistantProfile = !isDoctorReferral(data)
		? (data as Assistant<PERSON><PERSON><PERSON><PERSON>)?.clinic?.assistantProfile
		: null;

	const clinicInfo = data?.clinic;

	return (
		<div
			className={`flex w-full flex-col space-y-1 p-5 ${poppins.className}`}
			ref={componentRef}
		>
			<div className="w-full">
				<div className="flex items-center justify-between">
					<div className="w-3/4"></div>
					<div className="flex w-1/2 flex-col text-sm">
						{/* Clinic Details */}
						<p className="font-bold">
							{doctorProfile
								? [
										doctorProfile?.first_name,
										doctorProfile?.middle_name,
										doctorProfile?.last_name,
									]
										.filter((item) => item)
										.join(' ')
								: assistantProfile
									? [
											assistantProfile?.first_name,
											assistantProfile?.middle_name,
											assistantProfile?.last_name,
										]
											.filter((item) => item)
											.join(' ')
									: 'Name not set'}
						</p>
						<p className="font-semibold">
							{doctorProfile?.doctor?.specialty || 'Specialty not set'}
						</p>
						<p className="text-xs">Clinic Name: {clinicInfo?.name}</p>
						<p className="text-xs">Clinic Address: {clinicInfo?.address}</p>
						<p className="text-xs">Contact No: {clinicInfo?.phone}</p>
					</div>
				</div>
			</div>

			{/* Patient Information */}
			<div className="flex flex-row space-x-5 pb-2">
				<div className="text-xs">
					<p>Patient Name: {data?.patient_name?.toUpperCase()}</p>
					<p>Gender: {data?.patient_gender}</p>
					<p>
						Age:{' '}
						{data?.patient_birthdate &&
							new Date().getFullYear() -
								new Date(data?.patient_birthdate).getFullYear() +
								' YEARS'}
					</p>
					<p>Address: {data?.patient_address?.toUpperCase()}</p>
					<p>Contact: {data?.patient_phone}</p>
					<p>Date: {formatDate(data?.created_at)}</p>
				</div>
			</div>

			{/* <div className="flex flex-col space-y-2 mx-5 bg-[url('/images/prescription/PrescriptionLogoMarkBackground.png')] min-h-[250px] bg-center bg-4/6 bg-no-repeat"> */}
			<div className="flex min-h-[250px] flex-col space-y-2">
				<div className="flex w-full flex-row space-x-3 pb-2">
					<div className="w-[45%]">
						<div className="flex flex-col rounded-lg border border-black p-5 text-sm">
							<div className="flex flex-col">
								<p className="font-bold">Name</p>
								<p className="text-[1FB5B4]">{data?.patient_name}</p>
							</div>
							<div className="flex flex-col">
								<p className=" font-bold">Contact Number</p>
								<p className="text-[1FB5B4]">{data?.patient_phone}</p>
							</div>

							<div className="flex flex-col">
								<p className=" font-bold">Address</p>
								<p className="text-[1FB5B4]">{data?.patient_address}</p>
							</div>
							<div className="flex flex-col">
								<p className=" font-bold">Occupation</p>
								<p className="text-[1FB5B4]">
									{data?.patient_occupation || '-- --'}
								</p>
							</div>
							<div className="flex flex-col">
								<p className=" font-bold">Birthday</p>
								<p className="text-[1FB5B4]">{data?.patient_birthdate}</p>
							</div>
						</div>
						<p className="text-center text-[10px] font-medium italic">
							Scan for Secured Copy of <br /> E-Medical Records
						</p>
					</div>
					<div className="w-[65%] space-y-2 text-sm">
						<div className="flex flex-col rounded-lg border border-black p-5">
							<div className="mb-2 flex items-center justify-between space-x-5">
								<div className="flex w-full items-center justify-between">
									<div className="flex w-full flex-row space-x-1">
										<div>
											<p className="font-bold">To Doctor:</p>
											<p>{data?.doctor_name}</p>
											<p className="font-bold">Referral Purpose:</p>
											<p>{data?.purpose}</p>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div className="flex flex-col rounded-lg border border-black  p-5">
							<p className="font-bold ">Vital Signs</p>
							<div className="mt-5 flex space-x-2">
								<p className="font-bold">Blood Pressure:</p>
								<p>
									{data?.vitalSign?.systolic || '-- --'} /{' '}
									{data?.vitalSign?.diastolic || '-- --'}{' '}
								</p>
							</div>
							<div className="flex space-x-2">
								<p className="font-bold">Pulse Rate:</p>
								<p>{data?.vitalSign?.pulse_rate || '-- --'}</p>
							</div>
							<div className="flex space-x-2">
								<p className="font-bold">Height{'(CM)'}:</p>
								<p>{data?.vitalSign?.height || '-- --'}</p>
							</div>
							<div className="flex space-x-2">
								<p className="font-bold">Weight:</p>
								<p>
									{data?.vitalSign?.weight || '-- --'}{' '}
									{data?.vitalSign?.weight_type || '-- --'}
								</p>
							</div>
							<div className="flex space-x-2">
								<p className="font-bold">Temperature:</p>
								<p>
									{data?.vitalSign?.temperature || '-- --'}{' '}
									{data?.vitalSign?.temperature_type || '-- --'}{' '}
								</p>
							</div>
							<div className="flex space-x-2">
								<p className="font-bold">Respiration:</p>
								<p>{data?.vitalSign?.respiration || '-- --'} </p>
							</div>
						</div>
					</div>
				</div>
				<div className="flex w-full flex-col items-end">
					<div className="w-2/5">
						{doctorProfile?.doctor?.e_signature && (
							<div className="flex w-full items-center justify-center">
								<img
									src={doctorProfile?.doctor?.e_signature}
									width={100}
									height={100}
									alt="signature"
									className="w-1/2"
								/>
							</div>
						)}
						<div className="border-1" />
						<p className="font-bold">
							{doctorProfile ? 'Dr. ' : ''}
							{doctorProfile
								? [
										doctorProfile?.first_name,
										doctorProfile?.middle_name,
										doctorProfile?.last_name,
									]
										.filter((item) => item)
										.join(' ')
								: assistantProfile
									? [
											assistantProfile?.first_name,
											assistantProfile?.middle_name,
											assistantProfile?.last_name,
										]
											.filter((item) => item)
											.join(' ') + ' (Assistant)'
									: '-- --'}
						</p>
						<p className="text-xs">
							License Number: {doctorProfile?.doctor?.prc_number || '-- --'}
						</p>
						{data?.professional_tax_receipt && (
							<p className="text-xs">PTR: {data?.professional_tax_receipt}</p>
						)}
						<p className="mt-5 text-xs font-semibold">
							This referral is electronically generated. Should you wish to
							verify authenticity kindly call the doctor’s clinic or email us at{' '}
							<br /> <EMAIL>
						</p>
					</div>
				</div>
			</div>
			<div className="relative">
				<div className="flex items-center justify-between">
					<div className="w-1/4">
						<img
							src="/elena-logo-black.webp"
							width={130}
							height={200}
							alt="header"
							className="w-3/4"
						/>
					</div>
				</div>
			</div>
		</div>
	);
};
