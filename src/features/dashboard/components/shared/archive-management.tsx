'use client';

import {
	Award,
	FileText,
	FlaskConical,
	Stethoscope,
	Users,
} from 'lucide-react';
import { useQueryState } from 'nuqs';
import React from 'react';

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { searchParamParsers } from '@/core/lib/search-params';

import HorizontalScrollBar from '../HorizontalScrollBar';
import { PatientManagement } from './patient-management';
import { RoleAwareDiagnosticRequestManagement } from './role-aware-diagnostic-request-management';
import { RoleAwareLabRequestManagement } from './role-aware-lab-request-management';
import { RoleAwareMedicalCertificateManagement } from './role-aware-medical-certificate-management';
import { RoleAwarePrescriptionManagement } from './role-aware-prescription-management';
import { RoleAwareReferralManagement } from './role-aware-referral-management';

// Import assistant table components for additional document types
// Import role-aware components

export function ArchivesManagement() {
	// URL search parameter using nuqs
	const [documentType, setDocumentType] = useQueryState(
		'type',
		searchParamParsers.type
	);

	// Sync activeTab with URL parameter
	React.useEffect(() => {
		// No need for local activeTab state, use documentType directly
	}, [documentType]);

	// Handle tab change
	const handleTabChange = (value: string) => {
		// Update URL to reflect the current tab
		const validTypes = [
			'prescriptions',
			'lab-requests',
			'referrals',
			'diagnostic-requests',
			'medical-certificates',
			'patients',
		];

		if (validTypes.includes(value)) {
			setDocumentType(
				value as
					| 'prescriptions'
					| 'lab-requests'
					| 'referrals'
					| 'diagnostic-requests'
					| 'medical-certificates'
					| 'patients'
			);
		}
	};

	// Get available tabs based on role
	const getAvailableTabs = () => {
		const commonTabs = [
			{
				value: 'prescriptions',
				label: 'Prescriptions',
				icon: FileText,
				description: 'Patient prescriptions and medications',
			},
			{
				value: 'lab-requests',
				label: 'Lab Requests',
				icon: FlaskConical,
				description: 'Laboratory test requests',
			},
			{
				value: 'diagnostic-requests',
				label: 'Diagnostic Requests',
				icon: Stethoscope,
				description: 'Diagnostic test requests and imaging orders',
			},
			{
				value: 'referrals',
				label: 'Referrals',
				icon: Users,
				description: 'Patient referrals to specialists',
			},
			{
				value: 'medical-certificates',
				label: 'Medical Certificates',
				icon: Award,
				description: 'Medical certificates and clearances',
			},
			{
				value: 'patients',
				label: 'Patients',
				icon: Users,
				description: 'Archived patient records',
			},
		];

		return commonTabs;
	};

	const availableTabs = getAvailableTabs();

	return (
		<div className="flex flex-col gap-4 px-4">
			<div className="mb-4 flex flex-col items-start justify-between gap-4 lg:flex-row">
				<div>
					<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
						Archive Management
					</h1>
					<p className="text-muted-foreground">
						Manage your archived medical documents and patient records
					</p>
				</div>
			</div>

			<Tabs value={documentType} onValueChange={handleTabChange}>
				<HorizontalScrollBar>
					<TabsList className="flex w-full md:space-x-2">
						{availableTabs.map((tab) => (
							<TabsTrigger
								key={tab.value}
								value={tab.value}
								className="flex items-center gap-2"
							>
								<tab.icon className="h-4 w-4" />
								<span className="hidden sm:inline">{tab.label}</span>
							</TabsTrigger>
						))}
					</TabsList>
				</HorizontalScrollBar>

				{/* Prescription Management */}
				<TabsContent value="prescriptions" className="space-y-4">
					<RoleAwarePrescriptionManagement isArchived={true} />
				</TabsContent>

				{/* Lab Request Management */}
				<TabsContent value="lab-requests" className="space-y-4">
					<RoleAwareLabRequestManagement isArchived={true} />
				</TabsContent>

				{/* Referral Management */}
				<TabsContent value="referrals" className="space-y-4">
					<RoleAwareReferralManagement isArchived={true} />
				</TabsContent>

				{/* Diagnostic Requests - Assistant only */}
				<TabsContent value="diagnostic-requests" className="space-y-4">
					<RoleAwareDiagnosticRequestManagement isArchived={true} />
				</TabsContent>

				{/* Medical Certificates - Assistant only */}
				<TabsContent value="medical-certificates" className="space-y-4">
					<RoleAwareMedicalCertificateManagement isArchived={true} />
				</TabsContent>

				{/* Patient Management - Doctor only */}
				<TabsContent value="patients" className="space-y-4">
					<PatientManagement isArchived={true} />
				</TabsContent>
			</Tabs>
		</div>
	);
}
