'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { useSession } from '@/core/hooks/useSession';
import { useCurrentRole } from '@/features/dashboard/context/DashboardRoleContext';
import { useUserRole } from '@/features/dashboard/hooks/useRoleAwareDashboard';

export function RoleDebug() {
	const { userRoles, isAuthenticated, user } = useSession();
	const roleFromHook = useUserRole();
	const roleFromContext = useCurrentRole();

	// Get localStorage data
	let localStorageRole = null;
	let localStorageSession = null;

	if (typeof window !== 'undefined') {
		try {
			localStorageRole = window.localStorage.getItem('userRole');
			const sessionData = window.localStorage.getItem('session');
			if (sessionData) {
				localStorageSession = JSON.parse(sessionData);
			}
		} catch (error) {
			console.error('Error reading localStorage:', error);
		}
	}

	return (
		<Card className="mb-4 border-yellow-200 bg-yellow-50">
			<CardHeader>
				<CardTitle className="text-yellow-800">Role Detection Debug</CardTitle>
			</CardHeader>
			<CardContent className="space-y-4">
				<div>
					<h4 className="font-semibold text-yellow-800">Current Roles:</h4>
					<p>
						Role from Hook:{' '}
						<span className="font-mono">{roleFromHook || 'null'}</span>
					</p>
					<p>
						Role from Context:{' '}
						<span className="font-mono">{roleFromContext || 'null'}</span>
					</p>
				</div>

				<div>
					<h4 className="font-semibold text-yellow-800">Session Data:</h4>
					<p>
						Is Authenticated:{' '}
						<span className="font-mono">
							{isAuthenticated ? 'true' : 'false'}
						</span>
					</p>
					<p>
						User ID: <span className="font-mono">{user?.id || 'null'}</span>
					</p>
					<p>User Roles:</p>
					<ul className="ml-4 list-disc">
						<li>
							is_doctor:{' '}
							<span className="font-mono">
								{userRoles?.is_doctor ? 'true' : 'false'}
							</span>
						</li>
						<li>
							is_assistant:{' '}
							<span className="font-mono">
								{userRoles?.is_assistant ? 'true' : 'false'}
							</span>
						</li>
						<li>
							is_admin:{' '}
							<span className="font-mono">
								{userRoles?.is_admin ? 'true' : 'false'}
							</span>
						</li>
						<li>
							is_patient:{' '}
							<span className="font-mono">
								{userRoles?.is_patient ? 'true' : 'false'}
							</span>
						</li>
					</ul>
				</div>

				<div>
					<h4 className="font-semibold text-yellow-800">LocalStorage:</h4>
					<p>
						Role Hint:{' '}
						<span className="font-mono">{localStorageRole || 'null'}</span>
					</p>
					<p>
						Session Data Available:{' '}
						<span className="font-mono">
							{localStorageSession ? 'true' : 'false'}
						</span>
					</p>
					{localStorageSession && (
						<details className="mt-2">
							<summary className="cursor-pointer text-sm text-yellow-700">
								View Session Data
							</summary>
							<pre className="mt-2 max-h-40 overflow-auto rounded bg-yellow-100 p-2 text-xs">
								{JSON.stringify(localStorageSession, null, 2)}
							</pre>
						</details>
					)}
				</div>

				<div>
					<h4 className="font-semibold text-yellow-800">URL Info:</h4>
					<p>
						Pathname:{' '}
						<span className="font-mono">
							{typeof window !== 'undefined' ? window.location.pathname : 'N/A'}
						</span>
					</p>
					<p>
						Full URL:{' '}
						<span className="font-mono">
							{typeof window !== 'undefined' ? window.location.href : 'N/A'}
						</span>
					</p>
				</div>
			</CardContent>
		</Card>
	);
}
