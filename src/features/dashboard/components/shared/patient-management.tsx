'use client';

import { Plus } from 'lucide-react';
import { useQueryStates } from 'nuqs';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { searchParamParsers } from '@/core/lib/search-params';
import {
	useCurrentRole,
	useDashboardPermissions,
} from '@/features/dashboard/context/DashboardRoleContext';
import {
	useClinics,
	usePatients,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';

import { CreatePatientDialog } from '../doctor-dashboard/patient/create-patient-dialog';
import { EditPatientDialog } from '../doctor-dashboard/patient/edit-patient-dialog';
// Import existing patient components (these will be reused)
import { PatientDetails } from '../doctor-dashboard/patient/patient-details';
import { PatientList } from '../doctor-dashboard/patient/patient-list';

// Extended search params for patient filtering
interface IPatientSearchParams {
	search?: string;
	page?: number;
	pageSize?: number;
	bloodType?: string | null;
	gender?: string | null;
	status?: string | null;
	isArchived?: boolean;
}

export function PatientManagement({
	isArchived = false,
}: {
	isArchived?: boolean;
}) {
	const role = useCurrentRole();
	const permissions = useDashboardPermissions();

	// URL search parameters using nuqs
	const [{ action, id: patientId, search, page }, setSearchParams] =
		useQueryStates({
			action: searchParamParsers.action,
			id: searchParamParsers.id,
			search: searchParamParsers.search,
			page: searchParamParsers.page,
		});
	const [filters, setFilters] = useState({
		bloodType: 'all',
		gender: 'all',
		status: 'all',
	});
	const [createDialogOpen, setCreateDialogOpen] = useState(action === 'create');
	const [editDialogOpen, setEditDialogOpen] = useState(false);

	const pageSize = 10;

	// Use role-aware hooks
	const {
		data: patientsResponse,
		isLoading: patientsLoading,
		error: patientsError,
	} = usePatients({
		search: search || undefined,
		page: page || 1,
		pageSize,
		...filters,
		isArchived,
	} as IPatientSearchParams);

	const { data: clinicsResponse } = useClinics({
		pageSize: 100,
	});

	const patients = Array.isArray(patientsResponse?.data?.data)
		? patientsResponse.data.data
		: [];
	const clinics = Array.isArray(clinicsResponse?.data?.data)
		? clinicsResponse.data.data
		: [];
	const totalPages = patientsResponse?.data?.meta?.last_page || 1;
	const currentPage = page || 1;

	// Navigation handlers
	const navigateToCreate = () => {
		setSearchParams({ action: 'create' });
		setCreateDialogOpen(true);
	};

	const navigateToEdit = (id: number) => {
		setSearchParams({ action: 'edit', id });
		setEditDialogOpen(true);
		console.log('isEditDialogOpen', editDialogOpen);
	};

	const navigateToDetail = (id: number) => {
		setSearchParams({ action: 'view', id });
	};

	const navigateToList = () => {
		setSearchParams({ action: null, id: null });
	};

	const handleDialogClose = () => {
		setCreateDialogOpen(false);
		setEditDialogOpen(false);
	};

	const handleSearch = (searchTerm: string) => {
		setSearchParams({ search: searchTerm || null, page: null });
	};

	const handlePageChange = (newPage: number) => {
		setSearchParams({ page: newPage });
	};

	const handleFilterChange = (filterType: string, value: string) => {
		setFilters((prev) => ({ ...prev, [filterType]: value }));
		setSearchParams({ page: null }); // Reset to first page when filtering
	};

	// Role-specific titles and descriptions
	const getTitle = () => {
		switch (role) {
			case 'doctor':
				return 'Patient Management';
			case 'assistant':
				return 'Patient Records';
			default:
				return 'Patients';
		}
	};

	const getDescription = () => {
		switch (role) {
			case 'doctor':
				return 'Manage your patients and their medical records';
			case 'assistant':
				return 'View and assist with patient records';
			default:
				return 'Patient information';
		}
	};

	// Render current view
	const renderCurrentView = () => {
		if (patientId) {
			return (
				<PatientDetails
					patientId={patientId}
					onEdit={navigateToEdit}
					onBack={navigateToList}
				/>
			);
		}

		// default (doctor)
		return (
			<PatientList
				patients={patients}
				isLoading={patientsLoading}
				error={patientsError}
				currentPage={currentPage}
				totalPages={totalPages}
				onPageChange={handlePageChange}
				onCreatePatient={navigateToCreate}
				onViewPatient={navigateToDetail}
				onEditPatient={navigateToEdit}
			/>
		);
	};

	return (
		<div
			className={`flex flex-1 flex-col gap-4 pt-0 ${isArchived ? 'p-0' : 'p-4'}`}
		>
			{/* Header */}
			{!patientId && !isArchived && (
				<div className="flex items-start justify-between">
					<div className="mb-4">
						<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
							{getTitle()}
						</h1>
						<p className="text-muted-foreground">{getDescription()}</p>
					</div>
					{permissions.canManagePatients && (
						<Button
							onClick={navigateToCreate}
							className="gap-2 bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
						>
							<Plus className="h-4 w-4" />
							Add Patient
						</Button>
					)}
				</div>
			)}

			{/* Filters */}
			{!patientId && (
				<div className="flex flex-col gap-4 lg:flex-row">
					<div className="flex-1">
						<Input
							placeholder="Search patients..."
							value={search || ''}
							onChange={(e) => handleSearch(e.target.value)}
							className="max-w-sm"
						/>
					</div>
					<div className="flex flex-row items-center gap-4">
						<Select
							value={filters.bloodType}
							onValueChange={(value) => handleFilterChange('bloodType', value)}
						>
							<SelectTrigger className="w-40">
								<SelectValue placeholder="Blood Type" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">All Blood Types</SelectItem>
								<SelectItem value="A+">A+</SelectItem>
								<SelectItem value="A-">A-</SelectItem>
								<SelectItem value="B+">B+</SelectItem>
								<SelectItem value="B-">B-</SelectItem>
								<SelectItem value="AB+">AB+</SelectItem>
								<SelectItem value="AB-">AB-</SelectItem>
								<SelectItem value="O+">O+</SelectItem>
								<SelectItem value="O-">O-</SelectItem>
							</SelectContent>
						</Select>
						<Select
							value={filters.gender}
							onValueChange={(value) => handleFilterChange('gender', value)}
						>
							<SelectTrigger className="w-32">
								<SelectValue placeholder="Gender" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">All Genders</SelectItem>
								<SelectItem value="Male">Male</SelectItem>
								<SelectItem value="Female">Female</SelectItem>
								<SelectItem value="Other">Other</SelectItem>
							</SelectContent>
						</Select>
						<Select
							value={filters.status}
							onValueChange={(value) => handleFilterChange('status', value)}
						>
							<SelectTrigger className="w-32">
								<SelectValue placeholder="Status" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">All Status</SelectItem>
								<SelectItem value="1">Active</SelectItem>
								<SelectItem value="0">Inactive</SelectItem>
							</SelectContent>
						</Select>
					</div>
				</div>
			)}

			{/* Main Content */}
			{renderCurrentView()}

			{/* Dialogs */}
			{permissions.canManagePatients && (
				<CreatePatientDialog
					open={createDialogOpen}
					onOpenChange={(open) => {
						if (!open) handleDialogClose();
					}}
					clinics={clinics}
				/>
			)}

			{patientId && (
				<EditPatientDialog
					open={editDialogOpen}
					onOpenChange={(open) => {
						if (!open) {
							setEditDialogOpen(false);
						}
					}}
					patientId={patientId}
					onClose={handleDialogClose}
				/>
			)}
		</div>
	);
}
