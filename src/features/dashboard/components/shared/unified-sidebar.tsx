'use client';

import {
	Archive,
	BarChart3,
	Calendar,
	CreditCard,
	FileClock,
	FileText,
	Home,
	ListCheck,
	Settings,
	UserCheck,
	Users,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

import {
	Sidebar,
	SidebarContent,
	<PERSON>barFooter,
	SidebarHeader,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
} from '@/components/ui/sidebar';
import { useSession } from '@/core/hooks/useSession';
import { NavMain } from '@/features/dashboard/components/nav-main';
import { NavSecondary } from '@/features/dashboard/components/nav-secondary';
import { NavUser } from '@/features/dashboard/components/nav-user';
import {
	useCurrentRole,
	useDashboardNavigation,
} from '@/features/dashboard/context/DashboardRoleContext';

// Role-aware navigation data
const getNavData = (
	role: 'doctor' | 'assistant' | 'admin' | 'patient' | null
) => {
	const commonNavItems = [
		{
			title: 'Dashboard',
			url: '/dashboard?tab=overview',
			icon: role === 'doctor' ? BarChart3 : Home,
		},
		{
			title: 'Appointments',
			url: '/dashboard?tab=appointments',
			icon: ListCheck,
		},
		{
			title: 'Medical Documents',
			url: '/dashboard?tab=medical-documents',
			icon: FileText,
		},
		{
			title: 'History',
			url: '/dashboard?tab=history',
			icon: FileClock,
		},
		{
			title: 'Calendar',
			url: '/dashboard?tab=calendar',
			icon: Calendar,
		},
		{
			title: 'Patients',
			url: '/dashboard?tab=patients',
			icon: Users,
		},

		...(role === 'doctor'
			? [
					{
						title: 'Clinics',
						url: '/dashboard?tab=clinics',
						icon: Home,
					},
					{
						title: 'Assistants',
						url: '/dashboard?tab=assistants',
						icon: UserCheck,
					},
				]
			: []),
		{
			title: 'Archive',
			url: '/dashboard?tab=archive',
			icon: Archive,
		},
	];

	// Doctor-specific navigation items
	const doctorOnlyItems = [
		{
			title: 'Subscription',
			url: '/dashboard?tab=subscription',
			icon: CreditCard,
		},
	];

	const navMain =
		role === 'doctor'
			? [...commonNavItems, ...doctorOnlyItems]
			: commonNavItems;

	const navSecondary = [
		{
			title: 'Settings',
			url: '/dashboard?tab=settings',
			icon: Settings,
		},
	];

	return {
		navMain,
		navSecondary,
	};
};

export function UnifiedSidebar() {
	const router = useRouter();
	const role = useCurrentRole();
	const navigation = useDashboardNavigation();
	const { user } = useSession();

	// Get role-specific navigation data
	const navData = getNavData(role);

	// Filter navigation items based on permissions
	const filteredNavMain = navData.navMain.filter((item) => {
		// Extract tab from URL
		const urlParams = new URLSearchParams(item.url.split('?')[1] || '');
		const tab = urlParams.get('tab');

		// Check if user can access this tab
		return !tab || navigation.canAccessTab(tab);
	});

	return (
		<Sidebar variant="inset">
			<SidebarHeader>
				<SidebarMenu>
					<SidebarMenuItem>
						<SidebarMenuButton
							size="lg"
							onClick={() => router.push('/dashboard')}
						>
							<div className="flex w-full items-center rounded-lg">
								<img src="/elena-banner.png" alt="Elena" className="h-9" />
							</div>
						</SidebarMenuButton>
					</SidebarMenuItem>
				</SidebarMenu>
			</SidebarHeader>
			<SidebarContent>
				<NavMain items={filteredNavMain} />
				<NavSecondary items={navData.navSecondary} className="mt-auto" />
			</SidebarContent>
			<SidebarFooter>
				<NavUser
					user={{
						name:
							(user?.profile?.first_name || '') +
								' ' +
								(user?.profile?.last_name || '') || 'User',
						email: user?.email || '<EMAIL>',
						avatar: '/avatars/default.jpg',
						role,
					}}
				/>
			</SidebarFooter>
		</Sidebar>
	);
}
