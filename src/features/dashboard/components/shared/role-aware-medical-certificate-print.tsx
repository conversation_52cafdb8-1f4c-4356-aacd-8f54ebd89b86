import React from 'react';

import { poppins } from '@/components/common/font';
import { formatDate } from '@/core/lib/utils';

import { AssistantMedicalCertificate } from '../../types/assistant.type';
import {
	IMedicalCertificate,
	IMedicalCertificateItem,
} from '../../types/doctor.types';

type MedicalCertificateModalProps = {
	data: IMedicalCertificate | AssistantMedicalCertificate;
	componentRef: React.RefObject<HTMLDivElement | null>;
};

export const CheckMedical = () => {
	return (
		<div className="w-6 bg-black">
			<svg
				xmlns="http://www.w3.org/2000/svg"
				width="25"
				height="24"
				viewBox="0 0 25 24"
				fill="none"
			>
				<path
					d="M5.56006 14L9.56006 17L18.5601 6"
					stroke="white"
					strokeWidth="2"
				/>
			</svg>
		</div>
	);
};

export const MedicalCertificatePrint: React.FC<
	MedicalCertificateModalProps
> = ({ data, componentRef }) => {
	// Type guard to check if this is a doctor prescription
	const isDoctorMedCert = (
		prescription: IMedicalCertificate | AssistantMedicalCertificate
	): prescription is IMedicalCertificate => {
		return 'doctorProfile' in (prescription.clinic || {});
	};

	// Get the appropriate profile based on prescription type
	const doctorProfile = isDoctorMedCert(data)
		? data?.clinic?.doctorProfile
		: null;
	const assistantProfile = !isDoctorMedCert(data)
		? (data as AssistantMedicalCertificate)?.clinic?.assistantProfile
		: null;
	const clinicInfo = data?.clinic;

	return (
		<div
			className={`flex w-full flex-col space-y-1 p-5 ${poppins.className}`}
			ref={componentRef}
		>
			<div className="w-full">
				<div className="flex items-center justify-between">
					<div className="w-3/4"></div>
					<div className="flex w-1/2 flex-col text-sm">
						{/* Clinic Details */}
						<p className="font-bold">
							{doctorProfile
								? [
										doctorProfile?.first_name,
										doctorProfile?.middle_name,
										doctorProfile?.last_name,
									]
										.filter((item) => item)
										.join(' ')
								: assistantProfile
									? [
											assistantProfile?.first_name,
											assistantProfile?.middle_name,
											assistantProfile?.last_name,
										]
											.filter((item) => item)
											.join(' ')
									: 'Name not set'}
						</p>
						<p className="font-semibold">
							{doctorProfile?.doctor?.specialty ||
								(assistantProfile ? 'Assistant' : 'Specialty not set')}
						</p>
						<p className="text-xs">Clinic Name: {clinicInfo?.name}</p>
						<p className="text-xs">Clinic Address: {clinicInfo?.address}</p>
						<p className="text-xs">Contact No: {clinicInfo?.phone}</p>
					</div>
				</div>
			</div>

			{/* Patient Information */}
			<div className="flex flex-row space-x-5 pb-2">
				<div className="text-xs ">
					<p>Patient Name: {data?.patient_name?.toUpperCase()}</p>
					<p>Gender: {data?.patient_gender}</p>
					<p>
						Age:{' '}
						{data?.patient_birthdate &&
							new Date().getFullYear() -
								new Date(data?.patient_birthdate).getFullYear() +
								' YEARS'}
					</p>
					<p>Address: {data?.patient_address?.toUpperCase()}</p>
					<p>Contact: {data?.patient_phone}</p>
					<p>Date: {formatDate(data?.created_at)}</p>
					<p>Medical Certificate No: {data?.medical_number}</p>
				</div>
			</div>

			<div className="mx-5 flex flex-col space-y-5">
				<div className="w-full text-center text-3xl font-bold">
					<p>MEDICAL CERTIFICATE</p>
				</div>

				<div className="space-y-2 text-sm">
					{/* mapping */}
					{data?.medicalCertificateItems?.map(
						(medical: IMedicalCertificateItem, index: number) => (
							<div key={index} className="flex flex-row items-center space-x-2">
								<div>
									<CheckMedical />
								</div>
								<p> {medical?.description}</p>
							</div>
						)
					)}
				</div>
				<div className="text-sm">
					<p className="text-wrap">
						The patient needs to consult a physical doctor for a fit-to-work
						medical certificate. This certificate is being issued upon the
						patients request for whatever purpose it may serve, except for
						medico-legal purposes.
					</p>
				</div>
				<div className="flex w-full flex-col items-end">
					<div className="w-2/5">
						{doctorProfile?.doctor?.e_signature && (
							<div className="flex w-full items-center justify-center">
								<img
									src={doctorProfile?.doctor?.e_signature}
									width={100}
									height={100}
									alt="signature"
									className="w-1/2"
								/>
							</div>
						)}
						<div className="border-1" />
						<p className="font-bold">
							{doctorProfile ? 'Dr. ' : ''}
							{doctorProfile
								? [
										doctorProfile?.first_name,
										doctorProfile?.middle_name,
										doctorProfile?.last_name,
									]
										.filter((item) => item)
										.join(' ')
								: assistantProfile
									? [
											assistantProfile?.first_name,
											assistantProfile?.middle_name,
											assistantProfile?.last_name,
										]
											.filter((item) => item)
											.join(' ') + ' (Assistant)'
									: '-- --'}
						</p>
						<p className="text-xs">
							License Number: {doctorProfile?.doctor?.prc_number || '-- --'}
						</p>
						{data?.professional_tax_receipt && (
							<p className="text-xs">PTR: {data?.professional_tax_receipt}</p>
						)}
						<p className="mt-5 text-xs font-semibold">
							This medical certificate is electronically generated. Should you
							wish to verify authenticity kindly call the doctor’s clinic or
							email us at <br /> <EMAIL>
						</p>
					</div>
				</div>
			</div>
			<div className="relative">
				<div className="flex items-center justify-between">
					<div className="w-1/4">
						<img
							src="/elena-logo-black.webp"
							width={130}
							height={200}
							alt="header"
							className="w-3/4"
						/>
					</div>
				</div>
			</div>
		</div>
	);
};
