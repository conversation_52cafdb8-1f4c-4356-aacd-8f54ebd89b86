'use client';

import {
	Calendar,
	CalendarCheck,
	Clock,
	Eye,
	MoreHorizontal,
	Plus,
	QrCode,
} from 'lucide-react';
import { useQueryState } from 'nuqs';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { searchParamParsers } from '@/core/lib/search-params';
import {
	useCurrentRole,
	useDashboardPermissions,
} from '@/features/dashboard/context/DashboardRoleContext';
import {
	useClinics,
	useTodaysAttendedAppointments,
	useTodaysQueueAppointments,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import {
	EAppointmentStatus,
	IAppointment,
} from '@/features/dashboard/types/doctor.types';

interface IAppointmentQueueProps {
	onViewAppointment?: (appointmentId: number) => void;
	onCreateAppointment?: () => void;
	onQRAppointment?: () => void;
}

interface IAppointmentRowProps {
	appointment: IAppointment;
	onStatusUpdate?: (appointmentId: number, status: string) => void;
	onView: (appointmentId: number) => void;
	canUpdateStatus: boolean;
}

const AppointmentRow = ({
	appointment,
	onStatusUpdate,
	onView,
	canUpdateStatus,
}: IAppointmentRowProps) => {
	const getStatusBadge = (status: string) => {
		const statusConfig: Record<
			string,
			{
				variant: 'secondary' | 'default' | 'outline' | 'destructive';
				label: string;
			}
		> = {
			[EAppointmentStatus.CREATED]: {
				variant: 'secondary' as const,
				label: 'Created',
			},
			[EAppointmentStatus.CONFIRMED]: {
				variant: 'default' as const,
				label: 'Confirmed',
			},
			[EAppointmentStatus.DECLINED]: {
				variant: 'destructive' as const,
				label: 'Declined',
			},
			[EAppointmentStatus.WAITING]: {
				variant: 'outline' as const,
				label: 'Waiting',
			},
			[EAppointmentStatus.ONGOING]: {
				variant: 'default' as const,
				label: 'Ongoing',
			},
			[EAppointmentStatus.COMPLETED]: {
				variant: 'secondary' as const,
				label: 'Completed',
			},
			[EAppointmentStatus.CANCELLED]: {
				variant: 'destructive' as const,
				label: 'Cancelled',
			},
			[EAppointmentStatus.NO_SHOW]: {
				variant: 'destructive' as const,
				label: 'No Show',
			},
			[EAppointmentStatus.FOLLOW_UP]: {
				variant: 'outline' as const,
				label: 'Follow-up',
			},
			[EAppointmentStatus.RESCHEDULED]: {
				variant: 'outline' as const,
				label: 'Rescheduled',
			},
		};

		const config = statusConfig[status] || {
			variant: 'secondary' as const,
			label: status,
		};

		return <Badge variant={config.variant}>{config.label}</Badge>;
	};

	const formatTime = (dateString: string) => {
		return new Date(dateString).toLocaleTimeString('en-US', {
			hour: '2-digit',
			minute: '2-digit',
		});
	};

	return (
		<TableRow>
			<TableCell>
				<div className="flex items-center gap-3">
					<div className="bg-muted flex h-8 w-8 items-center justify-center rounded-full">
						<span className="text-xs font-medium">
							{appointment.queue_number || '#'}
						</span>
					</div>
					<div>
						<p className="font-medium">
							{appointment.patient?.profile?.first_name}{' '}
							{appointment.patient?.profile?.last_name}
						</p>
						<p className="text-muted-foreground text-sm">
							{appointment.patient?.profile?.phone}
						</p>
					</div>
				</div>
			</TableCell>
			<TableCell>
				<div>
					<p className="font-medium">{appointment.clinic?.name}</p>
					<p className="text-muted-foreground text-sm">
						{appointment.clinic?.address}
					</p>
				</div>
			</TableCell>
			<TableCell>
				<div className="flex items-center gap-2">
					<Clock className="h-4 w-4" />
					<span>{formatTime(appointment.appointment_date)}</span>
				</div>
			</TableCell>
			<TableCell>
				<div>
					<p className="font-medium">
						{appointment.visitReason?.name || 'N/A'}
					</p>
					<p className="text-muted-foreground text-sm">
						{appointment.consultationType?.name || 'N/A'}
					</p>
				</div>
			</TableCell>
			<TableCell>{getStatusBadge(appointment.status)}</TableCell>
			<TableCell>
				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						<Button variant="ghost" className="h-8 w-8 p-0">
							<MoreHorizontal className="h-4 w-4" />
						</Button>
					</DropdownMenuTrigger>
					<DropdownMenuContent align="end">
						<DropdownMenuLabel>Actions</DropdownMenuLabel>
						<DropdownMenuItem onClick={() => onView(appointment.id)}>
							<Eye className="mr-2 h-4 w-4" />
							View Details
						</DropdownMenuItem>
						{canUpdateStatus && onStatusUpdate && (
							<>
								<DropdownMenuSeparator />
								<DropdownMenuItem
									onClick={() =>
										onStatusUpdate(appointment.id, EAppointmentStatus.CONFIRMED)
									}
								>
									Mark as Confirmed
								</DropdownMenuItem>
								<DropdownMenuItem
									onClick={() =>
										onStatusUpdate(appointment.id, EAppointmentStatus.COMPLETED)
									}
								>
									Mark as Completed
								</DropdownMenuItem>
								<DropdownMenuItem
									onClick={() =>
										onStatusUpdate(appointment.id, EAppointmentStatus.CANCELLED)
									}
								>
									Cancel Appointment
								</DropdownMenuItem>
							</>
						)}
					</DropdownMenuContent>
				</DropdownMenu>
			</TableCell>
		</TableRow>
	);
};

export function AppointmentQueue({
	onViewAppointment,
	onCreateAppointment,
	onQRAppointment,
}: IAppointmentQueueProps) {
	const role = useCurrentRole();
	const permissions = useDashboardPermissions();
	const [searchTerm, setSearchTerm] = useState('');
	const [selectedClinic, setSelectedClinic] = useState<string>('all');
	const [, setAppointmentId] = useQueryState(
		'appointmentId',
		searchParamParsers.appointmentId
	);

	// Use role-aware hooks
	const { data: queueData, isLoading: isLoadingQueue } =
		useTodaysQueueAppointments({
			search: searchTerm || undefined,
			clinicId:
				selectedClinic && selectedClinic !== 'all'
					? parseInt(selectedClinic)
					: undefined,
		});

	const { data: attendedData, isLoading: isLoadingAttended } =
		useTodaysAttendedAppointments({
			search: searchTerm || undefined,
			clinicId:
				selectedClinic && selectedClinic !== 'all'
					? parseInt(selectedClinic)
					: undefined,
		});

	const { data: clinicsData } = useClinics();

	const queueAppointments = Array.isArray(queueData?.data)
		? queueData.data
		: [];
	const attendedAppointments = Array.isArray(attendedData?.data)
		? attendedData.data
		: [];
	const clinics = Array.isArray(clinicsData?.data) ? clinicsData.data : [];

	const handleViewAppointment = (id: number) => {
		if (onViewAppointment) {
			onViewAppointment(id);
		} else {
			setAppointmentId(id);
		}
	};

	const handleStatusUpdate = (appointmentId: number, status: string) => {
		// TODO: Implement status update based on role
		console.log('Update appointment status:', appointmentId, status);
	};

	// Role-specific titles
	const getTitle = () => {
		switch (role) {
			case 'doctor':
				return 'Appointment Queue';
			case 'assistant':
				return 'Patient Queue Management';
			default:
				return 'Appointments';
		}
	};

	return (
		<div className="space-y-4">
			<div className="flex items-center justify-between">
				<h2 className="text-xl font-semibold">{getTitle()}</h2>
				<div className="flex gap-2">
					{permissions.canManageAppointments && (
						<Button className="gap-2" onClick={onCreateAppointment}>
							<Plus className="h-4 w-4" />
							New Appointment
						</Button>
					)}
					<Button variant="outline" className="gap-2" onClick={onQRAppointment}>
						<QrCode className="h-4 w-4" />
						QR Appointment
					</Button>
				</div>
			</div>

			{/* Filters */}
			<div className="flex gap-4">
				<div className="flex-1">
					<Input
						placeholder="Search patients..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="max-w-sm"
					/>
				</div>
				<Select value={selectedClinic} onValueChange={setSelectedClinic}>
					<SelectTrigger className="w-48">
						<SelectValue placeholder="All Clinics" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all">All Clinics</SelectItem>
						{clinics.map((clinic) => (
							<SelectItem key={clinic.id} value={clinic.id.toString()}>
								{clinic.name}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
			</div>

			{/* Appointment Tabs */}
			<Tabs defaultValue="queue" className="w-full">
				<TabsList>
					<TabsTrigger value="queue" className="gap-2">
						<Clock className="h-4 w-4" />
						Today&apos;s Queue ({queueAppointments.length})
					</TabsTrigger>
					<TabsTrigger value="attended" className="gap-2">
						<CalendarCheck className="h-4 w-4" />
						Attended ({attendedAppointments.length})
					</TabsTrigger>
				</TabsList>

				<TabsContent value="queue" className="space-y-4">
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Calendar className="h-5 w-5" />
								Today&apos;s Queue
							</CardTitle>
						</CardHeader>
						<CardContent>
							{isLoadingQueue ? (
								<div className="space-y-2">
									{[...Array(5)].map((_, i) => (
										<Skeleton key={i} className="h-16 w-full" />
									))}
								</div>
							) : queueAppointments.length === 0 ? (
								<div className="text-muted-foreground py-8 text-center">
									No appointments in queue for today
								</div>
							) : (
								<Table>
									<TableHeader>
										<TableRow>
											<TableHead>Patient</TableHead>
											<TableHead>Clinic</TableHead>
											<TableHead>Time</TableHead>
											<TableHead>Visit Reason</TableHead>
											<TableHead>Status</TableHead>
											<TableHead>Actions</TableHead>
										</TableRow>
									</TableHeader>
									<TableBody>
										{queueAppointments.map((appointment) => (
											<AppointmentRow
												key={appointment.id}
												appointment={appointment}
												onView={handleViewAppointment}
												onStatusUpdate={handleStatusUpdate}
												canUpdateStatus={permissions.canManageAppointments}
											/>
										))}
									</TableBody>
								</Table>
							)}
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="attended" className="space-y-4">
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<CalendarCheck className="h-5 w-5" />
								Attended Today
							</CardTitle>
						</CardHeader>
						<CardContent>
							{isLoadingAttended ? (
								<div className="space-y-2">
									{[...Array(3)].map((_, i) => (
										<Skeleton key={i} className="h-16 w-full" />
									))}
								</div>
							) : attendedAppointments.length === 0 ? (
								<div className="text-muted-foreground py-8 text-center">
									No completed appointments today
								</div>
							) : (
								<Table>
									<TableHeader>
										<TableRow>
											<TableHead>Patient</TableHead>
											<TableHead>Clinic</TableHead>
											<TableHead>Time</TableHead>
											<TableHead>Visit Reason</TableHead>
											<TableHead>Status</TableHead>
											<TableHead>Actions</TableHead>
										</TableRow>
									</TableHeader>
									<TableBody>
										{attendedAppointments.map((appointment) => (
											<AppointmentRow
												key={appointment.id}
												appointment={appointment}
												onView={handleViewAppointment}
												canUpdateStatus={false} // No status updates for completed
											/>
										))}
									</TableBody>
								</Table>
							)}
						</CardContent>
					</Card>
				</TabsContent>
			</Tabs>
		</div>
	);
}
