'use client';

import { Plus, Search } from 'lucide-react';
import { useQueryStates } from 'nuqs';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { searchParamParsers } from '@/core/lib/search-params';
import { useAllDiagnosticRequests } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import {
	IDiagnosticRequestListParams,
	IDiagnosticRequestsResponse,
} from '@/features/dashboard/types/doctor.types';

import { CreateDiagnosticRequestDialog } from '../doctor-dashboard/diagnostic-request/create-diagnostic-request-dialog';
import { DiagnosticRequestDetailDialog } from '../doctor-dashboard/diagnostic-request/diagnostic-request-detail-dialog';
import { DiagnosticRequestList } from '../doctor-dashboard/diagnostic-request/diagnostic-request-list';
import { DiagnosticRequestPrintDialog } from '../doctor-dashboard/diagnostic-request/diagnostic-request-print-dialog';

export function RoleAwareDiagnosticRequestManagement({
	isArchived = false,
}: {
	isArchived?: boolean;
}) {
	// URL search parameters using nuqs
	const [{ search, page, action, id }, setSearchParams] = useQueryStates({
		search: searchParamParsers.search,
		page: searchParamParsers.page,
		action: searchParamParsers.action,
		id: searchParamParsers.id,
	});

	const [createDialogOpen, setCreateDialogOpen] = useState(action === 'create');
	const [detailDialogOpen, setDetailDialogOpen] = useState(!!id);
	const [printDialogOpen, setPrintDialogOpen] = useState(!!id);
	const [selectedDiagnosticRequestId, setSelectedDiagnosticRequestId] =
		useState<number | null>(id ? Number(id) : null);

	// Build query parameters
	const queryParams: IDiagnosticRequestListParams = {
		page,
		pageSize: 10,
		...(search && { search }),
	};

	// Use role-aware hook
	const {
		data: diagnosticRequestsResponse,
		isLoading,
		error,
	} = useAllDiagnosticRequests({ ...queryParams, isArchived });

	const diagnosticRequests =
		(diagnosticRequestsResponse as IDiagnosticRequestsResponse)?.data?.data ||
		[];
	const meta = (diagnosticRequestsResponse as IDiagnosticRequestsResponse)?.data
		?.meta;

	// Handle search
	const handleSearch = (query: string) => {
		setSearchParams({ search: query, page: 1 }); // Reset to first page when searching
	};

	// Handle navigation
	const navigateToList = () => {
		setSearchParams({ action: null, id: null });
	};

	const handleCreateDiagnosticRequest = () => {
		setCreateDialogOpen(true);
		setSearchParams({ action: 'create' });
	};

	const handleViewDiagnosticRequest = (diagnosticRequestId: number) => {
		setSelectedDiagnosticRequestId(diagnosticRequestId);
		setDetailDialogOpen(true);
		setSearchParams({ action: 'view', id: diagnosticRequestId });
	};

	const handleCloseCreateDialog = () => {
		setCreateDialogOpen(false);
		navigateToList();
	};

	const handlePrintDiagnosticRequest = (diagnosticRequestId: number) => {
		setSelectedDiagnosticRequestId(diagnosticRequestId);
		setPrintDialogOpen(true);
		setSearchParams({ action: 'print', id: diagnosticRequestId });
	};

	return (
		<div className="space-y-4">
			{/* Search and Filters */}
			<div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
				<div className="relative max-w-sm flex-1">
					<Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
					<Input
						placeholder="Search diagnostic requests by patient name or address..."
						value={search}
						onChange={(e) => handleSearch(e.target.value)}
						className="pl-10"
					/>
				</div>
				{!isArchived && (
					<Button
						onClick={handleCreateDiagnosticRequest}
						className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
					>
						<Plus className="mr-2 h-4 w-4" />
						Create Diagnostic Request
					</Button>
				)}
			</div>

			{/* Diagnostic Request List */}

			<DiagnosticRequestList
				diagnosticRequests={diagnosticRequests}
				isLoading={isLoading}
				error={error}
				currentPage={page}
				totalPages={meta?.last_page || 1}
				onPageChange={(newPage) => setSearchParams({ page: newPage })}
				onViewDiagnosticRequest={handleViewDiagnosticRequest}
				onPrintDiagnosticRequest={handlePrintDiagnosticRequest}
			/>

			{/* Dialogs - Only for doctors */}
			<CreateDiagnosticRequestDialog
				open={createDialogOpen}
				onClose={handleCloseCreateDialog}
			/>

			{/* Dialogs - View */}
			{selectedDiagnosticRequestId !== null && (
				<DiagnosticRequestDetailDialog
					onPrint={handlePrintDiagnosticRequest}
					diagnosticRequestId={selectedDiagnosticRequestId}
					open={detailDialogOpen && action === 'view'}
					onOpenChange={(open) => {
						if (!open) {
							setDetailDialogOpen(false);
							setSearchParams({ action: null, id: null });
						} else {
							setDetailDialogOpen(true);
						}
					}}
				/>
			)}

			{/* Dialog - Print */}
			{selectedDiagnosticRequestId !== null && (
				<DiagnosticRequestPrintDialog
					diagnosticRequestId={selectedDiagnosticRequestId}
					open={printDialogOpen && action === 'print'}
					onOpenChange={(open) => {
						if (!open) {
							setPrintDialogOpen(false);
							setSearchParams({ action: null, id: null });
						} else {
							setPrintDialogOpen(true);
						}
					}}
				/>
			)}
		</div>
	);
}
