'use client';

import { Plus, Search } from 'lucide-react';
import { useQueryStates } from 'nuqs';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { searchParamParsers } from '@/core/lib/search-params';
import { useAllPrescriptions } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import {
	IPrescriptionListParams,
	IPrescriptionsResponse,
} from '@/features/dashboard/types/doctor.types';

// Import doctor components for doctors
import { CreatePrescriptionDialog } from '../doctor-dashboard/prescription/create-prescription-dialog';
import { PrescriptionDetailDialog } from '../doctor-dashboard/prescription/prescription-detail-dialog';
import { PrescriptionList } from '../doctor-dashboard/prescription/prescription-list';

import '../doctor-dashboard/prescription/prescription-list';

import { PrescriptionPrintDialog } from '../doctor-dashboard/prescription/prescription-print-dialog';

export function RoleAwarePrescriptionManagement({
	isArchived = false,
}: {
	isArchived?: boolean;
}) {
	// URL search parameters using nuqs
	const [{ search, page, action, id }, setSearchParams] = useQueryStates({
		search: searchParamParsers.search,
		page: searchParamParsers.page,
		action: searchParamParsers.action,
		id: searchParamParsers.id,
	});

	const [createDialogOpen, setCreateDialogOpen] = useState(action === 'create');
	const [detailDialogOpen, setDetailDialogOpen] = useState(!!id);
	const [printDialogOpen, setPrintDialogOpen] = useState(!!id);
	const [selectedPrescriptionId, setSelectedPrescriptionId] = useState<
		number | null
	>(id ? Number(id) : null);

	// Build query parameters
	const queryParams: IPrescriptionListParams = {
		page,
		pageSize: 10,
		...(search && { search }),
	};

	// Use role-aware hook
	const {
		data: prescriptionsResponse,
		isLoading,
		error,
	} = useAllPrescriptions({ ...queryParams, isArchived });

	const prescriptions =
		(prescriptionsResponse as IPrescriptionsResponse)?.data?.data || [];
	const meta = (prescriptionsResponse as IPrescriptionsResponse)?.data?.meta;

	// Handle search
	const handleSearch = (query: string) => {
		setSearchParams({ search: query, page: 1 });
	};

	// Handle navigation
	const navigateToList = () => {
		setSearchParams({ action: null, id: null });
	};

	const handleCreatePrescription = () => {
		setCreateDialogOpen(true);
		setSearchParams({ action: 'create' });
	};

	const handleViewPrescription = (prescriptionId: number) => {
		setSelectedPrescriptionId(prescriptionId);
		setDetailDialogOpen(true);
		setSearchParams({ id: prescriptionId, action: 'view' });
	};

	const handleCloseCreateDialog = () => {
		setCreateDialogOpen(false);
		navigateToList();
	};
	const handlePrintPrescription = (prescriptionId: number) => {
		setSelectedPrescriptionId(prescriptionId);
		setPrintDialogOpen(true);
		setSearchParams({ id: prescriptionId, action: 'print' });
	};

	return (
		<div className="space-y-4">
			{/* Search and Filters */}
			<div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
				<div className="relative max-w-sm flex-1">
					<Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
					<Input
						placeholder="Search prescriptions by patient name..."
						value={search}
						onChange={(e) => handleSearch(e.target.value)}
						className="pl-10"
					/>
				</div>

				{!isArchived && (
					<Button
						onClick={handleCreatePrescription}
						className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
					>
						<Plus className="mr-2 h-4 w-4" />
						Create Prescription
					</Button>
				)}
			</div>

			{/* Prescription List */}
			<div className="space-y-4">
				<PrescriptionList
					prescriptions={prescriptions}
					isLoading={isLoading}
					error={error}
					currentPage={page}
					totalPages={meta?.last_page || 1}
					onPageChange={(newPage) => setSearchParams({ page: newPage })}
					onViewPrescription={handleViewPrescription}
					onPrintPrescription={handlePrintPrescription}
				/>
			</div>

			<CreatePrescriptionDialog
				open={createDialogOpen}
				onOpenChange={setCreateDialogOpen}
				onClose={handleCloseCreateDialog}
			/>

			{/* Prescription Detail Dialog */}
			{selectedPrescriptionId !== null && (
				<PrescriptionDetailDialog
					onPrint={handlePrintPrescription}
					prescriptionId={selectedPrescriptionId}
					open={detailDialogOpen && action === 'view'}
					onOpenChange={(open) => {
						if (!open) {
							setDetailDialogOpen(false);
							setSearchParams({ id: null });
						} else {
							setDetailDialogOpen(true);
						}
					}}
				/>
			)}

			{/* Prescription Print Dialog */}
			{selectedPrescriptionId !== null && (
				<PrescriptionPrintDialog
					prescriptionId={selectedPrescriptionId}
					open={printDialogOpen && action === 'print'}
					onOpenChange={(open) => {
						if (!open) {
							setPrintDialogOpen(false);
							setSearchParams({ id: null });
						} else {
							setPrintDialogOpen(true);
						}
					}}
				/>
			)}
		</div>
	);
}
