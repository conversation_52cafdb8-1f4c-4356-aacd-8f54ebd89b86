'use client';

import { Calendar, FileText, Heart, Home, QrCode, User } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react';

import {
	Sidebar,
	SidebarContent,
	SidebarFooter,
	SidebarHeader,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
} from '@/components/ui/sidebar';
import { useSession } from '@/core/hooks/useSession';
import { NavMain } from '@/features/dashboard/components/nav-main';
import { NavSecondary } from '@/features/dashboard/components/nav-secondary';
import { NavUser } from '@/features/dashboard/components/nav-user';

const patientNavData = {
	navMain: [
		{
			title: 'Dashboard',
			url: '/dashboard',
			icon: Home,
			isActive: true,
			items: [
				{
					title: 'Overview',
					url: '/dashboard?tab=overview',
				},
			],
		},
		{
			title: 'My Appointments',
			url: '/dashboard?tab=appointments',
			icon: Calendar,
			items: [
				{
					title: 'Upcoming',
					url: '/dashboard?tab=appointments&view=upcoming',
				},
				{
					title: 'History',
					url: '/dashboard?tab=appointments&view=history',
				},
			],
		},
		{
			title: 'Medical Documents',
			url: '/dashboard?tab=medical-documents',
			icon: FileText,
			items: [
				{
					title: 'All Documents',
					url: '/dashboard?tab=medical-documents&type=all',
				},
				{
					title: 'Prescriptions',
					url: '/dashboard?tab=medical-documents&type=prescriptions',
				},
				{
					title: 'Lab Results',
					url: '/dashboard?tab=medical-documents&type=lab-results',
				},
				{
					title: 'Medical Certificates',
					url: '/dashboard?tab=medical-documents&type=certificates',
				},
				{
					title: 'Referrals',
					url: '/dashboard?tab=medical-documents&type=referrals',
				},
			],
		},
		{
			title: 'Medical History',
			url: '/dashboard?tab=medical-history',
			icon: Heart,
			items: [
				{
					title: 'Allergies',
					url: '/dashboard?tab=medical-history&section=allergies',
				},
				{
					title: 'Illnesses',
					url: '/dashboard?tab=medical-history&section=illnesses',
				},
				{
					title: 'Surgeries',
					url: '/dashboard?tab=medical-history&section=surgeries',
				},
				{
					title: 'Family History',
					url: '/dashboard?tab=medical-history&section=family',
				},
				{
					title: 'Lifestyle',
					url: '/dashboard?tab=medical-history&section=lifestyle',
				},
			],
		},
	],
	navSecondary: [
		{
			title: 'My Profile',
			url: '/dashboard?tab=profile',
			icon: User,
		},
		{
			title: 'QR Code',
			url: '/dashboard?tab=qr-code',
			icon: QrCode,
		},
	],
};

export function PatientSidebar({
	...props
}: React.ComponentProps<typeof Sidebar>) {
	const { user } = useSession();
	const router = useRouter();

	return (
		<Sidebar variant="inset" {...props}>
			<SidebarHeader>
				<SidebarMenu>
					<SidebarMenuItem>
						<SidebarMenuButton
							size="lg"
							onClick={() => router.push('/dashboard')}
						>
							<div className="flex w-full items-center rounded-lg">
								<img src="/elena-banner.png" alt="Elena" className="h-9" />
							</div>
						</SidebarMenuButton>
					</SidebarMenuItem>
				</SidebarMenu>
			</SidebarHeader>
			<SidebarContent>
				<NavMain items={patientNavData.navMain} />
				<NavSecondary items={patientNavData.navSecondary} className="mt-auto" />
			</SidebarContent>
			<SidebarFooter>
				<NavUser
					user={{
						name: user ? `${user.first_name} ${user.last_name}` : 'Patient',
						email: user?.email || '<EMAIL>',
						avatar: '/avatars/patient.jpg',
					}}
				/>
			</SidebarFooter>
		</Sidebar>
	);
}
