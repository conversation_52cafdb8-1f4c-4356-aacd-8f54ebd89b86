'use client';

import { useQueryState } from 'nuqs';

import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import {
	SidebarInset,
	SidebarProvider,
	SidebarTrigger,
} from '@/components/ui/sidebar';
import { searchParamParsers } from '@/core/lib/search-params';
import { PatientAppointmentDetail } from '@/features/dashboard/components/patient-dashboard/patient-appointment-detail';
import { PatientAppointments } from '@/features/dashboard/components/patient-dashboard/patient-appointments';
import { PatientDashboardOverview } from '@/features/dashboard/components/patient-dashboard/patient-dashboard-overview';
import { PatientDocumentViewer } from '@/features/dashboard/components/patient-dashboard/patient-document-viewer';
import { PatientMedicalDocuments } from '@/features/dashboard/components/patient-dashboard/patient-medical-documents';
import { PatientMedicalHistory } from '@/features/dashboard/components/patient-dashboard/patient-medical-history';
import { PatientProfile } from '@/features/dashboard/components/patient-dashboard/patient-profile';
import { PatientQRCode } from '@/features/dashboard/components/patient-dashboard/patient-qr-code';
import { PatientSidebar } from '@/features/dashboard/components/patient-dashboard/patient-sidebar';
import { EPatientDashboardTab } from '@/features/dashboard/types/patient.types';

const getBreadcrumbData = (tab: string) => {
	switch (tab) {
		case EPatientDashboardTab.OVERVIEW:
			return { title: 'Dashboard Overview', parent: 'Dashboard' };
		case EPatientDashboardTab.APPOINTMENTS:
			return { title: 'My Appointments', parent: 'Healthcare' };
		case EPatientDashboardTab.MEDICAL_DOCUMENTS:
			return { title: 'Medical Documents', parent: 'Healthcare' };
		case EPatientDashboardTab.PROFILE:
			return { title: 'My Profile', parent: 'Account' };
		case EPatientDashboardTab.MEDICAL_HISTORY:
			return { title: 'Medical History', parent: 'Healthcare' };
		case EPatientDashboardTab.QR_CODE:
			return { title: 'QR Code', parent: 'Account' };
		default:
			return { title: 'Dashboard Overview', parent: 'Dashboard' };
	}
};

const renderTabContent = (tab: string) => {
	switch (tab) {
		case EPatientDashboardTab.APPOINTMENTS:
			return <PatientAppointments />;
		case EPatientDashboardTab.MEDICAL_DOCUMENTS:
			return <PatientMedicalDocuments />;
		case EPatientDashboardTab.PROFILE:
			return <PatientProfile />;
		case EPatientDashboardTab.MEDICAL_HISTORY:
			return <PatientMedicalHistory />;
		case EPatientDashboardTab.QR_CODE:
			return <PatientQRCode />;
		case EPatientDashboardTab.OVERVIEW:
		default:
			return <PatientDashboardOverview />;
	}
};

export function PatientDashboard() {
	const [tab] = useQueryState('tab', searchParamParsers.tab);
	const [appointmentId] = useQueryState('appointmentId', searchParamParsers.id);
	const [documentId] = useQueryState('documentId', searchParamParsers.id);
	const currentTab = tab || EPatientDashboardTab.OVERVIEW;
	const breadcrumbData = getBreadcrumbData(currentTab);

	// If we have an appointmentId, show the appointment detail view
	if (appointmentId && currentTab === EPatientDashboardTab.APPOINTMENTS) {
		return (
			<>
				<PatientSidebar />
				<SidebarInset>
					<header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
						<div className="flex items-center gap-2 px-4">
							<SidebarTrigger className="-ml-1" />
							<Separator orientation="vertical" className="mr-2 h-4" />
							<Breadcrumb>
								<BreadcrumbList>
									<BreadcrumbItem className="hidden md:block">
										<BreadcrumbLink href="/dashboard">
											{breadcrumbData.parent}
										</BreadcrumbLink>
									</BreadcrumbItem>
									<BreadcrumbSeparator className="hidden md:block" />
									<BreadcrumbItem>
										<BreadcrumbPage>Appointment Details</BreadcrumbPage>
									</BreadcrumbItem>
								</BreadcrumbList>
							</Breadcrumb>
						</div>
					</header>
					<PatientAppointmentDetail />
				</SidebarInset>
			</>
		);
	}

	// If we have a documentId, show the document viewer
	if (documentId && currentTab === EPatientDashboardTab.MEDICAL_DOCUMENTS) {
		return (
			<>
				<PatientSidebar />
				<SidebarInset>
					<header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
						<div className="flex items-center gap-2 px-4">
							<SidebarTrigger className="-ml-1" />
							<Separator orientation="vertical" className="mr-2 h-4" />
							<Breadcrumb>
								<BreadcrumbList>
									<BreadcrumbItem className="hidden md:block">
										<BreadcrumbLink href="/dashboard">
											{breadcrumbData.parent}
										</BreadcrumbLink>
									</BreadcrumbItem>
									<BreadcrumbSeparator className="hidden md:block" />
									<BreadcrumbItem>
										<BreadcrumbPage>Document Viewer</BreadcrumbPage>
									</BreadcrumbItem>
								</BreadcrumbList>
							</Breadcrumb>
						</div>
					</header>
					<PatientDocumentViewer />
				</SidebarInset>
			</>
		);
	}

	return (
		<SidebarProvider>
			<PatientSidebar />
			<SidebarInset>
				<header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
					<div className="flex items-center gap-2 px-4">
						<SidebarTrigger className="-ml-1" />
						<Separator orientation="vertical" className="mr-2 h-4" />
						<Breadcrumb>
							<BreadcrumbList>
								<BreadcrumbItem className="hidden md:block">
									<BreadcrumbLink href="/dashboard">
										{breadcrumbData.parent}
									</BreadcrumbLink>
								</BreadcrumbItem>
								<BreadcrumbSeparator className="hidden md:block" />
								<BreadcrumbItem>
									<BreadcrumbPage>{breadcrumbData.title}</BreadcrumbPage>
								</BreadcrumbItem>
							</BreadcrumbList>
						</Breadcrumb>
					</div>
				</header>
				{renderTabContent(currentTab)}
			</SidebarInset>
		</SidebarProvider>
	);
}
