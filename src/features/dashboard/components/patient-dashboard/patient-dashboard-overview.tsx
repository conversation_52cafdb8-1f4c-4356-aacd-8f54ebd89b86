'use client';

import { User } from 'lucide-react';

import { FeaturePlaceholder, Placeholder } from '@/components/ui/placeholder';

export function PatientDashboardOverview() {
	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			{/* Welcome Section */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
						Welcome back, Patient!
					</h1>
					<p className="text-muted-foreground">
						Your patient dashboard is currently under development
					</p>
				</div>
			</div>

			{/* Patient Dashboard Placeholder */}
			<div className="grid gap-6">
				<Placeholder
					title="Patient Dashboard Overview"
					description="The patient dashboard is currently under development. This will include your health overview, upcoming appointments, recent medical documents, and health alerts."
					icon={<User className="h-5 w-5 text-blue-600" />}
					variant="info"
				/>

				{/* Feature Placeholders Grid */}
				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
					<FeaturePlaceholder
						feature="Upcoming Appointments"
						description="View and manage your upcoming medical appointments."
					/>
					<FeaturePlaceholder
						feature="Medical Documents"
						description="Access your prescriptions, lab results, and medical certificates."
					/>
					<FeaturePlaceholder
						feature="Health Overview"
						description="Track your vital signs and health metrics."
					/>
					<FeaturePlaceholder
						feature="Medical History"
						description="Review your complete medical history and records."
					/>
					<FeaturePlaceholder
						feature="Health Alerts"
						description="Receive important health reminders and notifications."
					/>
					<FeaturePlaceholder
						feature="QR Code Access"
						description="Quick access to your patient information via QR code."
					/>
				</div>
			</div>
		</div>
	);
}
