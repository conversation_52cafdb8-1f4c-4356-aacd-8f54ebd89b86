'use client';

import { format } from 'date-fns';
import {
	Calendar,
	Clock,
	Eye,
	FileText,
	Loader2,
	MapPin,
	Phone,
	Plus,
	Stethoscope,
	Trash2,
	Upload,
	User,
} from 'lucide-react';
import { useEffect, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import {
	useAddDoctorNote,
	useAddMedicalRecord,
	useAppointmentDetail,
	useCreateDiagnosticRequest,
	useCreateDiagnosticRequestItem,
	useCreateLabRequest,
	useCreateLabRequestItem,
	useCreateMedicalCertificate,
	useCreateMedicalCertificateItem,
	useCreatePrescription,
	useCreatePrescriptionItem,
	useCreateReferral,
	useRemoveDiagnosticRequest,
	useRemoveDiagnosticRequestItem,
	useRemoveLabRequest,
	useRemoveLabRequestItem,
	useRemoveMedicalCertificate,
	useRemoveMedicalCertificateItem,
	useRemoveMedicalRecord,
	useRemovePrescription,
	useRemovePrescriptionItem,
	useUpdateAppointmentDetails,
	useUpdateVitalSigns,
	useUploadLabResult,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import {
	EAppointmentStatus,
	IAssistantNote,
	IDiagnosticRequest,
	IDiagnosticRequestItem,
	IDoctorNote,
	ILaboratoryResult,
	ILabRequest,
	ILabRequestItem,
	IMedicalCertificate,
	IMedicalCertificateItem,
	IPrescription,
	IPrescriptionItem,
	IReferral,
} from '@/features/dashboard/types/doctor.types';

import { RescheduleAppointmentDialog } from './reschedule-appointment-dialog';

interface IAppointmentDetailsDialogProps {
	appointmentId: number | null;
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

const getStatusBadge = (status: string) => {
	const statusConfig = {
		[EAppointmentStatus.CREATED]: {
			variant: 'secondary' as const,
			label: 'Created',
		},
		[EAppointmentStatus.CONFIRMED]: {
			variant: 'default' as const,
			label: 'Confirmed',
		},
		[EAppointmentStatus.DECLINED]: {
			variant: 'destructive' as const,
			label: 'Declined',
		},
		[EAppointmentStatus.WAITING]: {
			variant: 'outline' as const,
			label: 'Waiting',
		},
		[EAppointmentStatus.ONGOING]: {
			variant: 'default' as const,
			label: 'Ongoing',
		},
		[EAppointmentStatus.COMPLETED]: {
			variant: 'default' as const,
			label: 'Completed',
		},
		[EAppointmentStatus.FOLLOW_UP]: {
			variant: 'secondary' as const,
			label: 'Follow-up',
		},
		[EAppointmentStatus.NO_SHOW]: {
			variant: 'destructive' as const,
			label: 'No Show',
		},
		[EAppointmentStatus.RESCHEDULED]: {
			variant: 'outline' as const,
			label: 'Rescheduled',
		},
		[EAppointmentStatus.CANCELLED]: {
			variant: 'destructive' as const,
			label: 'Cancelled',
		},
	};

	const config = statusConfig[status as EAppointmentStatus] || {
		variant: 'secondary' as const,
		label: status,
	};

	return <Badge variant={config.variant}>{config.label}</Badge>;
};

export function AppointmentDetailsDialog({
	appointmentId,
	open,
	onOpenChange,
}: IAppointmentDetailsDialogProps) {
	const [activeTab, setActiveTab] = useState('overview');
	const [doctorNote, setDoctorNote] = useState('');
	const [vitalSigns, setVitalSigns] = useState({
		systolic: '',
		diastolic: '',
		pulseRate: '',
		respiration: '',
		height: '',
		heightType: '',
		weight: '',
		weightType: '',
		temperature: '',
		temperatureType: '',
		oxygenSaturation: '',
		capillaryBloodGlucose: '',
		bodyMassIndex: '',
	});
	const [prescriptionForm, setPrescriptionForm] = useState({
		patient_name: '',
		patient_address: '',
		patient_phone: '',
		patient_gender: '',
		patient_birthdate: '',
		professional_tax_receipt: '',
	});
	const [medicationForm, setMedicationForm] = useState({
		generic: '',
		brand: '',
		dosageForm: '',
		dosage: '',
		frequency: '',
		quantity: 0,
		instruction: '',
	});
	const [showPrescriptionForm, setShowPrescriptionForm] = useState(false);
	const [showMedicationForm, setShowMedicationForm] = useState<number | null>(
		null
	);
	const [testForm, setTestForm] = useState({
		name: '',
	});
	const [showDiagnosticRequestForm, setShowDiagnosticRequestForm] =
		useState(false);
	const [showTestForm, setShowTestForm] = useState<number | null>(null);
	const [labTestForm, setLabTestForm] = useState({
		test_type: '',
		instructions: '',
	});
	const [showLabRequestForm, setShowLabRequestForm] = useState(false);
	const [showLabTestForm, setShowLabTestForm] = useState<number | null>(null);
	const [labResultFiles, setLabResultFiles] = useState<File[]>([]);
	const [labResultNotes, setLabResultNotes] = useState('');
	const [conditionForm, setConditionForm] = useState({
		description: '',
	});
	const [showConditionForm, setShowConditionForm] = useState<number | null>(
		null
	);
	const [medicalRecordFile, setMedicalRecordFile] = useState<File | null>(null);
	const [showMedicalRecordForm, setShowMedicalRecordForm] = useState(false);
	const [referralForm, setReferralForm] = useState({
		referredDoctorName: '',
		purposeOfReferral: '',
	});
	const [showReferralForm, setShowReferralForm] = useState(false);
	const [medicalInfo, setMedicalInfo] = useState({
		chiefComplaint: '',
		diagnosis: '',
		prognosis: '',
	});

	const {
		data: appointment,
		isLoading,
		error,
	} = useAppointmentDetail(appointmentId || 0, {
		enabled: !!appointmentId && open,
	});

	// Populate vital signs and medical info when appointment data loads
	useEffect(() => {
		if (appointment?.vitalSign) {
			setVitalSigns({
				systolic: appointment.vitalSign.systolic || '',
				diastolic: appointment.vitalSign.diastolic || '',
				pulseRate: appointment.vitalSign.pulse_rate || '',
				respiration: appointment.vitalSign.respiration || '',
				height: appointment.vitalSign.height || '',
				heightType: appointment.vitalSign.height_type || '',
				weight: appointment.vitalSign.weight || '',
				weightType: appointment.vitalSign.weight_type || '',
				temperature: appointment.vitalSign.temperature || '',
				temperatureType: appointment.vitalSign.temperature_type || '',
				oxygenSaturation: appointment.vitalSign.oxygen_saturation || '',
				capillaryBloodGlucose:
					appointment.vitalSign.capillary_blood_glucose || '',
				bodyMassIndex: appointment.vitalSign.body_mass_index || '',
			});
		}

		// Populate medical information
		if (appointment) {
			setMedicalInfo({
				chiefComplaint: appointment.chief_complaint || '',
				diagnosis: appointment.diagnosis || '',
				prognosis: appointment.prognosis || '',
			});
		}
	}, [appointment]);

	const { mutate: updateVitalSignsMutation, isPending: isUpdatingVitals } =
		useUpdateVitalSigns();
	const { mutate: addNoteMutation, isPending: isAddingNote } =
		useAddDoctorNote();
	const {
		mutate: createPrescriptionMutation,
		isPending: isCreatingPrescription,
	} = useCreatePrescription();
	const {
		mutate: removePrescriptionMutation,
		isPending: isRemovingPrescription,
	} = useRemovePrescription();
	const { mutate: createPrescriptionItemMutation, isPending: isCreatingItem } =
		useCreatePrescriptionItem();
	const { mutate: removePrescriptionItemMutation, isPending: isRemovingItem } =
		useRemovePrescriptionItem();
	const {
		mutate: createDiagnosticRequestMutation,
		isPending: isCreatingDiagnosticRequest,
	} = useCreateDiagnosticRequest();
	const {
		mutate: removeDiagnosticRequestMutation,
		isPending: isRemovingDiagnosticRequest,
	} = useRemoveDiagnosticRequest();
	const {
		mutate: createDiagnosticRequestItemMutation,
		isPending: isCreatingTest,
	} = useCreateDiagnosticRequestItem();
	const {
		mutate: removeDiagnosticRequestItemMutation,
		isPending: isRemovingTest,
	} = useRemoveDiagnosticRequestItem();
	const { mutate: createLabRequestMutation, isPending: isCreatingLabRequest } =
		useCreateLabRequest();
	const { mutate: removeLabRequestMutation, isPending: isRemovingLabRequest } =
		useRemoveLabRequest();
	const { mutate: createLabRequestItemMutation, isPending: isCreatingLabTest } =
		useCreateLabRequestItem();
	const { mutate: removeLabRequestItemMutation, isPending: isRemovingLabTest } =
		useRemoveLabRequestItem();
	const { mutate: uploadLabResultMutation, isPending: isUploadingLabResult } =
		useUploadLabResult();
	const { mutate: createMedicalCertificateMutation } =
		useCreateMedicalCertificate();
	const {
		mutate: removeMedicalCertificateMutation,
		isPending: isRemovingMedicalCertificate,
	} = useRemoveMedicalCertificate();
	const {
		mutate: createMedicalCertificateItemMutation,
		isPending: isCreatingCondition,
	} = useCreateMedicalCertificateItem();
	const {
		mutate: removeMedicalCertificateItemMutation,
		isPending: isRemovingCondition,
	} = useRemoveMedicalCertificateItem();
	const { mutate: addMedicalRecordMutation, isPending: isAddingMedicalRecord } =
		useAddMedicalRecord();
	const {
		mutate: removeMedicalRecordMutation,
		isPending: isRemovingMedicalRecord,
	} = useRemoveMedicalRecord();
	const { mutate: createReferralMutation, isPending: isCreatingReferral } =
		useCreateReferral();
	const { mutate: updateDetailsMutation, isPending: isUpdatingDetails } =
		useUpdateAppointmentDetails();

	const handleSaveVitalSigns = () => {
		if (!appointmentId) return;

		// Filter out empty string values to only send non-empty fields
		const filteredVitalSigns = Object.fromEntries(
			Object.entries(vitalSigns).filter(([, value]) => value !== '')
		);

		updateVitalSignsMutation({
			appointmentId,
			vitalSigns: filteredVitalSigns,
		});
	};

	const handleAddNote = () => {
		if (!appointmentId || !doctorNote.trim()) return;

		addNoteMutation({
			appointmentId,
			note: doctorNote,
		});

		setDoctorNote('');
	};

	const handleCreatePrescription = () => {
		if (!appointmentId || !appointment?.patient) return;

		const prescriptionData = {
			patient_name: `${appointment.patient.profile?.first_name} ${appointment.patient.profile?.last_name}`,
			patient_address:
				appointment.patient.profile?.profileCurrentAddress?.address || '',
			patient_phone: appointment.patient.profile?.phone || '',
			patient_gender: appointment.patient.profile?.gender || '',
			patient_birthdate: appointment.patient.profile?.birthday || '',
			professional_tax_receipt: prescriptionForm.professional_tax_receipt || '',
		};

		createPrescriptionMutation({
			appointmentId,
			prescriptionData,
		});

		setShowPrescriptionForm(false);
	};

	const handleRemovePrescription = (prescriptionId: number) => {
		if (!appointmentId) return;

		removePrescriptionMutation({
			prescriptionId,
			appointmentId,
		});
	};

	const handleAddMedication = (prescriptionId: number) => {
		if (!appointmentId || !medicationForm.generic.trim()) return;

		createPrescriptionItemMutation({
			prescriptionId,
			appointmentId,
			itemData: medicationForm,
		});

		setMedicationForm({
			generic: '',
			brand: '',
			dosageForm: '',
			dosage: '',
			frequency: '',
			quantity: 0,
			instruction: '',
		});
		setShowMedicationForm(null);
	};

	const handleRemoveMedication = (
		prescriptionId: number,
		prescriptionItemId: number
	) => {
		if (!appointmentId) return;

		removePrescriptionItemMutation({
			prescriptionId,
			prescriptionItemId,
			appointmentId,
		});
	};

	const handleCreateDiagnosticRequest = () => {
		if (!appointmentId || !appointment?.patient) return;

		const diagnosticRequestData = {
			patient_name: `${appointment.patient.profile?.first_name} ${appointment.patient.profile?.last_name}`,
			patient_address:
				appointment.patient.profile?.profileCurrentAddress?.address || '',
			patient_phone: appointment.patient.profile?.phone || '',
			patient_gender: appointment.patient.profile?.gender || '',
			patient_birthdate: appointment.patient.profile?.birthday || '',
		};

		createDiagnosticRequestMutation({
			appointmentId,
			diagnosticRequestData,
		});

		setShowDiagnosticRequestForm(false);
	};

	const handleRemoveDiagnosticRequest = (diagnosticRequestId: number) => {
		if (!appointmentId) return;

		removeDiagnosticRequestMutation({
			diagnosticRequestId,
			appointmentId,
		});
	};

	const handleAddTest = (diagnosticRequestId: number) => {
		if (!appointmentId || !testForm.name.trim()) return;

		createDiagnosticRequestItemMutation({
			diagnosticRequestId,
			appointmentId,
			itemData: testForm,
		});

		setTestForm({
			name: '',
		});
		setShowTestForm(null);
	};

	const handleRemoveTest = (
		diagnosticRequestId: number,
		diagnosticRequestItemId: number
	) => {
		if (!appointmentId) return;

		removeDiagnosticRequestItemMutation({
			diagnosticRequestId,
			diagnosticRequestItemId,
			appointmentId,
		});
	};

	const handleCreateLabRequest = () => {
		if (!appointmentId || !appointment?.patient) return;

		const labRequestData = {
			patient_name: `${appointment.patient.profile?.first_name} ${appointment.patient.profile?.last_name}`,
			patient_address:
				appointment.patient.profile?.profileCurrentAddress?.address || '',
			patient_phone: appointment.patient.profile?.phone || '',
			patient_gender: appointment.patient.profile?.gender || '',
			patient_birthdate: appointment.patient.profile?.birthday || '',
		};

		createLabRequestMutation({
			appointmentId,
			labRequestData,
		});

		setShowLabRequestForm(false);
	};

	const handleRemoveLabRequest = (labRequestId: number) => {
		if (!appointmentId) return;

		removeLabRequestMutation({
			labRequestId,
			appointmentId,
		});
	};

	const handleAddLabTest = (labRequestId: number) => {
		if (!appointmentId || !labTestForm.test_type.trim()) return;

		// Combine test_type and instructions into name field for backend
		const name = labTestForm.instructions.trim()
			? `${labTestForm.test_type} - ${labTestForm.instructions}`
			: labTestForm.test_type;

		createLabRequestItemMutation({
			labRequestId,
			appointmentId,
			itemData: { name },
		});

		setLabTestForm({
			test_type: '',
			instructions: '',
		});
		setShowLabTestForm(null);
	};

	const handleRemoveLabTest = (
		labRequestId: number,
		labRequestItemId: number
	) => {
		if (!appointmentId) return;

		removeLabRequestItemMutation({
			labRequestId,
			labRequestItemId,
			appointmentId,
		});
	};

	const handleUploadLabResult = () => {
		if (!appointmentId || labResultFiles.length === 0) return;

		const formData = new FormData();
		// Backend expects 'record' field, not 'result_files'
		formData.append('record', labResultFiles[0]);
		if (labResultNotes.trim()) {
			formData.append('notes', labResultNotes);
		}

		uploadLabResultMutation({
			appointmentId,
			resultData: formData,
		});

		setLabResultFiles([]);
		setLabResultNotes('');
	};

	const handleCreateMedicalCertificate = () => {
		if (!appointmentId) return;

		createMedicalCertificateMutation(appointmentId);
	};

	const handleRemoveMedicalCertificate = (medicalCertificateId: number) => {
		if (!appointmentId) return;

		removeMedicalCertificateMutation({
			medicalCertificateId,
			appointmentId,
		});
	};

	const handleAddCondition = (medicalCertificateId: number) => {
		if (!appointmentId || !conditionForm.description.trim()) return;

		createMedicalCertificateItemMutation({
			medicalCertificateId,
			appointmentId,
			itemData: conditionForm,
		});

		setConditionForm({
			description: '',
		});
		setShowConditionForm(null);
	};

	const handleRemoveCondition = (
		medicalCertificateId: number,
		medicalCertificateItemId: number
	) => {
		if (!appointmentId) return;

		removeMedicalCertificateItemMutation({
			medicalCertificateId,
			medicalCertificateItemId,
			appointmentId,
		});
	};

	const handleAddMedicalRecord = () => {
		if (!appointmentId || !medicalRecordFile) return;

		addMedicalRecordMutation({
			appointmentId,
			recordFile: medicalRecordFile,
		});

		setMedicalRecordFile(null);
		setShowMedicalRecordForm(false);
	};

	const handleRemoveMedicalRecord = (medicalRecordId: number) => {
		if (!appointmentId) return;

		removeMedicalRecordMutation({
			appointmentId,
			medicalRecordId,
		});
	};

	const handleCreateReferral = () => {
		if (!appointmentId || !appointment?.patient) return;

		const patient = appointment.patient;
		const profile = patient?.profile;

		if (!profile) {
			console.error('Patient profile not found in appointment data');
			return;
		}

		// Get patient address from current address or permanent address
		const patientAddress =
			profile.profileCurrentAddress?.address ||
			profile.profilePermanentAddress?.address ||
			'';

		const referralData = {
			patient_name:
				`${profile.first_name || ''} ${profile.middle_name || ''} ${profile.last_name || ''}`.trim(),
			patient_address: patientAddress,
			patient_phone: profile.phone || '',
			patient_gender: profile.gender || '',
			patient_birthdate: profile.birthday || '',
			doctorName: referralForm.referredDoctorName,
			purpose: referralForm.purposeOfReferral,
		};

		createReferralMutation({
			appointmentId,
			referralData,
		});

		setReferralForm({
			referredDoctorName: '',
			purposeOfReferral: '',
		});
		setShowReferralForm(false);
	};

	const handleUpdateMedicalInfo = () => {
		if (!appointmentId) return;

		updateDetailsMutation({
			appointmentId,
			data: medicalInfo,
		});
	};

	if (!appointmentId) {
		return null;
	}

	const formatDate = (dateString: string) => {
		try {
			return format(new Date(dateString), 'EEEE, MMMM d, yyyy');
		} catch {
			return dateString;
		}
	};

	const formatTime = (dateString: string) => {
		try {
			return format(new Date(dateString), 'h:mm a');
		} catch {
			return dateString;
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
				<DialogHeader>
					<div className="flex items-center justify-between">
						<div>
							<DialogTitle className="flex items-center gap-2">
								<Stethoscope className="h-5 w-5 text-[oklch(0.7448_0.1256_202.74)]" />
								Appointment Details
							</DialogTitle>
							<DialogDescription>
								View and manage appointment information and medical records.
							</DialogDescription>
						</div>
						{appointment && (
							<RescheduleAppointmentDialog
								appointment={appointment}
								trigger={
									<Button variant="outline" size="sm">
										<Clock className="mr-2 h-4 w-4" />
										Reschedule
									</Button>
								}
							/>
						)}
					</div>
				</DialogHeader>

				{isLoading ? (
					<div className="flex items-center justify-center py-8">
						<div className="text-center">
							<div className="mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-[oklch(0.7448_0.1256_202.74)]"></div>
							<p className="text-muted-foreground mt-2 text-sm">
								Loading appointment details...
							</p>
						</div>
					</div>
				) : error ? (
					<div className="flex items-center justify-center py-8">
						<div className="text-center">
							<p className="text-destructive text-sm">
								Failed to load appointment details
							</p>
						</div>
					</div>
				) : appointment ? (
					<Tabs
						value={activeTab}
						onValueChange={setActiveTab}
						className="w-full"
					>
						<TabsList className="grid w-full grid-cols-8">
							<TabsTrigger value="overview">Overview</TabsTrigger>
							<TabsTrigger value="vitals">Vitals</TabsTrigger>
							<TabsTrigger value="prescription">Prescription</TabsTrigger>
							<TabsTrigger value="lab-requests">Lab Requests</TabsTrigger>
							<TabsTrigger value="diagnostics">Diagnostics</TabsTrigger>
							<TabsTrigger value="certificates">Certificates</TabsTrigger>
							<TabsTrigger value="referrals">Referrals</TabsTrigger>
							<TabsTrigger value="medical-records">Records</TabsTrigger>
						</TabsList>

						<TabsContent value="overview" className="space-y-6">
							{/* Patient Information */}
							<div className="grid grid-cols-1 gap-6 md:grid-cols-2">
								<div className="space-y-4">
									<div className="flex items-center gap-2">
										<User className="text-muted-foreground h-4 w-4" />
										<h3 className="font-semibold">Patient Information</h3>
									</div>
									<div className="space-y-2">
										<p className="font-medium">
											{appointment.patient?.profile?.first_name}{' '}
											{appointment.patient?.profile?.last_name}
										</p>
										<div className="text-muted-foreground flex items-center gap-2 text-sm">
											<Phone className="h-3 w-3" />
											{appointment.patient?.profile?.phone || 'No phone number'}
										</div>
									</div>
								</div>

								<div className="space-y-4">
									<div className="flex items-center gap-2">
										<MapPin className="text-muted-foreground h-4 w-4" />
										<h3 className="font-semibold">Clinic Information</h3>
									</div>
									<div className="space-y-2">
										<p className="font-medium">{appointment.clinic?.name}</p>
										<p className="text-muted-foreground text-sm">
											{appointment.clinic?.address}
										</p>
									</div>
								</div>
							</div>

							<Separator />

							{/* Appointment Details */}
							<div className="space-y-4">
								<div className="flex items-center gap-2">
									<Calendar className="text-muted-foreground h-4 w-4" />
									<h3 className="font-semibold">Appointment Details</h3>
								</div>
								<div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
									<div>
										<p className="text-muted-foreground text-sm font-medium">
											Date
										</p>
										<p className="font-medium">
											{formatDate(appointment.appointment_date)}
										</p>
									</div>
									<div>
										<p className="text-muted-foreground text-sm font-medium">
											Time
										</p>
										<div className="flex items-center gap-2">
											<Clock className="h-3 w-3" />
											<p className="font-medium">
												{formatTime(appointment.appointment_date)}
											</p>
										</div>
									</div>
									<div>
										<p className="text-muted-foreground text-sm font-medium">
											Status
										</p>
										{getStatusBadge(appointment.status)}
									</div>
									<div>
										<p className="text-muted-foreground text-sm font-medium">
											Visit Reason
										</p>
										<p className="font-medium">
											{appointment.visitReason?.name}
										</p>
									</div>
									<div>
										<p className="text-muted-foreground text-sm font-medium">
											Consultation Type
										</p>
										<p className="font-medium">
											{appointment.consultationType?.name}
										</p>
									</div>
									<div>
										<p className="text-muted-foreground text-sm font-medium">
											Payment Type
										</p>
										<p className="font-medium">
											{appointment.paymentType?.name}
										</p>
									</div>
								</div>
							</div>

							<Separator />

							{/* Medical Information */}
							<div className="space-y-4">
								<div className="flex items-center justify-between">
									<div className="flex items-center gap-2">
										<FileText className="text-muted-foreground h-4 w-4" />
										<h3 className="font-semibold">Medical Information</h3>
									</div>
									<Button
										onClick={handleUpdateMedicalInfo}
										disabled={isUpdatingDetails}
										size="sm"
										className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.6448_0.1256_202.74)]"
									>
										{isUpdatingDetails ? 'Saving...' : 'Save Medical Info'}
									</Button>
								</div>
								<div className="grid grid-cols-1 gap-4">
									<div className="space-y-2">
										<Label htmlFor="chief-complaint">Chief Complaint</Label>
										<Textarea
											id="chief-complaint"
											placeholder="Enter chief complaint..."
											value={medicalInfo.chiefComplaint}
											onChange={(e) =>
												setMedicalInfo((prev) => ({
													...prev,
													chiefComplaint: e.target.value,
												}))
											}
											rows={3}
										/>
									</div>
									<div className="space-y-2">
										<Label htmlFor="diagnosis">Diagnosis</Label>
										<Textarea
											id="diagnosis"
											placeholder="Enter diagnosis..."
											value={medicalInfo.diagnosis}
											onChange={(e) =>
												setMedicalInfo((prev) => ({
													...prev,
													diagnosis: e.target.value,
												}))
											}
											rows={3}
										/>
									</div>
									<div className="space-y-2">
										<Label htmlFor="prognosis">Prognosis</Label>
										<Textarea
											id="prognosis"
											placeholder="Enter prognosis..."
											value={medicalInfo.prognosis}
											onChange={(e) =>
												setMedicalInfo((prev) => ({
													...prev,
													prognosis: e.target.value,
												}))
											}
											rows={3}
										/>
									</div>
								</div>
							</div>
						</TabsContent>

						<TabsContent value="vitals" className="space-y-4">
							<div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
								<div className="space-y-2">
									<label className="text-sm font-medium">Blood Pressure</label>
									<div className="flex gap-2">
										<input
											type="number"
											placeholder="Systolic"
											value={vitalSigns.systolic}
											onChange={(e) =>
												setVitalSigns((prev) => ({
													...prev,
													systolic: e.target.value,
												}))
											}
											className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
										/>
										<span className="flex items-center text-sm">/</span>
										<input
											type="number"
											placeholder="Diastolic"
											value={vitalSigns.diastolic}
											onChange={(e) =>
												setVitalSigns((prev) => ({
													...prev,
													diastolic: e.target.value,
												}))
											}
											className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
										/>
									</div>
								</div>
								<div className="space-y-2">
									<label className="text-sm font-medium">Pulse Rate</label>
									<input
										type="number"
										placeholder="BPM"
										value={vitalSigns.pulseRate}
										onChange={(e) =>
											setVitalSigns((prev) => ({
												...prev,
												pulseRate: e.target.value,
											}))
										}
										className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
									/>
								</div>
								<div className="space-y-2">
									<label className="text-sm font-medium">Respiration</label>
									<input
										type="number"
										placeholder="breaths/min"
										value={vitalSigns.respiration}
										onChange={(e) =>
											setVitalSigns((prev) => ({
												...prev,
												respiration: e.target.value,
											}))
										}
										className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
									/>
								</div>
								<div className="space-y-2">
									<label className="text-sm font-medium">Temperature</label>
									<div className="flex gap-2">
										<input
											type="number"
											step="0.1"
											placeholder="Temperature"
											value={vitalSigns.temperature}
											onChange={(e) =>
												setVitalSigns((prev) => ({
													...prev,
													temperature: e.target.value,
												}))
											}
											className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
										/>
										<select
											value={vitalSigns.temperatureType}
											onChange={(e) =>
												setVitalSigns((prev) => ({
													...prev,
													temperatureType: e.target.value,
												}))
											}
											className="border-input bg-background focus-visible:ring-ring flex h-9 w-20 rounded-md border px-3 py-1 text-sm shadow-sm transition-colors focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
										>
											<option value="">Unit</option>
											<option value="C">°C</option>
											<option value="F">°F</option>
										</select>
									</div>
								</div>
								<div className="space-y-2">
									<label className="text-sm font-medium">Weight</label>
									<div className="flex gap-2">
										<input
											type="number"
											step="0.1"
											placeholder="Weight"
											value={vitalSigns.weight}
											onChange={(e) =>
												setVitalSigns((prev) => ({
													...prev,
													weight: e.target.value,
												}))
											}
											className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
										/>
										<select
											value={vitalSigns.weightType}
											onChange={(e) =>
												setVitalSigns((prev) => ({
													...prev,
													weightType: e.target.value,
												}))
											}
											className="border-input bg-background focus-visible:ring-ring flex h-9 w-20 rounded-md border px-3 py-1 text-sm shadow-sm transition-colors focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
										>
											<option value="">Unit</option>
											<option value="kg">kg</option>
											<option value="lbs">lbs</option>
										</select>
									</div>
								</div>
								<div className="space-y-2">
									<label className="text-sm font-medium">Height</label>
									<div className="flex gap-2">
										<input
											type="number"
											step="0.1"
											placeholder="Height"
											value={vitalSigns.height}
											onChange={(e) =>
												setVitalSigns((prev) => ({
													...prev,
													height: e.target.value,
												}))
											}
											className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
										/>
										<select
											value={vitalSigns.heightType}
											onChange={(e) =>
												setVitalSigns((prev) => ({
													...prev,
													heightType: e.target.value,
												}))
											}
											className="border-input bg-background focus-visible:ring-ring flex h-9 w-20 rounded-md border px-3 py-1 text-sm shadow-sm transition-colors focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
										>
											<option value="">Unit</option>
											<option value="cm">cm</option>
											<option value="ft">ft</option>
											<option value="in">in</option>
										</select>
									</div>
								</div>
								<div className="space-y-2">
									<label className="text-sm font-medium">
										Oxygen Saturation
									</label>
									<input
										type="number"
										placeholder="%"
										value={vitalSigns.oxygenSaturation}
										onChange={(e) =>
											setVitalSigns((prev) => ({
												...prev,
												oxygenSaturation: e.target.value,
											}))
										}
										className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
									/>
								</div>
								<div className="space-y-2">
									<label className="text-sm font-medium">
										Capillary Blood Glucose
									</label>
									<input
										type="number"
										step="0.1"
										placeholder="mg/dL"
										value={vitalSigns.capillaryBloodGlucose}
										onChange={(e) =>
											setVitalSigns((prev) => ({
												...prev,
												capillaryBloodGlucose: e.target.value,
											}))
										}
										className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
									/>
								</div>
								<div className="space-y-2">
									<label className="text-sm font-medium">Body Mass Index</label>
									<input
										type="number"
										step="0.1"
										placeholder="kg/m²"
										value={vitalSigns.bodyMassIndex}
										onChange={(e) =>
											setVitalSigns((prev) => ({
												...prev,
												bodyMassIndex: e.target.value,
											}))
										}
										className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
									/>
								</div>
							</div>
							<div className="space-y-4">
								{/* Existing Doctor Notes */}
								{appointment?.appointmentDoctorNotes &&
									appointment.appointmentDoctorNotes.length > 0 && (
										<div className="space-y-2">
											<label className="text-sm font-medium">
												Previous Doctor&apos;s Notes
											</label>
											<div className="space-y-2">
												{appointment.appointmentDoctorNotes.map(
													(note: IDoctorNote) => (
														<div
															key={note.id}
															className="rounded-lg border bg-gray-50 p-3"
														>
															<p className="text-sm">{note.note}</p>
															<p className="mt-1 text-xs text-gray-500">
																Added on {formatDate(note.created_at)}
															</p>
														</div>
													)
												)}
											</div>
										</div>
									)}

								{/* Add New Doctor Note */}
								<div className="space-y-2">
									<label className="text-sm font-medium">
										Add Doctor&apos;s Note
									</label>
									<textarea
										placeholder="Enter clinical observations and notes..."
										rows={4}
										value={doctorNote}
										onChange={(e) => setDoctorNote(e.target.value)}
										className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex min-h-[60px] w-full rounded-md border px-3 py-2 text-sm shadow-sm focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
									/>
									<div className="flex justify-end">
										<Button
											onClick={handleAddNote}
											disabled={isAddingNote || !doctorNote.trim()}
											size="sm"
											className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
										>
											{isAddingNote ? 'Adding...' : 'Add Note'}
										</Button>
									</div>
								</div>
								{/* Assistant Notes */}
								{appointment?.appointmentAssistantNotes &&
								appointment.appointmentAssistantNotes.length > 0 ? (
									<div className="space-y-2">
										<label className="text-sm font-medium">
											Assistant&apos;s Notes
										</label>
										<div className="space-y-2">
											{appointment.appointmentAssistantNotes.map(
												(note: IAssistantNote) => (
													<div
														key={note.id}
														className="rounded-lg border bg-blue-50 p-3"
													>
														<p className="text-sm">{note.note}</p>
														<p className="mt-1 text-xs text-gray-500">
															Added on {formatDate(note.created_at)}
														</p>
													</div>
												)
											)}
										</div>
									</div>
								) : (
									<div className="space-y-2">
										<label className="text-sm font-medium">
											Assistant&apos;s Notes
										</label>
										<div className="rounded-lg border bg-gray-50 p-3">
											<p className="text-sm text-gray-500">
												No assistant notes added yet.
											</p>
										</div>
									</div>
								)}
								<div className="flex justify-end">
									<Button
										onClick={handleSaveVitalSigns}
										disabled={isUpdatingVitals}
										className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
									>
										{isUpdatingVitals ? 'Saving...' : 'Save Vital Signs'}
									</Button>
								</div>
							</div>
						</TabsContent>

						<TabsContent value="prescription" className="space-y-4">
							<div className="flex items-center justify-between">
								<h3 className="text-lg font-semibold">Prescriptions</h3>
								<Button
									onClick={() => setShowPrescriptionForm(true)}
									size="sm"
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
								>
									<Plus className="mr-2 h-4 w-4" />
									New Prescription
								</Button>
							</div>

							{/* Create Prescription Form */}
							{showPrescriptionForm && (
								<div className="rounded-lg border p-4">
									<div className="mb-4 flex items-center justify-between">
										<h4 className="font-medium">Create New Prescription</h4>
										<Button
											variant="outline"
											size="sm"
											onClick={() => setShowPrescriptionForm(false)}
										>
											Cancel
										</Button>
									</div>
									<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
										<div className="space-y-2">
											<label className="text-sm font-medium">
												Professional Tax Receipt (Optional)
											</label>
											<input
												type="text"
												placeholder="Enter PTR number"
												value={prescriptionForm.professional_tax_receipt}
												onChange={(e) =>
													setPrescriptionForm((prev) => ({
														...prev,
														professional_tax_receipt: e.target.value,
													}))
												}
												className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
											/>
										</div>
									</div>
									<div className="mt-4 flex justify-end">
										<Button
											onClick={handleCreatePrescription}
											disabled={isCreatingPrescription}
											className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
										>
											{isCreatingPrescription
												? 'Creating...'
												: 'Create Prescription'}
										</Button>
									</div>
								</div>
							)}

							{/* Existing Prescriptions */}
							{appointment?.prescriptions &&
							appointment.prescriptions.length > 0 ? (
								<div className="space-y-4">
									{appointment.prescriptions.map(
										(prescription: IPrescription) => (
											<div
												key={prescription.id}
												className="rounded-lg border p-4"
											>
												<div className="mb-4 flex items-center justify-between">
													<div>
														<h4 className="font-medium">
															Prescription #{prescription.id}
														</h4>
														<p className="text-muted-foreground text-sm">
															Created: {formatDate(prescription.created_at)}
														</p>
													</div>
													<div className="flex gap-2">
														<Button
															variant="outline"
															size="sm"
															onClick={() =>
																setShowMedicationForm(prescription.id)
															}
														>
															<Plus className="mr-2 h-4 w-4" />
															Add Medication
														</Button>
														<Button
															variant="outline"
															size="sm"
															onClick={() =>
																handleRemovePrescription(prescription.id)
															}
															disabled={isRemovingPrescription}
														>
															<Trash2 className="mr-2 h-4 w-4" />
															Remove
														</Button>
													</div>
												</div>

												{/* Add Medication Form */}
												{showMedicationForm === prescription.id && (
													<div className="mb-4 rounded border p-3">
														<div className="mb-3 flex items-center justify-between">
															<h5 className="font-medium">Add Medication</h5>
															<Button
																variant="outline"
																size="sm"
																onClick={() => setShowMedicationForm(null)}
															>
																Cancel
															</Button>
														</div>
														<div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3">
															<div className="space-y-2">
																<label className="text-sm font-medium">
																	Generic Name *
																</label>
																<input
																	type="text"
																	placeholder="Enter generic name"
																	value={medicationForm.generic}
																	onChange={(e) =>
																		setMedicationForm((prev) => ({
																			...prev,
																			generic: e.target.value,
																		}))
																	}
																	className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
																/>
															</div>
															<div className="space-y-2">
																<label className="text-sm font-medium">
																	Brand Name *
																</label>
																<input
																	type="text"
																	placeholder="Enter brand name"
																	value={medicationForm.brand}
																	onChange={(e) =>
																		setMedicationForm((prev) => ({
																			...prev,
																			brand: e.target.value,
																		}))
																	}
																	className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
																/>
															</div>
															<div className="space-y-2">
																<label className="text-sm font-medium">
																	Dosage Form *
																</label>
																<input
																	type="text"
																	placeholder="e.g., Tablet, Capsule, Syrup"
																	value={medicationForm.dosageForm}
																	onChange={(e) =>
																		setMedicationForm((prev) => ({
																			...prev,
																			dosageForm: e.target.value,
																		}))
																	}
																	className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
																/>
															</div>
															<div className="space-y-2">
																<label className="text-sm font-medium">
																	Dosage *
																</label>
																<input
																	type="text"
																	placeholder="e.g., 500mg"
																	value={medicationForm.dosage}
																	onChange={(e) =>
																		setMedicationForm((prev) => ({
																			...prev,
																			dosage: e.target.value,
																		}))
																	}
																	className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
																/>
															</div>
															<div className="space-y-2">
																<label className="text-sm font-medium">
																	Frequency *
																</label>
																<input
																	type="text"
																	placeholder="e.g., 3 times daily"
																	value={medicationForm.frequency}
																	onChange={(e) =>
																		setMedicationForm((prev) => ({
																			...prev,
																			frequency: e.target.value,
																		}))
																	}
																	className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
																/>
															</div>
															<div className="space-y-2">
																<label className="text-sm font-medium">
																	Quantity *
																</label>
																<input
																	type="number"
																	placeholder="e.g., 21"
																	value={medicationForm.quantity}
																	onChange={(e) =>
																		setMedicationForm((prev) => ({
																			...prev,
																			quantity: parseInt(e.target.value) || 0,
																		}))
																	}
																	className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
																/>
															</div>
														</div>
														<div className="mt-3 space-y-2">
															<label className="text-sm font-medium">
																Instruction *
															</label>
															<textarea
																placeholder="Special instructions for the patient"
																rows={2}
																value={medicationForm.instruction}
																onChange={(e) =>
																	setMedicationForm((prev) => ({
																		...prev,
																		instruction: e.target.value,
																	}))
																}
																className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex min-h-[60px] w-full rounded-md border px-3 py-2 text-sm shadow-sm focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
															/>
														</div>
														<div className="mt-3 flex justify-end">
															<Button
																onClick={() =>
																	handleAddMedication(prescription.id)
																}
																disabled={
																	isCreatingItem ||
																	!medicationForm.generic.trim()
																}
																size="sm"
																className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
															>
																{isCreatingItem
																	? 'Adding...'
																	: 'Add Medication'}
															</Button>
														</div>
													</div>
												)}

												{/* Prescription Items */}
												{prescription.prescriptionItems &&
												prescription.prescriptionItems.length > 0 ? (
													<div className="space-y-2">
														<h5 className="font-medium">Medications</h5>
														{prescription.prescriptionItems.map(
															(item: IPrescriptionItem) => (
																<div
																	key={item.id}
																	className="flex items-center justify-between rounded border p-3"
																>
																	<div className="flex-1">
																		<div className="flex items-center gap-4">
																			<div>
																				<p className="font-medium">
																					{item.generic} ({item.brand})
																				</p>
																				<p className="text-muted-foreground text-sm">
																					{item.dosage_form} • {item.dosage} •{' '}
																					{item.frequency}
																				</p>
																				{item.quantity && (
																					<p className="text-muted-foreground text-sm">
																						Quantity: {item.quantity}
																					</p>
																				)}
																				{item.instruction && (
																					<p className="text-muted-foreground text-sm">
																						Instructions: {item.instruction}
																					</p>
																				)}
																			</div>
																		</div>
																	</div>
																	<Button
																		variant="outline"
																		size="sm"
																		onClick={() =>
																			handleRemoveMedication(
																				prescription.id,
																				item.id
																			)
																		}
																		disabled={isRemovingItem}
																	>
																		<Trash2 className="h-4 w-4" />
																	</Button>
																</div>
															)
														)}
													</div>
												) : (
													<p className="text-muted-foreground text-sm">
														No medications added yet.
													</p>
												)}
											</div>
										)
									)}
								</div>
							) : (
								<div className="py-8 text-center">
									<FileText className="text-muted-foreground mx-auto h-12 w-12" />
									<h3 className="mt-2 text-sm font-semibold">
										No Prescriptions
									</h3>
									<p className="text-muted-foreground mt-1 text-sm">
										Create a prescription to add medications for this
										appointment.
									</p>
								</div>
							)}
						</TabsContent>

						<TabsContent value="lab-requests" className="space-y-4">
							<div className="flex items-center justify-between">
								<h3 className="text-lg font-semibold">Laboratory Requests</h3>
								<div className="flex gap-2">
									<Button
										onClick={() => setShowLabRequestForm(true)}
										size="sm"
										className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
									>
										<Plus className="mr-2 h-4 w-4" />
										New Lab Request
									</Button>
								</div>
							</div>

							{/* Create Lab Request Form */}
							{showLabRequestForm && (
								<div className="rounded-lg border p-4">
									<div className="mb-4 flex items-center justify-between">
										<h4 className="font-medium">Create New Lab Request</h4>
										<Button
											variant="outline"
											size="sm"
											onClick={() => setShowLabRequestForm(false)}
										>
											Cancel
										</Button>
									</div>
									<p className="text-muted-foreground mb-4 text-sm">
										Patient information will be automatically populated from the
										appointment.
									</p>
									<div className="flex justify-end">
										<Button
											onClick={handleCreateLabRequest}
											disabled={isCreatingLabRequest}
											className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
										>
											{isCreatingLabRequest
												? 'Creating...'
												: 'Create Lab Request'}
										</Button>
									</div>
								</div>
							)}

							{/* Upload Lab Results */}
							<div className="rounded-lg border p-4">
								<h4 className="mb-3 font-medium">Upload Lab Results</h4>
								<div className="space-y-3">
									<div>
										<label className="text-sm font-medium">Result File</label>
										<input
											type="file"
											accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
											onChange={(e) => {
												const files = Array.from(e.target.files || []);
												setLabResultFiles(files);
											}}
											className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring mt-1 flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
										/>
										{labResultFiles.length > 0 && (
											<p className="text-muted-foreground mt-1 text-sm">
												{labResultFiles[0].name} selected
											</p>
										)}
									</div>
									<div>
										<label className="text-sm font-medium">
											Notes (Optional)
										</label>
										<textarea
											placeholder="Additional notes about the lab results"
											rows={2}
											value={labResultNotes}
											onChange={(e) => setLabResultNotes(e.target.value)}
											className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring mt-1 flex min-h-[60px] w-full rounded-md border px-3 py-2 text-sm shadow-sm focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
										/>
									</div>
									<div className="flex justify-end">
										<Button
											onClick={handleUploadLabResult}
											disabled={
												isUploadingLabResult || labResultFiles.length === 0
											}
											size="sm"
											className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
										>
											<Upload className="mr-2 h-4 w-4" />
											{isUploadingLabResult ? 'Uploading...' : 'Upload Results'}
										</Button>
									</div>
								</div>
							</div>

							{/* Uploaded Laboratory Results */}
							{appointment?.laboratoryResults &&
								appointment.laboratoryResults.length > 0 && (
									<div className="space-y-4">
										<h4 className="font-medium">Uploaded Laboratory Results</h4>
										{appointment.laboratoryResults.map(
											(result: ILaboratoryResult) => (
												<div key={result.id} className="rounded-lg border p-4">
													<div className="flex items-center justify-between">
														<div>
															<p className="font-medium">
																Laboratory Result #{result.id}
															</p>
															<p className="text-muted-foreground text-sm">
																Patient: {result.patient_name}
															</p>
															<p className="text-muted-foreground text-sm">
																Uploaded: {formatDate(result.created_at)}
															</p>
														</div>
														<div className="flex gap-2">
															<Button
																variant="outline"
																size="sm"
																onClick={() =>
																	window.open(result.record, '_blank')
																}
															>
																<Eye className="mr-2 h-4 w-4" />
																View Result
															</Button>
														</div>
													</div>
												</div>
											)
										)}
									</div>
								)}

							{/* Existing Lab Requests */}
							{appointment?.labRequests &&
							appointment.labRequests.length > 0 ? (
								<div className="space-y-4">
									{appointment.labRequests.map((labRequest: ILabRequest) => (
										<div key={labRequest.id} className="rounded-lg border p-4">
											<div className="mb-4 flex items-center justify-between">
												<div>
													<h4 className="font-medium">
														Lab Request #{labRequest.id}
													</h4>
													<p className="text-muted-foreground text-sm">
														Created: {formatDate(labRequest.created_at)}
													</p>
												</div>
												<div className="flex gap-2">
													<Button
														variant="outline"
														size="sm"
														onClick={() => setShowLabTestForm(labRequest.id)}
													>
														<Plus className="mr-2 h-4 w-4" />
														Add Test
													</Button>
													<Button
														variant="outline"
														size="sm"
														onClick={() =>
															handleRemoveLabRequest(labRequest.id)
														}
														disabled={isRemovingLabRequest}
													>
														<Trash2 className="mr-2 h-4 w-4" />
														Remove
													</Button>
												</div>
											</div>

											{/* Add Lab Test Form */}
											{showLabTestForm === labRequest.id && (
												<div className="mb-4 rounded border p-3">
													<div className="mb-3 flex items-center justify-between">
														<h5 className="font-medium">Add Lab Test</h5>
														<Button
															variant="outline"
															size="sm"
															onClick={() => setShowLabTestForm(null)}
														>
															Cancel
														</Button>
													</div>
													<div className="grid grid-cols-1 gap-3 md:grid-cols-2">
														<div className="space-y-2">
															<label className="text-sm font-medium">
																Test Type *
															</label>
															<input
																type="text"
																placeholder="e.g., Blood Chemistry, Urinalysis, CBC"
																value={labTestForm.test_type}
																onChange={(e) =>
																	setLabTestForm((prev) => ({
																		...prev,
																		test_type: e.target.value,
																	}))
																}
																className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
															/>
														</div>
													</div>
													<div className="mt-3 space-y-2">
														<label className="text-sm font-medium">
															Instructions
														</label>
														<textarea
															placeholder="Special instructions for the lab test"
															rows={2}
															value={labTestForm.instructions}
															onChange={(e) =>
																setLabTestForm((prev) => ({
																	...prev,
																	instructions: e.target.value,
																}))
															}
															className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex min-h-[60px] w-full rounded-md border px-3 py-2 text-sm shadow-sm focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
														/>
													</div>
													<div className="mt-3 flex justify-end">
														<Button
															onClick={() => handleAddLabTest(labRequest.id)}
															disabled={
																isCreatingLabTest ||
																!labTestForm.test_type.trim()
															}
															size="sm"
															className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
														>
															{isCreatingLabTest ? 'Adding...' : 'Add Test'}
														</Button>
													</div>
												</div>
											)}

											{/* Lab Request Items */}
											{labRequest.labRequestItems &&
											labRequest.labRequestItems.length > 0 ? (
												<div className="space-y-2">
													<h5 className="font-medium">Lab Tests</h5>
													{labRequest.labRequestItems.map(
														(item: ILabRequestItem) => (
															<div
																key={item.id}
																className="flex items-center justify-between rounded border p-3"
															>
																<div className="flex-1">
																	<div className="flex items-center gap-4">
																		<div>
																			<p className="font-medium">
																				{item.name || 'Unnamed Test'}
																			</p>
																			{/* Note: Instructions field not available in current API response */}
																		</div>
																	</div>
																</div>
																<Button
																	variant="outline"
																	size="sm"
																	onClick={() =>
																		handleRemoveLabTest(labRequest.id, item.id)
																	}
																	disabled={isRemovingLabTest}
																>
																	<Trash2 className="h-4 w-4" />
																</Button>
															</div>
														)
													)}
												</div>
											) : (
												<p className="text-muted-foreground text-sm">
													No lab tests added yet.
												</p>
											)}
										</div>
									))}
								</div>
							) : (
								<div className="py-8 text-center">
									<FileText className="text-muted-foreground mx-auto h-12 w-12" />
									<h3 className="mt-2 text-sm font-semibold">
										No Lab Requests
									</h3>
									<p className="text-muted-foreground mt-1 text-sm">
										Create a lab request to add tests for this appointment.
									</p>
								</div>
							)}
						</TabsContent>

						<TabsContent value="diagnostics" className="space-y-4">
							<div className="flex items-center justify-between">
								<h3 className="text-lg font-semibold">Diagnostic Requests</h3>
								<Button
									onClick={() => setShowDiagnosticRequestForm(true)}
									size="sm"
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
								>
									<Plus className="mr-2 h-4 w-4" />
									New Diagnostic Request
								</Button>
							</div>

							{/* Create Diagnostic Request Form */}
							{showDiagnosticRequestForm && (
								<div className="rounded-lg border p-4">
									<div className="mb-4 flex items-center justify-between">
										<h4 className="font-medium">
											Create New Diagnostic Request
										</h4>
										<Button
											variant="outline"
											size="sm"
											onClick={() => setShowDiagnosticRequestForm(false)}
										>
											Cancel
										</Button>
									</div>
									<p className="text-muted-foreground mb-4 text-sm">
										Patient information will be automatically populated from the
										appointment.
									</p>
									<div className="flex justify-end">
										<Button
											onClick={handleCreateDiagnosticRequest}
											disabled={isCreatingDiagnosticRequest}
											className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
										>
											{isCreatingDiagnosticRequest
												? 'Creating...'
												: 'Create Diagnostic Request'}
										</Button>
									</div>
								</div>
							)}

							{/* Existing Diagnostic Requests */}
							{appointment?.diagnosticRequests &&
							appointment.diagnosticRequests.length > 0 ? (
								<div className="space-y-4">
									{appointment.diagnosticRequests.map(
										(diagnosticRequest: IDiagnosticRequest) => (
											<div
												key={diagnosticRequest.id}
												className="rounded-lg border p-4"
											>
												<div className="mb-4 flex items-center justify-between">
													<div>
														<h4 className="font-medium">
															Diagnostic Request #{diagnosticRequest.id}
														</h4>
														<p className="text-muted-foreground text-sm">
															Created:{' '}
															{formatDate(diagnosticRequest.created_at)}
														</p>
													</div>
													<div className="flex gap-2">
														<Button
															variant="outline"
															size="sm"
															onClick={() =>
																setShowTestForm(diagnosticRequest.id)
															}
														>
															<Plus className="mr-2 h-4 w-4" />
															Add Test
														</Button>
														<Button
															variant="outline"
															size="sm"
															onClick={() =>
																handleRemoveDiagnosticRequest(
																	diagnosticRequest.id
																)
															}
															disabled={isRemovingDiagnosticRequest}
														>
															<Trash2 className="mr-2 h-4 w-4" />
															Remove
														</Button>
													</div>
												</div>

												{/* Add Test Form */}
												{showTestForm === diagnosticRequest.id && (
													<div className="mb-4 rounded border p-3">
														<div className="mb-3 flex items-center justify-between">
															<h5 className="font-medium">Add Test</h5>
															<Button
																variant="outline"
																size="sm"
																onClick={() => setShowTestForm(null)}
															>
																Cancel
															</Button>
														</div>
														<div className="space-y-2">
															<label className="text-sm font-medium">
																Test Name *
															</label>
															<input
																type="text"
																placeholder="Enter test name (e.g., Blood Test, X-Ray, MRI)"
																value={testForm.name}
																onChange={(e) =>
																	setTestForm((prev) => ({
																		...prev,
																		name: e.target.value,
																	}))
																}
																className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
															/>
														</div>
														<div className="mt-3 flex justify-end">
															<Button
																onClick={() =>
																	handleAddTest(diagnosticRequest.id)
																}
																disabled={
																	isCreatingTest || !testForm.name.trim()
																}
																size="sm"
																className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
															>
																{isCreatingTest ? 'Adding...' : 'Add Test'}
															</Button>
														</div>
													</div>
												)}

												{/* Diagnostic Request Items */}
												{diagnosticRequest.diagnosticRequestItems &&
												diagnosticRequest.diagnosticRequestItems.length > 0 ? (
													<div className="space-y-2">
														<h5 className="font-medium">Tests</h5>
														{diagnosticRequest.diagnosticRequestItems.map(
															(item: IDiagnosticRequestItem) => (
																<div
																	key={item.id}
																	className="flex items-center justify-between rounded border p-3"
																>
																	<div className="flex-1">
																		<div className="flex items-center gap-4">
																			<div>
																				<p className="font-medium">
																					{item.name || 'Unnamed Test'}
																				</p>
																			</div>
																		</div>
																	</div>
																	<Button
																		variant="outline"
																		size="sm"
																		onClick={() =>
																			handleRemoveTest(
																				diagnosticRequest.id,
																				item.id
																			)
																		}
																		disabled={isRemovingTest}
																	>
																		<Trash2 className="h-4 w-4" />
																	</Button>
																</div>
															)
														)}
													</div>
												) : (
													<p className="text-muted-foreground text-sm">
														No tests added yet.
													</p>
												)}
											</div>
										)
									)}
								</div>
							) : (
								<div className="py-8 text-center">
									<Stethoscope className="text-muted-foreground mx-auto h-12 w-12" />
									<h3 className="mt-2 text-sm font-semibold">
										No Diagnostic Requests
									</h3>
									<p className="text-muted-foreground mt-1 text-sm">
										Create a diagnostic request to add tests for this
										appointment.
									</p>
								</div>
							)}
						</TabsContent>

						<TabsContent value="certificates" className="space-y-4">
							<div className="flex items-center justify-between">
								<h3 className="text-lg font-semibold">Medical Certificates</h3>
								<Button
									onClick={handleCreateMedicalCertificate}
									size="sm"
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
								>
									<Plus className="mr-2 h-4 w-4" />
									New Certificate
								</Button>
							</div>

							{/* Existing Medical Certificates */}
							{appointment?.medicalCertificates &&
							appointment.medicalCertificates.length > 0 ? (
								<div className="space-y-4">
									{appointment.medicalCertificates.map(
										(medicalCertificate: IMedicalCertificate) => (
											<div
												key={medicalCertificate.id}
												className="rounded-lg border p-4"
											>
												<div className="mb-4 flex items-center justify-between">
													<div>
														<h4 className="font-medium">
															Medical Certificate #{medicalCertificate.id}
														</h4>
														<p className="text-muted-foreground text-sm">
															Created:{' '}
															{formatDate(medicalCertificate.created_at)}
														</p>
														{medicalCertificate.professional_tax_receipt && (
															<p className="text-muted-foreground text-sm">
																PTR:{' '}
																{medicalCertificate.professional_tax_receipt}
															</p>
														)}
														{medicalCertificate.medical_number && (
															<p className="text-muted-foreground text-sm">
																Medical Number:{' '}
																{medicalCertificate.medical_number}
															</p>
														)}
													</div>
													<div className="flex gap-2">
														<Button
															variant="outline"
															size="sm"
															onClick={() =>
																setShowConditionForm(medicalCertificate.id)
															}
														>
															<Plus className="mr-2 h-4 w-4" />
															Add Condition
														</Button>
														<Button
															variant="outline"
															size="sm"
															onClick={() =>
																handleRemoveMedicalCertificate(
																	medicalCertificate.id
																)
															}
															disabled={isRemovingMedicalCertificate}
														>
															<Trash2 className="mr-2 h-4 w-4" />
															Remove
														</Button>
													</div>
												</div>

												{/* Add Condition Form */}
												{showConditionForm === medicalCertificate.id && (
													<div className="mb-4 rounded border p-3">
														<div className="mb-3 flex items-center justify-between">
															<h5 className="font-medium">Add Condition</h5>
															<Button
																variant="outline"
																size="sm"
																onClick={() => setShowConditionForm(null)}
															>
																Cancel
															</Button>
														</div>
														<div className="space-y-3">
															<div className="space-y-2">
																<label className="text-sm font-medium">
																	Description
																</label>
																<textarea
																	placeholder="Enter condition or recommendation"
																	value={conditionForm.description}
																	onChange={(e) =>
																		setConditionForm((prev) => ({
																			...prev,
																			description: e.target.value,
																		}))
																	}
																	className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-[oklch(0.7448_0.1256_202.74)] focus:ring-1 focus:ring-[oklch(0.7448_0.1256_202.74)] focus:outline-none"
																	rows={3}
																/>
															</div>
															<div className="flex justify-end">
																<Button
																	onClick={() =>
																		handleAddCondition(medicalCertificate.id)
																	}
																	disabled={
																		!conditionForm.description.trim() ||
																		isCreatingCondition
																	}
																	size="sm"
																	className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
																>
																	{isCreatingCondition
																		? 'Adding...'
																		: 'Add Condition'}
																</Button>
															</div>
														</div>
													</div>
												)}

												{/* Certificate Items */}
												{medicalCertificate.medicalCertificateItems &&
												medicalCertificate.medicalCertificateItems.length >
													0 ? (
													<div className="space-y-2">
														<h5 className="font-medium">
															Conditions/Recommendations
														</h5>
														{medicalCertificate.medicalCertificateItems.map(
															(item: IMedicalCertificateItem) => (
																<div
																	key={item.id}
																	className="flex items-start justify-between rounded border p-3"
																>
																	<div className="flex-1">
																		<p className="text-sm">
																			{item.description}
																		</p>
																		<p className="text-muted-foreground text-xs">
																			Added: {formatDate(item.created_at)}
																		</p>
																	</div>
																	<Button
																		variant="outline"
																		size="sm"
																		onClick={() =>
																			handleRemoveCondition(
																				medicalCertificate.id,
																				item.id
																			)
																		}
																		disabled={isRemovingCondition}
																	>
																		<Trash2 className="h-3 w-3" />
																	</Button>
																</div>
															)
														)}
													</div>
												) : (
													<p className="text-muted-foreground text-sm">
														No conditions added yet.
													</p>
												)}
											</div>
										)
									)}
								</div>
							) : (
								<div className="py-8 text-center">
									<p className="text-muted-foreground">
										No medical certificates created yet.
									</p>
								</div>
							)}
						</TabsContent>

						{/* Referrals Tab */}
						<TabsContent value="referrals" className="space-y-4">
							<div className="flex items-center justify-between">
								<h3 className="text-lg font-semibold">Referrals</h3>
								<Button
									onClick={() => setShowReferralForm(true)}
									size="sm"
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
								>
									<Plus className="mr-2 h-4 w-4" />
									New Referral
								</Button>
							</div>

							{/* Create Referral Form */}
							{showReferralForm && (
								<div className="rounded-lg border p-4">
									<div className="mb-4 flex items-center justify-between">
										<h4 className="font-medium">Create New Referral</h4>
										<Button
											variant="outline"
											size="sm"
											onClick={() => setShowReferralForm(false)}
										>
											Cancel
										</Button>
									</div>
									<div className="space-y-4">
										<div className="space-y-2">
											<label className="text-sm font-medium">
												Referred Doctor Name *
											</label>
											<input
												type="text"
												placeholder="Enter the name of the doctor you're referring to"
												value={referralForm.referredDoctorName}
												onChange={(e) =>
													setReferralForm((prev) => ({
														...prev,
														referredDoctorName: e.target.value,
													}))
												}
												className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
											/>
										</div>
										<div className="space-y-2">
											<label className="text-sm font-medium">
												Purpose of Referral *
											</label>
											<textarea
												placeholder="Describe the reason for this referral"
												value={referralForm.purposeOfReferral}
												onChange={(e) =>
													setReferralForm((prev) => ({
														...prev,
														purposeOfReferral: e.target.value,
													}))
												}
												rows={4}
												className="border-input bg-background placeholder:text-muted-foreground focus-visible:ring-ring flex w-full resize-none rounded-md border px-3 py-2 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
											/>
										</div>
									</div>
									<div className="mt-4 flex justify-end">
										<Button
											onClick={handleCreateReferral}
											disabled={
												isCreatingReferral ||
												!referralForm.referredDoctorName.trim() ||
												!referralForm.purposeOfReferral.trim()
											}
											className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
										>
											{isCreatingReferral ? 'Creating...' : 'Create Referral'}
										</Button>
									</div>
								</div>
							)}

							{/* Existing Referrals */}
							{appointment?.referrals && appointment.referrals.length > 0 ? (
								<div className="space-y-4">
									{appointment.referrals.map((referral: IReferral) => (
										<div key={referral.id} className="rounded-lg border p-4">
											<div className="mb-4 flex items-center justify-between">
												<div>
													<h4 className="font-medium">
														Referral #{referral.id}
													</h4>
													<p className="text-muted-foreground text-sm">
														Created: {formatDate(referral.created_at)}
													</p>
												</div>
											</div>

											<div className="space-y-3">
												<div>
													<p className="text-muted-foreground mb-1 text-sm">
														Referred to Doctor
													</p>
													<p className="font-medium">{referral.doctor_name}</p>
												</div>
												<div>
													<p className="text-muted-foreground mb-1 text-sm">
														Purpose of Referral
													</p>
													<div className="bg-muted/50 rounded-lg p-3">
														<p className="text-sm whitespace-pre-wrap">
															{referral.purpose}
														</p>
													</div>
												</div>
											</div>
										</div>
									))}
								</div>
							) : (
								<div className="py-8 text-center">
									<FileText className="text-muted-foreground mx-auto h-12 w-12" />
									<h3 className="mt-2 text-sm font-semibold">No Referrals</h3>
									<p className="text-muted-foreground mt-1 text-sm">
										Create a referral to refer this patient to another medical
										professional.
									</p>
								</div>
							)}
						</TabsContent>

						{/* Medical Records Tab */}
						<TabsContent value="medical-records" className="space-y-6">
							<div className="space-y-4">
								<div className="flex items-center justify-between">
									<h3 className="text-lg font-medium">Medical Records</h3>
									<Button
										onClick={() => setShowMedicalRecordForm(true)}
										size="sm"
										className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
									>
										<Plus className="mr-2 h-4 w-4" />
										Add Record
									</Button>
								</div>

								{showMedicalRecordForm && (
									<div className="space-y-4 rounded-lg border p-4">
										<h4 className="font-medium">Upload Medical Record</h4>
										<div className="space-y-2">
											<Label htmlFor="medical-record-file">
												Select File (PDF, DOC, DOCX, JPG, PNG)
											</Label>
											<Input
												id="medical-record-file"
												type="file"
												accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
												onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
													const file = e.target.files?.[0];
													if (file) {
														setMedicalRecordFile(file);
													}
												}}
											/>
										</div>
										<div className="flex gap-2">
											<Button
												onClick={handleAddMedicalRecord}
												disabled={!medicalRecordFile || isAddingMedicalRecord}
												size="sm"
												className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
											>
												{isAddingMedicalRecord ? (
													<>
														<Loader2 className="mr-2 h-4 w-4 animate-spin" />
														Uploading...
													</>
												) : (
													<>
														<Upload className="mr-2 h-4 w-4" />
														Upload Record
													</>
												)}
											</Button>
											<Button
												onClick={() => {
													setShowMedicalRecordForm(false);
													setMedicalRecordFile(null);
												}}
												variant="outline"
												size="sm"
											>
												Cancel
											</Button>
										</div>
									</div>
								)}

								{appointment?.medicalRecords &&
								appointment.medicalRecords.length > 0 ? (
									<div className="space-y-3">
										{appointment.medicalRecords.map(
											(record: {
												id: number;
												record: string;
												created_at: string;
											}) => (
												<div
													key={record.id}
													className="flex items-center justify-between rounded-lg border p-3"
												>
													<div className="flex items-center gap-3">
														<FileText className="text-muted-foreground h-5 w-5" />
														<div>
															<p className="font-medium">Medical Record</p>
															<p className="text-muted-foreground text-sm">
																Uploaded on{' '}
																{format(
																	new Date(record.created_at),
																	'MMM d, yyyy h:mm a'
																)}
															</p>
														</div>
													</div>
													<div className="flex items-center gap-2">
														<Button
															onClick={() => {
																// Open the medical record file
																window.open(record.record, '_blank');
															}}
															variant="outline"
															size="sm"
														>
															<Eye className="mr-2 h-4 w-4" />
															View
														</Button>
														<Button
															onClick={() =>
																handleRemoveMedicalRecord(record.id)
															}
															variant="outline"
															size="sm"
															disabled={isRemovingMedicalRecord}
														>
															<Trash2 className="h-4 w-4" />
														</Button>
													</div>
												</div>
											)
										)}
									</div>
								) : (
									<div className="py-8 text-center">
										<p className="text-muted-foreground">
											No medical records uploaded yet.
										</p>
									</div>
								)}
							</div>
						</TabsContent>
					</Tabs>
				) : null}
			</DialogContent>
		</Dialog>
	);
}
