'use client';

import {
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Eye,
	MoreHorizontal,
	Search,
} from 'lucide-react';
import { useQueryState } from 'nuqs';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import { searchParamParsers } from '@/core/lib/search-params';
import { AppointmentDetailsPage } from '@/features/dashboard/components/doctor-dashboard/appointment-details-page';
import { useTodaysAttendedAppointments } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import { IAppointment } from '@/features/dashboard/types/doctor.types';

import { useClinics } from '../../hooks/useDoctorAssistantDashboard';

interface IAttendedAppointmentRowProps {
	appointment: IAppointment;
	onView: (appointmentId: number) => void;
}

const AttendedAppointmentRow = ({
	appointment,
	onView,
}: IAttendedAppointmentRowProps) => {
	const getStatusBadge = () => {
		return <Badge variant="secondary">Completed</Badge>;
	};

	const formatTime = (dateString: string) => {
		return new Date(dateString).toLocaleTimeString('en-US', {
			hour: '2-digit',
			minute: '2-digit',
		});
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString('en-US', {
			month: 'short',
			day: 'numeric',
		});
	};

	return (
		<TableRow>
			<TableCell>
				<div className="flex items-center gap-3">
					<div className="bg-muted flex h-8 w-8 items-center justify-center rounded-full">
						<span className="text-xs font-medium">
							{appointment.queue_number || '#'}
						</span>
					</div>
					<div>
						<p className="font-medium">
							{appointment.patient?.profile?.first_name}{' '}
							{appointment.patient?.profile?.last_name}
						</p>
						<p className="text-muted-foreground text-sm">
							{appointment.patient?.profile?.phone}
						</p>
					</div>
				</div>
			</TableCell>
			<TableCell>
				<div>
					<p className="font-medium">{appointment.clinic?.name}</p>
					<p className="text-muted-foreground text-sm">
						{appointment.clinic?.address}
					</p>
				</div>
			</TableCell>
			<TableCell>
				<div>
					<p className="font-medium">
						{formatTime(appointment.appointment_date)}
					</p>
					<p className="text-muted-foreground text-sm">
						{formatDate(appointment.appointment_date)}
					</p>
				</div>
			</TableCell>
			<TableCell>
				<div>
					<p className="font-medium">
						{appointment.visitReason?.name || 'N/A'}
					</p>
					<p className="text-muted-foreground text-sm">
						{appointment.consultationType?.name || 'N/A'}
					</p>
				</div>
			</TableCell>
			<TableCell>{getStatusBadge()}</TableCell>
			<TableCell>
				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						<Button variant="ghost" className="h-8 w-8 p-0">
							<MoreHorizontal className="h-4 w-4" />
						</Button>
					</DropdownMenuTrigger>
					<DropdownMenuContent align="end">
						<DropdownMenuLabel>Actions</DropdownMenuLabel>
						<DropdownMenuItem onClick={() => onView(appointment.id)}>
							<Eye className="mr-2 h-4 w-4" />
							View Details
						</DropdownMenuItem>
					</DropdownMenuContent>
				</DropdownMenu>
			</TableCell>
		</TableRow>
	);
};

const LoadingSkeleton = () => (
	<div className="space-y-3">
		{[...Array(5)].map((_, i) => (
			<div key={i} className="flex items-center space-x-4">
				<Skeleton className="h-8 w-8 rounded-full" />
				<div className="space-y-2">
					<Skeleton className="h-4 w-32" />
					<Skeleton className="h-3 w-24" />
				</div>
			</div>
		))}
	</div>
);

export function DoctorAttendedAppointments() {
	const [appointmentId, setAppointmentId] = useQueryState(
		'appointmentId',
		searchParamParsers.appointmentId
	);
	const [searchTerm, setSearchTerm] = useState('');
	const [selectedClinic, setSelectedClinic] = useState<string>('all');

	const { data: clinicsResponse } = useClinics();

	const clinics = clinicsResponse?.data?.data || [];

	const {
		data: appointmentsResponse,
		isLoading,
		error,
	} = useTodaysAttendedAppointments({
		search: searchTerm,
		clinicId: selectedClinic !== 'all' ? parseInt(selectedClinic) : undefined,
	});

	const appointments = Array.isArray(appointmentsResponse?.data)
		? appointmentsResponse.data
		: [];

	const handleViewAppointment = (id: number) => {
		setAppointmentId(id);
	};

	// If appointmentId is present, show appointment details
	if (appointmentId) {
		return <AppointmentDetailsPage appointmentId={appointmentId} />;
	}

	if (error) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
				<Card className="border-destructive">
					<CardContent className="p-6">
						<div className="text-center">
							<Calendar className="text-destructive mx-auto mb-2 h-8 w-8" />
							<p className="text-destructive text-sm">
								Failed to load attended appointments
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<div>
							<CardTitle className="text-xl text-[oklch(0.7448_0.1256_202.74)]">
								Today&apos;s Attended Appointments
							</CardTitle>
							<p className="text-muted-foreground text-sm">
								View completed consultations for today
							</p>
						</div>
					</div>
				</CardHeader>
				<CardContent>
					{/* Filters */}
					<div className="mb-6 flex flex-col gap-4 md:flex-row md:items-center">
						<div className="relative flex-1">
							<Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
							<Input
								placeholder="Search by patient name..."
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className="pl-10"
							/>
						</div>
						<Select value={selectedClinic} onValueChange={setSelectedClinic}>
							<SelectTrigger className="w-full md:w-48">
								<SelectValue placeholder="Filter by clinic" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">All Clinics</SelectItem>
								{clinics.map((clinic) => (
									<SelectItem key={clinic.id} value={clinic.id.toString()}>
										{clinic.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					{/* Appointments Table */}
					<div className="rounded-md border">
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Patient</TableHead>
									<TableHead>Clinic</TableHead>
									<TableHead>Time</TableHead>
									<TableHead>Reason</TableHead>
									<TableHead>Status</TableHead>
									<TableHead>Actions</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{isLoading ? (
									<TableRow>
										<TableCell colSpan={6}>
											<LoadingSkeleton />
										</TableCell>
									</TableRow>
								) : appointments.length === 0 ? (
									<TableRow>
										<TableCell colSpan={6} className="py-8 text-center">
											<div className="flex flex-col items-center gap-2">
												<CalendarCheck className="text-muted-foreground h-8 w-8" />
												<p className="text-muted-foreground">
													No attended appointments for today
												</p>
											</div>
										</TableCell>
									</TableRow>
								) : (
									appointments.map((appointment) => (
										<AttendedAppointmentRow
											key={appointment.id}
											appointment={appointment}
											onView={handleViewAppointment}
										/>
									))
								)}
							</TableBody>
						</Table>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
