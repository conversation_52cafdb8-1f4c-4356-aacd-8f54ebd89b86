'use client';

import {
	Archive,
	FileText,
	FlaskConical,
	Stethoscope,
	Users,
} from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export function DoctorArchiveManagement() {
	const [activeTab, setActiveTab] = useState('prescription');

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
						Archive Management
					</h1>
					<p className="text-muted-foreground">
						Manage your archived medical documents and patient records
					</p>
				</div>
			</div>

			<Card>
				<CardHeader>
					<CardTitle>Archived Documents</CardTitle>
				</CardHeader>
				<CardContent>
					<Tabs value={activeTab} onValueChange={setActiveTab}>
						<TabsList className="grid w-full grid-cols-5">
							<TabsTrigger value="prescription">Prescriptions</TabsTrigger>
							<TabsTrigger value="lab_request">Lab Requests</TabsTrigger>
							<TabsTrigger value="referral">Referrals</TabsTrigger>
							<TabsTrigger value="medical_certificate">
								Medical Certificates
							</TabsTrigger>
							<TabsTrigger value="patient">Patients</TabsTrigger>
						</TabsList>

						<TabsContent value="prescription" className="mt-6">
							<div className="flex flex-col items-center justify-center py-12 text-center">
								<FileText className="text-muted-foreground mb-4 h-12 w-12" />
								<h3 className="mb-2 text-lg font-semibold">
									Archived Prescriptions
								</h3>
								<p className="text-muted-foreground mb-4">
									View and manage your archived prescription documents
								</p>
								<Button variant="outline">
									<Archive className="mr-2 h-4 w-4" />
									View Archive
								</Button>
							</div>
						</TabsContent>

						<TabsContent value="lab_request" className="mt-6">
							<div className="flex flex-col items-center justify-center py-12 text-center">
								<FlaskConical className="text-muted-foreground mb-4 h-12 w-12" />
								<h3 className="mb-2 text-lg font-semibold">
									Archived Lab Requests
								</h3>
								<p className="text-muted-foreground mb-4">
									View and manage your archived lab request documents
								</p>
								<Button variant="outline">
									<Archive className="mr-2 h-4 w-4" />
									View Archive
								</Button>
							</div>
						</TabsContent>

						<TabsContent value="referral" className="mt-6">
							<div className="flex flex-col items-center justify-center py-12 text-center">
								<Stethoscope className="text-muted-foreground mb-4 h-12 w-12" />
								<h3 className="mb-2 text-lg font-semibold">
									Archived Referrals
								</h3>
								<p className="text-muted-foreground mb-4">
									View and manage your archived referral documents
								</p>
								<Button variant="outline">
									<Archive className="mr-2 h-4 w-4" />
									View Archive
								</Button>
							</div>
						</TabsContent>

						<TabsContent value="medical_certificate" className="mt-6">
							<div className="flex flex-col items-center justify-center py-12 text-center">
								<FileText className="text-muted-foreground mb-4 h-12 w-12" />
								<h3 className="mb-2 text-lg font-semibold">
									Archived Medical Certificates
								</h3>
								<p className="text-muted-foreground mb-4">
									View and manage your archived medical certificate documents
								</p>
								<Button variant="outline">
									<Archive className="mr-2 h-4 w-4" />
									View Archive
								</Button>
							</div>
						</TabsContent>

						<TabsContent value="patient" className="mt-6">
							<div className="flex flex-col items-center justify-center py-12 text-center">
								<Users className="text-muted-foreground mb-4 h-12 w-12" />
								<h3 className="mb-2 text-lg font-semibold">
									Archived Patients
								</h3>
								<p className="text-muted-foreground mb-4">
									View and manage your archived patient records
								</p>
								<Button variant="outline">
									<Archive className="mr-2 h-4 w-4" />
									View Archive
								</Button>
							</div>
						</TabsContent>
					</Tabs>
				</CardContent>
			</Card>
		</div>
	);
}
