'use client';

import { format } from 'date-fns';
import {
	Calendar,
	ChevronLeft,
	ChevronRight,
	Eye,
	Filter,
	MapPin,
	MoreHorizontal,
	Search,
	User,
} from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import {
	useAppointmentHistories,
	useClinics,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import type { IAppointment } from '@/features/dashboard/types/doctor.types';

interface IAppointmentHistoriesProps {
	onViewAppointment?: (appointmentId: number) => void;
}

const formatDate = (dateString: string) => {
	try {
		return format(new Date(dateString), 'MMM dd, yyyy');
	} catch {
		return 'Invalid date';
	}
};

const formatTime = (dateString: string) => {
	try {
		return format(new Date(dateString), 'h:mm a');
	} catch {
		return 'Invalid time';
	}
};

const getStatusColor = (status: string) => {
	switch (status?.toLowerCase()) {
		case 'completed':
			return 'bg-green-100 text-green-800 border-green-200';
		case 'cancelled':
			return 'bg-red-100 text-red-800 border-red-200';
		case 'no-show':
			return 'bg-orange-100 text-orange-800 border-orange-200';
		case 'rescheduled':
			return 'bg-blue-100 text-blue-800 border-blue-200';
		default:
			return 'bg-gray-100 text-gray-800 border-gray-200';
	}
};

export function AppointmentHistories({
	onViewAppointment,
}: IAppointmentHistoriesProps) {
	const [currentPage, setCurrentPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [searchTerm, setSearchTerm] = useState('');
	const [selectedClinic, setSelectedClinic] = useState<string>('all');
	const [selectedStatus, setSelectedStatus] = useState<string>('all');
	const [selectedCategory, setSelectedCategory] = useState<string>('old');

	// Fetch clinics for filter
	const { data: clinicsResponse } = useClinics();
	const clinics = clinicsResponse?.data?.data || [];

	// Prepare filter parameters
	const filterParams = {
		page: currentPage,
		pageSize,
		search: searchTerm || undefined,
		category: selectedCategory,
		clinicId: selectedClinic !== 'all' ? parseInt(selectedClinic) : undefined,
		status: selectedStatus !== 'all' ? selectedStatus : undefined,
	};

	const {
		data: historiesResponse,
		isLoading,
		error,
	} = useAppointmentHistories(filterParams);

	const appointments = historiesResponse?.data?.data || [];
	const totalPages = historiesResponse?.data?.meta?.last_page || 1;
	const total = historiesResponse?.data?.meta?.total || 0;

	const handleSearch = (value: string) => {
		setSearchTerm(value);
		setCurrentPage(1); // Reset to first page when searching
	};

	const handleClinicFilter = (value: string) => {
		setSelectedClinic(value);
		setCurrentPage(1);
	};

	const handleStatusFilter = (value: string) => {
		setSelectedStatus(value);
		setCurrentPage(1);
	};

	const handleCategoryFilter = (value: string) => {
		setSelectedCategory(value);
		setCurrentPage(1);
	};

	const handlePageSizeChange = (value: string) => {
		setPageSize(parseInt(value));
		setCurrentPage(1);
	};

	if (error) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
				<Card>
					<CardContent className="flex min-h-[400px] items-center justify-center">
						<div className="text-center">
							<p className="text-muted-foreground">
								Failed to load appointment histories
							</p>
							<p className="text-sm text-red-600">
								{error instanceof Error ? error.message : 'Unknown error'}
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<div>
							<CardTitle className="flex items-center gap-2">
								<Calendar className="h-5 w-5 text-[oklch(0.7448_0.1256_202.74)]" />
								Appointment Histories
							</CardTitle>
							<p className="text-muted-foreground text-sm">
								View and manage past appointments
							</p>
						</div>
						<div className="text-muted-foreground text-sm">
							Total: {total} appointments
						</div>
					</div>
				</CardHeader>
				<CardContent>
					{/* Filters */}
					<div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center">
						<div className="flex flex-1 items-center gap-2">
							<Search className="h-4 w-4 text-gray-400" />
							<Input
								placeholder="Search by patient name or email..."
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className="max-w-sm"
							/>
						</div>
						<div className="flex items-center gap-2">
							<Filter className="h-4 w-4 text-gray-400" />
							<Select
								value={selectedCategory}
								onValueChange={handleCategoryFilter}
							>
								<SelectTrigger className="w-32">
									<SelectValue placeholder="Category" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="old">Past</SelectItem>
									<SelectItem value="new">Upcoming</SelectItem>
								</SelectContent>
							</Select>
							<Select value={selectedClinic} onValueChange={handleClinicFilter}>
								<SelectTrigger className="w-40">
									<SelectValue placeholder="All Clinics" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">All Clinics</SelectItem>
									{clinics.map((clinic) => (
										<SelectItem key={clinic.id} value={clinic.id.toString()}>
											{clinic.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
							<Select value={selectedStatus} onValueChange={handleStatusFilter}>
								<SelectTrigger className="w-32">
									<SelectValue placeholder="All Status" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">All Status</SelectItem>
									<SelectItem value="completed">Completed</SelectItem>
									<SelectItem value="cancelled">Cancelled</SelectItem>
									<SelectItem value="no-show">No Show</SelectItem>
									<SelectItem value="rescheduled">Rescheduled</SelectItem>
								</SelectContent>
							</Select>
						</div>
					</div>

					{/* Table */}
					{isLoading ? (
						<div className="space-y-3">
							{Array.from({ length: 5 }).map((_, i) => (
								<Skeleton key={i} className="h-16 w-full" />
							))}
						</div>
					) : appointments.length === 0 ? (
						<div className="flex min-h-[300px] items-center justify-center">
							<div className="text-center">
								<Calendar className="mx-auto h-12 w-12 text-gray-400" />
								<h3 className="mt-2 text-sm font-medium text-gray-900">
									No appointments found
								</h3>
								<p className="text-muted-foreground mt-1 text-sm">
									{searchTerm ||
									selectedClinic !== 'all' ||
									selectedStatus !== 'all'
										? 'Try adjusting your filters'
										: 'No appointment history available'}
								</p>
							</div>
						</div>
					) : (
						<>
							<Table>
								<TableHeader>
									<TableRow>
										<TableHead>Patient</TableHead>
										<TableHead>Date & Time</TableHead>
										<TableHead>Clinic</TableHead>
										<TableHead>Visit Reason</TableHead>
										<TableHead>Status</TableHead>
										<TableHead className="text-right">Actions</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{appointments.map((appointment: IAppointment) => {
										const patientName = appointment.patient
											? `${appointment.patient.profile?.first_name || ''} ${appointment.patient.profile?.last_name || ''}`.trim()
											: 'Unknown Patient';

										return (
											<TableRow key={appointment.id}>
												<TableCell>
													<div className="flex items-center gap-3">
														<div className="bg-muted flex h-8 w-8 items-center justify-center rounded-full">
															<User className="h-4 w-4" />
														</div>
														<div>
															<p className="font-medium">{patientName}</p>
															<p className="text-muted-foreground text-sm">
																{appointment.patient?.profile?.user?.email ||
																	'No email'}
															</p>
														</div>
													</div>
												</TableCell>
												<TableCell>
													<div className="flex items-center gap-2">
														<Calendar className="h-4 w-4 text-gray-400" />
														<div>
															<p className="font-medium">
																{formatDate(appointment.appointment_date)}
															</p>
															<p className="text-muted-foreground text-sm">
																{formatTime(appointment.appointment_date)}
															</p>
														</div>
													</div>
												</TableCell>
												<TableCell>
													<div className="flex items-center gap-2">
														<MapPin className="h-4 w-4 text-gray-400" />
														<span>{appointment.clinic?.name || 'Unknown'}</span>
													</div>
												</TableCell>
												<TableCell>
													<span className="text-sm">
														{appointment.visitReason?.name ||
															appointment.appointment_reason ||
															'Not specified'}
													</span>
												</TableCell>
												<TableCell>
													<Badge
														variant="outline"
														className={getStatusColor(appointment.status)}
													>
														{appointment.status}
													</Badge>
												</TableCell>
												<TableCell className="text-right">
													<DropdownMenu>
														<DropdownMenuTrigger asChild>
															<Button variant="ghost" className="h-8 w-8 p-0">
																<span className="sr-only">Open menu</span>
																<MoreHorizontal className="h-4 w-4" />
															</Button>
														</DropdownMenuTrigger>
														<DropdownMenuContent align="end">
															<DropdownMenuLabel>Actions</DropdownMenuLabel>
															<DropdownMenuItem
																onClick={() =>
																	onViewAppointment?.(appointment.id)
																}
															>
																<Eye className="mr-2 h-4 w-4" />
																View Details
															</DropdownMenuItem>
														</DropdownMenuContent>
													</DropdownMenu>
												</TableCell>
											</TableRow>
										);
									})}
								</TableBody>
							</Table>

							{/* Pagination */}
							<div className="flex items-center justify-between pt-4">
								<div className="flex items-center gap-2">
									<span className="text-muted-foreground text-sm">
										Rows per page:
									</span>
									<Select
										value={pageSize.toString()}
										onValueChange={handlePageSizeChange}
									>
										<SelectTrigger className="w-16">
											<SelectValue />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="5">5</SelectItem>
											<SelectItem value="10">10</SelectItem>
											<SelectItem value="20">20</SelectItem>
											<SelectItem value="50">50</SelectItem>
										</SelectContent>
									</Select>
								</div>
								<div className="flex items-center gap-2">
									<span className="text-muted-foreground text-sm">
										Page {currentPage} of {totalPages}
									</span>
									<Button
										variant="outline"
										size="sm"
										onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
										disabled={currentPage === 1}
									>
										<ChevronLeft className="h-4 w-4" />
									</Button>
									<Button
										variant="outline"
										size="sm"
										onClick={() =>
											setCurrentPage(Math.min(totalPages, currentPage + 1))
										}
										disabled={currentPage === totalPages}
									>
										<ChevronRight className="h-4 w-4" />
									</Button>
								</div>
							</div>
						</>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
