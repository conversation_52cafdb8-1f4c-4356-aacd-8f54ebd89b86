'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useHookstate } from '@hookstate/core';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogClose,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {
	useDoctorProfile,
	useUpdateDoctorBasicInfo,
} from '@/features/dashboard/hooks/useDoctorDashboard';

const profileSchema = z.object({
	firstName: z.string().optional(),
	middleName: z.string().optional(),
	lastName: z.string().optional(),
	suffix: z.string().optional(),
	phone: z.string().optional(),
	gender: z.string().optional(),
});

export type EditProfileForm = z.infer<typeof profileSchema>;

interface IEditProfileDialogProps {
	children: React.ReactNode;
}

export function EditProfileDialog({ children }: IEditProfileDialogProps) {
	const { data: profile } = useDoctorProfile();
	const updateBasicInfo = useUpdateDoctorBasicInfo();
	const dialog = useHookstate(false);

	const form = useForm<EditProfileForm>({
		resolver: zodResolver(profileSchema),
		values: {
			firstName: profile?.first_name || '',
			middleName: profile?.middle_name ?? '',
			lastName: profile?.last_name || '',
			suffix: profile?.suffix ?? '',
			phone: profile?.phone || '',
			gender: profile?.gender || '',
		},
	});

	const onSubmit = async (values: EditProfileForm) => {
		await updateBasicInfo.mutateAsync(
			{
				firstName: values.firstName,
				middleName: values.middleName || undefined,
				lastName: values.lastName,
				suffix: values.suffix || undefined,
				phone: values.phone,
				gender: values.gender,
			},
			{
				onSuccess: () => {
					dialog.set(false);
				},
			}
		);
	};

	return (
		<Dialog open={dialog.value} onOpenChange={dialog.set}>
			<DialogTrigger asChild>{children}</DialogTrigger>
			<DialogContent className="max-w-2xl">
				<DialogHeader>
					<DialogTitle>Edit Profile</DialogTitle>
					<DialogDescription>
						Manage your personal information and professional details.
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(onSubmit)}
						className="mt-3 space-y-4"
					>
						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<FormField
								control={form.control}
								name="firstName"
								render={({ field }) => (
									<FormItem>
										<FormLabel>First Name</FormLabel>
										<FormControl>
											<Input placeholder="First Name" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="middleName"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Middle Name</FormLabel>
										<FormControl>
											<Input placeholder="Middle Name" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="lastName"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Last Name</FormLabel>
										<FormControl>
											<Input placeholder="Last Name" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="suffix"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Suffix</FormLabel>
										<FormControl>
											<Input placeholder="Jr., Sr., III" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="phone"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Phone</FormLabel>
										<FormControl>
											<Input placeholder="Phone" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="gender"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Gender *</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
										>
											<FormControl>
												<SelectTrigger className="w-full">
													<SelectValue placeholder="Select gender" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												<SelectItem value="Male">Male</SelectItem>
												<SelectItem value="Female">Female</SelectItem>
												<SelectItem value="Other">Other</SelectItem>
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<div className="flex justify-end gap-2 pt-2">
							<DialogClose asChild>
								<Button variant={'outline'} type="button">
									Cancel
								</Button>
							</DialogClose>
							<Button type="submit" disabled={updateBasicInfo.isPending}>
								{updateBasicInfo.isPending ? 'Saving...' : 'Save Changes'}
							</Button>
						</div>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
