'use client';

import { useHookstate } from '@hookstate/core';
import { useRef, useState } from 'react';

import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { useUpdateDoctorESignature } from '@/features/dashboard/hooks/useDoctorDashboard';

export function UpdateESignatureDialog({
	children,
}: {
	children: React.ReactNode;
}) {
	const fileInputRef = useRef<HTMLInputElement | null>(null);
	const [fileName, setFileName] = useState('');
	const updateESign = useUpdateDoctorESignature();
	const dialog = useHookstate(false);

	const onSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		const file = fileInputRef.current?.files?.[0];
		if (!file) return;
		const fd = new FormData();
		// Field name expected by backend controller; if this differs, please confirm
		fd.append('eSignature', file);
		await updateESign.mutateAsync(fd);
		setFileName('');
		if (fileInputRef.current) fileInputRef.current.value = '';
	};

	return (
		<Dialog open={dialog.value} onOpenChange={dialog.set}>
			<DialogTrigger asChild>{children}</DialogTrigger>
			<DialogContent className="max-w-md">
				<DialogHeader>
					<DialogTitle>Upload E‑Signature</DialogTitle>
					<DialogDescription>
						Upload a transparent PNG of your signature.
					</DialogDescription>
				</DialogHeader>
				<form onSubmit={onSubmit} className="max-w-sm space-y-4">
					<Input
						ref={fileInputRef}
						type="file"
						accept="image/*"
						onChange={(e) => setFileName(e.target.files?.[0]?.name || '')}
					/>
					<div className="flex items-center justify-between">
						<div className="text-muted-foreground max-w-[60%] truncate text-sm">
							{fileName || 'No file selected'}
						</div>
						<Button type="submit" disabled={updateESign.isPending}>
							{updateESign.isPending ? 'Uploading...' : 'Upload'}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
}
