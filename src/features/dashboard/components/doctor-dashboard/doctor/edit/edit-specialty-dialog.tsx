'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useHookstate } from '@hookstate/core';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogClose,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	useDoctorProfile,
	useUpdateDoctorSpecialization,
} from '@/features/dashboard/hooks/useDoctorDashboard';

const speacialtySchema = z.object({
	specialty: z.string().optional(),
	subSpecialty: z.string().optional(),
	subSpecialization: z.string().optional(),
});

export type EditSpecialtyForm = z.infer<typeof speacialtySchema>;

interface IEditSpecialtyDialogProps {
	children: React.ReactNode;
}

export function EditSpecialtyDialog({ children }: IEditSpecialtyDialogProps) {
	const { data: profile } = useDoctorProfile();
	const updateSpeacialty = useUpdateDoctorSpecialization();
	const dialog = useHookstate(false);

	const form = useForm<EditSpecialtyForm>({
		resolver: zodResolver(speacialtySchema),
		values: {
			specialty: profile?.doctor?.specialty || '',
			subSpecialty: profile?.doctor?.sub_specialty || '',
			subSpecialization: profile?.doctor?.sub_specialization || '',
		},
	});

	const onSubmit = async (values: EditSpecialtyForm) => {
		await updateSpeacialty.mutateAsync(
			{
				specialty: values.specialty,
				subSpecialty: values.subSpecialty,
				subSpecialization: values.subSpecialization,
			},
			{
				onSuccess: () => {
					dialog.set(false);
				},
			}
		);
	};

	return (
		<Dialog open={dialog.value} onOpenChange={dialog.set}>
			<DialogTrigger asChild>{children}</DialogTrigger>
			<DialogContent className="max-w-2xl">
				<DialogHeader>
					<DialogTitle>Edit Profile</DialogTitle>
					<DialogDescription>
						Manage your personal information and professional details.
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(onSubmit)}
						className="mt-3 space-y-4"
					>
						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<FormField
								control={form.control}
								name="specialty"
								render={({ field }) => (
									<FormItem className="col-span-2">
										<FormLabel>Medical Specialty</FormLabel>
										<FormControl>
											<Input
												placeholder="e.g., Internal Medicine, Pediatrics, etc."
												{...field}
												className="focus:border-elena-primary focus:ring-elena-primary"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="subSpecialty"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Sub Specialty</FormLabel>
										<FormControl>
											<Input placeholder="Sub Specialty" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="subSpecialization"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Sub Specialization</FormLabel>
										<FormControl>
											<Input placeholder="Sub Specialization" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<div className="flex justify-end gap-2 pt-2">
							<DialogClose asChild>
								<Button variant={'outline'} type="button">
									Cancel
								</Button>
							</DialogClose>
							<Button type="submit" disabled={updateSpeacialty.isPending}>
								{updateSpeacialty.isPending ? 'Saving...' : 'Save Changes'}
							</Button>
						</div>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
