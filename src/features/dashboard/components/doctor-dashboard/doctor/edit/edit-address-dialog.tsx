'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useHookstate } from '@hookstate/core';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogClose,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import { Form } from '@/components/ui/form';
import { isValidApiAddress } from '@/core/validators/address.validator';
import { useUpdateDoctorAddress } from '@/features/dashboard/hooks/useDoctorDashboard';
import { IDoctorDetails } from '@/features/dashboard/types/doctor-details.type';

import AddressForm from '../../onboarding/forms/address.form';
import {
	addressFormSchema,
	TAddressFormData,
} from '../../onboarding/types/onboarding.type';

interface IEditAddressDialogProps {
	children: React.ReactNode;
	doctor: IDoctorDetails;
}

export function EditAddressDialog({
	children,
	doctor,
}: IEditAddressDialogProps) {
	const updateAddressMutation = useUpdateDoctorAddress();
	const isOpen = useHookstate(false);

	const form = useForm<TAddressFormData>({
		resolver: zodResolver(addressFormSchema),
		defaultValues: {
			currentAddress: {
				country: 'Philippines',
				region: '',
				province: '',
				city: '',
				barangay: '',
				streetName: '',
				lotBlockPhaseStreet: '',
				unitRoomFloorBuilding: '',
				subdivisionVillageZone: '',
				buildingApartment: '',
			},
			permanentAddress: {
				country: 'Philippines',
				region: '',
				province: '',
				city: '',
				barangay: '',
				streetName: '',
				lotBlockPhaseStreet: '',
				unitRoomFloorBuilding: '',
				subdivisionVillageZone: '',
				buildingApartment: '',
			},
		},
	});

	// Prepare existing data for the address form
	const hasExistingData = doctor
		? isValidApiAddress({
				profileCurrentAddress: doctor.profileCurrentAddress,
				profilePermanentAddress: doctor.profilePermanentAddress,
			})
		: { isValid: false, address: null };

	const onSubmit = (data: TAddressFormData) => {
		updateAddressMutation.mutate(data, {
			onSuccess: () => {
				isOpen.set(false);
			},
		});
	};

	return (
		<Dialog open={isOpen.get()} onOpenChange={isOpen.set}>
			<DialogTrigger asChild>{children}</DialogTrigger>
			<DialogContent className="max-w-4xl">
				<DialogHeader>
					<DialogTitle>Edit Address Information</DialogTitle>
					<DialogDescription>
						Update your current and permanent address information.
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
						<AddressForm form={form} hasExistingData={hasExistingData} />

						<div className="flex justify-end gap-2">
							<DialogClose asChild>
								<Button type="button" variant="outline">
									Cancel
								</Button>
							</DialogClose>
							<Button type="submit" disabled={updateAddressMutation.isPending}>
								{updateAddressMutation.isPending ? 'Saving...' : 'Save Changes'}
							</Button>
						</div>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
