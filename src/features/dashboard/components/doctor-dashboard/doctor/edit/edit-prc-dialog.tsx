'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useHookstate } from '@hookstate/core';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogClose,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	useDoctorProfile,
	useUpdateDoctorBasicInfo,
} from '@/features/dashboard/hooks/useDoctorDashboard';

const profileSchema = z.object({
	prcNumber: z.string().optional(),
});

export type EditProfileForm = z.infer<typeof profileSchema>;

interface IEditProfileDialogProps {
	children: React.ReactNode;
}

export function EditPRCDialog({ children }: IEditProfileDialogProps) {
	const { data: profile } = useDoctorProfile();
	const updateBasicInfo = useUpdateDoctorBasicInfo();
	const dialog = useHookstate(false);

	const form = useForm<EditProfileForm>({
		resolver: zodResolver(profileSchema),
		values: {
			prcNumber: profile?.doctor?.prc_number || '',
		},
	});

	const onSubmit = async (values: EditProfileForm) => {
		// Basic Info
		await updateBasicInfo.mutateAsync(
			{
				prcNumber: values.prcNumber,
			},
			{
				onSuccess: () => {
					dialog.set(false);
				},
			}
		);
	};

	return (
		<Dialog open={dialog.value} onOpenChange={dialog.set}>
			<DialogTrigger asChild>{children}</DialogTrigger>
			<DialogContent className="max-w-2xl">
				<DialogHeader>
					<DialogTitle>Edit PRC Number</DialogTitle>
					<DialogDescription>Manage your PRC number.</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(onSubmit)}
						className="mt-3 space-y-4"
					>
						<div className="">
							<FormField
								control={form.control}
								name="prcNumber"
								render={({ field }) => (
									<FormItem>
										<FormLabel>PRC Number</FormLabel>
										<FormControl>
											<Input placeholder="PRC Number" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<div className="flex justify-end gap-2 pt-2">
							<DialogClose asChild>
								<Button variant={'outline'} type="button">
									Cancel
								</Button>
							</DialogClose>
							<Button type="submit" disabled={updateBasicInfo.isPending}>
								{updateBasicInfo.isPending ? 'Saving...' : 'Save Changes'}
							</Button>
						</div>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
