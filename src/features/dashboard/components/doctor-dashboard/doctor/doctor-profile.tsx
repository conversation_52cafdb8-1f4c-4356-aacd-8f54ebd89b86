'use client';

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import XScroll from '@/components/ui/x-scroll';
import { useDoctorProfile } from '@/features/dashboard/hooks/useDoctorDashboard';

import AccountInfoTab from './doctor-details-tab/account-info-tab';
import AddressTab from './doctor-details-tab/address-tab';
import ClinicsTab from './doctor-details-tab/clinics-tab';
import DocumentsTab from './doctor-details-tab/documents-tab';
import PersonalInfoTab from './doctor-details-tab/personal-info-tab';
import ProfessionalInfoTab from './doctor-details-tab/professional-info-tab';
import ProfileStatusOverview from './doctor-details-tab/profile-status-overview';
import SubscriptionTab from './doctor-details-tab/subscription-tab';

export default function DoctorProfilePage() {
	const { data: doctor, isLoading } = useDoctorProfile();

	if (isLoading) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
				<div className="animate-pulse space-y-4">
					<div className="h-8 w-1/4 rounded bg-gray-200"></div>
					<div className="h-4 w-1/2 rounded bg-gray-200"></div>
					<div className="grid grid-cols-4 gap-4">
						{[...Array(4)].map((_, i) => (
							<div key={i} className="h-24 rounded bg-gray-200"></div>
						))}
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="flex flex-1 flex-col gap-6 p-4 pt-0">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
						Profile
					</h1>
					<p className="text-muted-foreground">Manage your account profile</p>
				</div>
			</div>

			{/* Doctor Details Content */}
			{!isLoading && doctor && (
				<div className="">
					{/* Profile Status Overview */}
					<ProfileStatusOverview doctor={doctor} />

					{/* Tabbed Content */}
					<Tabs defaultValue="personal" className="w-full">
						<XScroll>
							<TabsList className="space-x-2">
								<TabsTrigger value="personal">Personal Info</TabsTrigger>
								<TabsTrigger value="professional">
									Professional Info
								</TabsTrigger>
								<TabsTrigger value="clinics">Clinics</TabsTrigger>
								<TabsTrigger value="address">Address</TabsTrigger>
								<TabsTrigger value="documents">Documents</TabsTrigger>
								<TabsTrigger value="account">Account Info</TabsTrigger>
								<TabsTrigger value="subscription">Subscription</TabsTrigger>
							</TabsList>
						</XScroll>

						<TabsContent value="personal" className="mt-6">
							<PersonalInfoTab doctor={doctor} hasEditMode={true} />
						</TabsContent>

						<TabsContent value="professional" className="mt-6">
							<ProfessionalInfoTab doctor={doctor} hasEditMode={true} />
						</TabsContent>

						<TabsContent value="clinics" className="mt-6">
							<ClinicsTab doctor={doctor} />
						</TabsContent>

						<TabsContent value="address" className="mt-6">
							<AddressTab doctor={doctor} hasEditMode={true} />
						</TabsContent>

						<TabsContent value="documents" className="mt-6">
							<DocumentsTab doctor={doctor} hasEditMode={true} />
						</TabsContent>

						<TabsContent value="account" className="mt-6">
							<AccountInfoTab doctor={doctor} />
						</TabsContent>

						<TabsContent value="subscription" className="mt-6">
							<SubscriptionTab doctor={doctor} />
						</TabsContent>
					</Tabs>
				</div>
			)}
		</div>
	);
}
