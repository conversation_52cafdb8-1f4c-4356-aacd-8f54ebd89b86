import { format } from 'date-fns';
import { Calendar, Mail, User, UserCheck } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { IDoctorDetails } from '../../../../types/doctor-details.type';

interface IAccountInfoTabProps {
	doctor: IDoctorDetails;
}

export default function AccountInfoTab({ doctor }: IAccountInfoTabProps) {
	if (!doctor.user) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2 text-lg">
						<Mail className="h-5 w-5" />
						Account Information
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="py-8 text-center">
						<User className="mx-auto mb-4 h-12 w-12 text-gray-400" />
						<p className="text-gray-500">No account information available</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2 text-lg">
					<Mail className="h-5 w-5" />
					Account Information
				</CardTitle>
			</CardHeader>
			<CardContent className="space-y-4">
				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					<div className="flex items-center gap-3">
						<User className="text-muted-foreground h-4 w-4" />
						<div>
							<p className="text-sm font-medium">Username</p>
							<p className="text-sm text-gray-600">{doctor.user.username}</p>
						</div>
					</div>
					<div className="flex items-center gap-3">
						<Calendar className="text-muted-foreground h-4 w-4" />
						<div>
							<p className="text-sm font-medium">Last Login</p>
							<p className="text-sm text-gray-600">
								{doctor.user.last_login
									? format(new Date(doctor.user.last_login), 'MMMM dd, yyyy')
									: 'Never'}
							</p>
						</div>
					</div>
					<div className="flex items-center gap-3">
						<UserCheck className="text-muted-foreground h-4 w-4" />
						<div>
							<p className="text-sm font-medium">Account Status</p>
							<Badge variant={doctor.user.is_active ? 'default' : 'secondary'}>
								{doctor.user.is_active ? 'Active' : 'Inactive'}
							</Badge>
						</div>
					</div>
					<div className="flex items-center gap-3">
						<Calendar className="text-muted-foreground h-4 w-4" />
						<div>
							<p className="text-sm font-medium">Account Created</p>
							<p className="text-sm text-gray-600">
								{format(new Date(doctor.user.created_at), 'MMMM dd, yyyy')}
							</p>
						</div>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
