import { UserCheck } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { IDoctorDetails } from '../../../../types/doctor-details.type';

interface IProfileStatusOverviewProps {
	doctor: IDoctorDetails;
}

export default function ProfileStatusOverview({
	doctor,
}: IProfileStatusOverviewProps) {
	return (
		<Card className="mb-6">
			<CardHeader>
				<CardTitle className="flex items-center gap-2 text-lg">
					<UserCheck className="h-5 w-5" />
					Profile Status Overview
				</CardTitle>
			</CardHeader>
			<CardContent className="space-y-4">
				<div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
					<div className="flex items-center gap-3">
						<UserCheck className="text-muted-foreground h-4 w-4" />
						<div>
							<p className="text-sm font-medium">Profile Completion</p>
							<Badge variant={doctor.is_completed ? 'default' : 'secondary'}>
								{doctor.is_completed ? 'Complete' : 'Incomplete'}
							</Badge>
						</div>
					</div>
					<div className="flex items-center gap-3">
						<UserCheck className="text-muted-foreground h-4 w-4" />
						<div>
							<p className="text-sm font-medium">Verification Status</p>
							<Badge variant={doctor.is_verified ? 'default' : 'secondary'}>
								{doctor.is_verified ? 'Verified' : 'Unverified'}
							</Badge>
						</div>
					</div>
					<div className="flex items-center gap-3">
						<UserCheck className="text-muted-foreground h-4 w-4" />
						<div>
							<p className="text-sm font-medium">Application Status</p>
							<Badge
								variant={
									doctor.is_accepted
										? 'default'
										: doctor.is_rejected
											? 'destructive'
											: 'secondary'
								}
							>
								{doctor.is_accepted
									? 'Accepted'
									: doctor.is_rejected
										? 'Rejected'
										: 'Pending'}
							</Badge>
						</div>
					</div>
					<div className="flex items-center gap-3">
						<UserCheck className="text-muted-foreground h-4 w-4" />
						<div>
							<p className="text-sm font-medium">Account Status</p>
							<Badge variant={doctor.is_active ? 'default' : 'secondary'}>
								{doctor.is_active ? 'Active' : 'Inactive'}
							</Badge>
						</div>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
