import { format } from 'date-fns';
import { Calendar, CreditCard } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { IDoctorDetails } from '../../../../types/doctor-details.type';

interface ISubscriptionTabProps {
	doctor: IDoctorDetails;
}

export default function SubscriptionTab({ doctor }: ISubscriptionTabProps) {
	if (!doctor.doctor) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2 text-lg">
						<CreditCard className="h-5 w-5" />
						Subscription Information
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="py-8 text-center">
						<CreditCard className="mx-auto mb-4 h-12 w-12 text-gray-400" />
						<p className="text-gray-500">
							No subscription information available
						</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2 text-lg">
					<CreditCard className="h-5 w-5" />
					Subscription Information
				</CardTitle>
			</CardHeader>
			<CardContent className="space-y-4">
				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					<div className="flex items-center gap-3">
						<CreditCard className="text-muted-foreground h-4 w-4" />
						<div>
							<p className="text-sm font-medium">Subscription Status</p>
							<Badge
								variant={doctor.doctor.is_subscribed ? 'default' : 'secondary'}
							>
								{doctor.doctor.is_subscribed ? 'Subscribed' : 'Not Subscribed'}
							</Badge>
						</div>
					</div>
					<div className="flex items-center gap-3">
						<Calendar className="text-muted-foreground h-4 w-4" />
						<div>
							<p className="text-sm font-medium">Free Trial Status</p>
							<Badge
								variant={
									doctor.doctor.is_free_trial_done ? 'secondary' : 'default'
								}
							>
								{doctor.doctor.is_free_trial_done
									? 'Trial Completed'
									: 'Trial Available'}
							</Badge>
						</div>
					</div>
					{doctor.doctor.subscription_start && (
						<div className="flex items-center gap-3">
							<Calendar className="text-muted-foreground h-4 w-4" />
							<div>
								<p className="text-sm font-medium">Subscription Start</p>
								<p className="text-sm text-gray-600">
									{format(
										new Date(doctor.doctor.subscription_start),
										'MMMM dd, yyyy'
									)}
								</p>
							</div>
						</div>
					)}
					{doctor.doctor.subscription_end && (
						<div className="flex items-center gap-3">
							<Calendar className="text-muted-foreground h-4 w-4" />
							<div>
								<p className="text-sm font-medium">Subscription End</p>
								<p className="text-sm text-gray-600">
									{format(
										new Date(doctor.doctor.subscription_end),
										'MMMM dd, yyyy'
									)}
								</p>
							</div>
						</div>
					)}
				</div>
			</CardContent>
		</Card>
	);
}
