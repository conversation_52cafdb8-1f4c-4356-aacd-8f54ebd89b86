import { format } from 'date-fns';
import {
	Calendar,
	FileText,
	Globe,
	Mail,
	Phone,
	Stethoscope,
	User,
	UserCheck,
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { IDoctorDetails } from '../../../../types/doctor-details.type';
import { EditProfileDialog } from '../edit/edit-profile-dialog';

interface IPersonalInfoTabProps {
	doctor: IDoctorDetails;
	hasEditMode?: boolean;
}

export default function PersonalInfoTab({
	doctor,
	hasEditMode = false,
}: IPersonalInfoTabProps) {
	return (
		<Card>
			<CardHeader className="flex items-start justify-between">
				<CardTitle className="flex items-center gap-2 text-lg">
					<User className="h-5 w-5" />
					Personal Information
				</CardTitle>

				{hasEditMode && (
					<EditProfileDialog>
						<Button variant={'link'}>Edit</Button>
					</EditProfileDialog>
				)}
			</CardHeader>
			<CardContent className="space-y-4">
				<div className="flex items-center gap-3">
					<User className="text-muted-foreground h-4 w-4" />
					<div>
						<p className="text-sm font-medium">Full Name</p>
						<p className="text-sm text-gray-600">
							{doctor.first_name} {doctor.middle_name} {doctor.last_name}{' '}
							{doctor.suffix}
						</p>
					</div>
				</div>
				<div className="flex items-center gap-3">
					<Mail className="text-muted-foreground h-4 w-4" />
					<div>
						<p className="text-sm font-medium">Email</p>
						<p className="text-sm text-gray-600">
							{doctor.user?.email || 'N/A'}
						</p>
					</div>
				</div>
				<div className="flex items-center gap-3">
					<Phone className="text-muted-foreground h-4 w-4" />
					<div>
						<p className="text-sm font-medium">Phone</p>
						<p className="text-sm text-gray-600">{doctor.phone || 'N/A'}</p>
					</div>
				</div>
				<div className="flex items-center gap-3">
					<Calendar className="text-muted-foreground h-4 w-4" />
					<div>
						<p className="text-sm font-medium">Birthday</p>
						<p className="text-sm text-gray-600">
							{doctor.birthday
								? format(new Date(doctor.birthday), 'MMMM dd, yyyy')
								: 'N/A'}
						</p>
					</div>
				</div>
				<div className="flex items-center gap-3">
					<UserCheck className="text-muted-foreground h-4 w-4" />
					<div>
						<p className="text-sm font-medium">Gender</p>
						<p className="text-sm text-gray-600">{doctor.gender || 'N/A'}</p>
					</div>
				</div>

				{/* Additional Personal Information */}
				{(doctor.bio ||
					doctor.occupation ||
					doctor.civil_status ||
					doctor.blood_type ||
					doctor.height ||
					doctor.weight ||
					doctor.religion ||
					doctor.ethnicity ||
					doctor.nationality) && (
					<div className="border-t pt-4">
						<h4 className="mb-3 text-sm font-semibold text-gray-700">
							Additional Information
						</h4>
						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							{doctor.bio && (
								<div className="md:col-span-2">
									<div className="flex items-start gap-3">
										<FileText className="text-muted-foreground mt-1 h-4 w-4" />
										<div>
											<p className="text-sm font-medium">Bio</p>
											<p className="text-sm text-gray-600">{doctor.bio}</p>
										</div>
									</div>
								</div>
							)}
							{doctor.occupation && (
								<div className="flex items-center gap-3">
									<User className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Occupation</p>
										<p className="text-sm text-gray-600">{doctor.occupation}</p>
									</div>
								</div>
							)}
							{doctor.civil_status && (
								<div className="flex items-center gap-3">
									<UserCheck className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Civil Status</p>
										<p className="text-sm text-gray-600">
											{doctor.civil_status}
										</p>
									</div>
								</div>
							)}
							{doctor.blood_type && (
								<div className="flex items-center gap-3">
									<Stethoscope className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Blood Type</p>
										<p className="text-sm text-gray-600">{doctor.blood_type}</p>
									</div>
								</div>
							)}
							{(doctor.height || doctor.weight) && (
								<div className="flex items-center gap-3">
									<User className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Physical Details</p>
										<p className="text-sm text-gray-600">
											{doctor.height && (
												<span>
													Height: {doctor.height} {doctor.height_type || ''}
												</span>
											)}
											{doctor.height && doctor.weight && <span> | </span>}
											{doctor.weight && (
												<span>
													Weight: {doctor.weight} {doctor.weight_type || ''}
												</span>
											)}
										</p>
									</div>
								</div>
							)}
							{doctor.religion && (
								<div className="flex items-center gap-3">
									<User className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Religion</p>
										<p className="text-sm text-gray-600">{doctor.religion}</p>
									</div>
								</div>
							)}
							{doctor.ethnicity && (
								<div className="flex items-center gap-3">
									<User className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Ethnicity</p>
										<p className="text-sm text-gray-600">{doctor.ethnicity}</p>
									</div>
								</div>
							)}
							{doctor.nationality && (
								<div className="flex items-center gap-3">
									<Globe className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Nationality</p>
										<p className="text-sm text-gray-600">
											{doctor.nationality}
										</p>
									</div>
								</div>
							)}
						</div>
					</div>
				)}
			</CardContent>
		</Card>
	);
}
