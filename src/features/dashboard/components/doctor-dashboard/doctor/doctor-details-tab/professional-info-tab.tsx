import { format } from 'date-fns';
import { Calendar, FileText, Stethoscope, User<PERSON>heck } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { IDoctorDetails } from '../../../../types/doctor-details.type';
import { EditPRCDialog } from '../edit/edit-prc-dialog';
import { EditSpecialtyDialog } from '../edit/edit-specialty-dialog';

interface IProfessionalInfoTabProps {
	doctor: IDoctorDetails;
	hasEditMode?: boolean;
}

export default function ProfessionalInfoTab({
	doctor,
	hasEditMode = false,
}: IProfessionalInfoTabProps) {
	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2 text-lg">
					<Stethoscope className="h-5 w-5" />
					Professional Information
				</CardTitle>
			</CardHeader>
			<CardContent className="space-y-4">
				<div className="flex items-center gap-3">
					<Stethoscope className="text-muted-foreground h-4 w-4" />
					<div>
						<div className="text-sm font-medium">
							<span>Specialty</span>
							{hasEditMode && (
								<EditSpecialtyDialog>
									<Button className="h-8" variant={'link'}>
										Edit
									</Button>
								</EditSpecialtyDialog>
							)}
						</div>
						<p className="text-sm text-gray-600">
							{doctor.doctor?.specialty || 'N/A'}
						</p>
					</div>
				</div>
				{doctor.doctor?.sub_specialty && (
					<div className="flex items-center gap-3">
						<Stethoscope className="text-muted-foreground h-4 w-4" />
						<div>
							<p className="text-sm font-medium">Sub-Specialty</p>
							<p className="text-sm text-gray-600">
								{doctor.doctor.sub_specialty}
							</p>
						</div>
					</div>
				)}
				{doctor.doctor?.sub_specialization && (
					<div className="flex items-center gap-3">
						<Stethoscope className="text-muted-foreground h-4 w-4" />
						<div>
							<p className="text-sm font-medium">Sub-Specialization</p>
							<p className="text-sm text-gray-600">
								{doctor.doctor.sub_specialization}
							</p>
						</div>
					</div>
				)}
				<div className="flex items-center gap-3">
					<FileText className="text-muted-foreground h-4 w-4" />
					<div>
						<div className="text-sm font-medium">
							<span>PRC Number</span>
							{hasEditMode && (
								<EditPRCDialog>
									<Button className="h-8" variant={'link'}>
										Edit
									</Button>
								</EditPRCDialog>
							)}
						</div>
						<p className="text-sm text-gray-600">
							{doctor.doctor?.prc_number || 'N/A'}
						</p>
					</div>
				</div>
				{doctor.doctor?.prc_expiry_date && (
					<div className="flex items-center gap-3">
						<Calendar className="text-muted-foreground h-4 w-4" />
						<div>
							<p className="text-sm font-medium">PRC Expiry Date</p>
							<p className="text-sm text-gray-600">
								{format(
									new Date(doctor.doctor.prc_expiry_date),
									'MMMM dd, yyyy'
								)}
							</p>
						</div>
					</div>
				)}
				{doctor.doctor?.awards && (
					<div className="flex items-center gap-3">
						<UserCheck className="text-muted-foreground h-4 w-4" />
						<div>
							<p className="text-sm font-medium">Awards</p>
							<p className="text-sm text-gray-600">{doctor.doctor.awards}</p>
						</div>
					</div>
				)}
				<div className="flex items-center gap-3">
					<Calendar className="text-muted-foreground h-4 w-4" />
					<div>
						<p className="text-sm font-medium">Registration Date</p>
						<p className="text-sm text-gray-600">
							{format(new Date(doctor.created_at), 'MMMM dd, yyyy')}
						</p>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
