import { FileText, SignatureIcon } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { IDoctorDetails } from '../../../../types/doctor-details.type';
import { UpdateESignatureDialog } from '../edit/update-e-signature-dialog';

interface IDocumentsTabProps {
	doctor: IDoctorDetails;
	hasEditMode?: boolean;
}

export default function DocumentsTab({
	doctor,
	hasEditMode = false,
}: IDocumentsTabProps) {
	const hasPrcDocuments =
		doctor.doctor?.prc_image_front || doctor.doctor?.prc_image_back;

	if (!hasPrcDocuments) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2 text-lg">
						<FileText className="h-5 w-5" />
						PRC License Documents
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="py-8 text-center">
						<FileText className="mx-auto mb-4 h-12 w-12 text-gray-400" />
						<p className="text-gray-500">No PRC license documents available</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<div className="grid items-start gap-4 md:grid-cols-2">
			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<CardTitle className="flex items-center gap-2 text-lg">
							<SignatureIcon className="h-5 w-5" />
							E-Signature
						</CardTitle>

						{hasEditMode && (
							<UpdateESignatureDialog>
								<Button variant={'link'}>Update</Button>
							</UpdateESignatureDialog>
						)}
					</div>
				</CardHeader>
				<CardContent className="">
					<div className="">
						{doctor.doctor?.e_signature ? (
							<div>
								<div className="overflow-hidden rounded-lg border">
									<img
										src={`${doctor.doctor.e_signature}`}
										alt="PRC License Front"
										className="w-full object-cover"
										onError={(e) => {
											const target = e.target as HTMLImageElement;
											target.style.display = 'none';
											target.nextElementSibling?.classList.remove('hidden');
										}}
									/>
								</div>
							</div>
						) : (
							<div className="grid h-[200px] place-items-center p-4 text-center text-gray-500">
								<div>
									<FileText className="mx-auto mb-2 h-8 w-8" />
									<p className="text-sm">E-Signature not available</p>
								</div>
							</div>
						)}
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2 text-lg">
						<FileText className="h-5 w-5" />
						PRC License Documents
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="grid grid-cols-1 gap-4">
						{doctor.doctor?.prc_image_front ? (
							<div>
								<p className="mb-2 text-sm font-medium">PRC License (Front)</p>
								<div className="overflow-hidden rounded-lg border">
									<img
										src={`${doctor.doctor.prc_image_front}`}
										alt="PRC License Front"
										className="w-full object-cover"
										onError={(e) => {
											const target = e.target as HTMLImageElement;
											target.style.display = 'none';
											target.nextElementSibling?.classList.remove('hidden');
										}}
									/>
								</div>
							</div>
						) : (
							<div className="grid h-[200px] place-items-center p-4 text-center text-gray-500">
								<div>
									<FileText className="mx-auto mb-2 h-8 w-8" />
									<p className="text-sm">Image not available</p>
								</div>
							</div>
						)}

						{doctor.doctor?.prc_image_back ? (
							<div>
								<p className="mb-2 text-sm font-medium">PRC License (Back)</p>
								<div className="overflow-hidden rounded-lg border">
									<img
										src={`${doctor.doctor.prc_image_back}`}
										alt="PRC License Back"
										className="w-full object-cover"
										onError={(e) => {
											const target = e.target as HTMLImageElement;
											target.style.display = 'none';
											target.nextElementSibling?.classList.remove('hidden');
										}}
									/>
								</div>
							</div>
						) : (
							<div className="grid h-[200px] place-items-center p-4 text-center text-gray-500">
								<div>
									<FileText className="mx-auto mb-2 h-8 w-8" />
									<p className="text-sm">Image not available</p>
								</div>
							</div>
						)}
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
