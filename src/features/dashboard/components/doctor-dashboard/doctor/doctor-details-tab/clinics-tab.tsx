import { format } from 'date-fns';
import {
	Building2,
	Calendar,
	Clock,
	Globe,
	Mail,
	MapPin,
	Phone,
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { IDoctorDetails } from '../../../../types/doctor-details.type';

interface IClinicsTabProps {
	doctor: IDoctorDetails;
}

export default function ClinicsTab({ doctor }: IClinicsTabProps) {
	if (!doctor.clinics || doctor.clinics.length === 0) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2 text-lg">
						<Building2 className="h-5 w-5" />
						Associated Clinics
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="py-8 text-center">
						<Building2 className="mx-auto mb-4 h-12 w-12 text-gray-400" />
						<p className="text-gray-500">
							No clinics associated with this doctor
						</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2 text-lg">
					<Building2 className="h-5 w-5" />
					Associated Clinics ({doctor.clinics.length})
				</CardTitle>
			</CardHeader>
			<CardContent className="space-y-4">
				{doctor.clinics.map((clinic, index: number) => (
					<div
						key={clinic.id}
						className={`space-y-3 ${index > 0 ? 'border-t pt-4' : ''}`}
					>
						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<div className="flex items-center gap-3">
								<Building2 className="text-muted-foreground h-4 w-4" />
								<div>
									<p className="text-sm font-medium">Clinic Name</p>
									<p className="text-sm text-gray-600">
										{clinic.name || 'N/A'}
									</p>
								</div>
							</div>
							<div className="flex items-center gap-3">
								<MapPin className="text-muted-foreground h-4 w-4" />
								<div>
									<p className="text-sm font-medium">Address</p>
									<p className="text-sm text-gray-600">
										{clinic.address || 'N/A'}
									</p>
								</div>
							</div>
							{clinic.phone && (
								<div className="flex items-center gap-3">
									<Phone className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Phone</p>
										<p className="text-sm text-gray-600">{clinic.phone}</p>
									</div>
								</div>
							)}
							{clinic.email && (
								<div className="flex items-center gap-3">
									<Mail className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Email</p>
										<p className="text-sm text-gray-600">{clinic.email}</p>
									</div>
								</div>
							)}
							{clinic.website && (
								<div className="flex items-center gap-3">
									<Globe className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Website</p>
										<p className="text-sm text-gray-600">{clinic.website}</p>
									</div>
								</div>
							)}
							{(clinic.start_time || clinic.end_time) && (
								<div className="flex items-center gap-3">
									<Clock className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Operating Hours</p>
										<p className="text-sm text-gray-600">
											{clinic.start_time || 'N/A'} - {clinic.end_time || 'N/A'}
										</p>
									</div>
								</div>
							)}
						</div>
						{clinic.date_established && (
							<div className="flex items-center gap-3">
								<Calendar className="text-muted-foreground h-4 w-4" />
								<div>
									<p className="text-sm font-medium">Date Established</p>
									<p className="text-sm text-gray-600">
										{format(new Date(clinic.date_established), 'MMMM dd, yyyy')}
									</p>
								</div>
							</div>
						)}
					</div>
				))}
			</CardContent>
		</Card>
	);
}
