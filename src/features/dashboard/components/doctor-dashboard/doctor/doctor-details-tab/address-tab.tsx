import { MapPin } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { isValidApiAddress } from '@/core/validators/address.validator';

import { IDoctorDetails } from '../../../../types/doctor-details.type';
import { EditAddressDialog } from '../edit/edit-address-dialog';

interface IAddressTabProps {
	doctor: IDoctorDetails;
	hasEditMode?: boolean;
}

export default function AddressTab({
	doctor,
	hasEditMode = false,
}: IAddressTabProps) {
	const hasValidAddress = isValidApiAddress({
		profileCurrentAddress: doctor.profileCurrentAddress,
		profilePermanentAddress: doctor.profilePermanentAddress,
	});

	if (!hasValidAddress.isValid) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2 text-lg">
						<MapPin className="h-5 w-5" />
						Address Information
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="py-8 text-center">
						<MapPin className="mx-auto mb-4 h-12 w-12 text-gray-400" />
						<p className="text-gray-500">No address information available</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader className="flex items-start justify-between">
				<CardTitle className="flex items-center gap-2 text-lg">
					<MapPin className="h-5 w-5" />
					Address Information
				</CardTitle>

				{hasEditMode && (
					<EditAddressDialog doctor={doctor}>
						<Button variant={'link'}>Edit</Button>
					</EditAddressDialog>
				)}
			</CardHeader>
			<CardContent className="space-y-6">
				{/* Current Address */}
				{hasValidAddress.isValid && (
					<div>
						<h4 className="mb-3 text-sm font-semibold text-gray-700">
							Current Address
						</h4>
						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							{hasValidAddress.address?.profileCurrentAddress.country && (
								<div className="flex items-center gap-3">
									<MapPin className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Country</p>
										<p className="text-sm text-gray-600">
											{hasValidAddress.address?.profileCurrentAddress.country}
										</p>
									</div>
								</div>
							)}
							{hasValidAddress.address?.profileCurrentAddress.province
								.province_name && (
								<div className="flex items-center gap-3">
									<MapPin className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Province</p>
										<p className="text-sm text-gray-600">
											{
												hasValidAddress.address?.profileCurrentAddress.province
													.province_name
											}
										</p>
									</div>
								</div>
							)}
							{hasValidAddress.address?.profileCurrentAddress.city
								.city_name && (
								<div className="flex items-center gap-3">
									<MapPin className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">City</p>
										<p className="text-sm text-gray-600">
											{
												hasValidAddress.address?.profileCurrentAddress.city
													.city_name
											}
										</p>
									</div>
								</div>
							)}
							{hasValidAddress.address?.profileCurrentAddress.barangay
								.brgy_name && (
								<div className="flex items-center gap-3">
									<MapPin className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Barangay</p>
										<p className="text-sm text-gray-600">
											{
												hasValidAddress.address?.profileCurrentAddress.barangay
													.brgy_name
											}
										</p>
									</div>
								</div>
							)}
							{hasValidAddress.address?.profileCurrentAddress.street_name && (
								<div className="flex items-center gap-3">
									<MapPin className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Street</p>
										<p className="text-sm text-gray-600">
											{
												hasValidAddress.address?.profileCurrentAddress
													.street_name
											}
										</p>
									</div>
								</div>
							)}
							{hasValidAddress.address?.profileCurrentAddress
								.lot_block_phase_street && (
								<div className="flex items-center gap-3">
									<MapPin className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">
											Lot/Block/Phase/Street No.
										</p>
										<p className="text-sm text-gray-600">
											{
												hasValidAddress.address?.profileCurrentAddress
													.lot_block_phase_street
											}
										</p>
									</div>
								</div>
							)}
							{hasValidAddress.address?.profileCurrentAddress
								.unit_room_floor_building && (
								<div className="flex items-center gap-3">
									<MapPin className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">
											Unit/Room/Floor/Building No.
										</p>
										<p className="text-sm text-gray-600">
											{
												hasValidAddress.address?.profileCurrentAddress
													.unit_room_floor_building
											}
										</p>
									</div>
								</div>
							)}
							{hasValidAddress.address?.profileCurrentAddress
								.subdivision_village_zone && (
								<div className="flex items-center gap-3">
									<MapPin className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">
											Subdivision/Village/Zone
										</p>
										<p className="text-sm text-gray-600">
											{
												hasValidAddress.address?.profileCurrentAddress
													.subdivision_village_zone
											}
										</p>
									</div>
								</div>
							)}
							{hasValidAddress.address?.profileCurrentAddress
								.building_apartment && (
								<div className="flex items-center gap-3">
									<MapPin className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Building/Apartment</p>
										<p className="text-sm text-gray-600">
											{
												hasValidAddress.address?.profileCurrentAddress
													.building_apartment
											}
										</p>
									</div>
								</div>
							)}
						</div>
					</div>
				)}

				{/* Permanent Address */}
				{hasValidAddress.isValid && (
					<div>
						<h4 className="mb-3 text-sm font-semibold text-gray-700">
							Permanent Address
						</h4>
						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							{hasValidAddress.address?.profilePermanentAddress.country && (
								<div className="flex items-center gap-3">
									<MapPin className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Country</p>
										<p className="text-sm text-gray-600">
											{hasValidAddress.address?.profilePermanentAddress.country}
										</p>
									</div>
								</div>
							)}
							{hasValidAddress.address?.profilePermanentAddress.province
								.province_name && (
								<div className="flex items-center gap-3">
									<MapPin className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Province</p>
										<p className="text-sm text-gray-600">
											{
												hasValidAddress.address?.profilePermanentAddress
													.province.province_name
											}
										</p>
									</div>
								</div>
							)}
							{hasValidAddress.address?.profilePermanentAddress.city
								.city_name && (
								<div className="flex items-center gap-3">
									<MapPin className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">City</p>
										<p className="text-sm text-gray-600">
											{
												hasValidAddress.address?.profilePermanentAddress.city
													.city_name
											}
										</p>
									</div>
								</div>
							)}
							{hasValidAddress.address?.profilePermanentAddress.barangay
								.brgy_name && (
								<div className="flex items-center gap-3">
									<MapPin className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Barangay</p>
										<p className="text-sm text-gray-600">
											{
												hasValidAddress.address?.profilePermanentAddress
													.barangay.brgy_name
											}
										</p>
									</div>
								</div>
							)}
							{hasValidAddress.address?.profilePermanentAddress.street_name && (
								<div className="flex items-center gap-3">
									<MapPin className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Street</p>
										<p className="text-sm text-gray-600">
											{
												hasValidAddress.address?.profilePermanentAddress
													.street_name
											}
										</p>
									</div>
								</div>
							)}
							{hasValidAddress.address?.profilePermanentAddress
								.lot_block_phase_street && (
								<div className="flex items-center gap-3">
									<MapPin className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">
											Lot/Block/Phase/Street No.
										</p>
										<p className="text-sm text-gray-600">
											{
												hasValidAddress.address?.profilePermanentAddress
													.lot_block_phase_street
											}
										</p>
									</div>
								</div>
							)}
							{hasValidAddress.address?.profilePermanentAddress
								.unit_room_floor_building && (
								<div className="flex items-center gap-3">
									<MapPin className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">
											Unit/Room/Floor/Building No.
										</p>
										<p className="text-sm text-gray-600">
											{
												hasValidAddress.address?.profilePermanentAddress
													.unit_room_floor_building
											}
										</p>
									</div>
								</div>
							)}
							{hasValidAddress.address?.profilePermanentAddress
								.subdivision_village_zone && (
								<div className="flex items-center gap-3">
									<MapPin className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">
											Subdivision/Village/Zone
										</p>
										<p className="text-sm text-gray-600">
											{
												hasValidAddress.address?.profilePermanentAddress
													.subdivision_village_zone
											}
										</p>
									</div>
								</div>
							)}
							{hasValidAddress.address?.profilePermanentAddress
								.building_apartment && (
								<div className="flex items-center gap-3">
									<MapPin className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Building/Apartment</p>
										<p className="text-sm text-gray-600">
											{
												hasValidAddress.address?.profilePermanentAddress
													.building_apartment
											}
										</p>
									</div>
								</div>
							)}
						</div>
					</div>
				)}
			</CardContent>
		</Card>
	);
}
