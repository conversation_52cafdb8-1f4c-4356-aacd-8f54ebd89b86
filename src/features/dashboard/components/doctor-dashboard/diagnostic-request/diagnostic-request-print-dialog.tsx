'use client';

import { useRef } from 'react';
import { useReactToPrint } from 'react-to-print';

import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { useDiagnosticRequestDetail } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';

import { DiagnosticRequestPrint } from '../../shared/role-aware-diagnostic-request-print';

interface IDiagnosticRequestPrintDialogProps {
	diagnosticRequestId: number;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onClose: () => void;
}

export function DiagnosticRequestPrintDialog({
	diagnosticRequestId,
	open,
	onOpenChange,
}: Omit<IDiagnosticRequestPrintDialogProps, 'onClose'>) {
	// Fetch diagnosticRequest details
	const { data: diagnosticRequestResponse } = useDiagnosticRequestDetail(
		diagnosticRequestId,
		{
			enabled: open,
		}
	);

	const diagnosticRequest = diagnosticRequestResponse?.data;

	const componentRef = useRef<HTMLDivElement | null>(null);
	const handlePrint = useReactToPrint({
		contentRef: componentRef,
		documentTitle: 'Print This Document',
		onBeforePrint: async () => console.log('before printing...'),
		onAfterPrint: () => console.log('after printing...'),
	});

	const handleSubmit = async () => {
		handlePrint();
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogHeader className="hidden">
				<DialogTitle>Diagnostic Request Details</DialogTitle>
				<DialogDescription>
					Print Diagnostic Request #{diagnosticRequestId}
				</DialogDescription>
			</DialogHeader>
			<DialogContent className="max-w-2xl">
				{diagnosticRequest && (
					<DiagnosticRequestPrint
						data={diagnosticRequest}
						componentRef={componentRef}
					/>
				)}
				<Button onClick={handleSubmit}>Print</Button>
			</DialogContent>
		</Dialog>
	);
}
