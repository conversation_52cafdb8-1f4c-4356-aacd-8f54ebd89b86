'use client';

import { format } from 'date-fns';
import {
	Calendar,
	Edit,
	MapPin,
	Pencil,
	Phone,
	Plus,
	Printer,
	Trash2,
	User,
} from 'lucide-react';
import { useEffect, useState } from 'react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { IDiagnosticRequestType } from '@/core/api/data';
import {
	useCreateDiagnosticRequestItem,
	useCreateDiagnosticRequestType,
	useDiagnosticRequestDetail,
	useRemoveDiagnosticRequestItem,
	useUpdateDiagnosticRequestItem,
	useUpdateDiagnosticRequestPtr,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import { useDiagnosticRequestTypes } from '@/features/dashboard/hooks/useReferenceData';
import { IDiagnosticRequestItem } from '@/features/dashboard/types/doctor.types';

interface IDiagnosticRequestDetailDialogProps {
	diagnosticRequestId: number;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onPrint: (diagnosticRequestId: number) => void;
}

interface IFormData {
	name: string;
	description: string;
}

export function DiagnosticRequestDetailDialog({
	diagnosticRequestId,
	open,
	onOpenChange,
	onPrint,
}: IDiagnosticRequestDetailDialogProps) {
	const [newItemName, setNewItemName] = useState('');
	const [isAddingItem, setIsAddingItem] = useState(false);
	const [showCreateDialog, setShowCreateDialog] = useState(false);
	const [formData, setFormData] = useState<IFormData>({
		name: '',
		description: '',
	});

	const [showPtrForm, setShowPtrForm] = useState(false);
	const [ptrForm, setPtrForm] = useState({
		professionalTaxReceipt: '',
	});

	// Fetch appointment data to get the latest diagnostic request data
	const { data: diagnosticRequestResponse } = useDiagnosticRequestDetail(
		diagnosticRequestId,
		{ enabled: open }
	);

	const { data: diagnosticRequestTypesResponse } = useDiagnosticRequestTypes();

	const diagnosticRequestTypes = diagnosticRequestTypesResponse?.data || [];

	const diagnosticRequest = diagnosticRequestResponse?.data;

	const { mutate: createItem, isPending: isCreatingItem } =
		useCreateDiagnosticRequestItem();
	const { mutate: removeItem, isPending: isRemovingItem } =
		useRemoveDiagnosticRequestItem();
	const { mutate: updateItem, isPending: isUpdatingItem } =
		useUpdateDiagnosticRequestItem();
	const { mutate: createDiagnosticRequestType } =
		useCreateDiagnosticRequestType();
	const { mutate: updateProfessionalTaxReceipt } =
		useUpdateDiagnosticRequestPtr();

	const handleAddItem = () => {
		if (!newItemName.trim() || !diagnosticRequestId) return;

		createItem(
			{
				diagnosticRequestId,
				appointmentId: 0,
				itemData: {
					name: newItemName.trim(),
				},
			},
			{
				onSuccess: () => {
					setNewItemName('');
					setIsAddingItem(false);
				},
			}
		);
	};

	const handleRemoveItem = (itemId: number) => {
		if (!diagnosticRequestId) return;

		removeItem({
			diagnosticRequestId,
			diagnosticRequestItemId: itemId,
			appointmentId: 0,
		});
	};

	const handleUpdateItem = (itemId: number, itemData: { name: string }) => {
		if (!diagnosticRequestId) return;

		updateItem({
			diagnosticRequestId,
			diagnosticRequestItemId: itemId,
			itemData,
		});
	};

	const handleCreate = () => {
		if (formData.name.trim()) {
			createDiagnosticRequestType({
				name: formData.name.trim(),
				description: formData.description.trim() || undefined,
			});
			setFormData({ name: '', description: '' });
			setShowCreateDialog(false);
		}
	};

	const handleSubmitPtr = () => {
		if (!diagnosticRequestId || !ptrForm.professionalTaxReceipt) return;

		updateProfessionalTaxReceipt({
			diagnosticRequestId,
			data: {
				professionalTaxReceipt: ptrForm.professionalTaxReceipt,
			},
		});

		// Reset form and close dialog
		setShowPtrForm(false);
	};

	useEffect(() => {
		if (diagnosticRequest) {
			setPtrForm({
				professionalTaxReceipt:
					diagnosticRequest.professional_tax_receipt || '',
			});
		}
	}, [diagnosticRequest]);

	if (!diagnosticRequest) {
		return (
			<Dialog open={open} onOpenChange={onOpenChange}>
				<DialogContent className="max-w-4xl">
					<DialogHeader>
						<DialogTitle>Diagnostic Request Details</DialogTitle>
						<DialogDescription>Diagnostic request not found</DialogDescription>
					</DialogHeader>
					<Alert>
						<AlertDescription>
							The diagnostic request could not be found.
						</AlertDescription>
					</Alert>
				</DialogContent>
			</Dialog>
		);
	}

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
				<DialogHeader>
					<DialogTitle>Diagnostic Request Details</DialogTitle>
					<DialogDescription>
						View and manage diagnostic request items
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-6">
					{/* Patient Information */}
					<Card>
						<CardHeader>
							<div className="flex justify-between">
								<CardTitle className="flex items-center gap-2">
									<User className="h-5 w-5" />
									Patient Information
								</CardTitle>
								<Button
									onClick={() => onPrint(diagnosticRequestId)}
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
								>
									<Printer className="mr-2 h-4 w-4" />
									Print Diagnostic Request
								</Button>
							</div>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
								<div className="space-y-2">
									<Label className="text-sm font-medium text-gray-600">
										Name
									</Label>
									<p className="text-sm">{diagnosticRequest.patient_name}</p>
								</div>
								<div className="space-y-2">
									<Label className="text-sm font-medium text-gray-600">
										Gender
									</Label>
									<p className="text-sm">{diagnosticRequest.patient_gender}</p>
								</div>
								<div className="space-y-2">
									<Label className="text-sm font-medium text-gray-600">
										Phone
									</Label>
									<p className="flex items-center gap-1 text-sm">
										<Phone className="h-4 w-4" />
										{diagnosticRequest.patient_phone}
									</p>
								</div>
								<div className="space-y-2">
									<Label className="text-sm font-medium text-gray-600">
										Birthdate
									</Label>
									<p className="flex items-center gap-1 text-sm">
										<Calendar className="h-4 w-4" />
										{format(
											new Date(diagnosticRequest.patient_birthdate),
											'MMM dd, yyyy'
										)}
									</p>
								</div>
								<div className="space-y-2">
									<Label className="text-sm font-medium text-gray-600">
										Address
									</Label>
									<p className="flex items-start gap-1 text-sm">
										<MapPin className="mt-0.5 h-4 w-4" />
										{diagnosticRequest.patient_address}
									</p>
								</div>
								<div>
									<Label className="text-sm font-medium text-gray-600">
										Professional Tax Receipt
									</Label>
									<div className="flex items-center gap-2">
										<p className="text-sm">
											{diagnosticRequest?.professional_tax_receipt || '---'}
										</p>
										<Button
											variant={'ghost'}
											onClick={() => {
												setShowPtrForm(true);
											}}
											size="sm"
										>
											<Pencil />
											Edit
										</Button>
									</div>
								</div>
							</div>
						</CardContent>
					</Card>

					{/* Diagnostic Request Items */}
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center justify-between">
								<span>Diagnostic Tests</span>
								<Button
									onClick={() => setIsAddingItem(true)}
									size="sm"
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.6448_0.1256_202.74)]"
								>
									<Plus className="mr-2 h-4 w-4" />
									Add Test
								</Button>
							</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							{/* Ptr Form Dialog */}
							<Dialog
								open={showPtrForm}
								onOpenChange={(value) => {
									if (!value) {
										setShowPtrForm(false);
									}
								}}
							>
								<DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
									<DialogHeader>
										<DialogTitle>Edit Professional Tax Receipt</DialogTitle>
										<DialogDescription>
											Update the professional tax receipt number for this
											appointment.
										</DialogDescription>
									</DialogHeader>

									<div className="space-y-6">
										{/* Ptr Information */}
										<div className="space-y-4">
											<div className="space-y-2">
												<Label htmlFor="professionalTaxReceipt">
													Professional Tax Receipt *
												</Label>
												<Input
													id="professionalTaxReceipt"
													value={ptrForm.professionalTaxReceipt}
													onChange={(e) =>
														setPtrForm((prev) => ({
															...prev,
															professionalTaxReceipt: e.target.value,
														}))
													}
													placeholder="Enter professional tax receipt number"
												/>
											</div>
										</div>
									</div>

									<DialogFooter>
										<Button
											variant="outline"
											onClick={() => {
												setShowPtrForm(false);
											}}
										>
											Cancel
										</Button>
										<Button
											onClick={handleSubmitPtr}
											disabled={!ptrForm.professionalTaxReceipt}
											className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
										>
											Save
										</Button>
									</DialogFooter>
								</DialogContent>
							</Dialog>
							{/* Create Dialog */}
							<Dialog
								open={showCreateDialog}
								onOpenChange={setShowCreateDialog}
							>
								<DialogContent>
									<DialogHeader>
										<DialogTitle>Add New Diagnostic Test</DialogTitle>
										<DialogDescription>
											Create a new diagnostic test entry.
										</DialogDescription>
									</DialogHeader>
									<div className="grid gap-4">
										<div className="grid gap-2">
											<Label htmlFor="name">Name</Label>
											<Input
												id="name"
												value={formData.name}
												onChange={(e) =>
													setFormData({ ...formData, name: e.target.value })
												}
												placeholder="Enter name"
											/>
										</div>
										<div className="grid gap-2">
											<Label htmlFor="description">
												Description (Optional)
											</Label>
											<Textarea
												id="description"
												value={formData.description}
												onChange={(e) =>
													setFormData({
														...formData,
														description: e.target.value,
													})
												}
												placeholder="Enter description"
												rows={3}
											/>
										</div>
									</div>
									<DialogFooter>
										<Button
											variant="outline"
											onClick={() => setShowCreateDialog(false)}
										>
											Cancel
										</Button>
										<Button
											onClick={handleCreate}
											disabled={!formData.name.trim()}
										>
											Create
										</Button>
									</DialogFooter>
								</DialogContent>
							</Dialog>
							{/* Add new item form */}
							{isAddingItem && (
								<div className="rounded-lg border bg-gray-50 p-4">
									<div className="space-y-3">
										<Label htmlFor="clinic">Test Name *</Label>
										<Select
											value={newItemName}
											onValueChange={(value) => {
												if (value === 'new') {
													setShowCreateDialog(true);
													return;
												}

												setNewItemName(value);
											}}
										>
											<SelectTrigger className="w-full">
												<SelectValue placeholder="Choose a diagnostic test" />
											</SelectTrigger>
											<SelectContent>
												<SelectItem
													value="new"
													className="text-[oklch(0.7448_0.1256_202.74)]"
												>
													<div className="flex items-center gap-2">
														<Plus className="h-4 w-4" />
														Add Diagnostic Test
													</div>
												</SelectItem>
												{diagnosticRequestTypes.map((diagnosticRequestType) => (
													<SelectItem
														key={diagnosticRequestType.id}
														value={diagnosticRequestType.name}
													>
														{diagnosticRequestType.name}
													</SelectItem>
												))}
											</SelectContent>
										</Select>

										<div className="flex gap-2">
											<Button
												onClick={handleAddItem}
												disabled={!newItemName.trim() || isCreatingItem}
												size="sm"
												className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.6448_0.1256_202.74)]"
											>
												{isCreatingItem ? 'Adding...' : 'Add Test'}
											</Button>
											<Button
												onClick={() => {
													setIsAddingItem(false);
													setNewItemName('');
												}}
												variant="outline"
												size="sm"
											>
												Cancel
											</Button>
										</div>
									</div>
								</div>
							)}

							{/* Existing items */}
							{diagnosticRequest.diagnosticRequestItems &&
							diagnosticRequest.diagnosticRequestItems.length > 0 ? (
								<div className="space-y-3">
									{diagnosticRequest.diagnosticRequestItems.map(
										(item: IDiagnosticRequestItem) => (
											<DiagnosticRequestItem
												setShowCreateDialog={setShowCreateDialog}
												key={item.id}
												item={item}
												isRemovingItem={isRemovingItem}
												handleRemoveItem={handleRemoveItem}
												isUpdatingItem={isUpdatingItem}
												handleUpdateItem={handleUpdateItem}
												diagnosticRequestTypes={diagnosticRequestTypes}
											/>
										)
									)}
								</div>
							) : (
								<div className="py-8 text-center text-gray-500">
									<p>No diagnostic tests added yet.</p>
									<p className="text-sm">
										Click &quot;Add Test&quot; to get started.
									</p>
								</div>
							)}
						</CardContent>
					</Card>
				</div>
			</DialogContent>
		</Dialog>
	);
}

const DiagnosticRequestItem = ({
	item,
	isRemovingItem,
	handleRemoveItem,
	isUpdatingItem,
	handleUpdateItem,
	diagnosticRequestTypes,
	setShowCreateDialog,
}: {
	item: IDiagnosticRequestItem;
	handleRemoveItem: (itemId: number) => void;
	handleUpdateItem: (itemId: number, itemData: { name: string }) => void;
	isRemovingItem: boolean;
	isUpdatingItem: boolean;
	diagnosticRequestTypes: IDiagnosticRequestType[];
	setShowCreateDialog: (show: boolean) => void;
}) => {
	const [showUpdateForm, setShowUpdateForm] = useState(false);
	const [itemForm, setItemForm] = useState({
		name: item.name || '',
	});

	return (
		<div>
			{showUpdateForm ? (
				<>
					<div className="rounded-lg border bg-gray-50 p-4">
						<div className="space-y-3">
							<Label htmlFor="clinic">Test Name *</Label>
							<Select
								value={itemForm.name}
								onValueChange={(value) => {
									if (value === 'new') {
										setShowCreateDialog(true);
										return;
									}

									setItemForm({
										...itemForm,
										name: value,
									});
								}}
							>
								<SelectTrigger className="w-full">
									<SelectValue placeholder="Choose a diagnostic test" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem
										value="new"
										className="text-[oklch(0.7448_0.1256_202.74)]"
									>
										<div className="flex items-center gap-2">
											<Plus className="h-4 w-4" />
											Add Diagnostic Test
										</div>
									</SelectItem>
									{diagnosticRequestTypes.map((diagnosticRequestType) => (
										<SelectItem
											key={diagnosticRequestType.id}
											value={diagnosticRequestType.name}
										>
											{diagnosticRequestType.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>

							<div className="flex gap-2">
								<Button
									onClick={() => {
										handleUpdateItem(item.id, itemForm);
										setShowUpdateForm(false);
									}}
									disabled={isUpdatingItem}
									size="sm"
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.6448_0.1256_202.74)]"
								>
									{isUpdatingItem ? 'Saving...' : 'Save'}
								</Button>
								<Button
									onClick={() => setShowUpdateForm(false)}
									variant="outline"
									size="sm"
								>
									Cancel
								</Button>
							</div>
						</div>
					</div>
				</>
			) : (
				<div className="flex items-center justify-between gap-2 rounded-lg border p-3">
					<div className="flex-1">
						<p className="font-medium">{item.name}</p>
						<p className="text-sm text-gray-500">
							Added: {format(new Date(item.created_at), 'MMM dd, yyyy')}
						</p>
					</div>
					<Button
						onClick={() => {
							setShowUpdateForm(true);
						}}
						variant="outline"
						size="sm"
					>
						<Edit className="h-4 w-4" />
					</Button>
					<Button
						onClick={() => handleRemoveItem(item.id)}
						variant="outline"
						size="sm"
						disabled={isRemovingItem}
						className="text-red-600 hover:text-red-700"
					>
						<Trash2 className="h-4 w-4" />
					</Button>
				</div>
			)}
		</div>
	);
};
