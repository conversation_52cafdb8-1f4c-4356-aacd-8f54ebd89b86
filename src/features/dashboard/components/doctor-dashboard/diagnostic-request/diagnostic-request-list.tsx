'use client';

import { format } from 'date-fns';
import { <PERSON>, MoreHorizontal, Printer, Trash2, User } from 'lucide-react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import { useRemoveStandaloneDiagnosticRequest } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import { IDiagnosticRequest } from '@/features/dashboard/types/doctor.types';

interface IDiagnosticRequestListProps {
	diagnosticRequests: IDiagnosticRequest[];
	isLoading: boolean;
	error: Error | null;
	currentPage: number;
	totalPages: number;
	onPageChange: (page: number) => void;
	onViewDiagnosticRequest: (diagnosticRequestId: number) => void;
	onPrintDiagnosticRequest: (diagnosticRequestId: number) => void;
}

export function DiagnosticRequestList({
	diagnosticRequests,
	isLoading,
	error,
	currentPage,
	totalPages,
	onPageChange,
	onViewDiagnosticRequest,
	onPrintDiagnosticRequest,
}: IDiagnosticRequestListProps) {
	const { mutate: removeDiagnosticRequest } =
		useRemoveStandaloneDiagnosticRequest();

	const handleRemoveDiagnosticRequest = (diagnosticRequestId: number) => {
		if (confirm('Are you sure you want to remove this diagnostic request?')) {
			removeDiagnosticRequest(diagnosticRequestId);
		}
	};

	const formatDate = (dateString: string) => {
		try {
			return format(new Date(dateString), 'MMM dd, yyyy, h:mm a');
		} catch {
			return dateString;
		}
	};

	if (isLoading) {
		return (
			<div className="space-y-4">
				{[...Array(5)].map((_, index) => (
					<div key={index} className="flex items-center space-x-4">
						<Skeleton className="h-12 w-12 rounded-full" />
						<div className="space-y-2">
							<Skeleton className="h-4 w-[250px]" />
							<Skeleton className="h-4 w-[200px]" />
						</div>
					</div>
				))}
			</div>
		);
	}

	if (error) {
		return (
			<Alert variant="destructive">
				<AlertDescription>
					Failed to load diagnostic requests. Please try again.
				</AlertDescription>
			</Alert>
		);
	}

	if (!diagnosticRequests || diagnosticRequests.length === 0) {
		return (
			<div className="py-8 text-center">
				<User className="mx-auto h-12 w-12 text-gray-400" />
				<h3 className="mt-2 text-sm font-semibold text-gray-900">
					No diagnostic requests found
				</h3>
				<p className="mt-1 text-sm text-gray-500">
					Get started by creating a new diagnostic request.
				</p>
			</div>
		);
	}

	return (
		<div className="space-y-4">
			{/* Table */}
			<div className="rounded-md border">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>Patient</TableHead>
							<TableHead>Contact</TableHead>
							<TableHead>Clinic</TableHead>
							<TableHead>Tests</TableHead>
							<TableHead>Created</TableHead>
							<TableHead className="text-right">Actions</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{diagnosticRequests.map((diagnosticRequest) => (
							<TableRow key={diagnosticRequest.id}>
								<TableCell>
									<div className="flex flex-row items-center gap-2">
										<img
											className="bg-muted flex h-8 w-8 rounded-full"
											alt="patient avatar"
											src={'/placeholder-profile.jpg'}
										/>
										<div>
											<div className="font-medium">
												{diagnosticRequest.patient_name}
											</div>
											<div className="text-muted-foreground text-sm">
												{diagnosticRequest.patient_gender} •{' '}
												{diagnosticRequest.patient_birthdate &&
													new Date().getFullYear() -
														new Date(
															diagnosticRequest.patient_birthdate
														).getFullYear() +
														' years'}
											</div>
										</div>
									</div>
								</TableCell>
								<TableCell>
									<div className="text-sm">
										<div>{diagnosticRequest.patient_phone}</div>
										<div className="text-muted-foreground text-xs">
											{diagnosticRequest.patient_address}
										</div>
									</div>
								</TableCell>
								<TableCell>
									<div className="text-sm">
										{diagnosticRequest.clinic?.name || 'N/A'}
									</div>
								</TableCell>
								<TableCell>
									<Badge variant="secondary">
										{diagnosticRequest.diagnosticRequestItems?.length || 0}{' '}
										tests
									</Badge>
								</TableCell>
								<TableCell>
									<div className="text-sm">
										{formatDate(diagnosticRequest.created_at)}
									</div>
								</TableCell>
								<TableCell className="text-right">
									<DropdownMenu>
										<DropdownMenuTrigger asChild>
											<Button variant="ghost" className="h-8 w-8 p-0">
												<span className="sr-only">Open menu</span>
												<MoreHorizontal className="h-4 w-4" />
											</Button>
										</DropdownMenuTrigger>
										<DropdownMenuContent align="end">
											<DropdownMenuItem
												onClick={() =>
													onViewDiagnosticRequest(diagnosticRequest.id)
												}
											>
												<Eye className="mr-2 h-4 w-4" />
												Open Diagnostic Request
											</DropdownMenuItem>
											<DropdownMenuItem
												onClick={() =>
													onPrintDiagnosticRequest(diagnosticRequest.id)
												}
											>
												<Printer className="mr-2 h-4 w-4" />
												Print Diagnostic Request
											</DropdownMenuItem>
											<DropdownMenuItem
												onClick={() =>
													handleRemoveDiagnosticRequest(diagnosticRequest.id)
												}
												className="text-red-600"
											>
												<Trash2 className="mr-2 h-4 w-4" />
												Remove Diagnostic Request
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								</TableCell>
							</TableRow>
						))}
					</TableBody>
				</Table>
			</div>

			{/* Pagination */}
			{totalPages > 1 && (
				<div className="flex items-center justify-between">
					<div className="text-muted-foreground text-sm">
						Page {currentPage} of {totalPages}
					</div>
					<div className="flex items-center space-x-2">
						<Button
							variant="outline"
							size="sm"
							onClick={() => onPageChange(currentPage - 1)}
							disabled={currentPage <= 1}
						>
							Previous
						</Button>
						<Button
							variant="outline"
							size="sm"
							onClick={() => onPageChange(currentPage + 1)}
							disabled={currentPage >= totalPages}
						>
							Next
						</Button>
					</div>
				</div>
			)}
		</div>
	);
}
