'use client';

import { useRef } from 'react';
import { useReactToPrint } from 'react-to-print';

import { But<PERSON> } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { useLabRequestDetail } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';

import { LabRequestPrint } from '../../shared/role-aware-lab-request-print';

interface ILabRequestPrintDialogProps {
	labRequestId: number;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onClose: () => void;
}

export function LabRequestPrintDialog({
	labRequestId,
	open,
	onOpenChange,
}: Omit<ILabRequestPrintDialogProps, 'onClose'>) {
	// Fetch labRequest details
	const { data: labRequestResponse } = useLabRequestDetail(labRequestId, {
		enabled: open,
	});

	const labRequest = labRequestResponse?.data;

	const componentRef = useRef<HTMLDivElement | null>(null);
	const handlePrint = useReactToPrint({
		contentRef: componentRef,
		documentTitle: 'Print This Document',
		onBeforePrint: async () => console.log('before printing...'),
		onAfterPrint: () => console.log('after printing...'),
	});

	const handleSubmit = async () => {
		handlePrint();
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogHeader className="hidden">
				<DialogTitle>LabRequest Details</DialogTitle>
				<DialogDescription>Print labRequest #{labRequestId}</DialogDescription>
			</DialogHeader>
			<DialogContent className="max-w-2xl">
				{labRequest && (
					<LabRequestPrint data={labRequest} componentRef={componentRef} />
				)}
				<Button onClick={handleSubmit}>Print</Button>
			</DialogContent>
		</Dialog>
	);
}
