'use client';

import { format } from 'date-fns';
import { Eye, MoreHorizontal, Printer, Trash2, User } from 'lucide-react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import { useRemoveStandaloneLabRequest } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import { ILabRequest } from '@/features/dashboard/types/doctor.types';

interface ILabRequestListProps {
	labRequests: ILabRequest[];
	isLoading: boolean;
	error: Error | null;
	currentPage: number;
	totalPages: number;
	onPageChange: (page: number) => void;
	onViewLabRequest: (labRequestId: number) => void;
	onPrintLabRequest: (labRequestId: number) => void;
}

export function LabRequestList({
	labRequests,
	isLoading,
	error,
	currentPage,
	totalPages,
	onPageChange,
	onViewLabRequest,
	onPrintLabRequest,
}: ILabRequestListProps) {
	const { mutate: removeLabRequest } = useRemoveStandaloneLabRequest();

	const handleRemoveLabRequest = (labRequestId: number) => {
		if (confirm('Are you sure you want to remove this lab request?')) {
			removeLabRequest(labRequestId);
		}
	};

	const formatDate = (dateString: string) => {
		try {
			return format(new Date(dateString), 'MMM dd, yyyy, h:mm a');
		} catch {
			return dateString;
		}
	};

	if (isLoading) {
		return (
			<div className="space-y-4">
				{[...Array(5)].map((_, index) => (
					<div key={index} className="flex items-center space-x-4">
						<Skeleton className="h-12 w-12 rounded-full" />
						<div className="space-y-2">
							<Skeleton className="h-4 w-[250px]" />
							<Skeleton className="h-4 w-[200px]" />
						</div>
					</div>
				))}
			</div>
		);
	}

	if (error) {
		return (
			<Alert variant="destructive">
				<AlertDescription>
					Failed to load lab requests. Please try again.
				</AlertDescription>
			</Alert>
		);
	}

	if (!labRequests || labRequests.length === 0) {
		return (
			<div className="py-8 text-center">
				<User className="mx-auto h-12 w-12 text-gray-400" />
				<h3 className="mt-2 text-sm font-semibold text-gray-900">
					No lab requests found
				</h3>
				<p className="mt-1 text-sm text-gray-500">
					Get started by creating a new lab request.
				</p>
			</div>
		);
	}

	return (
		<div className="space-y-4">
			{/* Table */}
			<div className="rounded-md border">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>Patient</TableHead>
							<TableHead>Contact</TableHead>
							<TableHead>Clinic</TableHead>
							<TableHead>Tests</TableHead>
							<TableHead>Created</TableHead>
							<TableHead className="text-right">Actions</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{labRequests.map((labRequest) => (
							<TableRow key={labRequest.id}>
								<TableCell>
									<div className="flex flex-row items-center gap-2">
										<img
											className="bg-muted flex h-8 w-8 rounded-full"
											alt="patient avatar"
											src={'/placeholder-profile.jpg'}
										/>
										<div>
											<div className="font-medium">
												{labRequest.patient_name}
											</div>
											<div className="text-muted-foreground text-sm">
												{labRequest.patient_gender} •{' '}
												{labRequest.patient_birthdate &&
													new Date().getFullYear() -
														new Date(
															labRequest.patient_birthdate
														).getFullYear() +
														' years'}
											</div>
										</div>
									</div>
								</TableCell>
								<TableCell>
									<div className="text-sm">
										<div>{labRequest.patient_phone}</div>
										<div className="text-muted-foreground text-xs">
											{labRequest.patient_address}
										</div>
									</div>
								</TableCell>
								<TableCell>
									<div className="text-sm">
										{labRequest.clinic?.name || 'N/A'}
									</div>
								</TableCell>
								<TableCell>
									<Badge variant="secondary">
										{labRequest.labRequestItems?.length || 0} tests
									</Badge>
								</TableCell>
								<TableCell>
									<div className="text-sm">
										{formatDate(labRequest.created_at)}
									</div>
								</TableCell>
								<TableCell className="text-right">
									<DropdownMenu>
										<DropdownMenuTrigger asChild>
											<Button variant="ghost" className="h-8 w-8 p-0">
												<span className="sr-only">Open menu</span>
												<MoreHorizontal className="h-4 w-4" />
											</Button>
										</DropdownMenuTrigger>
										<DropdownMenuContent align="end">
											<DropdownMenuItem
												onClick={() => onViewLabRequest(labRequest.id)}
											>
												<Eye className="mr-2 h-4 w-4" />
												Open Lab Request
											</DropdownMenuItem>
											<DropdownMenuItem
												onClick={() => onPrintLabRequest(labRequest.id)}
											>
												<Printer className="mr-2 h-4 w-4" />
												Print Lab Request
											</DropdownMenuItem>
											<DropdownMenuItem
												onClick={() => handleRemoveLabRequest(labRequest.id)}
												className="text-red-600"
											>
												<Trash2 className="mr-2 h-4 w-4" />
												Remove Lab Request
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								</TableCell>
							</TableRow>
						))}
					</TableBody>
				</Table>
			</div>

			{/* Pagination */}
			{totalPages > 1 && (
				<div className="flex items-center justify-between">
					<div className="text-muted-foreground text-sm">
						Page {currentPage} of {totalPages}
					</div>
					<div className="flex items-center space-x-2">
						<Button
							variant="outline"
							size="sm"
							onClick={() => onPageChange(currentPage - 1)}
							disabled={currentPage <= 1}
						>
							Previous
						</Button>
						<Button
							variant="outline"
							size="sm"
							onClick={() => onPageChange(currentPage + 1)}
							disabled={currentPage >= totalPages}
						>
							Next
						</Button>
					</div>
				</div>
			)}
		</div>
	);
}
