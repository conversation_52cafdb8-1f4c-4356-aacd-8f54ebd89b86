'use client';

import { format } from 'date-fns';
import {
	Calendar,
	Edit,
	MapPin,
	Pencil,
	Phone,
	Plus,
	Printer,
	Trash2,
	User,
} from 'lucide-react';
import { useState } from 'react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Textarea } from '@/components/ui/textarea';
import { ILabRequestType } from '@/core/api/data';
import {
	useCreateLabRequestItem,
	useCreateLabRequestType,
	useLabRequestDetail,
	useRemoveLabRequestItem,
	useUpdateLabRequestItem,
	useUpdateLabRequestPtr,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import { useLabRequestTypes } from '@/features/dashboard/hooks/useReferenceData';
import { ILabRequestItem } from '@/features/dashboard/types/doctor.types';

interface ILabRequestDetailDialogProps {
	labRequestId: number;
	open: boolean;
	onOpenChange?: (open: boolean) => void;
	onClose: () => void;
	onPrint: (labRequestId: number) => void;
}

interface IFormData {
	name: string;
	description: string;
}

export function LabRequestDetailDialog({
	labRequestId,
	open,
	onOpenChange,
	onPrint,
}: Omit<ILabRequestDetailDialogProps, 'onClose'>) {
	const [showCreateDialog, setShowCreateDialog] = useState(false);
	const [showTestForm, setShowTestForm] = useState(false);
	const [testForm, setTestForm] = useState({
		test_type: '',
	});
	const [formData, setFormData] = useState<IFormData>({
		name: '',
		description: '',
	});

	const [showPtrForm, setShowPtrForm] = useState(false);
	const [ptrForm, setPtrForm] = useState({
		professionalTaxReceipt: '',
	});

	// Fetch lab request details
	const {
		data: labRequestResponse,
		isLoading,
		error,
	} = useLabRequestDetail(labRequestId, { enabled: open });

	const { mutate: createLabRequestItem } = useCreateLabRequestItem();
	const { mutate: removeLabRequestItem } = useRemoveLabRequestItem();
	const { mutate: updateLabRequestItem } = useUpdateLabRequestItem();
	const { mutate: createLabRequestType } = useCreateLabRequestType();
	const { mutate: updateProfessionalTaxReceipt } = useUpdateLabRequestPtr();

	const { data: labRequestTypesResponse } = useLabRequestTypes();

	const labRequestTypes = labRequestTypesResponse?.data || [];

	const labRequest = labRequestResponse?.data;

	const handleAddTest = () => {
		if (!testForm.test_type.trim()) return;

		// Combine test_type and instructions into name field for backend
		const name = testForm.test_type;

		createLabRequestItem({
			labRequestId,
			appointmentId: 0, // Not needed for standalone lab requests
			itemData: { name },
		});

		setTestForm({
			test_type: '',
		});
		setShowTestForm(false);
	};

	const handleRemoveTest = (labRequestItemId: number) => {
		if (confirm('Are you sure you want to remove this test?')) {
			removeLabRequestItem({
				labRequestId,
				labRequestItemId,
				appointmentId: 0, // Not needed for standalone lab requests
			});
		}
	};

	const handleUpdateTest = (
		labRequestItemId: number,
		itemData: { name: string }
	) => {
		updateLabRequestItem({
			labRequestId,
			labRequestItemId,
			itemData,
		});
	};

	const handleCreate = () => {
		if (formData.name.trim()) {
			createLabRequestType({
				name: formData.name.trim(),
				description: formData.description.trim() || undefined,
			});
			setFormData({ name: '', description: '' });
			setShowCreateDialog(false);
		}
	};

	const formatDate = (dateString: string) => {
		try {
			return format(new Date(dateString), 'MMM dd, yyyy');
		} catch {
			return dateString;
		}
	};

	const handleSubmitPtr = () => {
		if (!labRequestId || !ptrForm.professionalTaxReceipt) return;

		updateProfessionalTaxReceipt({
			labRequestId,
			data: {
				professionalTaxReceipt: ptrForm.professionalTaxReceipt,
			},
		});

		// Reset form and close dialog
		setShowPtrForm(false);
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-4xl">
				<DialogHeader>
					<DialogTitle>Lab Request Details</DialogTitle>
					<DialogDescription>
						View and manage lab request #{labRequestId}
					</DialogDescription>
				</DialogHeader>

				{isLoading ? (
					<div className="space-y-4">
						<Skeleton className="h-32 w-full" />
						<Skeleton className="h-24 w-full" />
						<Skeleton className="h-24 w-full" />
					</div>
				) : error ? (
					<Alert variant="destructive">
						<AlertDescription>
							Failed to load lab request details. Please try again.
						</AlertDescription>
					</Alert>
				) : labRequest ? (
					<div className="space-y-6">
						{/* Patient Information */}
						<Card>
							<CardHeader>
								<div className="flex justify-between">
									<CardTitle className="flex items-center gap-2">
										<User className="h-5 w-5" />
										Patient Information
									</CardTitle>
									<Button
										onClick={() => onPrint(labRequestId)}
										className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
									>
										<Printer className="mr-2 h-4 w-4" />
										Print Lab Request
									</Button>
								</div>
							</CardHeader>
							<CardContent className="grid grid-cols-2 gap-4">
								<div>
									<Label className="text-sm font-medium">Name</Label>
									<p className="text-sm">{labRequest.patient_name}</p>
								</div>
								<div>
									<Label className="text-sm font-medium">Gender</Label>
									<p className="text-sm">{labRequest.patient_gender}</p>
								</div>
								<div>
									<Label className="text-sm font-medium">Birth Date</Label>
									<p className="flex items-center gap-1 text-sm">
										<Calendar className="h-4 w-4" />
										{formatDate(labRequest.patient_birthdate)}
									</p>
								</div>
								<div>
									<Label className="text-sm font-medium">Phone</Label>
									<p className="flex items-center gap-1 text-sm">
										<Phone className="h-4 w-4" />
										{labRequest.patient_phone}
									</p>
								</div>
								<div className="col-span-2">
									<Label className="text-sm font-medium">Address</Label>
									<p className="flex items-center gap-1 text-sm">
										<MapPin className="h-4 w-4" />
										{labRequest.patient_address}
									</p>
								</div>
							</CardContent>
						</Card>

						{/* Clinic Information */}
						{labRequest.clinic && (
							<Card>
								<CardHeader>
									<CardTitle>Clinic Information</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="grid grid-cols-2 gap-4">
										<div>
											<Label className="text-sm font-medium">Clinic Name</Label>
											<p className="text-sm">{labRequest.clinic.name}</p>
										</div>
										<div>
											<Label className="text-sm font-medium">Created</Label>
											<p className="text-sm">
												{formatDate(labRequest.created_at)}
											</p>
										</div>
										<div>
											<Label className="text-sm font-medium">
												Professional Tax Receipt
											</Label>
											<div className="flex items-center gap-2">
												<p className="text-sm">
													{labRequest?.professional_tax_receipt || '---'}
												</p>
												<Button
													variant={'ghost'}
													onClick={() => {
														setShowPtrForm(true);
													}}
													size="sm"
												>
													<Pencil />
													Edit
												</Button>
											</div>
										</div>
									</div>
								</CardContent>
							</Card>
						)}

						{/* Laboratory Tests */}
						<Card>
							<CardHeader>
								<div className="flex items-center justify-between">
									<CardTitle>Laboratory Tests</CardTitle>
									<Button
										onClick={() => setShowTestForm(true)}
										size="sm"
										className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
									>
										<Plus className="mr-2 h-4 w-4" />
										Add Test
									</Button>
								</div>
							</CardHeader>
							<CardContent className="space-y-4">
								{/* Ptr Form Dialog */}
								<Dialog
									open={showPtrForm}
									onOpenChange={(value) => {
										if (!value) {
											setShowPtrForm(false);
										}
									}}
								>
									<DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
										<DialogHeader>
											<DialogTitle>Edit Professional Tax Receipt</DialogTitle>
											<DialogDescription>
												Update the professional tax receipt number for this
												appointment.
											</DialogDescription>
										</DialogHeader>

										<div className="space-y-6">
											{/* Ptr Information */}
											<div className="space-y-4">
												<div className="space-y-2">
													<Label htmlFor="professionalTaxReceipt">
														Professional Tax Receipt *
													</Label>
													<Input
														id="professionalTaxReceipt"
														value={ptrForm.professionalTaxReceipt}
														onChange={(e) =>
															setPtrForm((prev) => ({
																...prev,
																professionalTaxReceipt: e.target.value,
															}))
														}
														placeholder="Enter professional tax receipt number"
													/>
												</div>
											</div>
										</div>

										<DialogFooter>
											<Button
												variant="outline"
												onClick={() => {
													setShowPtrForm(false);
												}}
											>
												Cancel
											</Button>
											<Button
												onClick={handleSubmitPtr}
												disabled={!ptrForm.professionalTaxReceipt}
												className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
											>
												Save
											</Button>
										</DialogFooter>
									</DialogContent>
								</Dialog>
								{/* Create Dialog */}
								<Dialog
									open={showCreateDialog}
									onOpenChange={setShowCreateDialog}
								>
									<DialogContent>
										<DialogHeader>
											<DialogTitle>Add New Lab Test</DialogTitle>
											<DialogDescription>
												Create a new lab test entry.
											</DialogDescription>
										</DialogHeader>
										<div className="grid gap-4">
											<div className="grid gap-2">
												<Label htmlFor="name">Name</Label>
												<Input
													id="name"
													value={formData.name}
													onChange={(e) =>
														setFormData({ ...formData, name: e.target.value })
													}
													placeholder="Enter name"
												/>
											</div>
											<div className="grid gap-2">
												<Label htmlFor="description">
													Description (Optional)
												</Label>
												<Textarea
													id="description"
													value={formData.description}
													onChange={(e) =>
														setFormData({
															...formData,
															description: e.target.value,
														})
													}
													placeholder="Enter description"
													rows={3}
												/>
											</div>
										</div>
										<DialogFooter>
											<Button
												variant="outline"
												onClick={() => setShowCreateDialog(false)}
											>
												Cancel
											</Button>
											<Button
												onClick={handleCreate}
												disabled={!formData.name.trim()}
											>
												Create
											</Button>
										</DialogFooter>
									</DialogContent>
								</Dialog>
								{/* Add Test Form */}
								{showTestForm && (
									<Card className="border-dashed">
										<CardContent className="">
											<div className="grid grid-cols-1 gap-4">
												<div>
													<Label htmlFor="clinic">Test Name *</Label>
													<Select
														value={testForm.test_type}
														onValueChange={(value) => {
															if (value === 'new') {
																setShowCreateDialog(true);
																return;
															}

															setTestForm({
																...testForm,
																test_type: value,
															});
														}}
													>
														<SelectTrigger className="w-full">
															<SelectValue placeholder="Choose a lab test" />
														</SelectTrigger>
														<SelectContent>
															<SelectItem
																value="new"
																className="text-[oklch(0.7448_0.1256_202.74)]"
															>
																<div className="flex items-center gap-2">
																	<Plus className="h-4 w-4" />
																	Add Lab Test
																</div>
															</SelectItem>
															{labRequestTypes.map((labRequestType) => (
																<SelectItem
																	key={labRequestType.id}
																	value={labRequestType.name}
																>
																	{labRequestType.name}
																</SelectItem>
															))}
														</SelectContent>
													</Select>
												</div>
											</div>
											<div className="mt-4 flex gap-2">
												<Button onClick={handleAddTest} size="sm">
													Add Test
												</Button>
												<Button
													variant="outline"
													onClick={() => setShowTestForm(false)}
													size="sm"
												>
													Cancel
												</Button>
											</div>
										</CardContent>
									</Card>
								)}

								{/* Existing Tests */}
								{labRequest.labRequestItems &&
								labRequest.labRequestItems.length > 0 ? (
									<div className="space-y-3">
										{labRequest.labRequestItems.map((item: ILabRequestItem) => (
											<LabRequestItem
												setShowCreateDialog={setShowCreateDialog}
												labRequestTypes={labRequestTypes}
												key={item.id}
												item={item}
												handleRemoveTest={handleRemoveTest}
												handleUpdateTest={handleUpdateTest}
											/>
										))}
									</div>
								) : (
									<div className="py-8 text-center">
										<p className="text-muted-foreground">
											No tests added yet. Click &quot;Add Test&quot; to get
											started.
										</p>
									</div>
								)}
							</CardContent>
						</Card>
					</div>
				) : null}
			</DialogContent>
		</Dialog>
	);
}

const LabRequestItem = ({
	item,
	labRequestTypes,
	handleRemoveTest,
	handleUpdateTest,
	setShowCreateDialog,
}: {
	item: ILabRequestItem;
	labRequestTypes: ILabRequestType[];
	handleRemoveTest: (labRequestItemId: number) => void;
	handleUpdateTest: (
		labRequestItemId: number,
		itemData: { name: string }
	) => void;
	setShowCreateDialog: (open: boolean) => void;
}) => {
	const [showUpdateForm, setShowUpdateForm] = useState(false);
	const [testForm, setTestForm] = useState({
		name: item.name || '',
	});

	return (
		<Card key={item.id} className="border">
			<CardContent className="">
				{showUpdateForm ? (
					<>
						<div className="grid grid-cols-1 gap-4">
							<div>
								<Label htmlFor="clinic">Test Name *</Label>
								<Select
									value={testForm.name}
									onValueChange={(value) => {
										if (value === 'new') {
											setShowCreateDialog(true);
											return;
										}
										setTestForm({
											...testForm,
											name: value,
										});
									}}
								>
									<SelectTrigger className="w-full">
										<SelectValue placeholder="Choose a lab test" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem
											value="new"
											className="text-[oklch(0.7448_0.1256_202.74)]"
										>
											<div className="flex items-center gap-2">
												<Plus className="h-4 w-4" />
												Add Lab Test
											</div>
										</SelectItem>
										{labRequestTypes.map((labRequestType) => (
											<SelectItem
												key={labRequestType.id}
												value={labRequestType.name}
											>
												{labRequestType.name}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
						</div>
						<div className="mt-4 flex gap-2">
							<Button
								onClick={() => {
									handleUpdateTest(item.id, testForm);
									setShowUpdateForm(false);
								}}
								size="sm"
							>
								Save
							</Button>
							<Button
								variant="outline"
								onClick={() => setShowUpdateForm(false)}
								size="sm"
							>
								Cancel
							</Button>
						</div>
					</>
				) : (
					<div className="flex items-center justify-between gap-2">
						<div className="flex-1">
							<div className="flex items-center gap-4">
								<div>
									<p className="font-medium">{item.name || 'Unnamed Test'}</p>
								</div>
							</div>
						</div>
						<Button
							variant="outline"
							size="sm"
							onClick={() => {
								setShowUpdateForm(true);
							}}
						>
							<Edit className="h-4 w-4" />
						</Button>
						<Button
							variant="outline"
							size="sm"
							onClick={() => handleRemoveTest(item.id)}
							className="text-red-600 hover:text-red-700"
						>
							<Trash2 className="h-4 w-4" />
						</Button>
					</div>
				)}
			</CardContent>
		</Card>
	);
};
