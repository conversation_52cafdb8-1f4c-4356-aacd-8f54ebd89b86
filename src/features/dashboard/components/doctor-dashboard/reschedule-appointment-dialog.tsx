'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { format, subDays } from 'date-fns';
import { CalendarIcon, Clock } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/core/lib/utils';
import { useRescheduleAppointment } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import type { IAppointment } from '@/features/dashboard/types/doctor.types';

const rescheduleSchema = z.object({
	appointmentDate: z.date({
		required_error: 'Please select a date and time for the appointment.',
	}),
	appointmentTime: z
		.string()
		.min(1, 'Please select a time for the appointment.'),
});

type RescheduleFormData = z.infer<typeof rescheduleSchema>;

interface IRescheduleAppointmentDialogProps {
	appointment: IAppointment;
	trigger: React.ReactNode;
}

export function RescheduleAppointmentDialog({
	appointment,
	trigger,
}: IRescheduleAppointmentDialogProps) {
	const [open, setOpen] = useState(false);
	const { mutate: rescheduleAppointment, isPending } =
		useRescheduleAppointment();

	const form = useForm<RescheduleFormData>({
		resolver: zodResolver(rescheduleSchema),
		defaultValues: {
			appointmentTime: '09:00',
		},
	});

	const onSubmit = (data: RescheduleFormData) => {
		// Combine date and time into ISO string
		const [hours, minutes] = data.appointmentTime.split(':');

		// Create a new date object and set the time properly
		const year = data.appointmentDate.getFullYear();
		const month = data.appointmentDate.getMonth();
		const day = data.appointmentDate.getDate();

		// Create the appointment date with the selected time
		const appointmentDateTime = new Date(
			year,
			month,
			day,
			parseInt(hours),
			parseInt(minutes),
			0,
			0
		);

		rescheduleAppointment(
			{
				appointmentId: appointment.id,
				appointmentDate: appointmentDateTime.toISOString(),
			},
			{
				onSuccess: () => {
					setOpen(false);
					form.reset();
				},
			}
		);
	};

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogTrigger asChild>{trigger}</DialogTrigger>
			<DialogContent className="sm:max-w-[500px]">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<Clock className="h-5 w-5" />
						Reschedule Appointment
					</DialogTitle>
					<DialogDescription>
						Reschedule the appointment for{' '}
						{appointment.patient?.profile?.first_name}{' '}
						{appointment.patient?.profile?.last_name}. The patient will be
						notified via email.
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
						<div className="grid grid-cols-2 gap-4">
							{/* Date Selection */}
							<FormField
								control={form.control}
								name="appointmentDate"
								render={({ field }) => (
									<FormItem className="flex flex-col">
										<FormLabel>Appointment Date</FormLabel>
										<Popover>
											<PopoverTrigger asChild>
												<FormControl>
													<Button
														variant="outline"
														className={cn(
															'w-full pl-3 text-left font-normal',
															!field.value && 'text-muted-foreground'
														)}
													>
														{field.value ? (
															format(field.value, 'PPP')
														) : (
															<span>Pick a date</span>
														)}
														<CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
													</Button>
												</FormControl>
											</PopoverTrigger>
											<PopoverContent className="w-auto p-0" align="start">
												<Calendar
													mode="single"
													selected={field.value}
													onSelect={field.onChange}
													disabled={(date) => date < subDays(new Date(), 1)}
												/>
											</PopoverContent>
										</Popover>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Time Selection */}
							<FormField
								control={form.control}
								name="appointmentTime"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Appointment Time</FormLabel>
										<FormControl>
											<Input type="time" {...field} className="w-full" />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<DialogFooter>
							<Button
								type="button"
								variant="outline"
								onClick={() => setOpen(false)}
								disabled={isPending}
							>
								Cancel
							</Button>
							<Button type="submit" disabled={isPending}>
								{isPending ? 'Rescheduling...' : 'Reschedule Appointment'}
							</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
