'use client';

import { format } from 'date-fns';
import {
	Calendar,
	MapPin,
	Pencil,
	Phone,
	Plus,
	Printer,
	User,
} from 'lucide-react';
import { useEffect, useState } from 'react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { IMedicalDescription } from '@/core/api/data';
import {
	useCreateMedicalCertificateItem,
	useCreateMedicalDescription,
	useMedicalCertificateDetail,
	useRemoveMedicalCertificateItem,
	useUpdateMedicalCertificatePtr,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import { useMedicalDescriptions } from '@/features/dashboard/hooks/useReferenceData';
import { IMedicalCertificateItem } from '@/features/dashboard/types/doctor.types';

interface IMedicalCertificateDetailDialogProps {
	medicalCertificateId: number;
	appointmentId: number;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onPrint: (medicalCertificateId: number) => void;
}

interface IFormData {
	name: string;
	description?: string;
}

export function MedicalCertificateDetailDialog({
	medicalCertificateId,
	appointmentId,
	open,
	onOpenChange,
	onPrint,
}: IMedicalCertificateDetailDialogProps) {
	const [showCreateDialog, setShowCreateDialog] = useState(false);
	const [formData, setFormData] = useState<IFormData>({
		name: '',
		description: '',
	});

	const [showPtrForm, setShowPtrForm] = useState(false);
	const [ptrForm, setPtrForm] = useState({
		professionalTaxReceipt: '',
	});

	// Fetch medical certificate details
	const { data: medicalCertificateResponse } = useMedicalCertificateDetail(
		medicalCertificateId,
		{ enabled: open }
	);

	const { data: medicalDescriptionsResponse } = useMedicalDescriptions();
	const { mutate: createMedicalDescription } = useCreateMedicalDescription();
	const { mutate: updateProfessionalTaxReceipt } =
		useUpdateMedicalCertificatePtr();

	const medicalDescriptions = medicalDescriptionsResponse?.data || [];

	const medicalCertificate = medicalCertificateResponse?.data;

	const { mutate: createItem } = useCreateMedicalCertificateItem();
	const { mutate: removeItem } = useRemoveMedicalCertificateItem();

	const handleAddItem = (description: string) => {
		if (!medicalCertificateId) return;

		createItem(
			{
				medicalCertificateId,
				appointmentId,
				itemData: {
					description,
				},
			},
			{
				onSuccess: () => {},
			}
		);
	};

	const handleRemoveItem = (itemId: number) => {
		if (!medicalCertificateId) return;

		removeItem({
			medicalCertificateId,
			medicalCertificateItemId: itemId,
			appointmentId,
		});
	};

	const handleCreate = () => {
		if (formData.name.trim()) {
			createMedicalDescription({
				name: formData.name.trim(),
			});
			setFormData({ name: '', description: '' });
			setShowCreateDialog(false);
		}
	};

	const handleSubmitPtr = () => {
		if (!medicalCertificateId || !ptrForm.professionalTaxReceipt) return;

		updateProfessionalTaxReceipt({
			medicalCertificateId,
			data: {
				professionalTaxReceipt: ptrForm.professionalTaxReceipt,
			},
		});

		// Reset form and close dialog
		setShowPtrForm(false);
	};

	const selectedCertificates = medicalCertificate?.medicalCertificateItems?.map(
		(cert: IMedicalCertificateItem) => cert.description
	);
	const otherEntry = selectedCertificates?.find((cert: string) =>
		cert?.startsWith('Other:')
	);
	const otherValueEntry = otherEntry
		? otherEntry.split('Other:')[1].trim()
		: '';

	const [otherValue, setOtherValue] = useState<string>(otherValueEntry);
	const [selectedMedical, setSelectedMedical] = useState<string[]>(
		selectedCertificates || []
	);

	const handleCheckboxChange = (option: string, checked: boolean) => {
		if (option === 'Other:') {
			if (selectedMedical.some((item) => item.startsWith('Other:'))) {
				// If "Other" is already selected, remove it and clear the input
				setSelectedMedical((prev) =>
					prev.filter((item) => !item.startsWith('Other:'))
				);
				setOtherValue(''); // Clear "Other" input
			} else {
				// Add "Other" checkbox only if there is an input value
				setSelectedMedical((prev) => [...prev, `Other: ${otherValue}`]);
			}
		} else {
			// For other checkboxes, add/remove based on the selection
			setSelectedMedical((prev) =>
				prev.includes(option)
					? prev.filter((item) => item !== option)
					: [...prev, option]
			);
		}

		if (checked) {
			const isExisting = medicalCertificate?.medicalCertificateItems?.find(
				(cert: IMedicalCertificateItem) => cert.description === option
			);

			if (isExisting) return;

			handleAddItem(option);
		} else {
			const findedItem = medicalCertificate?.medicalCertificateItems?.find(
				(cert: IMedicalCertificateItem) => cert.description === option
			);

			const itemId = findedItem?.id;

			if (!itemId) return;
			handleRemoveItem(itemId);
		}
	};

	const handleOtherInputChange = (
		e: React.ChangeEvent<HTMLTextAreaElement>
	) => {
		const value = e.target.value;
		setOtherValue(value);

		// Update the "Other:" item without duplicating it
		setSelectedMedical((prev) => {
			const filtered = prev.filter((item) => !item.startsWith('Other:'));

			// If there's a value in the input, add "Other: {value}", otherwise just return filtered
			return value.trim() ? [...filtered, `Other: ${value}`] : filtered;
		});
	};

	useEffect(() => {
		setSelectedMedical(selectedCertificates || []);
	}, [medicalCertificate]);

	if (!medicalCertificate) {
		return (
			<Dialog open={open} onOpenChange={onOpenChange}>
				<DialogContent className="max-w-4xl">
					<DialogHeader>
						<DialogTitle>Medical Certificate Details</DialogTitle>
						<DialogDescription>Medical certificate not found</DialogDescription>
					</DialogHeader>
					<Alert>
						<AlertDescription>
							The medical certificate could not be found.
						</AlertDescription>
					</Alert>
				</DialogContent>
			</Dialog>
		);
	}

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
				<DialogHeader>
					<DialogTitle>Medical Certificate Details</DialogTitle>
					<DialogDescription>
						View and manage medical certificate items
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-6">
					{/* Patient Information */}
					<Card>
						<CardHeader>
							<div className="flex justify-between">
								<CardTitle className="flex items-center gap-2">
									<User className="h-5 w-5" />
									Patient Information
								</CardTitle>
								<Button
									onClick={() => onPrint(medicalCertificateId)}
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
								>
									<Printer className="mr-2 h-4 w-4" />
									Print Medical Certificate
								</Button>
							</div>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
								<div className="space-y-2">
									<Label className="text-sm font-medium text-gray-600">
										Name
									</Label>
									<p className="text-sm">{medicalCertificate.patient_name}</p>
								</div>
								<div className="space-y-2">
									<Label className="text-sm font-medium text-gray-600">
										Gender
									</Label>
									<p className="text-sm">{medicalCertificate.patient_gender}</p>
								</div>
								<div className="space-y-2">
									<Label className="text-sm font-medium text-gray-600">
										Phone
									</Label>
									<p className="flex items-center gap-1 text-sm">
										<Phone className="h-4 w-4" />
										{medicalCertificate.patient_phone}
									</p>
								</div>
								<div className="space-y-2">
									<Label className="text-sm font-medium text-gray-600">
										Birthdate
									</Label>
									<p className="flex items-center gap-1 text-sm">
										<Calendar className="h-4 w-4" />
										{format(
											new Date(medicalCertificate.patient_birthdate),
											'MMM dd, yyyy'
										)}
									</p>
								</div>
								<div className="space-y-2">
									<Label className="text-sm font-medium text-gray-600">
										Address
									</Label>
									<p className="flex items-start gap-1 text-sm">
										<MapPin className="mt-0.5 h-4 w-4" />
										{medicalCertificate.patient_address}
									</p>
								</div>
								<div>
									<Label className="text-sm font-medium text-gray-600">
										Professional Tax Receipt
									</Label>
									<div className="flex items-center gap-2">
										<p className="text-sm">
											{medicalCertificate?.professional_tax_receipt || '---'}
										</p>
										<Button
											variant={'ghost'}
											onClick={() => {
												setShowPtrForm(true);
											}}
											size="sm"
										>
											<Pencil />
											Edit
										</Button>
									</div>
								</div>
							</div>
						</CardContent>
					</Card>
					{/* Ptr Form Dialog */}
					<Dialog
						open={showPtrForm}
						onOpenChange={(value) => {
							if (!value) {
								setShowPtrForm(false);
							}
						}}
					>
						<DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
							<DialogHeader>
								<DialogTitle>Edit Professional Tax Receipt</DialogTitle>
								<DialogDescription>
									Update the professional tax receipt number for this
									appointment.
								</DialogDescription>
							</DialogHeader>

							<div className="space-y-6">
								{/* Ptr Information */}
								<div className="space-y-4">
									<div className="space-y-2">
										<Label htmlFor="professionalTaxReceipt">
											Professional Tax Receipt *
										</Label>
										<Input
											id="professionalTaxReceipt"
											value={ptrForm.professionalTaxReceipt}
											onChange={(e) =>
												setPtrForm((prev) => ({
													...prev,
													professionalTaxReceipt: e.target.value,
												}))
											}
											placeholder="Enter professional tax receipt number"
										/>
									</div>
								</div>
							</div>

							<DialogFooter>
								<Button
									variant="outline"
									onClick={() => {
										setShowPtrForm(false);
									}}
								>
									Cancel
								</Button>
								<Button
									onClick={handleSubmitPtr}
									disabled={!ptrForm.professionalTaxReceipt}
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
								>
									Save
								</Button>
							</DialogFooter>
						</DialogContent>
					</Dialog>

					{/* Create Dialog */}
					<Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
						<DialogContent>
							<DialogHeader>
								<DialogTitle>Add Certificate Item</DialogTitle>
								<DialogDescription>
									Create a new certificate item entry.
								</DialogDescription>
							</DialogHeader>
							<div className="grid gap-4">
								<div className="grid gap-2">
									<Label htmlFor="name">Name</Label>
									<Input
										id="name"
										value={formData.name}
										onChange={(e) =>
											setFormData({ ...formData, name: e.target.value })
										}
										placeholder="Enter name"
									/>
								</div>
							</div>
							<DialogFooter>
								<Button
									variant="outline"
									onClick={() => setShowCreateDialog(false)}
								>
									Cancel
								</Button>
								<Button onClick={handleCreate} disabled={!formData.name.trim()}>
									Create
								</Button>
							</DialogFooter>
						</DialogContent>
					</Dialog>

					{/* Medical Certificate Items */}
					<Card>
						<CardHeader>
							<div className="flex justify-between">
								<CardTitle className="flex items-center justify-between">
									<span>Certificate Items</span>
									{/* <Button
									onClick={() => setIsAddingItem(true)}
									size="sm"
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.6448_0.1256_202.74)]"
								>
									<Plus className="mr-2 h-4 w-4" />
									Add Item
								</Button> */}
								</CardTitle>
								<Button type="button" onClick={() => setShowCreateDialog(true)}>
									<Plus /> Add Item
								</Button>
							</div>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="flex w-full flex-col justify-center space-y-1">
								{medicalDescriptions.map(
									(option: IMedicalDescription, index: number) => (
										<div key={index} className="flex flex-col">
											<div className="flex items-center space-x-2 hover:opacity-80">
												<Checkbox
													disabled={
														option?.name === 'Other:' &&
														otherValue.trim() === ''
													}
													checked={
														option?.name === 'Other:'
															? selectedMedical.some((item) =>
																	item.startsWith('Other:')
																)
															: selectedMedical.includes(option?.name)
													}
													onCheckedChange={(checked: boolean) =>
														handleCheckboxChange(option?.name, checked)
													}
												>
													{option?.name}
												</Checkbox>
												<label className="text-sm">{option?.name}</label>

												{/* Render input inline with the checkbox if "Other:" is selected */}
											</div>
											<div>
												{option?.name === 'Other:' && (
													<Textarea
														className="mt-2"
														value={otherValue}
														onChange={handleOtherInputChange}
														placeholder="Enter other details"
													/>
												)}
											</div>
										</div>
									)
								)}
							</div>
							{/* Add new item form */}
							{/* {isAddingItem && (
								<div className="rounded-lg border bg-gray-50 p-4">
									<div className="space-y-3">
										<Label htmlFor="newItemDescription">Description</Label>
										<Textarea
											id="newItemDescription"
											value={newItemDescription}
											onChange={(e) => setNewItemDescription(e.target.value)}
											placeholder="Enter certificate item description"
											rows={3}
										/>
										<div className="flex gap-2">
											<Button
												onClick={handleAddItem}
												disabled={!newItemDescription.trim() || isCreatingItem}
												size="sm"
												className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.6448_0.1256_202.74)]"
											>
												{isCreatingItem ? 'Adding...' : 'Add Item'}
											</Button>
											<Button
												onClick={() => {
													setIsAddingItem(false);
													setNewItemDescription('');
												}}
												variant="outline"
												size="sm"
											>
												Cancel
											</Button>
										</div>
									</div>
								</div>
							)} */}

							{/* Existing items */}
							{/* {medicalCertificate.medicalCertificateItems &&
							medicalCertificate.medicalCertificateItems.length > 0 ? (
								<div className="space-y-3">
									{medicalCertificate.medicalCertificateItems.map(
										(item: IMedicalCertificateItem) => (
											<div
												key={item.id}
												className="flex items-start justify-between rounded-lg border p-3"
											>
												<div className="flex-1">
													<p className="text-sm">{item.description}</p>
													<p className="mt-1 text-xs text-gray-500">
														Added:{' '}
														{format(new Date(item.created_at), 'MMM dd, yyyy')}
													</p>
												</div>
												<Button
													onClick={() => handleRemoveItem(item.id)}
													variant="outline"
													size="sm"
													disabled={isRemovingItem}
												>
													<Trash2 className="h-4 w-4" />
												</Button>
											</div>
										)
									)}
								</div>
							) : (
								<div className="py-8 text-center text-gray-500">
									<p>No certificate items added yet.</p>
									<p className="text-sm">
										Click &quot;Add Item&quot; to get started.
									</p>
								</div>
							)} */}
						</CardContent>
					</Card>
				</div>
			</DialogContent>
		</Dialog>
	);
}
