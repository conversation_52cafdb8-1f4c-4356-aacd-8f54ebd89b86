'use client';

import { useRef } from 'react';
import { useReactToPrint } from 'react-to-print';

import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { useMedicalCertificateDetail } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';

import { MedicalCertificatePrint } from '../../shared/role-aware-medical-certificate-print';

interface IMedicalCertificatePrintDialogProps {
	medicalCertificateId: number;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onClose: () => void;
}

export function MedicalCertificatePrintDialog({
	medicalCertificateId,
	open,
	onOpenChange,
}: Omit<IMedicalCertificatePrintDialogProps, 'onClose'>) {
	// Fetch medicalCertificate details
	const { data: medicalCertificateResponse } = useMedicalCertificateDetail(
		medicalCertificateId,
		{
			enabled: open,
		}
	);

	const medicalCertificate = medicalCertificateResponse?.data;

	const componentRef = useRef<HTMLDivElement | null>(null);
	const handlePrint = useReactToPrint({
		contentRef: componentRef,
		documentTitle: 'Print This Document',
		onBeforePrint: async () => console.log('before printing...'),
		onAfterPrint: () => console.log('after printing...'),
	});

	const handleSubmit = async () => {
		handlePrint();
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogHeader className="hidden">
				<DialogTitle>Medical Certificate Details</DialogTitle>
				<DialogDescription>
					Print medical Certificate #{medicalCertificateId}
				</DialogDescription>
			</DialogHeader>
			<DialogContent className="max-w-2xl">
				{medicalCertificate && (
					<MedicalCertificatePrint
						data={medicalCertificate}
						componentRef={componentRef}
					/>
				)}
				<Button onClick={handleSubmit}>Print</Button>
			</DialogContent>
		</Dialog>
	);
}
