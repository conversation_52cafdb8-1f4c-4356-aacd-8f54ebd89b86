'use client';

import { format } from 'date-fns';
import {
	Calendar,
	CheckCircle,
	ChevronDown,
	Clock,
	Edit,
	FileText,
	MapPin,
	Pencil,
	Phone,
	Play,
	Plus,
	Trash2,
	User,
} from 'lucide-react';
import Link from 'next/link';
import { useQueryState } from 'nuqs';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { searchParamParsers } from '@/core/lib/search-params';
import {
	useAddDoctorNote,
	useAddMedicalRecord,
	useAppointmentDetail,
	useCreateDiagnosticRequest,
	useCreateLabRequest,
	useCreateMedicalCertificate,
	useCreatePrescription,
	useCreateReferral,
	useRemoveDiagnosticRequest,
	useRemoveLabRequest,
	useRemoveMedicalCertificate,
	useRemoveMedicalRecord,
	useRemovePrescription,
	useRemoveReferral,
	useUpdateAppointmentDetails,
	useUpdateAppointmentPtr,
	useUpdateAppointmentStatus,
	useUpdateVitalSigns,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import {
	EAppointmentStatus,
	IAssistantNote,
	IDiagnosticRequest,
	IDoctorNote,
	ILabRequest,
	IMedicalCertificate,
	IPrescription,
	IReferral,
} from '@/features/dashboard/types/doctor.types';

import { useUserRole } from '../../hooks/useRoleAwareDashboard';
// Dialog imports
import { DiagnosticRequestDetailDialog } from './diagnostic-request/diagnostic-request-detail-dialog';
import { DiagnosticRequestPrintDialog } from './diagnostic-request/diagnostic-request-print-dialog';
import { LabRequestDetailDialog } from './lab-request/lab-request-detail-dialog';
import { LabRequestPrintDialog } from './lab-request/lab-request-print-dialog';
import { MedicalCertificateDetailDialog } from './medical-certificate/medical-certificate-detail-dialog';
import { MedicalCertificatePrintDialog } from './medical-certificate/medical-certificate-print-dialog';
import { PrescriptionDetailDialog } from './prescription/prescription-detail-dialog';
import { PrescriptionPrintDialog } from './prescription/prescription-print-dialog';
import { ReferralDetailDialog } from './referral/referral-detail-dialog';
import { ReferralPrintDialog } from './referral/referral-print-dialog';
import { RescheduleAppointmentDialog } from './reschedule-appointment-dialog';

// Print components (will be imported when used)
// import { DiagnosticRequestPrint } from '@/components/print/diagnostic-request-print';
// import { LabRequestPrint } from '@/components/print/lab-request-print';
// import { MedicalCertificatePrint } from '@/components/print/medical-certificate-print';
// import { PrescriptionPrint } from '@/components/print/prescription-print';
// import { ReferralPrint } from '@/components/print/referral-print';
interface IAppointmentDetailsPageProps {
	appointmentId: number;
}

const getStatusBadge = (status: string) => {
	const statusConfig = {
		[EAppointmentStatus.CREATED]: {
			variant: 'secondary' as const,
			label: 'Created',
			className: '',
		},
		[EAppointmentStatus.CONFIRMED]: {
			variant: 'default' as const,
			label: 'Confirmed',
			className: '',
		},
		[EAppointmentStatus.DECLINED]: {
			variant: 'destructive' as const,
			label: 'Declined',
			className: '',
		},
		[EAppointmentStatus.WAITING]: {
			variant: 'outline' as const,
			label: 'Waiting',
			className: 'border-yellow-500 text-yellow-700',
		},
		[EAppointmentStatus.ONGOING]: {
			variant: 'default' as const,
			label: 'Ongoing',
			className: 'bg-green-100 text-green-800 border-green-300 animate-pulse',
		},
		[EAppointmentStatus.COMPLETED]: {
			variant: 'default' as const,
			label: 'Completed',
			className: 'bg-gray-100 text-gray-700',
		},
		[EAppointmentStatus.FOLLOW_UP]: {
			variant: 'secondary' as const,
			label: 'Follow-up',
			className: '',
		},
		[EAppointmentStatus.NO_SHOW]: {
			variant: 'destructive' as const,
			label: 'No Show',
			className: '',
		},
		[EAppointmentStatus.RESCHEDULED]: {
			variant: 'outline' as const,
			label: 'Rescheduled',
			className: '',
		},
		[EAppointmentStatus.CANCELLED]: {
			variant: 'destructive' as const,
			label: 'Cancelled',
			className: '',
		},
	};

	const config = statusConfig[status as EAppointmentStatus] || {
		variant: 'secondary' as const,
		label: status,
		className: '',
	};

	return (
		<div className="flex items-center gap-2">
			<Badge variant={config.variant} className={config.className}>
				{status === EAppointmentStatus.ONGOING && (
					<div className="mr-1 h-2 w-2 animate-pulse rounded-full bg-green-500" />
				)}
				{config.label}
			</Badge>
			{status === EAppointmentStatus.ONGOING && (
				<span className="text-sm font-medium text-green-600">In Progress</span>
			)}
		</div>
	);
};

export function AppointmentDetailsPage({
	appointmentId,
}: IAppointmentDetailsPageProps) {
	const role = useUserRole();

	const [, setAppointmentId] = useQueryState(
		'appointmentId',
		searchParamParsers.appointmentId
	);

	const [activeTab, setActiveTab] = useState('overview');
	const [doctorNote, setDoctorNote] = useState('');
	// Vital signs state
	const [vitalSigns, setVitalSigns] = useState({
		systolic: '',
		diastolic: '',
		pulseRate: '',
		respiration: '',
		height: '',
		heightType: 'cm',
		weight: '',
		weightType: 'kg',
		temperature: '',
		temperatureType: 'C',
		oxygenSaturation: '',
		capillaryBloodGlucose: '',
		bodyMassIndex: '',
	});

	// Medical information state
	const [medicalInfo, setMedicalInfo] = useState({
		chiefComplaint: '',
		diagnosis: '',
		prognosis: '',
	});

	// Other states for different tabs
	const [prescriptions, setPrescriptions] = useState<IPrescription[]>([]);
	const [appointmentHistory, setAppointmentHistory] = useState<
		{
			id: number;
			visit_reason_id: number;
			status: string;
			appointment_reference_number: string;
			appointment_date: string;
			visitReason: {
				id: number;
				name: string;
				description: string;
				is_deleted: number;
				created_at: string;
				updated_at: string;
			};
		}[]
	>([]);
	const [labRequests, setLabRequests] = useState<ILabRequest[]>([]);
	const [diagnosticRequests, setDiagnosticRequests] = useState<
		IDiagnosticRequest[]
	>([]);
	const [referrals, setReferrals] = useState<IReferral[]>([]);
	const [medicalCertificates, setMedicalCertificates] = useState<
		IMedicalCertificate[]
	>([]);

	// Dialog states for editing
	const [editPrescriptionDialog, setEditPrescriptionDialog] = useState<{
		open: boolean;
		prescription: IPrescription | null;
	}>({ open: false, prescription: null });

	const [editLabRequestDialog, setEditLabRequestDialog] = useState<{
		open: boolean;
		labRequest: ILabRequest | null;
	}>({ open: false, labRequest: null });

	const [editReferralDialog, setEditReferralDialog] = useState<{
		open: boolean;
		referral: IReferral | null;
	}>({ open: false, referral: null });

	const [editDiagnosticRequestDialog, setEditDiagnosticRequestDialog] =
		useState<{
			open: boolean;
			diagnosticRequest: IDiagnosticRequest | null;
		}>({ open: false, diagnosticRequest: null });

	const [editMedicalCertificateDialog, setEditMedicalCertificateDialog] =
		useState<{
			open: boolean;
			medicalCertificate: IMedicalCertificate | null;
		}>({ open: false, medicalCertificate: null });

	// Dialog state for print
	const [printPrescriptionDialog, setPrintPrescriptionDialog] = useState<{
		open: boolean;
		prescription: IPrescription | null;
	}>({ open: false, prescription: null });

	const [printLabRequestDialog, setPrintLabRequestDialog] = useState<{
		open: boolean;
		labRequest: ILabRequest | null;
	}>({ open: false, labRequest: null });

	const [printDiagnosticRequestDialog, setPrintDiagnosticRequestDialog] =
		useState<{
			open: boolean;
			diagnosticRequest: IDiagnosticRequest | null;
		}>({ open: false, diagnosticRequest: null });

	const [printReferralDialog, setPrintReferralDialog] = useState<{
		open: boolean;
		referral: IReferral | null;
	}>({ open: false, referral: null });

	const [printMedicalCertificateDialog, setPrintMedicalCertificateDialog] =
		useState<{
			open: boolean;
			medicalCertificate: IMedicalCertificate | null;
		}>({ open: false, medicalCertificate: null });

	// Print functionality will be implemented with react-to-print in the future

	// Referral form state
	const [showReferralForm, setShowReferralForm] = useState(false);
	const [referralForm, setReferralForm] = useState({
		doctorName: '',
		purpose: '',
	});

	const [showPtrForm, setShowPtrForm] = useState(false);
	const [ptrForm, setPtrForm] = useState({
		professionalTaxReceipt: '',
	});

	const [showMedicalRecordForm, setShowMedicalRecordForm] = useState(false);
	const [medicalRecordFile, setMedicalRecordFile] = useState<File | null>(null);

	const {
		data: appointment,
		isLoading,
		error,
	} = useAppointmentDetail(appointmentId, {
		enabled: !!appointmentId,
	});

	const { mutate: addNote, isPending: isAddingNote } = useAddDoctorNote();

	// Mutation hooks for medical documents
	const { mutate: updateVitals } = useUpdateVitalSigns();
	const { mutate: updateStatus } = useUpdateAppointmentStatus();
	const { mutate: updateDetailsMutation, isPending: isUpdatingDetails } =
		useUpdateAppointmentDetails();
	const { mutate: createPrescription } = useCreatePrescription();
	const { mutate: createLabRequest } = useCreateLabRequest();
	const { mutate: createDiagnosticRequest } = useCreateDiagnosticRequest();
	const { mutate: createReferral } = useCreateReferral();
	const { mutate: createMedicalCertificate } = useCreateMedicalCertificate();
	const { mutate: addMedicalRecordMutation } = useAddMedicalRecord();

	// Remove mutation hooks
	const { mutate: removePrescription } = useRemovePrescription();
	const { mutate: removeLabRequest } = useRemoveLabRequest();
	const { mutate: removeDiagnosticRequest } = useRemoveDiagnosticRequest();
	const { mutate: removeReferral } = useRemoveReferral();
	const { mutate: removeMedicalCertificate } = useRemoveMedicalCertificate();
	const { mutate: removeMedicalRecordMutation } = useRemoveMedicalRecord();
	const { mutate: updateProfessionalTaxReceipt } = useUpdateAppointmentPtr();

	// Load appointment data and populate states
	React.useEffect(() => {
		if (appointment) {
			// Populate states with appointment data
			setPrescriptions(appointment.prescriptions || []);
			setLabRequests(appointment.labRequests || []);
			setDiagnosticRequests(appointment.diagnosticRequests || []);
			setReferrals(appointment.referrals || []);
			setMedicalCertificates(appointment.medicalCertificates || []);
			setAppointmentHistory(appointment.appointmentHistories || []);
			setPtrForm({
				professionalTaxReceipt: appointment.professional_tax_receipt || '',
			});
			// Populate vital signs if available
			if (appointment.vitalSign) {
				setVitalSigns({
					systolic: appointment.vitalSign.systolic || '',
					diastolic: appointment.vitalSign.diastolic || '',
					pulseRate: appointment.vitalSign.pulse_rate || '',
					respiration: appointment.vitalSign.respiration || '',
					height: appointment.vitalSign.height || '',
					heightType: appointment.vitalSign.height_type || 'cm',
					weight: appointment.vitalSign.weight || '',
					weightType: appointment.vitalSign.weight_type || 'kg',
					temperature: appointment.vitalSign.temperature || '',
					temperatureType: appointment.vitalSign.temperature_type || 'C',
					oxygenSaturation: appointment.vitalSign.oxygen_saturation || '',
					capillaryBloodGlucose:
						appointment.vitalSign.capillary_blood_glucose || '',
					bodyMassIndex: appointment.vitalSign.body_mass_index || '',
				});
			}

			// Populate medical information
			setMedicalInfo({
				chiefComplaint: appointment.chief_complaint || '',
				diagnosis: appointment.diagnosis || '',
				prognosis: appointment.prognosis || '',
			});
		}
	}, [appointment]);

	const handleAddNote = () => {
		if (!appointmentId || !doctorNote.trim()) return;

		addNote({
			appointmentId,
			note: doctorNote,
		});

		setDoctorNote('');
	};

	// const handleBack = () => {
	// 	setAppointmentId(null);
	// };

	// Vital signs handlers
	const handleSaveVitals = () => {
		if (!appointmentId) return;

		updateVitals({
			appointmentId,
			vitalSigns,
		});
	};

	// Medical info handlers
	const handleUpdateMedicalInfo = () => {
		if (!appointmentId) return;

		updateDetailsMutation({
			appointmentId,
			data: medicalInfo,
		});
	};

	// Status update handlers
	const handleStatusUpdate = (status: string) => {
		if (!appointmentId) return;

		updateStatus({
			appointmentId,
			status,
		});
	};

	const handleStartAppointment = () => {
		handleStatusUpdate(EAppointmentStatus.ONGOING);
	};

	const handleCompleteAppointment = () => {
		// First save medical info if there are changes
		if (
			medicalInfo.chiefComplaint ||
			medicalInfo.diagnosis ||
			medicalInfo.prognosis
		) {
			handleUpdateMedicalInfo();
		}
		// Then mark as completed
		handleStatusUpdate(EAppointmentStatus.COMPLETED);
	};

	// Prescription handlers
	const handleAddPrescription = () => {
		if (!appointmentId || !appointment) return;

		const prescriptionData = {
			patient_name: appointment.patient_name || '',
			patient_address: appointment.patient_address || '',
			patient_phone: appointment.patient_phone || '',
			patient_gender: appointment.patient_gender || '',
			patient_birthdate: appointment.patient_birthdate || '',
			professional_tax_receipt: appointment.professional_tax_receipt || '',
		};

		createPrescription({
			appointmentId,
			prescriptionData,
		});
	};

	const handleEditPrescription = (prescriptionId: number) => {
		const prescription = prescriptions.find((p) => p.id === prescriptionId);
		if (prescription) {
			setEditPrescriptionDialog({ open: true, prescription });
		}
	};

	const handleDeletePrescription = (prescriptionId: number) => {
		if (!appointmentId) return;

		removePrescription({
			appointmentId,
			prescriptionId,
		});
	};

	// Print handlers
	const handlePrintPrescription = (prescriptionId?: number) => {
		if (prescriptionId) {
			const prescription = prescriptions.find((p) => p.id === prescriptionId);
			if (prescription) {
				setPrintPrescriptionDialog({ open: true, prescription });
			}
		}
	};

	const handlePrintLabRequest = (labRequestId: number) => {
		if (!labRequestId) return;

		const labRequest = labRequests.find((lr) => lr.id === labRequestId);
		if (labRequest) {
			setPrintLabRequestDialog({ open: true, labRequest });
		}
	};

	const handlePrintDiagnosticRequest = (diagnosticRequestId: number) => {
		if (!diagnosticRequestId) return;

		const diagnosticRequest = diagnosticRequests.find(
			(dr) => dr.id === diagnosticRequestId
		);
		if (diagnosticRequest) {
			setPrintDiagnosticRequestDialog({ open: true, diagnosticRequest });
		}
	};

	const handlePrintReferral = (referralId: number) => {
		if (!referralId) return;

		const referral = referrals.find((r) => r.id === referralId);
		if (referral) {
			setPrintReferralDialog({ open: true, referral });
		}
	};

	const handlePrintMedicalCertificate = (medicalCertificateId: number) => {
		if (!medicalCertificateId) return;

		const medicalCertificate = medicalCertificates.find(
			(mc) => mc.id === medicalCertificateId
		);
		if (medicalCertificate) {
			setPrintMedicalCertificateDialog({ open: true, medicalCertificate });
		}
	};

	// Lab request handlers
	const handleAddLabRequest = () => {
		if (!appointmentId || !appointment) return;

		const labRequestData = {
			patient_name: appointment.patient_name || '',
			patient_address: appointment.patient_address || '',
			patient_phone: appointment.patient_phone || '',
			patient_gender: appointment.patient_gender || '',
			patient_birthdate: appointment.patient_birthdate || '',
		};

		createLabRequest({
			appointmentId,
			labRequestData,
		});
	};

	const handleEditLabRequest = (labRequestId: number) => {
		const labRequest = labRequests.find((lr) => lr.id === labRequestId);
		if (labRequest) {
			setEditLabRequestDialog({ open: true, labRequest });
		}
	};

	const handleDeleteLabRequest = (labRequestId: number) => {
		if (!appointmentId) return;

		removeLabRequest({
			appointmentId,
			labRequestId,
		});
	};

	// Diagnostic request handlers
	const handleAddDiagnosticRequest = () => {
		if (!appointmentId || !appointment) return;

		const diagnosticRequestData = {
			patient_name: appointment.patient_name || '',
			patient_address: appointment.patient_address || '',
			patient_phone: appointment.patient_phone || '',
			patient_gender: appointment.patient_gender || '',
			patient_birthdate: appointment.patient_birthdate || '',
		};

		createDiagnosticRequest({
			appointmentId,
			diagnosticRequestData,
		});
	};

	const handleEditDiagnosticRequest = (diagnosticRequestId: number) => {
		const diagnosticRequest = diagnosticRequests.find(
			(dr) => dr.id === diagnosticRequestId
		);
		if (diagnosticRequest) {
			setEditDiagnosticRequestDialog({ open: true, diagnosticRequest });
		}
	};

	const handleDeleteDiagnosticRequest = (diagnosticRequestId: number) => {
		if (!appointmentId) return;

		removeDiagnosticRequest({
			appointmentId,
			diagnosticRequestId,
		});
	};

	// Referral handlers
	const handleAddReferral = () => {
		if (!appointmentId || !appointment) return;

		// Open the referral form dialog
		setShowReferralForm(true);
	};

	const handleSubmitReferral = () => {
		if (
			!appointmentId ||
			!appointment ||
			!referralForm.doctorName ||
			!referralForm.purpose
		)
			return;

		const referralData = {
			patient_name: appointment.patient_name || '',
			patient_address: appointment.patient_address || '',
			patient_phone: appointment.patient_phone || '',
			patient_gender: appointment.patient_gender || '',
			patient_birthdate: appointment.patient_birthdate || '',
			professional_tax_receipt: appointment.professional_tax_receipt || '',
			doctorName: referralForm.doctorName,
			purpose: referralForm.purpose,
		};

		createReferral({
			appointmentId,
			referralData,
		});

		// Reset form and close dialog
		setReferralForm({ doctorName: '', purpose: '' });
		setShowReferralForm(false);
	};

	const handleEditReferral = (referralId: number) => {
		const referral = referrals.find((r) => r.id === referralId);
		if (referral) {
			setEditReferralDialog({ open: true, referral });
		}
	};

	const handleDeleteReferral = (referralId: number) => {
		removeReferral({
			referralId,
		});
	};

	// Medical certificate handlers
	const handleAddMedicalCertificate = () => {
		if (!appointmentId) return;

		createMedicalCertificate(appointmentId);
	};

	const handleEditMedicalCertificate = (certificateId: number) => {
		const certificate = medicalCertificates.find((c) => c.id === certificateId);
		if (certificate) {
			setEditMedicalCertificateDialog({
				open: true,
				medicalCertificate: certificate,
			});
		}
	};

	const handleDeleteMedicalCertificate = (certificateId: number) => {
		if (!appointmentId) return;

		removeMedicalCertificate({
			appointmentId,
			medicalCertificateId: certificateId,
		});
	};

	const handleAddMedicalRecord = () => {
		if (!appointmentId || !medicalRecordFile) return;

		addMedicalRecordMutation({
			appointmentId,
			recordFile: medicalRecordFile,
		});

		setMedicalRecordFile(null);
		setShowMedicalRecordForm(false);
	};

	const handleRemoveMedicalRecord = (medicalRecordId: number) => {
		if (!appointmentId) return;

		removeMedicalRecordMutation({
			appointmentId,
			medicalRecordId,
		});
	};

	const handleSubmitPtr = () => {
		if (!appointmentId || !appointment || !ptrForm.professionalTaxReceipt)
			return;

		updateProfessionalTaxReceipt({
			appointmentId,
			data: {
				professionalTaxReceipt: ptrForm.professionalTaxReceipt,
			},
		});

		// Reset form and close dialog
		setShowPtrForm(false);
	};

	if (isLoading) {
		return (
			<div className="flex items-center justify-center p-8">
				<div className="text-center">
					<div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-[oklch(0.7448_0.1256_202.74)]"></div>
					<p className="text-muted-foreground">
						Loading appointment details...
					</p>
				</div>
			</div>
		);
	}

	if (error || !appointment) {
		return (
			<div className="flex items-center justify-center p-8">
				<div className="text-center">
					<FileText className="text-muted-foreground mx-auto mb-4 h-8 w-8" />
					<p className="text-muted-foreground">
						Failed to load appointment details
					</p>
				</div>
			</div>
		);
	}

	const formatTime = (dateString: string) => {
		return new Date(dateString).toLocaleTimeString('en-US', {
			hour: '2-digit',
			minute: '2-digit',
		});
	};

	const formatDate = (dateString: string) => {
		return format(new Date(dateString), 'EEEE, MMMM d, yyyy');
	};

	const handleViewHistory = (appointmentId: number) => {
		if (!appointmentId) return;

		setAppointmentId(appointmentId);
	};

	return (
		<div className="flex flex-col gap-4 px-4">
			<div className="mb-4 flex flex-col items-start justify-between gap-4 lg:flex-row">
				<div>
					<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
						Appointment Details
					</h1>
					<p className="text-muted-foreground">
						View and manage appointment information and medical records.
					</p>
				</div>
				<RescheduleAppointmentDialog
					appointment={appointment}
					trigger={
						<Button variant="outline">
							<Clock className="mr-2 h-4 w-4" />
							Reschedule
						</Button>
					}
				/>
			</div>

			{/* Overview Cards */}
			<div className="grid gap-6 md:grid-cols-2">
				{/* Patient Information */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<User className="h-4 w-4" />
							Patient Information
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div>
							<p className="font-medium">
								{appointment.patient?.profile?.first_name}{' '}
								{appointment.patient?.profile?.last_name}
							</p>
							<div className="text-muted-foreground flex items-center gap-2 text-sm">
								<Phone className="h-3 w-3" />
								{appointment.patient?.profile?.phone || '---'}
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Clinic Information */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<MapPin className="h-4 w-4" />
							Clinic Information
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div>
							<p className="font-medium">{appointment.clinic?.name}</p>
							<p className="text-muted-foreground text-sm">
								{appointment.clinic?.address}
							</p>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Appointment Details */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Calendar className="h-4 w-4" />
						Appointment Details
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid gap-6 md:grid-cols-3">
						<div>
							<Label className="text-sm font-medium">Date</Label>
							<p className="text-sm">
								{formatDate(appointment.appointment_date)}
							</p>
						</div>
						<div>
							<Label className="text-sm font-medium">Time</Label>
							<div className="flex items-center gap-2">
								<Clock className="h-3 w-3" />
								<span className="text-sm">
									{formatTime(appointment.appointment_date)}
								</span>
							</div>
						</div>
						<div>
							<Label className="text-sm font-medium">Status & Actions</Label>
							<div className="mt-1 space-y-3">
								{getStatusBadge(appointment.status)}

								{/* Status Action Buttons */}
								<div className="flex flex-wrap gap-2">
									{appointment.status === EAppointmentStatus.WAITING && (
										<Button
											onClick={handleStartAppointment}
											size="sm"
											className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.6448_0.1256_202.74)]"
										>
											<Play className="mr-1 h-3 w-3" />
											Start Appointment
										</Button>
									)}

									{appointment.status === EAppointmentStatus.ONGOING && (
										<Button
											onClick={handleCompleteAppointment}
											size="sm"
											className="bg-green-600 hover:bg-green-700"
										>
											<CheckCircle className="mr-1 h-3 w-3" />
											Complete Appointment
										</Button>
									)}

									{/* Status Update Dropdown */}
									{appointment.status !== EAppointmentStatus.COMPLETED && (
										<DropdownMenu>
											<DropdownMenuTrigger asChild>
												<Button variant="outline" size="sm">
													Update Status
													<ChevronDown className="ml-1 h-3 w-3" />
												</Button>
											</DropdownMenuTrigger>
											<DropdownMenuContent align="start">
												<DropdownMenuItem
													onClick={() =>
														handleStatusUpdate(EAppointmentStatus.CONFIRMED)
													}
												>
													Mark as Confirmed
												</DropdownMenuItem>
												<DropdownMenuItem
													onClick={() =>
														handleStatusUpdate(EAppointmentStatus.DECLINED)
													}
												>
													Mark as Declined
												</DropdownMenuItem>
												<DropdownMenuItem
													onClick={() =>
														handleStatusUpdate(EAppointmentStatus.NO_SHOW)
													}
												>
													Mark as No Show
												</DropdownMenuItem>
												<DropdownMenuItem
													onClick={() =>
														handleStatusUpdate(EAppointmentStatus.WAITING)
													}
												>
													Mark as Waiting
												</DropdownMenuItem>
												<DropdownMenuItem
													onClick={() =>
														handleStatusUpdate(EAppointmentStatus.ONGOING)
													}
												>
													Mark as Ongoing
												</DropdownMenuItem>
												<DropdownMenuItem
													onClick={() =>
														handleStatusUpdate(EAppointmentStatus.COMPLETED)
													}
												>
													Mark as Completed
												</DropdownMenuItem>
												<DropdownMenuSeparator />
												<DropdownMenuItem
													onClick={() =>
														handleStatusUpdate(EAppointmentStatus.CANCELLED)
													}
													className="text-destructive"
												>
													Cancel Appointment
												</DropdownMenuItem>
											</DropdownMenuContent>
										</DropdownMenu>
									)}
								</div>
							</div>
						</div>
						<div>
							<Label className="text-sm font-medium">Visit Reason</Label>
							<p className="text-sm">
								{appointment.visitReason?.name || '---'}
							</p>
						</div>
						<div>
							<Label className="text-sm font-medium">Consultation Type</Label>
							<p className="text-sm">
								{appointment.consultationType?.name || '---'}
							</p>
						</div>
						<div>
							<Label className="text-sm font-medium">Payment Type</Label>
							<p className="text-sm">
								{appointment.paymentType?.name || '---'}
							</p>
						</div>
						<div>
							<Label className="text-sm font-medium">
								Professional Tax Receipt
							</Label>
							<div className="flex items-center gap-2">
								<p className="text-sm">
									{appointment?.professional_tax_receipt || '---'}
								</p>
								<Button
									variant={'ghost'}
									onClick={() => {
										setShowPtrForm(true);
									}}
									size="sm"
								>
									<Pencil />
									Edit
								</Button>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Tabs for detailed information */}
			<Tabs value={activeTab} onValueChange={setActiveTab}>
				<TabsList className="grid w-full grid-cols-8">
					<TabsTrigger value="overview">Overview</TabsTrigger>
					<TabsTrigger value="vitals">Vitals</TabsTrigger>
					<TabsTrigger value="history">History</TabsTrigger>
					<TabsTrigger value="prescription">Prescription</TabsTrigger>
					<TabsTrigger value="lab-requests">Lab Requests</TabsTrigger>
					<TabsTrigger value="diagnostics">Diagnostics</TabsTrigger>
					<TabsTrigger value="referrals">Referrals</TabsTrigger>
					<TabsTrigger value="certificates">Certificates</TabsTrigger>
				</TabsList>

				<TabsContent value="overview" className="space-y-4">
					<Card>
						<CardHeader>
							<div className="flex items-center justify-between">
								<CardTitle>Medical Information</CardTitle>
							</div>
						</CardHeader>
						<CardContent>
							<div className="grid gap-4">
								<div className="space-y-2">
									<Label htmlFor="chief-complaint">Chief Complaint</Label>
									<Textarea
										id="chief-complaint"
										placeholder="Enter chief complaint..."
										value={medicalInfo.chiefComplaint}
										onChange={(e) =>
											setMedicalInfo((prev) => ({
												...prev,
												chiefComplaint: e.target.value,
											}))
										}
										rows={3}
									/>
								</div>
								<div className="space-y-2">
									<Label htmlFor="diagnosis">Diagnosis</Label>
									<Textarea
										id="diagnosis"
										placeholder="Enter diagnosis..."
										value={medicalInfo.diagnosis}
										onChange={(e) =>
											setMedicalInfo((prev) => ({
												...prev,
												diagnosis: e.target.value,
											}))
										}
										rows={3}
									/>
								</div>
								<div className="space-y-2">
									<Label htmlFor="prognosis">Prognosis</Label>
									<Textarea
										id="prognosis"
										placeholder="Enter prognosis..."
										value={medicalInfo.prognosis}
										onChange={(e) =>
											setMedicalInfo((prev) => ({
												...prev,
												prognosis: e.target.value,
											}))
										}
										rows={3}
									/>
								</div>
								<Button
									onClick={handleUpdateMedicalInfo}
									disabled={isUpdatingDetails}
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.6448_0.1256_202.74)]"
								>
									{isUpdatingDetails ? 'Saving...' : 'Save Medical Info'}
								</Button>
							</div>
						</CardContent>
					</Card>

					{/* Doctor Notes */}
					<Card>
						<CardHeader>
							<CardTitle>Doctor Notes</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							{/* Existing Notes */}
							{(appointment.appointmentDoctorNotes &&
								appointment.appointmentDoctorNotes.length > 0) ||
							(appointment.doctor_notes &&
								appointment.doctor_notes.length > 0) ? (
								<div className="space-y-3">
									<Label className="text-sm font-medium">Previous Notes</Label>
									<div className="space-y-2">
										{(
											appointment.appointmentDoctorNotes ||
											appointment.doctor_notes ||
											[]
										).map((note: IDoctorNote) => (
											<div
												key={note.id}
												className="rounded-lg border bg-gray-50 p-3"
											>
												<p className="text-sm">{note.note}</p>
												<p className="text-muted-foreground mt-1 text-xs">
													{format(
														new Date(note.created_at),
														'MMM dd, yyyy - h:mm a'
													)}
												</p>
											</div>
										))}
									</div>
								</div>
							) : (
								<p className="text-muted-foreground text-sm">
									No doctor notes added yet.
								</p>
							)}

							{/* Add New Note */}
							{role === 'doctor' && (
								<div className="space-y-2">
									<Label htmlFor="doctor-note">Add Note</Label>
									<Textarea
										id="doctor-note"
										placeholder="Enter your notes about this appointment..."
										value={doctorNote}
										onChange={(e) => setDoctorNote(e.target.value)}
									/>
									<Button
										onClick={handleAddNote}
										disabled={isAddingNote || !doctorNote.trim()}
									>
										<Plus className="mr-2 h-4 w-4" />
										Add Note
									</Button>
								</div>
							)}
						</CardContent>
					</Card>
					{/* Assistant Notes */}
					<Card>
						<CardHeader>
							<div className="flex items-center justify-between">
								<CardTitle className="flex items-center gap-2">
									Assistant Notes
								</CardTitle>
							</div>
						</CardHeader>
						<CardContent className="space-y-4">
							{/* Existing Notes */}
							{appointment.appointmentAssistantNotes &&
							appointment.appointmentAssistantNotes.length > 0 ? (
								<div className="space-y-3">
									{appointment.appointmentAssistantNotes.map(
										(note: IAssistantNote, index: number) => (
											<div key={index} className="rounded-lg border p-3">
												<p className="text-sm">
													{note.note} -{' '}
													<span className="font-bold">
														{[
															note?.assistant?.profile?.first_name,
															note?.assistant?.profile?.last_name,
														].join(' ')}
													</span>
												</p>
												<p className="text-muted-foreground mt-2 text-xs">
													Added:{' '}
													{format(
														new Date(note.created_at),
														'MMM dd, yyyy hh:mm aa'
													)}
												</p>
											</div>
										)
									)}
								</div>
							) : (
								<p className="text-muted-foreground text-sm">
									No assistant notes added yet.
								</p>
							)}
							{/* Add New Note */}
							{role === 'assistant' && (
								<div className="space-y-2">
									<Label htmlFor="assistant-note">Add Note</Label>
									<Textarea
										id="assistant-note"
										placeholder="Enter your notes about this appointment..."
										value={doctorNote}
										onChange={(e) => setDoctorNote(e.target.value)}
									/>
									<Button
										onClick={handleAddNote}
										disabled={isAddingNote || !doctorNote.trim()}
									>
										<Plus className="mr-2 h-4 w-4" />
										Add Note
									</Button>
								</div>
							)}
						</CardContent>
					</Card>
					{/* Medical Records */}
					<Card>
						<CardHeader>
							<div className="flex items-center justify-between">
								<CardTitle className="flex items-center gap-2">
									Medical Records
								</CardTitle>
								<Button
									variant="default"
									size="default"
									onClick={() =>
										setShowMedicalRecordForm(!showMedicalRecordForm)
									}
									className="gap-2"
								>
									<Plus className="h-4 w-4" />
									Upload Record
								</Button>
							</div>
						</CardHeader>
						<CardContent className="space-y-4">
							{/* Existing Notes */}
							<div className="">
								{appointment.medicalRecords &&
								appointment.medicalRecords.length > 0 ? (
									<div className="grid w-full grid-cols-3 gap-4">
										{appointment.medicalRecords.map((note, index) => (
											<div
												key={index}
												className="flex cursor-pointer flex-row items-center justify-between rounded-lg border p-3"
											>
												<Link
													href={note.record}
													target="_blank"
													rel="noopener noreferrer"
												>
													<div className="flex flex-row gap-2">
														<FileText className="text-primary h-10 w-10" />
														<div className="flex flex-col">
															<p className="font-bold">
																Medical Record #{index + 1}
															</p>
															<p className="text-xs">
																{format(
																	new Date(note.created_at),
																	'MMM dd, yyyy, hh:mm aa'
																)}
															</p>
														</div>
													</div>
												</Link>
												<Button
													variant="destructive"
													size="icon"
													onClick={() => handleRemoveMedicalRecord(note.id)}
												>
													<Trash2 className="h-4 w-4" />
												</Button>
											</div>
										))}
									</div>
								) : (
									<p className="text-muted-foreground text-base">
										No medical records added yet.
									</p>
								)}
							</div>
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="vitals">
					<Card>
						<CardHeader>
							<CardTitle>Vital Signs</CardTitle>
						</CardHeader>
						<CardContent className="space-y-6">
							<div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
								{/* Blood Pressure */}
								<div className="space-y-2">
									<Label className="text-sm font-medium">Blood Pressure</Label>
									<div className="flex gap-2">
										<input
											type="number"
											placeholder="Systolic"
											className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
											value={vitalSigns.systolic}
											onChange={(e) =>
												setVitalSigns((prev) => ({
													...prev,
													systolic: e.target.value,
												}))
											}
										/>
										<span className="text-muted-foreground flex items-center text-sm">
											/
										</span>
										<input
											type="number"
											placeholder="Diastolic"
											className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
											value={vitalSigns.diastolic}
											onChange={(e) =>
												setVitalSigns((prev) => ({
													...prev,
													diastolic: e.target.value,
												}))
											}
										/>
									</div>
									<p className="text-muted-foreground text-xs">mmHg</p>
								</div>

								{/* Pulse Rate */}
								<div className="space-y-2">
									<Label className="text-sm font-medium">Pulse Rate</Label>
									<input
										type="number"
										placeholder="Enter pulse rate"
										className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
										value={vitalSigns.pulseRate}
										onChange={(e) =>
											setVitalSigns((prev) => ({
												...prev,
												pulseRate: e.target.value,
											}))
										}
									/>
									<p className="text-muted-foreground text-xs">bpm</p>
								</div>

								{/* Respiration */}
								<div className="space-y-2">
									<Label className="text-sm font-medium">Respiration</Label>
									<input
										type="number"
										placeholder="Enter respiration rate"
										className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
										value={vitalSigns.respiration}
										onChange={(e) =>
											setVitalSigns((prev) => ({
												...prev,
												respiration: e.target.value,
											}))
										}
									/>
									<p className="text-muted-foreground text-xs">breaths/min</p>
								</div>

								{/* Temperature */}
								<div className="space-y-2">
									<Label className="text-sm font-medium">Temperature</Label>
									<div className="flex gap-2">
										<input
											type="number"
											step="0.1"
											placeholder="Enter temperature"
											className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
											value={vitalSigns.temperature}
											onChange={(e) =>
												setVitalSigns((prev) => ({
													...prev,
													temperature: e.target.value,
												}))
											}
										/>
										<select
											className="border-input bg-background ring-offset-background focus-visible:ring-ring flex h-10 w-20 rounded-md border px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none"
											value={vitalSigns.temperatureType}
											onChange={(e) =>
												setVitalSigns((prev) => ({
													...prev,
													temperatureType: e.target.value,
												}))
											}
										>
											<option value="C">°C</option>
											<option value="F">°F</option>
										</select>
									</div>
								</div>

								{/* Height */}
								<div className="space-y-2">
									<Label className="text-sm font-medium">Height</Label>
									<div className="flex gap-2">
										<input
											type="number"
											step="0.1"
											placeholder="Enter height"
											className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
											value={vitalSigns.height}
											onChange={(e) =>
												setVitalSigns((prev) => ({
													...prev,
													height: e.target.value,
												}))
											}
										/>
										<select
											className="border-input bg-background ring-offset-background focus-visible:ring-ring flex h-10 w-20 rounded-md border px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none"
											value={vitalSigns.heightType}
											onChange={(e) =>
												setVitalSigns((prev) => ({
													...prev,
													heightType: e.target.value,
												}))
											}
										>
											<option value="cm">cm</option>
											<option value="ft">ft</option>
										</select>
									</div>
								</div>

								{/* Weight */}
								<div className="space-y-2">
									<Label className="text-sm font-medium">Weight</Label>
									<div className="flex gap-2">
										<input
											type="number"
											step="0.1"
											placeholder="Enter weight"
											className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
											value={vitalSigns.weight}
											onChange={(e) =>
												setVitalSigns((prev) => ({
													...prev,
													weight: e.target.value,
												}))
											}
										/>
										<select
											className="border-input bg-background ring-offset-background focus-visible:ring-ring flex h-10 w-20 rounded-md border px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none"
											value={vitalSigns.weightType}
											onChange={(e) =>
												setVitalSigns((prev) => ({
													...prev,
													weightType: e.target.value,
												}))
											}
										>
											<option value="kg">kg</option>
											<option value="lbs">lbs</option>
										</select>
									</div>
								</div>
							</div>

							{/* Additional vital signs */}
							<div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
								{/* Oxygen Saturation */}
								<div className="space-y-2">
									<Label className="text-sm font-medium">
										Oxygen Saturation
									</Label>
									<input
										type="number"
										placeholder="Enter oxygen saturation"
										className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
										value={vitalSigns.oxygenSaturation}
										onChange={(e) =>
											setVitalSigns((prev) => ({
												...prev,
												oxygenSaturation: e.target.value,
											}))
										}
									/>
									<p className="text-muted-foreground text-xs">%</p>
								</div>

								{/* Blood Glucose */}
								<div className="space-y-2">
									<Label className="text-sm font-medium">
										Capillary Blood Glucose
									</Label>
									<input
										type="number"
										placeholder="Enter capillary blood glucose"
										className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
										value={vitalSigns.capillaryBloodGlucose}
										onChange={(e) =>
											setVitalSigns((prev) => ({
												...prev,
												capillaryBloodGlucose: e.target.value,
											}))
										}
									/>
									<p className="text-muted-foreground text-xs">mg/dL</p>
								</div>

								{/* BMI */}
								<div className="space-y-2">
									<Label className="text-sm font-medium">Body Mass Index</Label>
									<input
										type="number"
										step="0.1"
										placeholder="Enter BMI"
										className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
										value={vitalSigns.bodyMassIndex}
										onChange={(e) =>
											setVitalSigns((prev) => ({
												...prev,
												bodyMassIndex: e.target.value,
											}))
										}
									/>
									<p className="text-muted-foreground text-xs">kg/m²</p>
								</div>
							</div>

							{/* Save Button */}
							<div className="flex justify-end">
								<Button
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.6448_0.1256_202.74)]"
									onClick={handleSaveVitals}
								>
									Save Vital Signs
								</Button>
							</div>
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="history">
					<Card>
						<CardHeader>
							<div className="flex items-center justify-between">
								<CardTitle>Appointment History</CardTitle>
							</div>
						</CardHeader>
						<CardContent>
							{appointmentHistory.length > 0 ? (
								<div className="space-y-4">
									{appointmentHistory.map((item, index) => (
										<div
											key={item.id || index}
											className="cursor-pointer rounded-lg border p-4"
											onClick={() => handleViewHistory(item.id)}
										>
											<div className="flex items-center justify-between">
												<h4 className="font-medium">
													{index + 1}. APPOINTMENT -{' '}
													{item.appointment_reference_number}
												</h4>
												<Badge
													className="font-bold uppercase"
													variant="outline"
												>
													{item.status}
												</Badge>
											</div>
											<div>
												<p className="text-sm">
													Reason: {item.visitReason.name}
												</p>
											</div>
											<div>
												<p className="text-sm">
													Date:{' '}
													{format(
														new Date(item.appointment_date),
														'MMM dd, yyyy, h:mm a'
													)}
												</p>
											</div>
										</div>
									))}
								</div>
							) : (
								<div className="py-8 text-center">
									<FileText className="text-muted-foreground mx-auto h-12 w-12" />
									<h3 className="mt-2 text-sm font-medium">
										No appointment histories
									</h3>
								</div>
							)}
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="prescription">
					<Card>
						<CardHeader>
							<div className="flex items-center justify-between">
								<CardTitle>Prescriptions</CardTitle>
								<Button
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.6448_0.1256_202.74)]"
									onClick={handleAddPrescription}
								>
									<Plus className="mr-2 h-4 w-4" />
									Add Prescription
								</Button>
							</div>
						</CardHeader>
						<CardContent>
							{prescriptions.length > 0 ? (
								<div className="space-y-4">
									{prescriptions.map(
										(prescription: IPrescription, index: number) => (
											<div
												key={prescription.id || index}
												className="space-y-3 rounded-lg border p-4"
											>
												<div className="flex items-center justify-between">
													<h4 className="font-medium">
														Prescription #{index + 1}
													</h4>
													<div className="flex gap-2">
														<Button
															variant="outline"
															size="sm"
															onClick={() =>
																handlePrintPrescription(prescription.id)
															}
														>
															<FileText className="mr-2 h-4 w-4" />
															Print
														</Button>
														<Button
															variant="outline"
															size="sm"
															onClick={() =>
																handleEditPrescription(prescription.id)
															}
														>
															<Edit className="mr-2 h-4 w-4" />
															Edit
														</Button>
														<Button
															variant="destructive"
															size="sm"
															onClick={() =>
																handleDeletePrescription(prescription.id)
															}
														>
															<Trash2 className="mr-2 h-4 w-4" />
															Delete
														</Button>
													</div>
												</div>
												<div className="grid grid-cols-1 gap-3 md:grid-cols-2">
													<div>
														<Label className="text-sm font-medium">
															Patient Name
														</Label>
														<p className="text-muted-foreground text-sm">
															{prescription.patient_name || 'Not specified'}
														</p>
													</div>
													<div>
														<Label className="text-sm font-medium">
															Created Date
														</Label>
														<p className="text-muted-foreground text-sm">
															{format(
																new Date(prescription.created_at),
																'MMM dd, yyyy'
															)}
														</p>
													</div>
												</div>
												{prescription.prescriptionItems &&
													prescription.prescriptionItems.length > 0 && (
														<div className="space-y-2">
															<Label className="text-sm font-medium">
																Prescription Items
															</Label>
															<div className="space-y-2">
																{prescription.prescriptionItems.map((item) => (
																	<div
																		key={item.id}
																		className="rounded border bg-gray-50 p-3"
																	>
																		<div className="grid grid-cols-1 gap-2 md:grid-cols-3">
																			<div>
																				<span className="text-xs font-medium text-gray-600">
																					Generic:
																				</span>
																				<p className="text-sm">
																					{item.generic}
																				</p>
																			</div>
																			<div>
																				<span className="text-xs font-medium text-gray-600">
																					Brand:
																				</span>
																				<p className="text-sm">{item.brand}</p>
																			</div>
																			<div>
																				<span className="text-xs font-medium text-gray-600">
																					Dosage:
																				</span>
																				<p className="text-sm">{item.dosage}</p>
																			</div>
																		</div>
																		{item.instruction && (
																			<div className="mt-2">
																				<span className="text-xs font-medium text-gray-600">
																					Instructions:
																				</span>
																				<p className="text-sm">
																					{item.instruction}
																				</p>
																			</div>
																		)}
																	</div>
																))}
															</div>
														</div>
													)}
											</div>
										)
									)}
								</div>
							) : (
								<div className="py-8 text-center">
									<FileText className="text-muted-foreground mx-auto h-12 w-12" />
									<h3 className="mt-2 text-sm font-medium">No prescriptions</h3>
									<p className="text-muted-foreground mt-1 text-sm">
										Get started by adding a prescription for this appointment.
									</p>
								</div>
							)}
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="lab-requests">
					<Card>
						<CardHeader>
							<div className="flex items-center justify-between">
								<CardTitle>Lab Requests</CardTitle>
								<Button
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.6448_0.1256_202.74)]"
									onClick={handleAddLabRequest}
								>
									<Plus className="mr-2 h-4 w-4" />
									Add Lab Request
								</Button>
							</div>
						</CardHeader>
						<CardContent>
							{labRequests.length > 0 ? (
								<div className="space-y-4">
									{labRequests.map((labRequest: ILabRequest, index: number) => (
										<div
											key={labRequest.id || index}
											className="space-y-3 rounded-lg border p-4"
										>
											<div className="flex items-center justify-between">
												<h4 className="font-medium">
													Lab Request #{index + 1}
												</h4>
												<div className="flex gap-2">
													<Button
														variant="outline"
														size="sm"
														onClick={() => handlePrintLabRequest(labRequest.id)}
													>
														<FileText className="mr-2 h-4 w-4" />
														Print
													</Button>
													<Button
														variant="outline"
														size="sm"
														onClick={() => handleEditLabRequest(labRequest.id)}
													>
														<Edit className="mr-2 h-4 w-4" />
														Edit
													</Button>
													<Button
														variant="destructive"
														size="sm"
														onClick={() =>
															handleDeleteLabRequest(labRequest.id)
														}
													>
														<Trash2 className="mr-2 h-4 w-4" />
														Delete
													</Button>
												</div>
											</div>
											<div className="grid grid-cols-1 gap-3 md:grid-cols-2">
												<div>
													<Label className="text-sm font-medium">
														Patient Name
													</Label>
													<p className="text-muted-foreground text-sm">
														{labRequest.patient_name || 'Not specified'}
													</p>
												</div>
												<div>
													<Label className="text-sm font-medium">
														Reference Number
													</Label>
													<p className="text-muted-foreground text-sm">
														{labRequest.reference_number || 'Not assigned'}
													</p>
												</div>
											</div>
											{labRequest.labRequestItems &&
												labRequest.labRequestItems.length > 0 && (
													<div className="space-y-2">
														<Label className="text-sm font-medium">
															Lab Tests
														</Label>
														<div className="space-y-2">
															{labRequest.labRequestItems.map((item) => (
																<div
																	key={item.id}
																	className="rounded border bg-gray-50 p-3"
																>
																	<div className="grid grid-cols-1 gap-2 md:grid-cols-2">
																		<div>
																			<span className="text-xs font-medium text-gray-600">
																				Test Name:
																			</span>
																			<p className="text-sm">
																				{item.name || 'Not specified'}
																			</p>
																		</div>
																		{item.laboratory && (
																			<div>
																				<span className="text-xs font-medium text-gray-600">
																					Laboratory:
																				</span>
																				<p className="text-sm">
																					{item.laboratory.name}
																				</p>
																			</div>
																		)}
																	</div>
																</div>
															))}
														</div>
													</div>
												)}
										</div>
									))}
								</div>
							) : (
								<div className="py-8 text-center">
									<FileText className="text-muted-foreground mx-auto h-12 w-12" />
									<h3 className="mt-2 text-sm font-medium">No lab requests</h3>
									<p className="text-muted-foreground mt-1 text-sm">
										Get started by adding a lab request for this appointment.
									</p>
								</div>
							)}
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="diagnostics">
					<Card>
						<CardHeader>
							<div className="flex items-center justify-between">
								<CardTitle>Diagnostic Requests</CardTitle>
								<Button
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.6448_0.1256_202.74)]"
									onClick={handleAddDiagnosticRequest}
								>
									<Plus className="mr-2 h-4 w-4" />
									Add Diagnostic Request
								</Button>
							</div>
						</CardHeader>
						<CardContent>
							{diagnosticRequests.length > 0 ? (
								<div className="space-y-4">
									{diagnosticRequests.map(
										(diagnosticRequest: IDiagnosticRequest, index: number) => (
											<div
												key={diagnosticRequest.id || index}
												className="space-y-3 rounded-lg border p-4"
											>
												<div className="flex items-center justify-between">
													<h4 className="font-medium">
														Diagnostic Request #{index + 1}
													</h4>
													<div className="flex gap-2">
														<Button
															variant="outline"
															size="sm"
															onClick={() =>
																handlePrintDiagnosticRequest(
																	diagnosticRequest.id
																)
															}
														>
															<FileText className="mr-2 h-4 w-4" />
															Print
														</Button>
														<Button
															variant="outline"
															size="sm"
															onClick={() =>
																handleEditDiagnosticRequest(
																	diagnosticRequest.id
																)
															}
														>
															<Edit className="mr-2 h-4 w-4" />
															Edit
														</Button>
														<Button
															variant="destructive"
															size="sm"
															onClick={() =>
																handleDeleteDiagnosticRequest(
																	diagnosticRequest.id
																)
															}
														>
															<Trash2 className="mr-2 h-4 w-4" />
															Delete
														</Button>
													</div>
												</div>
												<div className="grid grid-cols-1 gap-3 md:grid-cols-2">
													<div>
														<Label className="text-sm font-medium">
															Patient Name
														</Label>
														<p className="text-muted-foreground text-sm">
															{diagnosticRequest.patient_name ||
																'Not specified'}
														</p>
													</div>
													<div>
														<Label className="text-sm font-medium">
															Created Date
														</Label>
														<p className="text-muted-foreground text-sm">
															{format(
																new Date(diagnosticRequest.created_at),
																'MMM dd, yyyy'
															)}
														</p>
													</div>
												</div>
												{diagnosticRequest.diagnosticRequestItems &&
													diagnosticRequest.diagnosticRequestItems.length >
														0 && (
														<div className="space-y-2">
															<Label className="text-sm font-medium">
																Diagnostic Tests
															</Label>
															<div className="space-y-2">
																{diagnosticRequest.diagnosticRequestItems.map(
																	(item) => (
																		<div
																			key={item.id}
																			className="rounded border bg-gray-50 p-3"
																		>
																			<div>
																				<span className="text-xs font-medium text-gray-600">
																					Test Name:
																				</span>
																				<p className="text-sm">{item.name}</p>
																			</div>
																		</div>
																	)
																)}
															</div>
														</div>
													)}
											</div>
										)
									)}
								</div>
							) : (
								<div className="py-8 text-center">
									<FileText className="text-muted-foreground mx-auto h-12 w-12" />
									<h3 className="mt-2 text-sm font-medium">
										No diagnostic requests
									</h3>
									<p className="text-muted-foreground mt-1 text-sm">
										Get started by adding a diagnostic request for this
										appointment.
									</p>
								</div>
							)}
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="referrals">
					<Card>
						<CardHeader>
							<div className="flex items-center justify-between">
								<CardTitle>Referrals</CardTitle>
								<Button
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.6448_0.1256_202.74)]"
									onClick={handleAddReferral}
								>
									<Plus className="mr-2 h-4 w-4" />
									Add Referral
								</Button>
							</div>
						</CardHeader>
						<CardContent>
							{referrals.length > 0 ? (
								<div className="space-y-4">
									{referrals.map((referral: IReferral, index: number) => (
										<div
											key={referral.id || index}
											className="space-y-3 rounded-lg border p-4"
										>
											<div className="flex items-center justify-between">
												<h4 className="font-medium">Referral #{index + 1}</h4>
												<div className="flex gap-2">
													<Button
														variant="outline"
														size="sm"
														onClick={() => handlePrintReferral(referral.id)}
													>
														<FileText className="mr-2 h-4 w-4" />
														Print
													</Button>
													<Button
														variant="outline"
														size="sm"
														onClick={() => handleEditReferral(referral.id)}
													>
														<Edit className="mr-2 h-4 w-4" />
														Edit
													</Button>
													<Button
														variant="destructive"
														size="sm"
														onClick={() => handleDeleteReferral(referral.id)}
													>
														<Trash2 className="mr-2 h-4 w-4" />
														Delete
													</Button>
												</div>
											</div>
											<div className="grid grid-cols-1 gap-3 md:grid-cols-2">
												<div>
													<Label className="text-sm font-medium">
														Doctor Name
													</Label>
													<p className="text-muted-foreground text-sm">
														{referral.doctor_name || 'Not specified'}
													</p>
												</div>
												<div>
													<Label className="text-sm font-medium">
														Created Date
													</Label>
													<p className="text-muted-foreground text-sm">
														{referral.created_at
															? format(
																	new Date(referral.created_at),
																	'MMM dd, yyyy'
																)
															: 'Not specified'}
													</p>
												</div>
											</div>
											{referral.purpose && (
												<div>
													<Label className="text-sm font-medium">Purpose</Label>
													<p className="text-muted-foreground text-sm">
														{referral.purpose}
													</p>
												</div>
											)}
										</div>
									))}
								</div>
							) : (
								<div className="py-8 text-center">
									<FileText className="text-muted-foreground mx-auto h-12 w-12" />
									<h3 className="mt-2 text-sm font-medium">No referrals</h3>
									<p className="text-muted-foreground mt-1 text-sm">
										Get started by adding a referral for this appointment.
									</p>
								</div>
							)}
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="certificates">
					<Card>
						<CardHeader>
							<div className="flex items-center justify-between">
								<CardTitle>Medical Certificates</CardTitle>
								<Button
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.6448_0.1256_202.74)]"
									onClick={handleAddMedicalCertificate}
								>
									<Plus className="mr-2 h-4 w-4" />
									Add Medical Certificate
								</Button>
							</div>
						</CardHeader>
						<CardContent>
							{medicalCertificates.length > 0 ? (
								<div className="space-y-4">
									{medicalCertificates.map(
										(certificate: IMedicalCertificate, index: number) => (
											<div
												key={certificate.id || index}
												className="space-y-3 rounded-lg border p-4"
											>
												<div className="flex items-center justify-between">
													<h4 className="font-medium">
														Medical Certificate #{index + 1}
													</h4>
													<div className="flex gap-2">
														<Button
															variant="outline"
															size="sm"
															onClick={() =>
																handlePrintMedicalCertificate(certificate.id)
															}
														>
															<FileText className="mr-2 h-4 w-4" />
															Print
														</Button>
														<Button
															variant="outline"
															size="sm"
															onClick={() =>
																handleEditMedicalCertificate(certificate.id)
															}
														>
															<Edit className="mr-2 h-4 w-4" />
															Edit
														</Button>
														<Button
															variant="destructive"
															size="sm"
															onClick={() =>
																handleDeleteMedicalCertificate(certificate.id)
															}
														>
															<Trash2 className="mr-2 h-4 w-4" />
															Delete
														</Button>
													</div>
												</div>
												<div className="grid grid-cols-1 gap-3 md:grid-cols-2">
													<div>
														<Label className="text-sm font-medium">
															Patient Name
														</Label>
														<p className="text-muted-foreground text-sm">
															{certificate.patient_name || 'Not specified'}
														</p>
													</div>
													<div>
														<Label className="text-sm font-medium">
															Medical Number
														</Label>
														<p className="text-muted-foreground text-sm">
															{certificate.medical_number || 'Not assigned'}
														</p>
													</div>
												</div>
												{certificate.medicalCertificateItems &&
													certificate.medicalCertificateItems.length > 0 && (
														<div className="space-y-2">
															<Label className="text-sm font-medium">
																Certificate Items
															</Label>
															<div className="space-y-2">
																{certificate.medicalCertificateItems.map(
																	(item) => (
																		<div
																			key={item.id}
																			className="rounded border bg-gray-50 p-3"
																		>
																			<div>
																				<span className="text-xs font-medium text-gray-600">
																					Description:
																				</span>
																				<p className="text-sm">
																					{item.description}
																				</p>
																			</div>
																		</div>
																	)
																)}
															</div>
														</div>
													)}
											</div>
										)
									)}
								</div>
							) : (
								<div className="py-8 text-center">
									<FileText className="text-muted-foreground mx-auto h-12 w-12" />
									<h3 className="mt-2 text-sm font-medium">
										No medical certificates
									</h3>
									<p className="text-muted-foreground mt-1 text-sm">
										Get started by adding a medical certificate for this
										appointment.
									</p>
								</div>
							)}
						</CardContent>
					</Card>
				</TabsContent>
			</Tabs>
			{/* Print Dialog */}
			{printPrescriptionDialog.open && printPrescriptionDialog.prescription && (
				<PrescriptionPrintDialog
					prescriptionId={printPrescriptionDialog.prescription.id}
					open={printPrescriptionDialog.open}
					onOpenChange={(open) =>
						!open &&
						setPrintPrescriptionDialog({ open: false, prescription: null })
					}
				/>
			)}

			{printLabRequestDialog.open && printLabRequestDialog.labRequest && (
				<LabRequestPrintDialog
					labRequestId={printLabRequestDialog.labRequest.id}
					open={printLabRequestDialog.open}
					onOpenChange={(open) =>
						!open && setPrintLabRequestDialog({ open: false, labRequest: null })
					}
				/>
			)}

			{printDiagnosticRequestDialog.open &&
				printDiagnosticRequestDialog.diagnosticRequest && (
					<DiagnosticRequestPrintDialog
						diagnosticRequestId={
							printDiagnosticRequestDialog.diagnosticRequest.id
						}
						open={printDiagnosticRequestDialog.open}
						onOpenChange={(open) =>
							!open &&
							setPrintDiagnosticRequestDialog({
								open: false,
								diagnosticRequest: null,
							})
						}
					/>
				)}

			{printReferralDialog.open && printReferralDialog.referral && (
				<ReferralPrintDialog
					referralId={printReferralDialog.referral.id}
					open={printReferralDialog.open}
					onOpenChange={(open) =>
						!open && setPrintReferralDialog({ open: false, referral: null })
					}
				/>
			)}

			{printMedicalCertificateDialog.open &&
				printMedicalCertificateDialog.medicalCertificate && (
					<MedicalCertificatePrintDialog
						medicalCertificateId={
							printMedicalCertificateDialog.medicalCertificate.id
						}
						open={printMedicalCertificateDialog.open}
						onOpenChange={(open) =>
							!open &&
							setPrintMedicalCertificateDialog({
								open: false,
								medicalCertificate: null,
							})
						}
					/>
				)}

			{/* Edit Dialogs */}
			{editPrescriptionDialog.open && editPrescriptionDialog.prescription && (
				<PrescriptionDetailDialog
					onPrint={handlePrintPrescription}
					prescriptionId={editPrescriptionDialog.prescription.id}
					open={editPrescriptionDialog.open}
					onOpenChange={(open) =>
						!open &&
						setEditPrescriptionDialog({ open: false, prescription: null })
					}
				/>
			)}

			{editLabRequestDialog.open && editLabRequestDialog.labRequest && (
				<LabRequestDetailDialog
					onPrint={handlePrintLabRequest}
					labRequestId={editLabRequestDialog.labRequest.id}
					open={editLabRequestDialog.open}
					onOpenChange={(open) =>
						!open && setEditLabRequestDialog({ open: false, labRequest: null })
					}
				/>
			)}

			{editReferralDialog.open && editReferralDialog.referral && (
				<ReferralDetailDialog
					onPrint={handlePrintReferral}
					referralId={editReferralDialog.referral.id}
					open={editReferralDialog.open}
					onOpenChange={(open) =>
						!open && setEditReferralDialog({ open: false, referral: null })
					}
				/>
			)}

			{editDiagnosticRequestDialog.open &&
				editDiagnosticRequestDialog.diagnosticRequest && (
					<DiagnosticRequestDetailDialog
						onPrint={handlePrintDiagnosticRequest}
						diagnosticRequestId={
							editDiagnosticRequestDialog.diagnosticRequest.id
						}
						open={editDiagnosticRequestDialog.open}
						onOpenChange={(open) =>
							!open &&
							setEditDiagnosticRequestDialog({
								open: false,
								diagnosticRequest: null,
							})
						}
					/>
				)}

			{editMedicalCertificateDialog.open &&
				editMedicalCertificateDialog.medicalCertificate && (
					<MedicalCertificateDetailDialog
						onPrint={handlePrintMedicalCertificate}
						medicalCertificateId={
							editMedicalCertificateDialog.medicalCertificate.id
						}
						appointmentId={appointmentId!}
						open={editMedicalCertificateDialog.open}
						onOpenChange={(open) =>
							!open &&
							setEditMedicalCertificateDialog({
								open: false,
								medicalCertificate: null,
							})
						}
					/>
				)}

			{/* Referral Form Dialog */}
			<Dialog
				open={showReferralForm}
				onOpenChange={(value) => {
					if (!value) {
						setShowReferralForm(false);
						setReferralForm({ doctorName: '', purpose: '' });
					}
				}}
			>
				<DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
					<DialogHeader>
						<DialogTitle>Create New Referral</DialogTitle>
						<DialogDescription>
							Create a referral to another medical professional for your
							patient.
						</DialogDescription>
					</DialogHeader>

					<div className="space-y-6">
						{/* Referral Information */}
						<div className="space-y-4">
							<div className="space-y-2">
								<Label htmlFor="referredDoctor">Referred Doctor Name *</Label>
								<Input
									id="referredDoctor"
									value={referralForm.doctorName}
									onChange={(e) =>
										setReferralForm((prev) => ({
											...prev,
											doctorName: e.target.value,
										}))
									}
									placeholder="Enter the name of the doctor you're referring to"
								/>
							</div>

							<div className="space-y-2">
								<Label htmlFor="purpose">Purpose of Referral *</Label>
								<Textarea
									id="purpose"
									value={referralForm.purpose}
									onChange={(e) =>
										setReferralForm((prev) => ({
											...prev,
											purpose: e.target.value,
										}))
									}
									placeholder="Describe the reason for this referral"
									rows={4}
								/>
							</div>
						</div>
					</div>

					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => {
								setShowReferralForm(false);
								setReferralForm({ doctorName: '', purpose: '' });
							}}
						>
							Cancel
						</Button>
						<Button
							onClick={handleSubmitReferral}
							disabled={!referralForm.doctorName || !referralForm.purpose}
							className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
						>
							Create Referral
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Ptr Form Dialog */}
			<Dialog
				open={showPtrForm}
				onOpenChange={(value) => {
					if (!value) {
						setShowPtrForm(false);
					}
				}}
			>
				<DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
					<DialogHeader>
						<DialogTitle>Edit Professional Tax Receipt</DialogTitle>
						<DialogDescription>
							Update the professional tax receipt number for this appointment.
						</DialogDescription>
					</DialogHeader>

					<div className="space-y-6">
						{/* Ptr Information */}
						<div className="space-y-4">
							<div className="space-y-2">
								<Label htmlFor="professionalTaxReceipt">
									Professional Tax Receipt *
								</Label>
								<Input
									id="professionalTaxReceipt"
									value={ptrForm.professionalTaxReceipt}
									onChange={(e) =>
										setPtrForm((prev) => ({
											...prev,
											professionalTaxReceipt: e.target.value,
										}))
									}
									placeholder="Enter professional tax receipt number"
								/>
							</div>
						</div>
					</div>

					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => {
								setShowPtrForm(false);
							}}
						>
							Cancel
						</Button>
						<Button
							onClick={handleSubmitPtr}
							disabled={!ptrForm.professionalTaxReceipt}
							className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
						>
							Save
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Medical Record Dialog */}
			<Dialog
				open={showMedicalRecordForm}
				onOpenChange={(value) => {
					if (!value) {
						setShowMedicalRecordForm(false);
					}
				}}
			>
				<DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
					<DialogHeader>
						<DialogTitle>Upload Record</DialogTitle>
					</DialogHeader>

					<div className="space-y-6">
						{/* Referral Information */}
						<div className="space-y-4">
							<div className="space-y-2">
								<Label htmlFor="recordFile">Upload File *</Label>
								<Input
									id="recordFile"
									type="file"
									accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
									onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
										const file = e.target.files?.[0];
										if (file) {
											setMedicalRecordFile(file);
										}
									}}
								/>
							</div>
						</div>
					</div>

					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => {
								setShowMedicalRecordForm(false);
								setMedicalRecordFile(null);
							}}
						>
							Cancel
						</Button>
						<Button
							onClick={handleAddMedicalRecord}
							disabled={!medicalRecordFile}
							className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
						>
							Upload Record
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
}
