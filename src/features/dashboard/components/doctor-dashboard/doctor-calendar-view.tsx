'use client';

import {
	addMonths,
	eachDayOfInterval,
	endOfMonth,
	endOfWeek,
	format,
	startOfMonth,
	startOfWeek,
	subMonths,
} from 'date-fns';
import {
	Calendar as CalendarIcon,
	ChevronLeft,
	ChevronRight,
	Plus,
	QrCode,
} from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import {
	useAppointmentCalendar,
	useClinics,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import { IAppointment } from '@/features/dashboard/types/doctor.types';

interface IDoctorCalendarViewProps {
	onCreateAppointment?: (date: Date) => void;
	onViewAppointment?: (appointmentId: number) => void;
	onQRAppointment?: () => void;
}

// Helper function to generate calendar days
const generateCalendarDays = (date: Date) => {
	const monthStart = startOfMonth(date);
	const monthEnd = endOfMonth(date);
	const calendarStart = startOfWeek(monthStart);
	const calendarEnd = endOfWeek(monthEnd);

	return eachDayOfInterval({
		start: calendarStart,
		end: calendarEnd,
	});
};

export function DoctorCalendarView({
	onCreateAppointment,
	onViewAppointment,
	onQRAppointment,
}: IDoctorCalendarViewProps) {
	const [currentDate, setCurrentDate] = useState(new Date());
	const [selectedClinic, setSelectedClinic] = useState<string>('all');
	const [selectedStatus, setSelectedStatus] = useState<string>('all');

	// Calculate date range for current month
	const monthStart = startOfMonth(currentDate);
	const monthEnd = endOfMonth(currentDate);
	const fromDate = format(monthStart, 'yyyy-MM-dd');
	const toDate = format(monthEnd, 'yyyy-MM-dd');

	// Fetch data
	const { data: clinicsResponse } = useClinics();
	const clinics = clinicsResponse?.data?.data || [];

	// Prepare filter parameters for calendar endpoint
	const calendarParams = {
		clinicId: selectedClinic !== 'all' ? parseInt(selectedClinic) : undefined,
		status: selectedStatus !== 'all' ? selectedStatus : undefined,
		fromDate,
		toDate,
	};

	// Debug: Log filter parameters
	console.log('Calendar filter params:', calendarParams);
	console.log('Selected clinic:', selectedClinic);
	console.log('Selected status:', selectedStatus);

	const {
		data: appointmentsResponse,
		isLoading,
		error,
	} = useAppointmentCalendar(calendarParams);

	const appointments = appointmentsResponse?.data || [];

	// Debug: Log appointment data to see structure
	if (appointments.length > 0) {
		console.log('Calendar appointments data:', appointments[0]);
		console.log('Patient data structure:', appointments[0].patient);
		console.log('Visit reason structure:', appointments[0].visitReason);
	}

	// Group appointments by date
	const appointmentsByDate = appointments.reduce(
		(acc: Record<string, IAppointment[]>, appointment) => {
			const date = format(new Date(appointment.appointment_date), 'yyyy-MM-dd');
			if (!acc[date]) {
				acc[date] = [];
			}
			acc[date].push(appointment);
			return acc;
		},
		{}
	);

	// Navigation handlers
	const handlePreviousMonth = () => {
		setCurrentDate(subMonths(currentDate, 1));
	};

	const handleNextMonth = () => {
		setCurrentDate(addMonths(currentDate, 1));
	};

	const handleToday = () => {
		setCurrentDate(new Date());
	};

	// Get appointment count for a specific date
	const getAppointmentCount = (date: Date) => {
		const dateStr = format(date, 'yyyy-MM-dd');
		return appointmentsByDate[dateStr]?.length || 0;
	};

	// Get status color for appointment count badge
	const getStatusColor = (count: number) => {
		if (count === 0) return 'bg-gray-100 text-gray-600';
		if (count <= 3) return 'bg-blue-100 text-blue-800';
		if (count <= 6) return 'bg-yellow-100 text-yellow-800';
		return 'bg-red-100 text-red-800';
	};

	// Get appointment status color
	const getAppointmentStatusColor = (status: string) => {
		switch (status.toLowerCase()) {
			case 'confirmed':
				return 'bg-blue-50 text-blue-700 border-blue-200';
			case 'waiting':
				return 'bg-yellow-50 text-yellow-700 border-yellow-200';
			case 'ongoing':
				return 'bg-green-50 text-green-700 border-green-200';
			case 'completed':
				return 'bg-gray-50 text-gray-700 border-gray-200';
			case 'cancelled':
				return 'bg-red-50 text-red-700 border-red-200';
			default:
				return 'bg-blue-50 text-blue-700 border-blue-200';
		}
	};

	// Custom day renderer for calendar
	const renderDay = (date: Date) => {
		const count = getAppointmentCount(date);
		const dateStr = format(date, 'yyyy-MM-dd');
		const dayAppointments = appointmentsByDate[dateStr] || [];

		return (
			<div className="relative h-full min-h-[60px] w-full p-1">
				<div className="flex items-center justify-between">
					<div className="mb-1 text-sm font-medium">{format(date, 'd')} </div>

					{count > 0 && (
						<div
							className={`text-xxs rounded border px-1 text-center ${getStatusColor(count)}`}
						>
							{count} apt{count !== 1 ? 's' : ''}
						</div>
					)}
				</div>

				{count > 0 && (
					<div className="space-y-1">
						{/* Show first few appointments */}
						{dayAppointments.slice(0, 2).map((appointment) => {
							// Safely extract patient info with fallbacks
							const patientFirstName =
								appointment.patient?.profile?.first_name || 'Unknown';
							const patientLastName =
								appointment.patient?.profile?.last_name || 'Patient';
							const appointmentReason =
								appointment.visitReason?.name || 'No reason specified';
							const appointmentStatus = appointment.status || 'unknown';

							return (
								<div
									key={appointment.id}
									className={`text-xxs cursor-pointer truncate rounded border p-0.5 hover:opacity-80 ${getAppointmentStatusColor(appointmentStatus)}`}
									onClick={(e) => {
										e.stopPropagation();
										onViewAppointment?.(appointment.id);
									}}
									title={`${patientFirstName} ${patientLastName} - ${appointmentReason} (${appointmentStatus})`}
								>
									<div className="font-medium">
										{patientFirstName} {patientLastName}
									</div>
									{/* <div className="text-xs opacity-75">
										{appointment.appointment_date
											? format(new Date(appointment.appointment_date), 'h:mm a')
											: 'No time'}
									</div> */}
								</div>
							);
						})}

						{count > 2 && (
							<div className="text-xs text-gray-500">+{count - 2} more</div>
						)}
					</div>
				)}

				{count === 0 && (
					<Button
						variant="ghost"
						size="sm"
						className="absolute right-1 bottom-1 h-6 w-6 p-0 opacity-0 transition-opacity group-hover:opacity-100"
						onClick={(e) => {
							e.stopPropagation();
							onCreateAppointment?.(date);
						}}
					>
						<Plus className="h-3 w-3" />
					</Button>
				)}
			</div>
		);
	};

	if (error) {
		return (
			<Card>
				<CardContent className="p-6">
					<div className="text-center text-red-600">
						Error loading calendar data. Please try again.
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			{/* Calendar Header */}
			<Card>
				<CardHeader className="pb-4">
					<div className="flex items-center justify-between">
						<CardTitle className="flex items-center gap-2">
							<CalendarIcon className="h-5 w-5" />
							Calendar View
						</CardTitle>

						<div className="flex items-center gap-2">
							<Button variant="outline" size="sm" onClick={handleToday}>
								Today
							</Button>
							<Button variant="outline" size="sm" onClick={handlePreviousMonth}>
								<ChevronLeft className="h-4 w-4" />
							</Button>
							<Button variant="outline" size="sm" onClick={handleNextMonth}>
								<ChevronRight className="h-4 w-4" />
							</Button>
						</div>
					</div>

					<div className="flex items-center gap-4">
						<h2 className="text-2xl font-bold">
							{format(currentDate, 'MMMM yyyy')}
						</h2>

						<div className="ml-auto flex flex-wrap items-center gap-2">
							<Button
								variant="default"
								size="sm"
								onClick={() => onCreateAppointment?.(new Date())}
								className="gap-2"
								aria-label="Create new appointment"
							>
								<Plus className="h-4 w-4" />
								<span className="hidden sm:inline">New Appointment</span>
								<span className="sm:hidden">New</span>
							</Button>

							<Button
								variant="outline"
								size="sm"
								onClick={onQRAppointment}
								className="gap-2"
								aria-label="QR appointment"
							>
								<QrCode className="h-4 w-4" />
								<span className="hidden sm:inline">QR Appointment</span>
								<span className="sm:hidden">QR</span>
							</Button>

							<Select value={selectedClinic} onValueChange={setSelectedClinic}>
								<SelectTrigger className="w-[140px] sm:w-[180px]">
									<SelectValue placeholder="All Clinics" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">All Clinics</SelectItem>
									{clinics.map((clinic) => (
										<SelectItem key={clinic.id} value={clinic.id.toString()}>
											{clinic.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>

							<Select value={selectedStatus} onValueChange={setSelectedStatus}>
								<SelectTrigger className="w-[120px] sm:w-[150px]">
									<SelectValue placeholder="All Status" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">All Status</SelectItem>
									<SelectItem value="confirmed">Confirmed</SelectItem>
									<SelectItem value="waiting">Waiting</SelectItem>
									<SelectItem value="ongoing">Ongoing</SelectItem>
									<SelectItem value="completed">Completed</SelectItem>
									<SelectItem value="cancelled">Cancelled</SelectItem>
								</SelectContent>
							</Select>
						</div>
					</div>
				</CardHeader>
			</Card>

			{/* Calendar Grid */}
			<Card className="flex-1">
				<CardContent className="p-6">
					{isLoading ? (
						<div className="grid grid-cols-7 gap-2">
							{Array.from({ length: 35 }).map((_, i) => (
								<Skeleton key={i} className="h-20 w-full" />
							))}
						</div>
					) : (
						<div className="w-full">
							{/* Custom Calendar Grid */}
							<div className="mb-4 grid grid-cols-7 gap-1">
								{['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(
									(day) => (
										<div
											key={day}
											className="text-muted-foreground p-2 text-center text-sm font-medium"
										>
											<span className="hidden sm:inline">{day}</span>
											<span className="sm:hidden">{day.slice(0, 1)}</span>
										</div>
									)
								)}
							</div>
							<div className="grid grid-cols-7 gap-1">
								{generateCalendarDays(currentDate).map((date, index) => (
									<div
										key={index}
										className={`
											hover:bg-accent relative min-h-[80px] cursor-pointer rounded-lg border p-1
											transition-colors sm:min-h-[100px] sm:p-2
											${format(date, 'yyyy-MM') !== format(currentDate, 'yyyy-MM') ? 'opacity-50' : ''}
											${format(date, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd') ? 'bg-accent' : 'bg-background'}
										`}
										onClick={() => onCreateAppointment?.(date)}
										role="button"
										tabIndex={0}
										aria-label={`${format(date, 'EEEE, MMMM d, yyyy')} - ${getAppointmentCount(date)} appointments`}
										onKeyDown={(e) => {
											if (e.key === 'Enter' || e.key === ' ') {
												e.preventDefault();
												onCreateAppointment?.(date);
											}
										}}
									>
										{renderDay(date)}
									</div>
								))}
							</div>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
