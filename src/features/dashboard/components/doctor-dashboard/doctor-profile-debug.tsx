'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDoctorProfile } from '@/features/dashboard/hooks/useDoctorDashboard';

export function DoctorProfileDebug() {
	const { data: profile, isLoading, error } = useDoctorProfile();

	if (isLoading) {
		return (
			<Card>
				<CardHeader>
					<CardTitle>Doctor Profile Debug</CardTitle>
				</CardHeader>
				<CardContent>
					<p>Loading profile data...</p>
				</CardContent>
			</Card>
		);
	}

	if (error) {
		return (
			<Card>
				<CardHeader>
					<CardTitle>Doctor Profile Debug</CardTitle>
				</CardHeader>
				<CardContent>
					<p className="text-red-600">Error loading profile:</p>
					<pre className="mt-2 rounded bg-gray-100 p-2 text-sm">
						{JSON.stringify(error, null, 2)}
					</pre>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader>
				<CardTitle>Doctor Profile Debug</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="space-y-4">
					<div>
						<h3 className="mb-2 font-medium">Raw API Response:</h3>
						<pre className="max-h-96 overflow-auto rounded bg-gray-100 p-3 text-xs">
							{JSON.stringify(profile, null, 2)}
						</pre>
					</div>

					{profile && (
						<div>
							<h3 className="mb-2 font-medium">Onboarding Check Values:</h3>
							<div className="grid grid-cols-2 gap-4 text-sm">
								<div>
									<span className="font-medium">is_completed:</span>{' '}
									<span
										className={
											profile.is_completed === 0
												? 'text-red-600'
												: 'text-green-600'
										}
									>
										{profile.is_completed}
									</span>
								</div>
								<div>
									<span className="font-medium">doctor object exists:</span>{' '}
									<span
										className={
											!profile.doctor ? 'text-red-600' : 'text-green-600'
										}
									>
										{profile.doctor ? 'Yes' : 'No'}
									</span>
								</div>
								<div>
									<span className="font-medium">doctor.specialty:</span>{' '}
									<span
										className={
											!profile.doctor?.specialty
												? 'text-red-600'
												: 'text-green-600'
										}
									>
										{profile.doctor?.specialty || 'null'}
									</span>
								</div>
								<div>
									<span className="font-medium">doctor.prc_number:</span>{' '}
									<span
										className={
											!profile.doctor?.prc_number
												? 'text-red-600'
												: 'text-green-600'
										}
									>
										{profile.doctor?.prc_number || 'null'}
									</span>
								</div>
							</div>

							<div className="mt-4 rounded bg-blue-50 p-3">
								<h4 className="mb-2 font-medium text-blue-800">
									Onboarding Decision:
								</h4>
								<p className="text-blue-700">
									{profile.is_completed === 0 ||
									!profile.doctor ||
									!profile.doctor.specialty ||
									!profile.doctor.prc_number
										? '🔴 SHOULD SHOW ONBOARDING'
										: '🟢 SHOULD SKIP ONBOARDING'}
								</p>
							</div>
						</div>
					)}
				</div>
			</CardContent>
		</Card>
	);
}
