'use client';

import { Archive, Eye, Pause, Play, Settings } from 'lucide-react';
import { useState } from 'react';

import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	useArchiveClinic,
	useDisableClinic,
	useEnableClinic,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import { IClinic } from '@/features/dashboard/types/doctor.types';

interface IClinicStatusActionsProps {
	clinic: IClinic;
	onView?: (clinic: IClinic) => void;
	onEdit?: (clinic: IClinic) => void;
}

interface IConfirmationDialog {
	open: boolean;
	type: 'enable' | 'disable' | 'archive' | null;
	clinic: IClinic | null;
}

export function ClinicStatusActions({
	clinic,
	onView,
	onEdit,
}: IClinicStatusActionsProps) {
	const [confirmDialog, setConfirmDialog] = useState<IConfirmationDialog>({
		open: false,
		type: null,
		clinic: null,
	});

	const enableClinicMutation = useEnableClinic();
	const disableClinicMutation = useDisableClinic();
	const archiveClinicMutation = useArchiveClinic();

	const isLoading =
		enableClinicMutation.isPending ||
		disableClinicMutation.isPending ||
		archiveClinicMutation.isPending;

	const handleStatusAction = (
		type: 'enable' | 'disable' | 'archive',
		clinic: IClinic
	) => {
		setConfirmDialog({
			open: true,
			type,
			clinic,
		});
	};

	const executeStatusAction = async () => {
		if (!confirmDialog.clinic || !confirmDialog.type) return;

		try {
			switch (confirmDialog.type) {
				case 'enable':
					await enableClinicMutation.mutateAsync(confirmDialog.clinic.id);
					break;
				case 'disable':
					await disableClinicMutation.mutateAsync(confirmDialog.clinic.id);
					break;
				case 'archive':
					await archiveClinicMutation.mutateAsync(confirmDialog.clinic.id);
					break;
			}
			setConfirmDialog({ open: false, type: null, clinic: null });
		} catch {
			// Error handling is done in the mutation hooks
		}
	};

	const getConfirmationContent = () => {
		if (!confirmDialog.type || !confirmDialog.clinic) return null;

		const clinicName = confirmDialog.clinic.name;

		switch (confirmDialog.type) {
			case 'enable':
				return {
					title: 'Enable Clinic',
					description: `Are you sure you want to enable "${clinicName}"? This will make the clinic active and available for appointments.`,
					actionText: 'Enable',
				};
			case 'disable':
				return {
					title: 'Disable Clinic',
					description: `Are you sure you want to disable "${clinicName}"? This will make the clinic inactive and unavailable for new appointments.`,
					actionText: 'Disable',
				};
			case 'archive':
				return {
					title: 'Archive Clinic',
					description: `Are you sure you want to archive "${clinicName}"? This action will remove the clinic from active lists but preserve all data.`,
					actionText: 'Archive',
				};
			default:
				return null;
		}
	};

	const confirmationContent = getConfirmationContent();

	return (
		<>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="outline" size="sm" disabled={isLoading}>
						<Settings className="h-4 w-4" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end">
					{onView && (
						<DropdownMenuItem onClick={() => onView(clinic)}>
							<Eye className="mr-2 h-4 w-4" />
							View Details
						</DropdownMenuItem>
					)}

					{onEdit && (
						<DropdownMenuItem onClick={() => onEdit(clinic)}>
							<Settings className="mr-2 h-4 w-4" />
							Edit Clinic
						</DropdownMenuItem>
					)}

					<DropdownMenuSeparator />

					{clinic.status?.toLowerCase() === 'active' ? (
						<DropdownMenuItem
							onClick={() => handleStatusAction('disable', clinic)}
							className="text-orange-600"
						>
							<Pause className="mr-2 h-4 w-4" />
							Disable Clinic
						</DropdownMenuItem>
					) : clinic.status?.toLowerCase() === 'inactive' ? (
						<DropdownMenuItem
							onClick={() => handleStatusAction('enable', clinic)}
							className="text-green-600"
						>
							<Play className="mr-2 h-4 w-4" />
							Enable Clinic
						</DropdownMenuItem>
					) : null}

					{clinic.status?.toLowerCase() !== 'archived' && (
						<DropdownMenuItem
							onClick={() => handleStatusAction('archive', clinic)}
							className="text-red-600"
						>
							<Archive className="mr-2 h-4 w-4" />
							Archive Clinic
						</DropdownMenuItem>
					)}
				</DropdownMenuContent>
			</DropdownMenu>

			<AlertDialog
				open={confirmDialog.open}
				onOpenChange={(open) =>
					setConfirmDialog({ open, type: null, clinic: null })
				}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>{confirmationContent?.title}</AlertDialogTitle>
						<AlertDialogDescription>
							{confirmationContent?.description}
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={executeStatusAction}
							disabled={isLoading}
							className={
								confirmDialog.type === 'archive' ||
								confirmDialog.type === 'disable'
									? 'bg-destructive hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 text-white'
									: ''
							}
						>
							{isLoading ? 'Processing...' : confirmationContent?.actionText}
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</>
	);
}
