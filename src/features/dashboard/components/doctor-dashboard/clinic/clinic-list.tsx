'use client';

import { Eye, Plus } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import { useClinics } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import {
	IClinic,
	IClinicParams,
} from '@/features/dashboard/types/doctor.types';

import { ClinicStatusActions } from './clinic-status-actions';

interface IClinicListProps {
	onCreateClinic?: () => void;
	onViewClinic?: (clinic: IClinic) => void;
	onEditClinic?: (clinic: IClinic) => void;
}

export function ClinicList({
	onCreateClinic,
	onViewClinic,
	onEditClinic,
}: IClinicListProps) {
	const [searchTerm, setSearchTerm] = useState('');
	const [statusFilter, setStatusFilter] = useState<string>('');
	const [currentPage, setCurrentPage] = useState(1);

	const queryParams: IClinicParams = {
		search: searchTerm || undefined,
		status: statusFilter || undefined,
		page: currentPage,
		pageSize: 10,
	};

	// Use role-aware hook (backward compatibility with useAssistantHooks flag)
	const { data, isLoading, error } = useClinics(queryParams);

	const clinics = data?.data?.data || [];
	const meta = data?.data?.meta;

	const handleSearch = (value: string) => {
		setSearchTerm(value);
		setCurrentPage(1); // Reset to first page when searching
	};

	const handleStatusFilter = (value: string) => {
		setStatusFilter(value === 'all' ? '' : value);
		setCurrentPage(1); // Reset to first page when filtering
	};

	const formatDate = (dateString: string) => {
		if (!dateString) return 'N/A';
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	};

	const getStatusBadgeColor = (status: string) => {
		switch (status?.toLowerCase()) {
			case 'active':
				return 'bg-green-100 text-green-800';
			case 'inactive':
				return 'bg-red-100 text-red-800';
			case 'archived':
				return 'bg-gray-100 text-gray-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	};

	if (error) {
		return (
			<div className="flex flex-col items-center justify-center py-12">
				<p className="text-muted-foreground mb-4">Failed to load clinics</p>
				<Button variant="outline" onClick={() => window.location.reload()}>
					Try Again
				</Button>
			</div>
		);
	}

	return (
		<div className="flex flex-col gap-4 px-4">
			{/* Header */}
			<div className="mb-4 flex flex-col items-start justify-between gap-4 lg:flex-row">
				<div>
					<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
						My Clinics
					</h1>
					<p className="text-muted-foreground">
						Manage your clinic information and settings
					</p>
				</div>
				<div className="flex items-center justify-between">
					<div className="flex gap-2">
						{onCreateClinic && (
							<Button onClick={onCreateClinic}>
								<Plus className="mr-2 h-4 w-4" />
								Add New Clinic
							</Button>
						)}
					</div>
				</div>
			</div>

			{/* Filters */}
			<div className="flex flex-col gap-4 sm:flex-row sm:items-end">
				<div className="flex-1">
					<Input
						id="search"
						placeholder="Search by clinic name..."
						value={searchTerm}
						onChange={(e) => handleSearch(e.target.value)}
						className="max-w-sm"
					/>
				</div>

				<Select
					value={statusFilter || 'all'}
					onValueChange={handleStatusFilter}
				>
					<SelectTrigger>
						<SelectValue placeholder="All Statuses" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all">All Status</SelectItem>
						<SelectItem value="active">Active</SelectItem>
						<SelectItem value="inactive">Inactive</SelectItem>
						<SelectItem value="archived">Archived</SelectItem>
					</SelectContent>
				</Select>
			</div>

			{/* Table */}
			<div className="rounded-md border">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>Clinic Name</TableHead>
							<TableHead>Address</TableHead>
							<TableHead>Contact</TableHead>
							<TableHead>Status</TableHead>
							<TableHead>Date Established</TableHead>
							<TableHead className="text-right">Actions</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{isLoading ? (
							// Loading skeleton
							Array.from({ length: 5 }).map((_, index) => (
								<TableRow key={index}>
									<TableCell>
										<Skeleton className="h-4 w-32" />
									</TableCell>
									<TableCell>
										<Skeleton className="h-4 w-48" />
									</TableCell>
									<TableCell>
										<Skeleton className="h-4 w-24" />
									</TableCell>
									<TableCell>
										<Skeleton className="h-6 w-16" />
									</TableCell>
									<TableCell>
										<Skeleton className="h-4 w-20" />
									</TableCell>
									<TableCell>
										<Skeleton className="h-8 w-24" />
									</TableCell>
								</TableRow>
							))
						) : clinics.length === 0 ? (
							<TableRow>
								<TableCell colSpan={6} className="py-12 text-center">
									<div className="flex flex-col items-center gap-2">
										<p className="text-muted-foreground">
											{searchTerm || statusFilter
												? 'No clinics found matching your criteria'
												: 'No clinics found'}
										</p>
										{!searchTerm && !statusFilter && onCreateClinic && (
											<Button variant="outline" onClick={onCreateClinic}>
												<Plus className="mr-2 h-4 w-4" />
												Create Your First Clinic
											</Button>
										)}
									</div>
								</TableCell>
							</TableRow>
						) : (
							clinics.map((clinic) => (
								<TableRow key={clinic.id}>
									<TableCell className="font-medium">{clinic.name}</TableCell>
									<TableCell className="max-w-xs truncate">
										{clinic.address}
									</TableCell>
									<TableCell>
										<div className="text-sm">
											{clinic.phone && <div>{clinic.phone}</div>}
											{clinic.email && (
												<div className="text-muted-foreground">
													{clinic.email}
												</div>
											)}
											{!clinic.phone && !clinic.email && (
												<span className="text-muted-foreground">N/A</span>
											)}
										</div>
									</TableCell>
									<TableCell>
										<span
											className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusBadgeColor(
												clinic.status
											)}`}
										>
											{clinic.status || 'Unknown'}
										</span>
									</TableCell>
									<TableCell>{formatDate(clinic.date_established)}</TableCell>
									<TableCell className="text-right">
										<div className="flex items-center justify-end gap-2">
											{onViewClinic && (
												<Button
													variant="ghost"
													size="sm"
													onClick={() => onViewClinic(clinic)}
												>
													<Eye className="h-4 w-4" />
												</Button>
											)}
											<ClinicStatusActions
												clinic={clinic}
												onView={onViewClinic}
												onEdit={onEditClinic}
											/>
										</div>
									</TableCell>
								</TableRow>
							))
						)}
					</TableBody>
				</Table>
			</div>

			{/* Pagination */}
			{meta && meta.last_page > 1 && (
				<div className="flex items-center justify-between">
					<p className="text-muted-foreground text-sm">
						Showing {(meta.current_page - 1) * meta.per_page + 1} to{' '}
						{Math.min(meta.current_page * meta.per_page, meta.total)} of{' '}
						{meta.total} clinics
					</p>
					<div className="flex items-center gap-2">
						<Button
							variant="outline"
							size="sm"
							onClick={() => setCurrentPage(currentPage - 1)}
							disabled={currentPage <= 1}
						>
							Previous
						</Button>
						<span className="text-sm">
							Page {meta.current_page} of {meta.last_page}
						</span>
						<Button
							variant="outline"
							size="sm"
							onClick={() => setCurrentPage(currentPage + 1)}
							disabled={currentPage >= meta.last_page}
						>
							Next
						</Button>
					</div>
				</div>
			)}
		</div>
	);
}
