'use client';

import { useEffect, useState } from 'react';

import { But<PERSON> } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
	useClinicDetail,
	useCreateClinic,
	useUpdateClinic,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import {
	IBackendClinicSchedule,
	IClinic,
	IClinicSchedule,
	ICreateClinicRequest,
	IUpdateClinicRequest,
} from '@/features/dashboard/types/doctor.types';

import { ClinicSchedule } from './clinic-schedule';

interface IClinicFormDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	clinic?: IClinic;
	mode: 'create' | 'edit';
}

interface IFormData {
	name: string;
	address: string;
	phone: string;
	email: string;
	website: string;
	date_established: string;
	longitude: string;
	latitude: string;
	schedule: IClinicSchedule[];
}

const DAYS_OF_WEEK = [
	'Monday',
	'Tuesday',
	'Wednesday',
	'Thursday',
	'Friday',
	'Saturday',
	'Sunday',
];

const DEFAULT_SCHEDULE: IClinicSchedule[] = DAYS_OF_WEEK.map((day) => ({
	day,
	start_time: '09:00',
	end_time: '17:00',
	is_active: 1,
}));

const DEFAULT_FORM_DATA: IFormData = {
	name: '',
	address: '',
	phone: '',
	email: '',
	website: '',
	date_established: '',
	longitude: '',
	latitude: '',
	schedule: DEFAULT_SCHEDULE,
};

export function ClinicFormDialog({
	open,
	onOpenChange,
	clinic,
	mode,
}: IClinicFormDialogProps) {
	const [formData, setFormData] = useState<IFormData>(DEFAULT_FORM_DATA);
	const [errors, setErrors] = useState<Partial<IFormData>>({});

	const createClinicMutation = useCreateClinic();
	const updateClinicMutation = useUpdateClinic();

	// Fetch full clinic details when in edit mode
	const shouldFetchClinicData = mode === 'edit' && !!clinic?.id && open;
	const { data: fullClinicData, isLoading: isLoadingClinicData } =
		useClinicDetail(shouldFetchClinicData ? clinic!.id : 0);

	const isLoading =
		createClinicMutation.isPending ||
		updateClinicMutation.isPending ||
		(mode === 'edit' && shouldFetchClinicData && isLoadingClinicData);

	useEffect(() => {
		if (mode === 'edit' && clinic) {
			// Use full clinic data if available, otherwise use basic clinic data
			const clinicToUse = fullClinicData || clinic;

			setFormData({
				name: clinicToUse.name || '',
				address: clinicToUse.address || '',
				phone: clinicToUse.phone || '',
				email: clinicToUse.email || '',
				website: clinicToUse.website || '',
				date_established: clinicToUse.date_established || '',
				longitude: clinicToUse.longitude || '',
				latitude: clinicToUse.latitude || '',
				schedule: clinicToUse.clinicSchedules
					? clinicToUse.clinicSchedules
							// Filter out duplicates and prefer the latest entry for each day
							.filter(
								(
									schedule: IClinicSchedule,
									index: number,
									self: IClinicSchedule[]
								) => {
									const dayLower = schedule.day.toLowerCase();
									const laterEntry = self.find(
										(s: IClinicSchedule, i: number) =>
											i > index && s.day.toLowerCase() === dayLower
									);
									return !laterEntry;
								}
							)
							.map((s: IClinicSchedule) => ({
								day: s.day,
								start_time: s.start_time,
								end_time: s.end_time,
								is_active: s.is_active,
							}))
					: DEFAULT_SCHEDULE, // Use default schedule if no schedules exist
			});
		} else {
			setFormData(DEFAULT_FORM_DATA);
		}
		setErrors({});
	}, [clinic, fullClinicData, mode, open]);

	const validateForm = (): boolean => {
		const newErrors: Partial<IFormData> = {};

		if (!formData.name.trim()) {
			newErrors.name = 'Clinic name is required';
		}

		if (!formData.address.trim()) {
			newErrors.address = 'Clinic address is required';
		}

		if (formData.email && !isValidEmail(formData.email)) {
			newErrors.email = 'Please enter a valid email address';
		}

		if (formData.website && !isValidUrl(formData.website)) {
			newErrors.website = 'Please enter a valid website URL';
		}

		setErrors(newErrors);
		return Object.keys(newErrors).length === 0;
	};

	const isValidEmail = (email: string): boolean => {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailRegex.test(email);
	};

	const isValidUrl = (url: string): boolean => {
		try {
			new URL(url.startsWith('http') ? url : `https://${url}`);
			return true;
		} catch {
			return false;
		}
	};

	const handleInputChange = (field: keyof IFormData, value: string) => {
		setFormData((prev) => ({ ...prev, [field]: value }));
		if (errors[field]) {
			setErrors((prev) => ({ ...prev, [field]: undefined }));
		}
	};

	const handleScheduleChange = (schedule: IClinicSchedule[]) => {
		setFormData((prev) => ({ ...prev, schedule }));
	};

	const handleSubmit = async () => {
		if (!validateForm()) return;

		try {
			// Transform schedule data to match backend expectations (camelCase)
			const transformedSchedule: IBackendClinicSchedule[] | undefined =
				formData.schedule.length > 0
					? formData.schedule.map((schedule) => ({
							day: schedule.day,
							startTime: schedule.start_time,
							endTime: schedule.end_time,
							isActive: schedule.is_active,
						}))
					: undefined;

			if (mode === 'create') {
				const createData: ICreateClinicRequest = {
					name: formData.name,
					address: formData.address,
					...(formData.phone.trim() && { phone: formData.phone }),
					...(formData.email.trim() && { email: formData.email }),
					...(formData.website.trim() && { website: formData.website }),
					...(formData.date_established && {
						dateEstablished: new Date(formData.date_established),
					}),
					...(formData.longitude.trim() && { longitude: formData.longitude }),
					...(formData.latitude.trim() && { latitude: formData.latitude }),
					...(transformedSchedule &&
						transformedSchedule.length > 0 && {
							schedule: transformedSchedule,
						}),
				};
				await createClinicMutation.mutateAsync(createData);
			} else {
				const updateData: IUpdateClinicRequest = {
					clinicId: clinic!.id,
					name: formData.name,
					address: formData.address,
					phone: formData.phone || undefined,
					email: formData.email || undefined,
					website: formData.website || undefined,
					dateEstablished: formData.date_established || undefined,
					longitude: formData.longitude || undefined,
					latitude: formData.latitude || undefined,
					schedule: transformedSchedule,
				};
				await updateClinicMutation.mutateAsync(updateData);
			}
			onOpenChange(false);
		} catch {
			// Error handling is done in the mutation hooks
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-4xl">
				<DialogHeader>
					<DialogTitle>
						{mode === 'create' ? 'Create New Clinic' : 'Edit Clinic'}
					</DialogTitle>
					<DialogDescription>
						{mode === 'create'
							? 'Add a new clinic to your practice. Fill in the required information below.'
							: 'Update your clinic information and settings.'}
					</DialogDescription>
				</DialogHeader>

				<Tabs defaultValue="basic" className="w-full">
					<TabsList className="grid w-full grid-cols-3">
						<TabsTrigger value="basic">Basic Information</TabsTrigger>
						<TabsTrigger value="contact">Contact Details</TabsTrigger>
						<TabsTrigger value="schedule">Schedule</TabsTrigger>
					</TabsList>

					<TabsContent value="basic" className="space-y-4">
						<div className="grid gap-4">
							<div className="grid gap-2">
								<Label htmlFor="name">
									Clinic Name <span className="text-red-500">*</span>
								</Label>
								<Input
									id="name"
									value={formData.name}
									onChange={(e) => handleInputChange('name', e.target.value)}
									placeholder="Enter clinic name"
									className={errors.name ? 'border-red-500' : ''}
								/>
								{errors.name && (
									<span className="text-sm text-red-500">{errors.name}</span>
								)}
							</div>

							<div className="grid gap-2">
								<Label htmlFor="address">
									Address <span className="text-red-500">*</span>
								</Label>
								<Input
									id="address"
									value={formData.address}
									onChange={(e) => handleInputChange('address', e.target.value)}
									placeholder="Enter clinic address"
									className={errors.address ? 'border-red-500' : ''}
								/>
								{errors.address && (
									<span className="text-sm text-red-500">{errors.address}</span>
								)}
							</div>

							<div className="grid gap-2">
								<Label htmlFor="date_established">Date Established</Label>
								<Input
									id="date_established"
									type="date"
									value={formData.date_established}
									onChange={(e) =>
										handleInputChange('date_established', e.target.value)
									}
								/>
							</div>
						</div>
					</TabsContent>

					<TabsContent value="contact" className="space-y-4">
						<div className="grid gap-4">
							<div className="grid gap-2">
								<Label htmlFor="phone">Phone Number</Label>
								<Input
									id="phone"
									value={formData.phone}
									onChange={(e) => handleInputChange('phone', e.target.value)}
									placeholder="Enter phone number"
								/>
							</div>

							<div className="grid gap-2">
								<Label htmlFor="email">Email Address</Label>
								<Input
									id="email"
									type="email"
									value={formData.email}
									onChange={(e) => handleInputChange('email', e.target.value)}
									placeholder="Enter email address"
									className={errors.email ? 'border-red-500' : ''}
								/>
								{errors.email && (
									<span className="text-sm text-red-500">{errors.email}</span>
								)}
							</div>

							<div className="grid gap-2">
								<Label htmlFor="website">Website</Label>
								<Input
									id="website"
									value={formData.website}
									onChange={(e) => handleInputChange('website', e.target.value)}
									placeholder="Enter website URL"
									className={errors.website ? 'border-red-500' : ''}
								/>
								{errors.website && (
									<span className="text-sm text-red-500">{errors.website}</span>
								)}
							</div>

							<Separator />

							<div className="grid grid-cols-2 gap-4">
								<div className="grid gap-2">
									<Label htmlFor="latitude">Latitude</Label>
									<Input
										id="latitude"
										value={formData.latitude}
										onChange={(e) =>
											handleInputChange('latitude', e.target.value)
										}
										placeholder="e.g., 14.5995"
									/>
								</div>
								<div className="grid gap-2">
									<Label htmlFor="longitude">Longitude</Label>
									<Input
										id="longitude"
										value={formData.longitude}
										onChange={(e) =>
											handleInputChange('longitude', e.target.value)
										}
										placeholder="e.g., 120.9842"
									/>
								</div>
							</div>
							<p className="text-muted-foreground text-sm">
								Optional: Add coordinates for Google Maps integration
							</p>
						</div>
					</TabsContent>

					<TabsContent value="schedule" className="space-y-4">
						<ClinicSchedule
							schedule={formData.schedule}
							onChange={handleScheduleChange}
						/>
					</TabsContent>
				</Tabs>

				<DialogFooter>
					<Button
						type="button"
						variant="outline"
						onClick={() => onOpenChange(false)}
						disabled={isLoading}
					>
						Cancel
					</Button>
					<Button onClick={handleSubmit} disabled={isLoading}>
						{isLoading
							? mode === 'create'
								? 'Creating...'
								: 'Updating...'
							: mode === 'create'
								? 'Create Clinic'
								: 'Update Clinic'}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
