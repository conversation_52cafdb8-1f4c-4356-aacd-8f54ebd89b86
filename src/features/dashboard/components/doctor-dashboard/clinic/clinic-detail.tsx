'use client';

import {
	ChevronLeftIcon,
	Clock,
	Globe,
	Mail,
	MapPin,
	Phone,
	Settings,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { env } from '@/core/config/env';
import { useClinicDetail } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import { IClinic } from '@/features/dashboard/types/doctor.types';

import { ClinicStatusActions } from './clinic-status-actions';

interface IClinicDetailProps {
	clinicId: number;
	onEdit?: (clinic: IClinic) => void;
	onBack?: () => void;
}

export function ClinicDetail({ clinicId, onEdit, onBack }: IClinicDetailProps) {
	const { data: clinic, isLoading, error } = useClinicDetail(clinicId);

	if (isLoading) {
		return <ClinicDetailSkeleton />;
	}

	if (error || !clinic) {
		return (
			<div className="flex flex-col items-center justify-center py-12">
				<p className="text-muted-foreground mb-4">
					{error ? 'Failed to load clinic details' : 'Clinic not found'}
				</p>
				{onBack && (
					<Button variant="outline" onClick={onBack}>
						Go Back
					</Button>
				)}
			</div>
		);
	}

	const formatDate = (dateString: string) => {
		if (!dateString) return 'Not specified';
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	};

	const formatTime = (timeString: string) => {
		if (!timeString) return '';
		const [hours, minutes] = timeString.split(':');
		const hour = parseInt(hours, 10);
		const ampm = hour >= 12 ? 'PM' : 'AM';
		const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
		return `${displayHour}:${minutes} ${ampm}`;
	};

	const hasCoordinates = clinic.latitude && clinic.longitude;

	return (
		<div className="space-y-6">
			<div>
				{onBack && (
					<div className="mb-2 flex justify-start">
						<Button
							variant="outline"
							className="flex h-8 items-center"
							onClick={onBack}
						>
							<ChevronLeftIcon className="size-4" />
							<span className="-translate-y-0.5">Back to Clinics</span>
						</Button>
					</div>
				)}

				{/* Header */}
				<div className="flex items-center justify-between">
					<div>
						<h1 className="text-2xl font-bold">{clinic.name}</h1>
						<p className="text-muted-foreground">Clinic Details</p>
					</div>
					<div className="flex items-center gap-2">
						{onEdit && (
							<Button variant="outline" onClick={() => onEdit(clinic)}>
								<Settings className="mr-2 h-4 w-4" />
								Edit Clinic
							</Button>
						)}
						<ClinicStatusActions clinic={clinic} onEdit={onEdit} />
					</div>
				</div>
			</div>

			<div className="grid gap-6 md:grid-cols-2">
				{/* Basic Information */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<MapPin className="h-5 w-5" />
							Basic Information
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div>
							<label className="text-sm font-medium">Clinic Name</label>
							<p className="text-muted-foreground">{clinic.name}</p>
						</div>

						<div>
							<label className="text-sm font-medium">Address</label>
							<p className="text-muted-foreground">{clinic.address}</p>
						</div>

						<div>
							<label className="text-sm font-medium">Date Established</label>
							<p className="text-muted-foreground">
								{formatDate(clinic.date_established)}
							</p>
						</div>

						<div>
							<label className="text-sm font-medium">Status</label>
							<p
								className={`font-medium ${
									clinic.status?.toLowerCase() === 'active'
										? 'text-green-600'
										: clinic.status?.toLowerCase() === 'inactive'
											? 'text-red-600'
											: 'text-gray-600'
								}`}
							>
								{clinic.status || 'Unknown'}
							</p>
						</div>
					</CardContent>
				</Card>

				{/* Contact Information */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Phone className="h-5 w-5" />
							Contact Information
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="flex items-center gap-2">
							<Phone className="text-muted-foreground h-4 w-4" />
							<div>
								<label className="text-sm font-medium">Phone</label>
								<p className="text-muted-foreground">
									{clinic.phone || 'Not provided'}
								</p>
							</div>
						</div>

						<div className="flex items-center gap-2">
							<Mail className="text-muted-foreground h-4 w-4" />
							<div>
								<label className="text-sm font-medium">Email</label>
								<p className="text-muted-foreground">
									{clinic.email || 'Not provided'}
								</p>
							</div>
						</div>

						<div className="flex items-center gap-2">
							<Globe className="text-muted-foreground h-4 w-4" />
							<div>
								<label className="text-sm font-medium">Website</label>
								<div>
									{clinic.website ? (
										<a
											href={
												clinic.website.startsWith('http')
													? clinic.website
													: `https://${clinic.website}`
											}
											target="_blank"
											rel="noopener noreferrer"
											className="text-elena-primary hover:underline"
										>
											{clinic.website}
										</a>
									) : (
										<p className="text-muted-foreground">Not provided</p>
									)}
								</div>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Schedule */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Clock className="h-5 w-5" />
							Operating Schedule
						</CardTitle>
					</CardHeader>
					<CardContent>
						{clinic.clinicSchedules && clinic.clinicSchedules.length > 0 ? (
							<div className="space-y-2">
								{/* Filter out duplicates and prefer capitalized day names */}
								{(
									clinic.clinicSchedules as Array<{
										day: string;
										start_time: string;
										end_time: string;
										is_active: number;
									}>
								)
									.filter((schedule, index, self) => {
										// Keep only the latest entry for each day (prefer capitalized)
										const dayLower = schedule.day.toLowerCase();
										const laterEntry = self.find(
											(s, i) => i > index && s.day.toLowerCase() === dayLower
										);
										return !laterEntry;
									})
									.map((daySchedule) => (
										<div
											key={daySchedule.day}
											className="flex items-center justify-between py-1"
										>
											<span className="font-medium">
												{daySchedule.day.charAt(0).toUpperCase() +
													daySchedule.day.slice(1).toLowerCase()}
											</span>
											<span
												className={
													daySchedule.is_active === 1
														? 'text-muted-foreground'
														: 'text-red-500'
												}
											>
												{daySchedule.is_active === 1
													? `${formatTime(daySchedule.start_time)} - ${formatTime(daySchedule.end_time)}`
													: 'Closed'}
											</span>
										</div>
									))}
							</div>
						) : (
							<p className="text-muted-foreground">No schedule configured</p>
						)}
					</CardContent>
				</Card>

				{/* Location */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<MapPin className="h-5 w-5" />
							Location
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						{hasCoordinates ? (
							<div className="space-y-2">
								<div>
									<label className="text-sm font-medium">Coordinates</label>
									<p className="text-muted-foreground">
										Lat: {clinic.latitude}, Lng: {clinic.longitude}
									</p>
								</div>
								<div className="overflow-hidden rounded-lg border">
									<iframe
										src={`https://www.google.com/maps/embed/v1/place?key=${env.NEXT_PUBLIC_GOOGLE_MAP_KEY}&q=${clinic.latitude},${clinic.longitude}&zoom=15`}
										width="100%"
										height="200"
										style={{ border: 0 }}
										allowFullScreen
										loading="lazy"
										referrerPolicy="no-referrer-when-downgrade"
										title="Clinic Location"
									/>
								</div>
							</div>
						) : (
							<div>
								<p className="text-muted-foreground">
									No coordinates provided for map display
								</p>
								<p className="text-muted-foreground text-sm">
									Add latitude and longitude to enable map integration
								</p>
							</div>
						)}
					</CardContent>
				</Card>
			</div>
		</div>
	);
}

function ClinicDetailSkeleton() {
	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<Skeleton className="h-8 w-64" />
					<Skeleton className="mt-2 h-4 w-32" />
				</div>
				<div className="flex gap-2">
					<Skeleton className="h-9 w-24" />
					<Skeleton className="h-9 w-24" />
				</div>
			</div>

			<div className="grid gap-6 md:grid-cols-2">
				{Array.from({ length: 4 }).map((_, i) => (
					<Card key={i}>
						<CardHeader>
							<Skeleton className="h-6 w-48" />
						</CardHeader>
						<CardContent className="space-y-4">
							{Array.from({ length: 3 }).map((_, j) => (
								<div key={j}>
									<Skeleton className="h-4 w-24" />
									<Skeleton className="mt-1 h-4 w-full" />
								</div>
							))}
						</CardContent>
					</Card>
				))}
			</div>
		</div>
	);
}
