'use client';

import { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { cn } from '@/core/lib/utils';
import { IClinicSchedule } from '@/features/dashboard/types/doctor.types';

interface IClinicScheduleProps {
	schedule?: IClinicSchedule[];
	onChange: (schedule: IClinicSchedule[]) => void;
	className?: string;
}

const DAYS_OF_WEEK = [
	'Monday',
	'Tuesday',
	'Wednesday',
	'Thursday',
	'Friday',
	'Saturday',
	'Sunday',
];

const DEFAULT_SCHEDULE: IClinicSchedule[] = DAYS_OF_WEEK.map((day) => ({
	day,
	start_time: '09:00',
	end_time: '17:00',
	is_active: 1,
}));

export function ClinicSchedule({
	schedule,
	onChange,
	className,
}: IClinicScheduleProps) {
	const [scheduleData, setScheduleData] = useState<IClinicSchedule[]>(
		schedule && schedule.length > 0 ? schedule : DEFAULT_SCHEDULE
	);

	// Update schedule data when the schedule prop changes
	useEffect(() => {
		if (schedule && schedule.length > 0) {
			setScheduleData(schedule);
		} else {
			setScheduleData(DEFAULT_SCHEDULE);
		}
	}, [schedule]);

	const updateSchedule = (updatedSchedule: IClinicSchedule[]) => {
		setScheduleData(updatedSchedule);
		onChange(updatedSchedule);
	};

	const handleDayToggle = (dayIndex: number, isActive: boolean) => {
		const updated = [...scheduleData];
		updated[dayIndex] = {
			...updated[dayIndex],
			is_active: isActive ? 1 : 0,
		};
		updateSchedule(updated);
	};

	const handleTimeChange = (
		dayIndex: number,
		field: 'start_time' | 'end_time',
		value: string
	) => {
		const updated = [...scheduleData];
		updated[dayIndex] = {
			...updated[dayIndex],
			[field]: value,
		};
		updateSchedule(updated);
	};

	const setAllDays = (isActive: boolean) => {
		const updated = scheduleData.map((day) => ({
			...day,
			is_active: isActive ? 1 : 0,
		}));
		updateSchedule(updated);
	};

	const setStandardHours = () => {
		const updated = scheduleData.map((day) => ({
			...day,
			start_time: '09:00',
			end_time: '17:00',
		}));
		updateSchedule(updated);
	};

	return (
		<div className={cn('space-y-4', className)}>
			<div className="flex items-center justify-between">
				<Label className="text-base font-medium">Clinic Schedule</Label>
				<div className="flex gap-2">
					<Button
						type="button"
						variant="outline"
						size="sm"
						onClick={() => setAllDays(true)}
					>
						Enable All
					</Button>
					<Button
						type="button"
						variant="outline"
						size="sm"
						onClick={() => setAllDays(false)}
					>
						Disable All
					</Button>
					<Button
						type="button"
						variant="outline"
						size="sm"
						onClick={setStandardHours}
					>
						Standard Hours
					</Button>
				</div>
			</div>

			<div className="space-y-3">
				{scheduleData.map((daySchedule, index) => (
					<div
						key={daySchedule.day}
						className="flex items-center gap-4 rounded-lg border p-4"
					>
						<div className="flex items-center space-x-2">
							<Switch
								checked={daySchedule.is_active === 1}
								onCheckedChange={(checked) => handleDayToggle(index, checked)}
							/>
							<Label className="w-20 text-sm font-medium">
								{daySchedule.day}
							</Label>
						</div>

						<div className="flex flex-1 items-center gap-4">
							<div className="flex items-center gap-2">
								<Label htmlFor={`start-${index}`} className="text-sm">
									Start:
								</Label>
								<Input
									id={`start-${index}`}
									type="time"
									value={daySchedule.start_time}
									onChange={(e) =>
										handleTimeChange(index, 'start_time', e.target.value)
									}
									disabled={daySchedule.is_active === 0}
									className="w-32"
								/>
							</div>

							<div className="flex items-center gap-2">
								<Label htmlFor={`end-${index}`} className="text-sm">
									End:
								</Label>
								<Input
									id={`end-${index}`}
									type="time"
									value={daySchedule.end_time}
									onChange={(e) =>
										handleTimeChange(index, 'end_time', e.target.value)
									}
									disabled={daySchedule.is_active === 0}
									className="w-32"
								/>
							</div>
						</div>

						{daySchedule.is_active === 0 && (
							<span className="text-muted-foreground text-sm">Closed</span>
						)}
					</div>
				))}
			</div>

			<div className="text-muted-foreground text-sm">
				<p>
					Set your clinic&apos;s operating hours for each day of the week.
					Toggle days on/off and adjust times as needed.
				</p>
			</div>
		</div>
	);
}
