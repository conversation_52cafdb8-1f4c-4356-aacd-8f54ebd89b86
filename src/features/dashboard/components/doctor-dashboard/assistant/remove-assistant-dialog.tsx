'use client';

import { useState } from 'react';

import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Skeleton } from '@/components/ui/skeleton';
import {
	useAssistantClinics,
	useRemoveAssistant,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import { IAssistant, IClinic } from '@/features/dashboard/types/doctor.types';

interface IClinicAssistant {
	id: number;
	profileId: number;
	clinicId: number;
	clinic: IClinic;
}

interface IRemoveAssistantDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	assistant: IAssistant | null;
}

export function RemoveAssistantDialog({
	open,
	onOpenChange,
	assistant,
}: IRemoveAssistantDialogProps) {
	const [selectedClinicId, setSelectedClinicId] = useState<string>('');
	const [showConfirmation, setShowConfirmation] = useState(false);

	const removeAssistantMutation = useRemoveAssistant();

	// Fetch clinics for this assistant
	const {
		data: clinicsData,
		isLoading: isLoadingClinics,
		error: clinicsError,
	} = useAssistantClinics(assistant?.profile_id || assistant?.id || 0);

	const clinics: IClinicAssistant[] = clinicsData?.data || [];

	const handleRemove = () => {
		if (!selectedClinicId) return;
		setShowConfirmation(true);
	};

	const confirmRemove = async () => {
		if (!assistant || !selectedClinicId) return;

		try {
			await removeAssistantMutation.mutateAsync({
				clinicId: parseInt(selectedClinicId),
				assistantProfileId: assistant.profile_id || assistant.id,
			});

			setShowConfirmation(false);
			setSelectedClinicId('');
			onOpenChange(false);
		} catch (error) {
			console.error('Error removing assistant from clinic:', error);
		}
	};

	const handleOpenChange = (newOpen: boolean) => {
		if (!newOpen) {
			setSelectedClinicId('');
			setShowConfirmation(false);
		}
		onOpenChange(newOpen);
	};

	const selectedClinic = clinics.find(
		(clinicAssistant) =>
			clinicAssistant.clinic.id.toString() === selectedClinicId
	)?.clinic;

	return (
		<>
			<Dialog open={open && !showConfirmation} onOpenChange={handleOpenChange}>
				<DialogContent className="sm:max-w-[425px]">
					<DialogHeader>
						<DialogTitle>Remove Assistant from Clinic</DialogTitle>
						<DialogDescription>
							Select which clinic to remove{' '}
							<strong>
								{assistant?.first_name} {assistant?.last_name}
							</strong>{' '}
							from.
						</DialogDescription>
					</DialogHeader>

					<div className="py-4">
						{isLoadingClinics ? (
							<div className="space-y-3">
								<Skeleton className="h-4 w-full" />
								<Skeleton className="h-4 w-full" />
								<Skeleton className="h-4 w-full" />
							</div>
						) : clinicsError ? (
							<div className="py-4 text-center">
								<p className="text-destructive text-sm">
									Error loading clinics. Please try again.
								</p>
							</div>
						) : clinics.length === 0 ? (
							<div className="py-4 text-center">
								<p className="text-muted-foreground text-sm">
									This assistant is not assigned to any clinics.
								</p>
							</div>
						) : (
							<div className="space-y-3">
								<Label className="text-sm font-medium">Select Clinic:</Label>
								<RadioGroup
									value={selectedClinicId}
									onValueChange={setSelectedClinicId}
								>
									{clinics.map((clinicAssistant) => (
										<div
											key={clinicAssistant.clinic.id}
											className="flex items-center space-x-2"
										>
											<RadioGroupItem
												value={clinicAssistant.clinic.id.toString()}
												id={`clinic-${clinicAssistant.clinic.id}`}
											/>
											<Label
												htmlFor={`clinic-${clinicAssistant.clinic.id}`}
												className="flex-1 cursor-pointer"
											>
												<div>
													<div className="font-medium">
														{clinicAssistant.clinic.name}
													</div>
													{clinicAssistant.clinic.address && (
														<div className="text-muted-foreground text-sm">
															{clinicAssistant.clinic.address}
														</div>
													)}
												</div>
											</Label>
										</div>
									))}
								</RadioGroup>
							</div>
						)}
					</div>

					<DialogFooter>
						<Button
							type="button"
							variant="outline"
							onClick={() => handleOpenChange(false)}
						>
							Cancel
						</Button>
						<Button
							type="button"
							variant="destructive"
							onClick={handleRemove}
							disabled={!selectedClinicId || removeAssistantMutation.isPending}
						>
							Remove from Clinic
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Confirmation Dialog */}
			<AlertDialog open={showConfirmation} onOpenChange={setShowConfirmation}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Confirm Removal</AlertDialogTitle>
						<AlertDialogDescription>
							Are you sure you want to remove{' '}
							<strong>
								{assistant?.first_name} {assistant?.last_name}
							</strong>{' '}
							from <strong>{selectedClinic?.name}</strong>? This action will
							remove their access to this clinic but will not delete their
							account.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={confirmRemove}
							className="bg-destructive hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 text-white"
							disabled={removeAssistantMutation.isPending}
						>
							{removeAssistantMutation.isPending
								? 'Removing...'
								: 'Remove from Clinic'}
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</>
	);
}
