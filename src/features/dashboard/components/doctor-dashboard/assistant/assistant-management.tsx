'use client';

import { Plus, Search, UserCheck, Users } from 'lucide-react';
import { useQueryStates } from 'nuqs';
import React, { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { searchParamParsers } from '@/core/lib/search-params';
import {
	useAssistants,
	useClinics,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';

import { AssistantList } from './assistant-list';
import { ClinicAssistantsList } from './clinic-assistants-list';
import { CreateAssistantDialog } from './create-assistant-dialog';

export function AssistantManagement() {
	// URL search parameters using nuqs
	const [{ action, search, page }, setSearchParams] = useQueryStates({
		action: searchParamParsers.action,
		search: searchParamParsers.search,
		page: searchParamParsers.page,
	});
	const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(
		action === 'create'
	);
	const [activeTab, setActiveTab] = useState('all');

	// Fetch assistants and clinics
	const {
		data: assistantsData,
		isLoading: isLoadingAssistants,
		error: assistantsError,
	} = useAssistants({
		page,
		pageSize: 10,
	});

	const { data: clinicsData } = useClinics({});

	const assistants = assistantsData?.data?.data || [];
	const clinics = clinicsData?.data?.data || [];
	const totalAssistants = assistantsData?.data?.meta?.total || 0;

	const handleSearch = (value: string) => {
		setSearchParams({ search: value, page: 1 }); // Reset to first page when searching
	};

	// Handle URL-based actions (when user navigates directly with query params)
	React.useEffect(() => {
		if (action === 'create') {
			setIsCreateDialogOpen(true);
			// Clean up URL
			setSearchParams({ action: null });
		}
	}, [action, setSearchParams]);

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-semibold tracking-tight">
						Assistant Management
					</h1>
					<p className="text-muted-foreground">
						Manage your clinic assistants and their assignments
					</p>
				</div>
				<Button onClick={() => setIsCreateDialogOpen(true)} className="gap-2">
					<Plus className="h-4 w-4" />
					Add Assistant
				</Button>
			</div>

			{/* Stats Cards */}
			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							Total Assistants
						</CardTitle>
						<Users className="text-muted-foreground h-4 w-4" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{totalAssistants}</div>
						<p className="text-muted-foreground text-xs">Across all clinics</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							Active Clinics
						</CardTitle>
						<UserCheck className="text-muted-foreground h-4 w-4" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{clinics.length}</div>
						<p className="text-muted-foreground text-xs">
							With assistant support
						</p>
					</CardContent>
				</Card>
			</div>

			{/* Search and Filters */}
			<div className="flex items-center gap-4">
				<div className="relative max-w-sm flex-1">
					<Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
					<Input
						placeholder="Search assistants..."
						value={search}
						onChange={(e) => handleSearch(e.target.value)}
						className="pl-9"
					/>
				</div>
			</div>

			{/* Tabs */}
			<Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
				<TabsList>
					<TabsTrigger value="all">All Assistants</TabsTrigger>
					<TabsTrigger value="by-clinic">By Clinic</TabsTrigger>
				</TabsList>

				<TabsContent value="all" className="space-y-4">
					<AssistantList
						assistants={assistants}
						clinics={clinics}
						isLoading={isLoadingAssistants}
						error={assistantsError}
						currentPage={page}
						totalPages={assistantsData?.data?.meta?.last_page || 1}
						onPageChange={(newPage) => setSearchParams({ page: newPage })}
					/>
				</TabsContent>

				<TabsContent value="by-clinic" className="space-y-4">
					{clinics.map((clinic) => (
						<Card key={clinic.id}>
							<CardHeader>
								<CardTitle className="flex items-center gap-2">
									<Users className="h-5 w-5" />
									{clinic.name || `Clinic ${clinic.id}`}
								</CardTitle>
							</CardHeader>
							<CardContent>
								<ClinicAssistantsList clinicId={clinic.id} />
							</CardContent>
						</Card>
					))}
					{clinics.length === 0 && (
						<div className="py-8 text-center">
							<p className="text-muted-foreground">
								No clinics found. Create a clinic first to manage assistants.
							</p>
						</div>
					)}
				</TabsContent>
			</Tabs>

			{/* Create Assistant Dialog */}
			<CreateAssistantDialog
				open={isCreateDialogOpen}
				onOpenChange={setIsCreateDialogOpen}
				clinics={clinics}
			/>
		</div>
	);
}
