'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { useUpdateAssistant } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import { IAssistant } from '@/features/dashboard/types/doctor.types';

const editAssistantSchema = z.object({
	firstName: z.string().min(1, 'First name is required'),
	middleName: z.string().optional(),
	lastName: z.string().min(1, 'Last name is required'),
	suffix: z.string().optional(),
	email: z.string().email().optional().or(z.literal('')),
	gender: z.string().optional(),
	phone: z.string().optional(),
});

type EditAssistantFormData = z.infer<typeof editAssistantSchema>;

interface IEditAssistantDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	assistant: IAssistant | null;
}

export function EditAssistantDialog({
	open,
	onOpenChange,
	assistant,
}: IEditAssistantDialogProps) {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const updateAssistantMutation = useUpdateAssistant();

	const form = useForm<EditAssistantFormData>({
		resolver: zodResolver(editAssistantSchema),
		defaultValues: {
			firstName: '',
			middleName: '',
			lastName: '',
			suffix: '',
			email: '',
			gender: '',
			phone: '',
		},
	});

	// Update form when assistant changes
	useEffect(() => {
		if (assistant) {
			form.reset({
				firstName: assistant.first_name || '',
				middleName: assistant.middle_name || '',
				lastName: assistant.last_name || '',
				suffix: assistant.suffix || '',
				email: assistant.user?.email || '',
				gender: assistant.gender || '',
				phone: assistant.phone || '',
			});
		}
	}, [assistant, form]);

	const onSubmit = async (data: EditAssistantFormData) => {
		if (!assistant) return;

		setIsSubmitting(true);
		try {
			// Remove empty email if not provided
			if (!data.email) {
				delete data.email;
			}

			// Debug logging
			console.log('Assistant object:', assistant);
			console.log('Assistant profile_id:', assistant.profile_id);
			console.log('Assistant id:', assistant.id);

			// Use assistant.id if profile_id is not available (backend returns Profile objects with id field)
			const profileId = assistant.profile_id || assistant.id;

			const assistantData = {
				assistantProfileId: profileId,
				...data,
			};

			console.log('Assistant data being sent:', assistantData);

			await updateAssistantMutation.mutateAsync({
				clinicId: 1, // This might need to be dynamic based on the clinic
				assistantData,
			});

			onOpenChange(false);
		} catch (error) {
			console.error('Error updating assistant:', error);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleOpenChange = (newOpen: boolean) => {
		if (!newOpen) {
			form.reset();
		}
		onOpenChange(newOpen);
	};

	return (
		<Dialog open={open} onOpenChange={handleOpenChange}>
			<DialogContent className="sm:max-w-[500px]">
				<DialogHeader>
					<DialogTitle>Edit Assistant</DialogTitle>
					<DialogDescription>
						Update the assistant&apos;s information.
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
						<div className="grid grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="firstName"
								render={({ field }) => (
									<FormItem>
										<FormLabel>First Name *</FormLabel>
										<FormControl>
											<Input placeholder="John" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="lastName"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Last Name *</FormLabel>
										<FormControl>
											<Input placeholder="Doe" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<div className="grid grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="middleName"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Middle Name</FormLabel>
										<FormControl>
											<Input placeholder="Optional" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="suffix"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Suffix</FormLabel>
										<FormControl>
											<Input placeholder="Jr., Sr., etc." {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<FormField
							control={form.control}
							name="email"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Email</FormLabel>
									<FormControl>
										<Input
											type="email"
											placeholder="<EMAIL>"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<div className="grid grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="gender"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Gender</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Select gender" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												<SelectItem value="male">Male</SelectItem>
												<SelectItem value="female">Female</SelectItem>
												<SelectItem value="other">Other</SelectItem>
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="phone"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Phone</FormLabel>
										<FormControl>
											<Input placeholder="+1234567890" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<DialogFooter>
							<Button
								type="button"
								variant="outline"
								onClick={() => handleOpenChange(false)}
								disabled={isSubmitting}
							>
								Cancel
							</Button>
							<Button type="submit" disabled={isSubmitting}>
								{isSubmitting ? 'Updating...' : 'Update Assistant'}
							</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
