'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { useCreateAssistant } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import { IClinic } from '@/features/dashboard/types/doctor.types';

const createAssistantSchema = z
	.object({
		clinicId: z.string().min(1, 'Please select a clinic'),
		firstName: z.string().min(1, 'First name is required'),
		middleName: z.string().optional(),
		lastName: z.string().min(1, 'Last name is required'),
		suffix: z.string().optional(),
		email: z.string().email().optional().or(z.literal('')),
		username: z.string().min(3, 'Username must be at least 3 characters'),
		password: z.string().min(8, 'Password must be at least 8 characters'),
		password_confirmation: z.string(),
	})
	.refine((data) => data.password === data.password_confirmation, {
		message: "Passwords don't match",
		path: ['password_confirmation'],
	});

type CreateAssistantFormData = z.infer<typeof createAssistantSchema>;

interface ICreateAssistantDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	clinics: IClinic[];
}

export function CreateAssistantDialog({
	open,
	onOpenChange,
	clinics,
}: ICreateAssistantDialogProps) {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const createAssistantMutation = useCreateAssistant();

	const form = useForm<CreateAssistantFormData>({
		resolver: zodResolver(createAssistantSchema),
		defaultValues: {
			clinicId: '',
			firstName: '',
			middleName: '',
			lastName: '',
			suffix: '',
			email: '',
			username: '',
			password: '',
			password_confirmation: '',
		},
	});

	const onSubmit = async (data: CreateAssistantFormData) => {
		setIsSubmitting(true);
		try {
			const { clinicId, ...assistantData } = data;

			// Remove empty email if not provided
			if (!assistantData.email) {
				delete assistantData.email;
			}

			await createAssistantMutation.mutateAsync({
				clinicId: parseInt(clinicId),
				assistantData,
			});

			form.reset();
			onOpenChange(false);
		} catch (error) {
			console.error('Error creating assistant:', error);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleOpenChange = (newOpen: boolean) => {
		if (!newOpen) {
			form.reset();
		}
		onOpenChange(newOpen);
	};

	return (
		<Dialog open={open} onOpenChange={handleOpenChange}>
			<DialogContent className="sm:max-w-[500px]">
				<DialogHeader>
					<DialogTitle>Create New Assistant</DialogTitle>
					<DialogDescription>
						Add a new clinic assistant. No email verification is required for
						assistants.
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
						<FormField
							control={form.control}
							name="clinicId"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Clinic *</FormLabel>
									<Select
										onValueChange={field.onChange}
										defaultValue={field.value}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select a clinic" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											{clinics.map((clinic) => (
												<SelectItem
													key={clinic.id}
													value={clinic.id.toString()}
												>
													{clinic.name || `Clinic ${clinic.id}`}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>

						<div className="grid grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="firstName"
								render={({ field }) => (
									<FormItem>
										<FormLabel>First Name *</FormLabel>
										<FormControl>
											<Input placeholder="John" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="lastName"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Last Name *</FormLabel>
										<FormControl>
											<Input placeholder="Doe" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<div className="grid grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="middleName"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Middle Name</FormLabel>
										<FormControl>
											<Input placeholder="Optional" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="suffix"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Suffix</FormLabel>
										<FormControl>
											<Input placeholder="Jr., Sr., etc." {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<FormField
							control={form.control}
							name="email"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Email (Optional)</FormLabel>
									<FormControl>
										<Input
											type="email"
											placeholder="<EMAIL>"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="username"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Username *</FormLabel>
									<FormControl>
										<Input placeholder="assistant_username" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<div className="grid grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="password"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Password *</FormLabel>
										<FormControl>
											<Input
												type="password"
												placeholder="••••••••"
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="password_confirmation"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Confirm Password *</FormLabel>
										<FormControl>
											<Input
												type="password"
												placeholder="••••••••"
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<DialogFooter>
							<Button
								type="button"
								variant="outline"
								onClick={() => handleOpenChange(false)}
								disabled={isSubmitting}
							>
								Cancel
							</Button>
							<Button type="submit" disabled={isSubmitting}>
								{isSubmitting ? 'Creating...' : 'Create Assistant'}
							</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
