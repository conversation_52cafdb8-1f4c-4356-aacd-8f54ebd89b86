'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
	Di<PERSON>,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useUpdateAssistantCredentials } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import {
	IAssistant,
	IUpdateAssistantCredentialsRequest,
} from '@/features/dashboard/types/doctor.types';

const updateCredentialsSchema = z
	.object({
		username: z
			.string()
			.min(3, 'Username must be at least 3 characters')
			.optional(),
		password: z
			.string()
			.min(8, 'Password must be at least 8 characters')
			.optional(),
		password_confirmation: z.string().optional(),
	})
	.refine(
		(data) => {
			// If password is provided, confirmation must match
			if (data.password && data.password !== data.password_confirmation) {
				return false;
			}
			return true;
		},
		{
			message: "Passwords don't match",
			path: ['password_confirmation'],
		}
	)
	.refine(
		(data) => {
			// At least one field must be provided
			return data.username || data.password;
		},
		{
			message: 'Please provide at least username or password to update',
			path: ['username'],
		}
	);

type UpdateCredentialsFormData = z.infer<typeof updateCredentialsSchema>;

interface IUpdateCredentialsDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	assistant: IAssistant | null;
}

export function UpdateCredentialsDialog({
	open,
	onOpenChange,
	assistant,
}: IUpdateCredentialsDialogProps) {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const updateCredentialsMutation = useUpdateAssistantCredentials();

	const form = useForm<UpdateCredentialsFormData>({
		resolver: zodResolver(updateCredentialsSchema),
		defaultValues: {
			username: '',
			password: '',
			password_confirmation: '',
		},
	});

	// Update form when assistant changes
	useEffect(() => {
		if (assistant) {
			form.reset({
				username: assistant.user?.username || '',
				password: '',
				password_confirmation: '',
			});
		}
	}, [assistant, form]);

	const onSubmit = async (data: UpdateCredentialsFormData) => {
		if (!assistant) return;

		setIsSubmitting(true);
		try {
			// Only send fields that have values
			const profileId = assistant.profile_id || assistant.id;
			const credentialsData: IUpdateAssistantCredentialsRequest = {};

			if (data.username) {
				credentialsData.username = data.username;
			}

			if (data.password) {
				credentialsData.password = data.password;
			}

			await updateCredentialsMutation.mutateAsync({
				assistantProfileId: profileId,
				credentialsData,
			});

			form.reset();
			onOpenChange(false);
		} catch (error) {
			console.error('Error updating credentials:', error);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleOpenChange = (newOpen: boolean) => {
		if (!newOpen) {
			form.reset();
		}
		onOpenChange(newOpen);
	};

	return (
		<Dialog open={open} onOpenChange={handleOpenChange}>
			<DialogContent className="sm:max-w-[400px]">
				<DialogHeader>
					<DialogTitle>Update Login Credentials</DialogTitle>
					<DialogDescription>
						{assistant && (
							<>
								Update login credentials for{' '}
								<strong>
									{assistant.first_name} {assistant.last_name}
								</strong>
								. Leave fields empty to keep current values.
							</>
						)}
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
						<FormField
							control={form.control}
							name="username"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Username</FormLabel>
									<FormControl>
										<Input
											placeholder="Enter new username or leave empty"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="password"
							render={({ field }) => (
								<FormItem>
									<FormLabel>New Password</FormLabel>
									<FormControl>
										<Input
											type="password"
											placeholder="Enter new password or leave empty"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="password_confirmation"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Confirm New Password</FormLabel>
									<FormControl>
										<Input
											type="password"
											placeholder="Confirm new password"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<DialogFooter>
							<Button
								type="button"
								variant="outline"
								onClick={() => handleOpenChange(false)}
								disabled={isSubmitting}
							>
								Cancel
							</Button>
							<Button type="submit" disabled={isSubmitting}>
								{isSubmitting ? 'Updating...' : 'Update Credentials'}
							</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
