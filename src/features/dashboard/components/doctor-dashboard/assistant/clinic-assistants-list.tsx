'use client';

import { MoreHorizon<PERSON>, Users } from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import { useClinicAssistants } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import { IAssistant } from '@/features/dashboard/types/doctor.types';

import { EditAssistantDialog } from './edit-assistant-dialog';
import { RemoveAssistantDialog } from './remove-assistant-dialog';
import { UpdateCredentialsDialog } from './update-credentials-dialog';

// Interface for the clinic assistant response structure
interface IClinicAssistantResponse {
	id: number;
	profile_id: number;
	clinic_id: number;
	created_at: string;
	updated_at: string;
	profile: IAssistant;
}

interface IClinicAssistantsListProps {
	clinicId: number;
}

export function ClinicAssistantsList({ clinicId }: IClinicAssistantsListProps) {
	const [selectedAssistant, setSelectedAssistant] = useState<IAssistant | null>(
		null
	);
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
	const [isCredentialsDialogOpen, setIsCredentialsDialogOpen] = useState(false);
	const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false);

	const {
		data: assistantsData,
		isLoading,
		error,
	} = useClinicAssistants(clinicId, {
		pageSize: 50, // Show more assistants per clinic
	});

	// The API returns ClinicAssistant objects with nested profile data
	// Type assertion to handle the API response structure mismatch
	const rawData =
		(assistantsData?.data?.data as unknown as IClinicAssistantResponse[]) || [];
	const assistants = rawData.map((ca) => ca.profile);

	const handleEditAssistant = (assistant: IAssistant) => {
		setSelectedAssistant(assistant);
		setIsEditDialogOpen(true);
	};

	const handleUpdateCredentials = (assistant: IAssistant) => {
		setSelectedAssistant(assistant);
		setIsCredentialsDialogOpen(true);
	};

	const handleRemoveAssistant = (assistant: IAssistant) => {
		setSelectedAssistant(assistant);
		setIsRemoveDialogOpen(true);
	};

	if (isLoading) {
		return (
			<div className="space-y-3">
				<Skeleton className="h-4 w-full" />
				<Skeleton className="h-4 w-full" />
				<Skeleton className="h-4 w-full" />
			</div>
		);
	}

	if (error) {
		return (
			<div className="py-4 text-center">
				<p className="text-destructive text-sm">
					Error loading assistants. Please try again.
				</p>
			</div>
		);
	}

	if (assistants.length === 0) {
		return (
			<div className="py-8 text-center">
				<Users className="text-muted-foreground mx-auto h-12 w-12" />
				<p className="text-muted-foreground mt-2 text-sm">
					No assistants assigned to this clinic yet.
				</p>
			</div>
		);
	}

	return (
		<>
			<div className="rounded-md border">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>Name</TableHead>
							<TableHead>Username</TableHead>
							<TableHead>Status</TableHead>
							<TableHead className="w-[70px]">Actions</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{assistants.map((assistant) => (
							<TableRow key={assistant.id}>
								<TableCell className="font-medium">
									{`${assistant.first_name} ${assistant.last_name}`}
								</TableCell>
								<TableCell>{assistant.user?.username || 'N/A'}</TableCell>
								<TableCell>
									<Badge
										variant={
											assistant.is_active === 1 ? 'default' : 'secondary'
										}
									>
										{assistant.is_active === 1 ? 'Active' : 'Inactive'}
									</Badge>
								</TableCell>
								<TableCell>
									<DropdownMenu>
										<DropdownMenuTrigger asChild>
											<Button variant="ghost" className="h-8 w-8 p-0">
												<span className="sr-only">Open menu</span>
												<MoreHorizontal className="h-4 w-4" />
											</Button>
										</DropdownMenuTrigger>
										<DropdownMenuContent align="end">
											<DropdownMenuItem
												onClick={() => handleEditAssistant(assistant)}
											>
												Edit Assistant
											</DropdownMenuItem>
											<DropdownMenuItem
												onClick={() => handleUpdateCredentials(assistant)}
											>
												Update Credentials
											</DropdownMenuItem>
											<DropdownMenuItem
												onClick={() => handleRemoveAssistant(assistant)}
												className="text-destructive"
											>
												Remove from Clinic
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								</TableCell>
							</TableRow>
						))}
					</TableBody>
				</Table>
			</div>

			{/* Dialogs */}
			<EditAssistantDialog
				open={isEditDialogOpen}
				onOpenChange={setIsEditDialogOpen}
				assistant={selectedAssistant}
			/>

			<UpdateCredentialsDialog
				open={isCredentialsDialogOpen}
				onOpenChange={setIsCredentialsDialogOpen}
				assistant={selectedAssistant}
			/>

			<RemoveAssistantDialog
				open={isRemoveDialogOpen}
				onOpenChange={setIsRemoveDialogOpen}
				assistant={selectedAssistant}
			/>
		</>
	);
}
