'use client';

import { format } from 'date-fns';
import {
	Edit,
	MoreHorizontal,
	Shield,
	Trash2,
	UserMinus,
	UserPlus,
} from 'lucide-react';
import { useState } from 'react';

import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	Pagination,
	PaginationContent,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious,
} from '@/components/ui/pagination';
import { Skeleton } from '@/components/ui/skeleton';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import { useArchiveAssistant } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import { IAssistant, IClinic } from '@/features/dashboard/types/doctor.types';

import { AssignAssistantDialog } from './assign-assistant-dialog';
import { EditAssistantDialog } from './edit-assistant-dialog';
import { RemoveAssistantDialog } from './remove-assistant-dialog';
import { UpdateCredentialsDialog } from './update-credentials-dialog';

interface IAssistantListProps {
	assistants: IAssistant[];
	clinics: IClinic[];
	isLoading: boolean;
	error: Error | null;
	currentPage: number;
	totalPages: number;
	onPageChange: (page: number) => void;
}

export function AssistantList({
	assistants,
	clinics,
	isLoading,
	error,
	currentPage,
	totalPages,
	onPageChange,
}: IAssistantListProps) {
	const [selectedAssistant, setSelectedAssistant] = useState<IAssistant | null>(
		null
	);
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
	const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);
	const [isCredentialsDialogOpen, setIsCredentialsDialogOpen] = useState(false);
	const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

	const archiveAssistantMutation = useArchiveAssistant();

	const handleEditAssistant = (assistant: IAssistant) => {
		setSelectedAssistant(assistant);
		setIsEditDialogOpen(true);
	};

	const handleAssignAssistant = (assistant: IAssistant) => {
		setSelectedAssistant(assistant);
		setIsAssignDialogOpen(true);
	};

	const handleUpdateCredentials = (assistant: IAssistant) => {
		setSelectedAssistant(assistant);
		setIsCredentialsDialogOpen(true);
	};

	const handleRemoveFromClinic = (assistant: IAssistant) => {
		setSelectedAssistant(assistant);
		setIsRemoveDialogOpen(true);
	};

	const handleDeleteAssistant = (assistant: IAssistant) => {
		setSelectedAssistant(assistant);
		setIsDeleteDialogOpen(true);
	};

	if (isLoading) {
		return (
			<div className="space-y-4">
				{[...Array(5)].map((_, i) => (
					<Card key={i}>
						<CardContent className="p-6">
							<div className="flex items-center space-x-4">
								<Skeleton className="h-12 w-12 rounded-full" />
								<div className="space-y-2">
									<Skeleton className="h-4 w-[200px]" />
									<Skeleton className="h-4 w-[150px]" />
								</div>
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		);
	}

	if (error) {
		return (
			<Card>
				<CardContent className="p-6">
					<div className="text-center">
						<p className="text-destructive">
							Error loading assistants: {error.message}
						</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	if (assistants.length === 0) {
		return (
			<Card>
				<CardContent className="p-6">
					<div className="text-center">
						<p className="text-muted-foreground">
							No assistants found. Create your first assistant to get started.
						</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<>
			<Card>
				<CardHeader>
					<CardTitle>Assistants ({assistants.length})</CardTitle>
				</CardHeader>
				<CardContent>
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>Name</TableHead>
								<TableHead>Username</TableHead>
								<TableHead>Status</TableHead>
								<TableHead>Created</TableHead>
								<TableHead className="text-right">Actions</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{assistants.map((assistant) => (
								<TableRow key={assistant.id}>
									<TableCell>
										<div className="font-medium">
											{assistant.first_name} {assistant.last_name}
										</div>
										{assistant.middle_name && (
											<div className="text-muted-foreground text-sm">
												{assistant.middle_name}
											</div>
										)}
									</TableCell>
									<TableCell>
										<code className="text-sm">
											{assistant.user?.username || 'N/A'}
										</code>
									</TableCell>
									<TableCell>
										<Badge
											variant={assistant.is_active ? 'default' : 'secondary'}
										>
											{assistant.is_active ? 'Active' : 'Inactive'}
										</Badge>
									</TableCell>
									<TableCell>
										{format(new Date(assistant.created_at), 'MMM dd, yyyy')}
									</TableCell>
									<TableCell className="text-right">
										<DropdownMenu>
											<DropdownMenuTrigger asChild>
												<Button variant="ghost" className="h-8 w-8 p-0">
													<MoreHorizontal className="h-4 w-4" />
												</Button>
											</DropdownMenuTrigger>
											<DropdownMenuContent align="end">
												<DropdownMenuItem
													onClick={() => handleEditAssistant(assistant)}
												>
													<Edit className="mr-2 h-4 w-4" />
													Edit Details
												</DropdownMenuItem>
												<DropdownMenuItem
													onClick={() => handleAssignAssistant(assistant)}
												>
													<UserPlus className="mr-2 h-4 w-4" />
													Assign to Clinic
												</DropdownMenuItem>
												<DropdownMenuItem
													onClick={() => handleUpdateCredentials(assistant)}
												>
													<Shield className="mr-2 h-4 w-4" />
													Update Credentials
												</DropdownMenuItem>
												<DropdownMenuSeparator />
												<DropdownMenuItem
													className="text-destructive"
													onClick={() => handleRemoveFromClinic(assistant)}
												>
													<UserMinus className="mr-2 h-4 w-4" />
													Remove from Clinic
												</DropdownMenuItem>
												<DropdownMenuItem
													className="text-destructive"
													onClick={() => handleDeleteAssistant(assistant)}
												>
													<Trash2 className="mr-2 h-4 w-4" />
													Delete Assistant
												</DropdownMenuItem>
											</DropdownMenuContent>
										</DropdownMenu>
									</TableCell>
								</TableRow>
							))}
						</TableBody>
					</Table>
				</CardContent>
			</Card>

			{/* Pagination */}
			{totalPages > 1 && (
				<Pagination>
					<PaginationContent>
						<PaginationItem>
							<PaginationPrevious
								onClick={() => onPageChange(Math.max(1, currentPage - 1))}
								className={
									currentPage === 1 ? 'pointer-events-none opacity-50' : ''
								}
							/>
						</PaginationItem>
						{[...Array(totalPages)].map((_, i) => (
							<PaginationItem key={i + 1}>
								<PaginationLink
									onClick={() => onPageChange(i + 1)}
									isActive={currentPage === i + 1}
								>
									{i + 1}
								</PaginationLink>
							</PaginationItem>
						))}
						<PaginationItem>
							<PaginationNext
								onClick={() =>
									onPageChange(Math.min(totalPages, currentPage + 1))
								}
								className={
									currentPage === totalPages
										? 'pointer-events-none opacity-50'
										: ''
								}
							/>
						</PaginationItem>
					</PaginationContent>
				</Pagination>
			)}

			{/* Dialogs */}
			<EditAssistantDialog
				open={isEditDialogOpen}
				onOpenChange={setIsEditDialogOpen}
				assistant={selectedAssistant}
			/>

			<AssignAssistantDialog
				open={isAssignDialogOpen}
				onOpenChange={setIsAssignDialogOpen}
				assistant={selectedAssistant}
				clinics={clinics}
			/>

			<UpdateCredentialsDialog
				open={isCredentialsDialogOpen}
				onOpenChange={setIsCredentialsDialogOpen}
				assistant={selectedAssistant}
			/>

			<RemoveAssistantDialog
				open={isRemoveDialogOpen}
				onOpenChange={setIsRemoveDialogOpen}
				assistant={selectedAssistant}
			/>

			{/* Delete Assistant Confirmation Dialog */}
			<AlertDialog
				open={isDeleteDialogOpen}
				onOpenChange={setIsDeleteDialogOpen}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Delete Assistant</AlertDialogTitle>
						<AlertDialogDescription>
							Are you sure you want to permanently delete{' '}
							<strong>
								{selectedAssistant?.first_name} {selectedAssistant?.last_name}
							</strong>
							? This action cannot be undone and will permanently remove their
							account and all associated data.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={async () => {
								if (!selectedAssistant) return;

								try {
									await archiveAssistantMutation.mutateAsync(
										selectedAssistant.profile_id || selectedAssistant.id
									);
									setIsDeleteDialogOpen(false);
									setSelectedAssistant(null);
								} catch (error) {
									console.error('Error deleting assistant:', error);
								}
							}}
							disabled={archiveAssistantMutation.isPending}
							className="bg-destructive hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 text-white"
						>
							{archiveAssistantMutation.isPending
								? 'Deleting...'
								: 'Delete Assistant'}
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</>
	);
}
