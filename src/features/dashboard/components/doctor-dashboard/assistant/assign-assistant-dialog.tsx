'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import {
	useAssignAssistant,
	useAssistantClinics,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import { IAssistant, IClinic } from '@/features/dashboard/types/doctor.types';

const assignAssistantSchema = z.object({
	clinicId: z.string().min(1, 'Please select a clinic'),
});

type AssignAssistantFormData = z.infer<typeof assignAssistantSchema>;

interface IClinicAssistant {
	id: number;
	profileId: number;
	clinicId: number;
	clinic: IClinic;
}

interface IAssignAssistantDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	assistant: IAssistant | null;
	clinics: IClinic[];
}

export function AssignAssistantDialog({
	open,
	onOpenChange,
	assistant,
	clinics,
}: IAssignAssistantDialogProps) {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const assignAssistantMutation = useAssignAssistant();

	// Fetch clinics that this assistant is already assigned to
	const {
		data: assignedClinicsData,
		isLoading: isLoadingAssignedClinics,
		error: assignedClinicsError,
	} = useAssistantClinics(assistant?.profile_id || assistant?.id || 0);

	const assignedClinics: IClinicAssistant[] = assignedClinicsData?.data || [];
	const assignedClinicIds = new Set(
		assignedClinics.map((clinicAssistant) => clinicAssistant.clinic.id)
	);

	const form = useForm<AssignAssistantFormData>({
		resolver: zodResolver(assignAssistantSchema),
		defaultValues: {
			clinicId: '',
		},
	});

	const onSubmit = async (data: AssignAssistantFormData) => {
		if (!assistant) return;

		setIsSubmitting(true);
		try {
			await assignAssistantMutation.mutateAsync({
				clinicId: parseInt(data.clinicId),
				assistantData: {
					assistantProfileId: assistant.profile_id || assistant.id,
				},
			});

			form.reset();
			onOpenChange(false);
		} catch (error) {
			console.error('Error assigning assistant:', error);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleOpenChange = (newOpen: boolean) => {
		if (!newOpen) {
			form.reset();
		}
		onOpenChange(newOpen);
	};

	return (
		<Dialog open={open} onOpenChange={handleOpenChange}>
			<DialogContent className="sm:max-w-[400px]">
				<DialogHeader>
					<DialogTitle>Assign Assistant to Clinic</DialogTitle>
					<DialogDescription>
						{assistant && (
							<>
								Assign{' '}
								<strong>
									{assistant.first_name} {assistant.last_name}
								</strong>{' '}
								to a clinic.
							</>
						)}
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
						<FormField
							control={form.control}
							name="clinicId"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Select Clinic *</FormLabel>
									{isLoadingAssignedClinics ? (
										<div className="space-y-2">
											<Skeleton className="h-10 w-full" />
											<div className="text-muted-foreground text-sm">
												Loading clinic assignments...
											</div>
										</div>
									) : assignedClinicsError ? (
										<div className="space-y-2">
											<Select
												onValueChange={field.onChange}
												defaultValue={field.value}
											>
												<FormControl>
													<SelectTrigger className="w-full text-left">
														<SelectValue
															placeholder="Choose a clinic"
															className="w-full"
														/>
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													{clinics.map((clinic) => (
														<SelectItem
															key={clinic.id}
															value={clinic.id.toString()}
														>
															<div>
																<div className="font-medium">
																	{clinic.name || `Clinic ${clinic.id}`}
																</div>
																{clinic.address && (
																	<div className="text-muted-foreground text-sm">
																		{clinic.address}
																	</div>
																)}
															</div>
														</SelectItem>
													))}
												</SelectContent>
											</Select>
											<div className="text-destructive text-sm">
												Warning: Could not load current assignments. Please
												proceed with caution.
											</div>
										</div>
									) : (
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
										>
											<FormControl>
												<SelectTrigger className="w-full text-left">
													<SelectValue
														placeholder="Choose a clinic"
														className="w-full"
													/>
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{clinics.map((clinic) => {
													const isAssigned = assignedClinicIds.has(clinic.id);
													return (
														<SelectItem
															key={clinic.id}
															value={clinic.id.toString()}
															disabled={isAssigned}
															className={
																isAssigned
																	? 'cursor-not-allowed opacity-50'
																	: ''
															}
														>
															<div>
																<div className="flex w-full items-center justify-between">
																	<div className="font-medium">
																		{clinic.name || `Clinic ${clinic.id}`}
																	</div>
																	{isAssigned && (
																		<span className="text-muted-foreground ml-2 text-xs">
																			Already assigned
																		</span>
																	)}
																</div>
																{clinic.address && (
																	<div className="text-muted-foreground text-sm">
																		{clinic.address}
																	</div>
																)}
															</div>
														</SelectItem>
													);
												})}
											</SelectContent>
										</Select>
									)}
									<FormMessage />
								</FormItem>
							)}
						/>

						<DialogFooter>
							<Button
								type="button"
								variant="outline"
								onClick={() => handleOpenChange(false)}
								disabled={isSubmitting}
							>
								Cancel
							</Button>
							<Button type="submit" disabled={isSubmitting}>
								{isSubmitting ? 'Assigning...' : 'Assign to Clinic'}
							</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
