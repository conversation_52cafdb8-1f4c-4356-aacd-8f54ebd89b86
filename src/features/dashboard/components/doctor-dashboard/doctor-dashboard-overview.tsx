'use client';

import { Calendar, CalendarCheck, Stethoscope, Users } from 'lucide-react';

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useDashboardMetrics } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';

interface IMetricCardProps {
	title: string;
	value: number;
	icon: React.ComponentType<{ className?: string }>;
	description: string;
	isLoading?: boolean;
	trend?: {
		value: number;
		isPositive: boolean;
	};
}

const MetricCard = ({
	title,
	value,
	icon: Icon,
	description,
	isLoading,
	trend,
}: IMetricCardProps) => {
	if (isLoading) {
		return (
			<Card>
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle className="text-sm font-medium">
						<Skeleton className="h-4 w-24" />
					</CardTitle>
					<Skeleton className="h-4 w-4" />
				</CardHeader>
				<CardContent>
					<Skeleton className="mb-1 h-8 w-16" />
					<Skeleton className="h-3 w-32" />
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle className="text-sm font-medium">{title}</CardTitle>
				<Icon className="text-muted-foreground h-4 w-4" />
			</CardHeader>
			<CardContent>
				<div className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
					{value.toLocaleString()}
				</div>
				<p className="text-muted-foreground text-xs">{description}</p>
				{trend && (
					<div className="mt-2 flex items-center text-xs">
						<span
							className={trend.isPositive ? 'text-green-600' : 'text-red-600'}
						>
							{trend.isPositive ? '+' : '-'}
							{Math.abs(trend.value)}%
						</span>
						<span className="text-muted-foreground ml-1">from last month</span>
					</div>
				)}
			</CardContent>
		</Card>
	);
};

export function DoctorDashboardOverview() {
	const { data: metricsResponse, isLoading, error } = useDashboardMetrics();

	const metricsData = metricsResponse?.data;

	if (error) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
				<Card className="border-destructive">
					<CardContent className="p-6">
						<div className="text-center">
							<Stethoscope className="text-destructive mx-auto mb-2 h-8 w-8" />
							<p className="text-destructive text-sm">
								Failed to load dashboard metrics
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			{/* Welcome Message */}
			<div className="mb-4">
				<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
					Welcome to Your Dashboard
				</h1>
				<p className="text-muted-foreground">
					Here&apos;s an overview of your practice today
				</p>
			</div>

			{/* Metrics Cards */}
			<div className="grid auto-rows-min gap-4 md:grid-cols-2 lg:grid-cols-3">
				<MetricCard
					title="Today's Appointments"
					value={
						metricsData?.todaysTotalAppointments ||
						metricsData?.today_appointments ||
						0
					}
					icon={Calendar}
					description="Scheduled for today"
					isLoading={isLoading}
				/>
				<MetricCard
					title="Today's Attended"
					value={
						metricsData?.todaysTotalAttendedAppointments ||
						metricsData?.total_attended ||
						0
					}
					icon={CalendarCheck}
					description="Completed consultations today"
					isLoading={isLoading}
				/>
				<MetricCard
					title="Total Patients"
					value={
						metricsData?.totalPatients || metricsData?.registered_patients || 0
					}
					icon={Users}
					description="Total patients in your practice"
					isLoading={isLoading}
				/>
			</div>

			{/* Welcome Message for New Users */}
			{!isLoading &&
				(metricsData?.todaysTotalAppointments ||
					metricsData?.total_appointments ||
					0) === 0 &&
				(metricsData?.totalPatients ||
					metricsData?.registered_patients ||
					0) === 0 && (
					<Card className="border-[oklch(0.7448_0.1256_202.74)] bg-gradient-to-r from-[oklch(0.7448_0.1256_202.74)]/5 to-[oklch(0.7448_0.1256_202.74)]/10">
						<CardContent className="p-6">
							<div className="text-center">
								<Stethoscope className="mx-auto mb-4 h-12 w-12 text-[oklch(0.7448_0.1256_202.74)]" />
								<h3 className="mb-2 text-lg font-semibold text-[oklch(0.7448_0.1256_202.74)]">
									Welcome to Elena!
								</h3>
								<p className="text-muted-foreground mb-4">
									You&apos;re all set up and ready to go! Create your first
									appointment now with Elena&apos;s comprehensive practice
									management system.
								</p>
								<div className="flex flex-wrap justify-center gap-2 text-sm">
									<span className="bg-muted rounded-full px-3 py-1">
										✓ Profile Setup Complete
									</span>
									<span className="bg-muted rounded-full px-3 py-1">
										✓ Clinic Configuration Ready
									</span>
									<span className="bg-muted rounded-full px-3 py-1">
										✓ Ready for Patients
									</span>
								</div>
							</div>
						</CardContent>
					</Card>
				)}
		</div>
	);
}
