'use client';

import { useRouter } from 'next/navigation';
import { useQueryState } from 'nuqs';
import { useState } from 'react';

import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import {
	SidebarInset,
	SidebarProvider,
	SidebarTrigger,
} from '@/components/ui/sidebar';
import { searchParamParsers } from '@/core/lib/search-params';
import { AppointmentDetailsPage } from '@/features/dashboard/components/doctor-dashboard/appointment-details-page';
import { AppointmentHistories } from '@/features/dashboard/components/doctor-dashboard/appointment-histories';
import { AssistantManagement } from '@/features/dashboard/components/doctor-dashboard/assistant/assistant-management';
// ClinicManagement is now unified
// DoctorAppointmentQueue is now unified
import { DoctorArchiveManagement } from '@/features/dashboard/components/doctor-dashboard/doctor-archive-management';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ie<PERSON> } from '@/features/dashboard/components/doctor-dashboard/doctor-calendar-view';
import { CreateAppointmentDialog } from '@/features/dashboard/components/doctor-dashboard/doctor-create-appointment-dialog';
import { DoctorDashboardOverview } from '@/features/dashboard/components/doctor-dashboard/doctor-dashboard-overview';
import { DoctorSettingsManagement } from '@/features/dashboard/components/doctor-dashboard/doctor-settings-management';
// DoctorSidebar is now unified
import { DoctorSubscriptionManagement } from '@/features/dashboard/components/doctor-dashboard/doctor-subscription-management';
import { QRAppointmentDialog } from '@/features/dashboard/components/qr-appointment/qr-appointment-dialog';
// MedicalDocumentsManagement is now unified
// PatientManagement is now unified

// Import unified components
import { AppointmentQueue } from '@/features/dashboard/components/shared/appointment-queue';
import { ClinicManagement } from '@/features/dashboard/components/shared/clinic-management';
import { MedicalDocumentsManagement } from '@/features/dashboard/components/shared/medical-documents-management';
import { PatientManagement } from '@/features/dashboard/components/shared/patient-management';
import { UnifiedSidebar } from '@/features/dashboard/components/shared/unified-sidebar';
import { EDoctorDashboardTab } from '@/features/dashboard/types/doctor.types';

import { DoctorOnboardingGuard } from './onboarding/doctor-onboarding-guard';
import { DoctorPasswordChangeGuard } from './onboarding/doctor-password-change-guard';

// Appointment Queue wrapper for doctor dashboard
function DoctorAppointmentQueueWrapper() {
	const [appointmentId, setAppointmentId] = useQueryState(
		'appointmentId',
		searchParamParsers.appointmentId
	);
	const [createDialogOpen, setCreateDialogOpen] = useState(false);
	const [qrAppointmentOpen, setQrAppointmentOpen] = useState(false);

	const handleViewAppointment = (id: number) => {
		setAppointmentId(id);
	};

	const handleCreateAppointment = () => {
		setCreateDialogOpen(true);
	};

	const handleQRAppointment = () => {
		setQrAppointmentOpen(true);
	};

	const handlePatientVerified = () => {
		setCreateDialogOpen(true);
	};

	// If appointmentId is present, show appointment details page
	if (appointmentId) {
		return <AppointmentDetailsPage appointmentId={appointmentId} />;
	}

	return (
		<>
			<AppointmentQueue
				onViewAppointment={handleViewAppointment}
				onCreateAppointment={handleCreateAppointment}
				onQRAppointment={handleQRAppointment}
			/>

			{/* Create Appointment Dialog */}
			<CreateAppointmentDialog
				open={createDialogOpen}
				onOpenChange={setCreateDialogOpen}
			/>

			{/* QR Appointment Dialog */}
			<QRAppointmentDialog
				open={qrAppointmentOpen}
				onOpenChange={setQrAppointmentOpen}
				onPatientVerified={handlePatientVerified}
			/>
		</>
	);
}

// Placeholder components for tabs not yet implemented

const DoctorHistory = () => {
	const [appointmentId, setAppointmentId] = useQueryState(
		'appointmentId',
		searchParamParsers.appointmentId
	);

	const handleViewAppointment = (id: number) => {
		setAppointmentId(id);
	};

	// If appointmentId is present, show appointment details
	if (appointmentId) {
		return <AppointmentDetailsPage appointmentId={appointmentId} />;
	}

	return <AppointmentHistories onViewAppointment={handleViewAppointment} />;
};

const DoctorCalendar = () => {
	const [appointmentId, setAppointmentId] = useQueryState(
		'appointmentId',
		searchParamParsers.appointmentId
	);
	const [createDialogOpen, setCreateDialogOpen] = useState(false);
	const [selectedDate, setSelectedDate] = useState<Date | null>(null);

	const handleViewAppointment = (id: number) => {
		setAppointmentId(id);
	};

	const handleCreateAppointment = (date: Date) => {
		setSelectedDate(date);
		setCreateDialogOpen(true);
	};

	// If appointmentId is present, show appointment details
	if (appointmentId) {
		return <AppointmentDetailsPage appointmentId={appointmentId} />;
	}

	return (
		<>
			<DoctorCalendarView
				onViewAppointment={handleViewAppointment}
				onCreateAppointment={handleCreateAppointment}
			/>
			<CreateAppointmentDialog
				open={createDialogOpen}
				onOpenChange={setCreateDialogOpen}
				defaultDate={selectedDate}
			/>
		</>
	);
};

const getBreadcrumbData = (tab: string) => {
	const breadcrumbMap = {
		[EDoctorDashboardTab.OVERVIEW]: {
			title: 'Dashboard Overview',
			description: "View your practice metrics and today's summary",
		},
		[EDoctorDashboardTab.APPOINTMENTS]: {
			title: 'Appointments',
			description: "Manage today's appointment queue",
		},
		[EDoctorDashboardTab.PATIENTS]: {
			title: 'Patients',
			description: 'Manage your patient records',
		},
		[EDoctorDashboardTab.CLINICS]: {
			title: 'Clinics',
			description: 'Manage your clinic information',
		},
		[EDoctorDashboardTab.ASSISTANTS]: {
			title: 'Assistants',
			description: 'Manage your clinic assistants',
		},
		[EDoctorDashboardTab.MEDICAL_DOCUMENTS]: {
			title: 'Medical Documents',
			description: 'Create and manage medical documents',
		},
		[EDoctorDashboardTab.HISTORY]: {
			title: 'History',
			description: 'View appointment and patient history',
		},
		[EDoctorDashboardTab.CALENDAR]: {
			title: 'Calendar',
			description: 'Calendar view of appointments',
		},
		[EDoctorDashboardTab.ARCHIVE]: {
			title: 'Archive',
			description: 'Archived patients and documents',
		},
		[EDoctorDashboardTab.SUBSCRIPTION]: {
			title: 'Subscription',
			description: 'Manage your subscription and billing',
		},
		[EDoctorDashboardTab.SETTINGS]: {
			title: 'Settings',
			description: 'Account and practice settings',
		},
	};

	return (
		breadcrumbMap[tab as EDoctorDashboardTab] ||
		breadcrumbMap[EDoctorDashboardTab.OVERVIEW]
	);
};

const renderTabContent = (tab: string) => {
	switch (tab) {
		case EDoctorDashboardTab.APPOINTMENTS:
			return <DoctorAppointmentQueueWrapper />;
		case EDoctorDashboardTab.PATIENTS:
			return <PatientManagement />;
		case EDoctorDashboardTab.CLINICS:
			return <ClinicManagement />;
		case EDoctorDashboardTab.ASSISTANTS:
			return <AssistantManagement />;
		case EDoctorDashboardTab.MEDICAL_DOCUMENTS:
			return <MedicalDocumentsManagement />;
		case EDoctorDashboardTab.HISTORY:
			return <DoctorHistory />;
		case EDoctorDashboardTab.CALENDAR:
			return <DoctorCalendar />;
		case EDoctorDashboardTab.ARCHIVE:
			return <DoctorArchiveManagement />;
		case EDoctorDashboardTab.SUBSCRIPTION:
			return <DoctorSubscriptionManagement />;
		case EDoctorDashboardTab.SETTINGS:
			return <DoctorSettingsManagement />;
		case EDoctorDashboardTab.OVERVIEW:
		default:
			return <DoctorDashboardOverview />;
	}
};

export function DoctorDashboard() {
	const [currentTab] = useQueryState('tab', searchParamParsers.tab);
	const breadcrumbData = getBreadcrumbData(currentTab);
	const router = useRouter();

	return (
		<DoctorPasswordChangeGuard>
			<DoctorOnboardingGuard>
				<SidebarProvider>
					<UnifiedSidebar />
					<SidebarInset>
						<header className="flex h-16 shrink-0 items-center justify-between gap-2">
							<div className="flex items-center gap-2 px-4">
								<SidebarTrigger className="-ml-1" />
								<Separator
									orientation="vertical"
									className="mr-2 data-[orientation=vertical]:h-4"
								/>
								<Breadcrumb>
									<BreadcrumbList>
										<BreadcrumbItem className="hidden md:block">
											<BreadcrumbLink
												href="/dashboard"
												onClick={(e) => {
													e.preventDefault();
													router.push('/dashboard');
												}}
											>
												Doctor Dashboard
											</BreadcrumbLink>
										</BreadcrumbItem>
										<BreadcrumbSeparator className="hidden md:block" />
										<BreadcrumbItem>
											<BreadcrumbPage>{breadcrumbData.title}</BreadcrumbPage>
										</BreadcrumbItem>
									</BreadcrumbList>
								</Breadcrumb>
							</div>
						</header>
						<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
							{renderTabContent(currentTab)}
						</div>
					</SidebarInset>
				</SidebarProvider>
			</DoctorOnboardingGuard>
		</DoctorPasswordChangeGuard>
	);
}
