'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { CalendarIcon, Plus } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Checkbox } from '@/components/ui/checkbox';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { cn } from '@/core/lib/utils';
import { useCreateAppointment } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import {
	useAppointmentTypes,
	useConsultationTypes,
	useHmos,
	usePaymentTypes,
	useVisitReasons,
} from '@/features/dashboard/hooks/useReferenceData';

import {
	useClinics,
	usePatients,
} from '../../hooks/useDoctorAssistantDashboard';
import { CreatePatientDialog } from './patient/create-patient-dialog';

const appointmentSchema = z.object({
	clinicId: z.string().min(1, 'Please select a clinic'),
	patientProfileId: z.string().min(1, 'Please select a patient'),
	visitReasonId: z.string().min(1, 'Please select visit reason'),
	consultationTypeId: z.string().min(1, 'Please select consultation type'),
	appointmentTypeId: z.string().min(1, 'Please select appointment type'),
	patientHmoId: z.string().optional(),
	paymentTypeId: z.string().min(1, 'Please select payment type'),
	medicalCertificate: z.boolean(),
	appointmentDate: z.date({
		required_error: 'Please select appointment date',
	}),
	appointmentTime: z.string().min(1, 'Please select appointment time'),
});

type AppointmentFormData = z.infer<typeof appointmentSchema>;

interface ICreateAppointmentDialogProps {
	trigger?: React.ReactNode;
	open?: boolean;
	onOpenChange?: (open: boolean) => void;
	defaultDate?: Date | null;
	defaultPatientId?: string | null;
}

export function CreateAppointmentDialog({
	trigger,
	open: externalOpen,
	onOpenChange: externalOnOpenChange,
	defaultDate,
	defaultPatientId,
}: ICreateAppointmentDialogProps) {
	const [internalOpen, setInternalOpen] = useState(false);
	const open = externalOpen !== undefined ? externalOpen : internalOpen;
	const setOpen = externalOnOpenChange || setInternalOpen;
	const [selectedPatientId, setSelectedPatientId] = useState<string>('');
	const [createPatientOpen, setCreatePatientOpen] = useState(false);
	const [newlyCreatedPatientId, setNewlyCreatedPatientId] = useState<
		string | null
	>(null);
	const { data: clinicsResponse } = useClinics();
	const { data: patientsResponse } = usePatients();
	const { data: visitReasonsResponse } = useVisitReasons();
	const { data: consultationTypesResponse } = useConsultationTypes();
	const { data: appointmentTypesResponse } = useAppointmentTypes();
	const { data: paymentTypesResponse } = usePaymentTypes();
	const { data: hmosResponse } = useHmos();
	const { mutate: createAppointment, isPending } = useCreateAppointment();

	const clinics = clinicsResponse?.data?.data || [];
	const patients = patientsResponse?.data?.data || [];
	const visitReasons = visitReasonsResponse?.data || [];
	const consultationTypes = consultationTypesResponse?.data || [];
	const appointmentTypes = appointmentTypesResponse?.data || [];
	const paymentTypes = paymentTypesResponse?.data || [];
	const hmos = hmosResponse?.data || [];

	// Get selected patient's HMO data
	const selectedPatient = patients.find(
		(patient) => patient.id.toString() === selectedPatientId
	);
	const patientHmos = selectedPatient?.patient?.patient_hmo || [];

	const form = useForm<AppointmentFormData>({
		resolver: zodResolver(appointmentSchema),
		defaultValues: {
			clinicId: '',
			patientProfileId: '',
			visitReasonId: '',
			consultationTypeId: '',
			appointmentTypeId: '',
			patientHmoId: '',
			paymentTypeId: '',
			appointmentTime: '',
			medicalCertificate: false,
			appointmentDate: defaultDate || undefined,
		},
	});

	// Reset form when defaultDate changes
	useEffect(() => {
		if (defaultDate) {
			form.setValue('appointmentDate', defaultDate);
		}
	}, [defaultDate, form]);

	// Set default patient when defaultPatientId is provided
	useEffect(() => {
		if (defaultPatientId && open) {
			handlePatientChange(defaultPatientId);
		}
	}, [defaultPatientId, open]);

	// Handle newly created patient selection
	useEffect(() => {
		if (newlyCreatedPatientId) {
			// Auto-select the newly created patient
			handlePatientChange(newlyCreatedPatientId);
			setNewlyCreatedPatientId(null);
		}
	}, [newlyCreatedPatientId, patients]);

	// Handler for when a patient is successfully created
	const handlePatientCreated = (patientId: string) => {
		setNewlyCreatedPatientId(patientId);
	};

	// Handle patient selection and auto-select their HMO if available
	const handlePatientChange = (patientId: string) => {
		// Check if user selected "Add New Patient"
		if (patientId === 'new') {
			setCreatePatientOpen(true);
			return;
		}

		setSelectedPatientId(patientId);
		form.setValue('patientProfileId', patientId);

		// Auto-select patient's HMO if they have one
		const patient = patients.find((p) => p.id.toString() === patientId);
		const patientHmo = patient?.patient?.patient_hmo?.[0]; // Get first HMO if multiple

		if (patientHmo) {
			// Find matching HMO in the HMOs list by company name
			const matchingHmo = hmos.find(
				(hmo) => hmo.name.toLowerCase() === patientHmo.hmo_company.toLowerCase()
			);
			if (matchingHmo) {
				form.setValue('patientHmoId', matchingHmo.id.toString());
			}
		} else {
			// Reset HMO selection if patient has no HMO
			form.setValue('patientHmoId', '');
		}
	};

	const onSubmit = (data: AppointmentFormData) => {
		// Combine date and time into a proper datetime
		const appointmentDateTime = new Date(data.appointmentDate);
		const [hours, minutes] = data.appointmentTime.split(':');
		appointmentDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);

		const appointmentData = {
			clinicId: parseInt(data.clinicId),
			patientProfileId: parseInt(data.patientProfileId),
			visitReasonId: parseInt(data.visitReasonId),
			consultationTypeId: parseInt(data.consultationTypeId),
			appointmentTypeId: parseInt(data.appointmentTypeId),
			patientHmoId:
				data.patientHmoId && data.patientHmoId !== 'none'
					? parseInt(data.patientHmoId)
					: undefined,
			paymentTypeId: parseInt(data.paymentTypeId),
			medicalCertificate: data.medicalCertificate ? 1 : 0,
			appointmentDate: appointmentDateTime.toISOString(),
		};

		createAppointment(appointmentData, {
			onSuccess: () => {
				setOpen(false);
				form.reset();
			},
		});
	};

	const formatDate = (date: Date) => {
		return date.toLocaleDateString('en-US', {
			weekday: 'long',
			year: 'numeric',
			month: 'long',
			day: 'numeric',
		});
	};

	return (
		<>
			<Dialog open={open} onOpenChange={setOpen}>
				{trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
				<DialogContent className="max-w-2xl">
					<DialogHeader>
						<DialogTitle>Create New Appointment</DialogTitle>
						<DialogDescription>
							Schedule a new appointment for your patient. Fill in all required
							information below.
						</DialogDescription>
					</DialogHeader>

					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
							<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
								{/* Clinic Selection */}
								<FormField
									control={form.control}
									name="clinicId"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Clinic *</FormLabel>
											<Select
												onValueChange={field.onChange}
												defaultValue={field.value}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Select clinic" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													{clinics.map((clinic) => (
														<SelectItem
															key={clinic.id}
															value={clinic.id.toString()}
														>
															{clinic.name}
														</SelectItem>
													))}
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>

								{/* Patient Selection */}
								<FormField
									control={form.control}
									name="patientProfileId"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Patient *</FormLabel>
											<Select
												onValueChange={handlePatientChange}
												defaultValue={field.value}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Select patient" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<SelectItem
														value="new"
														className="text-[oklch(0.7448_0.1256_202.74)]"
													>
														<div className="flex items-center gap-2">
															<Plus className="h-4 w-4" />
															Add New Patient
														</div>
													</SelectItem>
													{patients.map((patient) => (
														<SelectItem
															key={patient.id}
															value={patient.id.toString()}
														>
															{patient.first_name} {patient.last_name}
														</SelectItem>
													))}
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							{/* Visit Reason */}
							<FormField
								control={form.control}
								name="visitReasonId"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Visit Reason *</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Select visit reason" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{visitReasons.map((reason) => (
													<SelectItem
														key={reason.id}
														value={reason.id.toString()}
													>
														{reason.name}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>

							<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
								{/* Consultation Type */}
								<FormField
									control={form.control}
									name="consultationTypeId"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Consultation Type *</FormLabel>
											<Select
												onValueChange={field.onChange}
												defaultValue={field.value}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Select type" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													{consultationTypes.map((type) => (
														<SelectItem
															key={type.id}
															value={type.id.toString()}
														>
															{type.name}
														</SelectItem>
													))}
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>

								{/* Appointment Type */}
								<FormField
									control={form.control}
									name="appointmentTypeId"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Appointment Type *</FormLabel>
											<Select
												onValueChange={field.onChange}
												defaultValue={field.value}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Select type" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													{appointmentTypes.map((type) => (
														<SelectItem
															key={type.id}
															value={type.id.toString()}
														>
															{type.name}
														</SelectItem>
													))}
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							{/* HMO Selection */}
							<FormField
								control={form.control}
								name="patientHmoId"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											HMO (Optional)
											{patientHmos.length > 0 && (
												<span className="text-muted-foreground ml-2 text-xs">
													- Patient has existing HMO:{' '}
													{patientHmos[0].hmo_company}
												</span>
											)}
										</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Select HMO" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												<SelectItem value="none">No HMO</SelectItem>
												{hmos.map((hmo) => (
													<SelectItem key={hmo.id} value={hmo.id.toString()}>
														{hmo.name}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Payment Type */}
							<FormField
								control={form.control}
								name="paymentTypeId"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Payment Type *</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Select payment type" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{paymentTypes.map((type) => (
													<SelectItem key={type.id} value={type.id.toString()}>
														{type.name}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>

							<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
								{/* Appointment Date */}
								<FormField
									control={form.control}
									name="appointmentDate"
									render={({ field }) => (
										<FormItem className="flex flex-col">
											<FormLabel>Appointment Date *</FormLabel>
											<Popover>
												<PopoverTrigger asChild>
													<FormControl>
														<Button
															variant="outline"
															className={cn(
																'w-full pl-3 text-left font-normal',
																!field.value && 'text-muted-foreground'
															)}
														>
															{field.value ? (
																formatDate(field.value)
															) : (
																<span>Pick a date</span>
															)}
															<CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
														</Button>
													</FormControl>
												</PopoverTrigger>
												<PopoverContent className="w-auto p-0" align="start">
													<Calendar
														mode="single"
														selected={field.value}
														onSelect={field.onChange}
														disabled={(date) => {
															const today = new Date();
															today.setHours(0, 0, 0, 0);
															const selectedDate = new Date(date);
															selectedDate.setHours(0, 0, 0, 0);
															return (
																selectedDate < today ||
																date < new Date('1900-01-01')
															);
														}}
													/>
												</PopoverContent>
											</Popover>
											<FormMessage />
										</FormItem>
									)}
								/>

								{/* Appointment Time */}
								<FormField
									control={form.control}
									name="appointmentTime"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Appointment Time *</FormLabel>
											<FormControl>
												<Input type="time" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							{/* Medical Certificate */}
							<FormField
								control={form.control}
								name="medicalCertificate"
								render={({ field }) => (
									<FormItem className="flex flex-row items-start space-y-0 space-x-3">
										<FormControl>
											<Checkbox
												checked={field.value}
												onCheckedChange={field.onChange}
											/>
										</FormControl>
										<div className="space-y-1 leading-none">
											<FormLabel>Request Medical Certificate</FormLabel>
											<p className="text-muted-foreground text-sm">
												Check if the patient needs a medical certificate for
												this visit.
											</p>
										</div>
									</FormItem>
								)}
							/>

							<DialogFooter>
								<Button
									type="button"
									variant="outline"
									onClick={() => setOpen(false)}
								>
									Cancel
								</Button>
								<Button
									type="submit"
									disabled={isPending}
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
								>
									{isPending ? 'Creating...' : 'Create Appointment'}
								</Button>
							</DialogFooter>
						</form>
					</Form>
				</DialogContent>
			</Dialog>

			{/* Create Patient Dialog */}
			<CreatePatientDialog
				trigger={null}
				clinics={clinics}
				open={createPatientOpen}
				onOpenChange={setCreatePatientOpen}
				onSuccess={handlePatientCreated}
			/>
		</>
	);
}
