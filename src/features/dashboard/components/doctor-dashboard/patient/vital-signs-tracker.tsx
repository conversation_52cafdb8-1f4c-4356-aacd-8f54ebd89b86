'use client';

import { format } from 'date-fns';
import {
	Activity,
	Calendar,
	Heart,
	Plus,
	Stethoscope,
	TrendingDown,
	TrendingUp,
} from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface IVitalSign {
	id: number;
	appointment_id: number;
	systolic: string;
	diastolic: string;
	pulse_rate: string;
	respiration: string;
	height: string;
	height_type: string;
	weight: string;
	weight_type: string;
	temperature: string;
	temperature_type: string;
	oxygen_saturation: string;
	capillary_blood_glucose: string;
	body_mass_index: string;
	created_at: string;
	updated_at: string;
}

interface IVitalSignsTrackerProps {
	patientId: number;
	recentVitalSign?: IVitalSign | null;
	vitalSignsHistory?: IVitalSign[];
}

const formatDate = (dateString: string) => {
	try {
		return format(new Date(dateString), 'MMM dd, yyyy hh:mm a');
	} catch {
		return 'Invalid date';
	}
};

const VitalSignCard = ({
	title,
	value,
	unit,
	icon: Icon,
	trend,
	normalRange,
}: {
	title: string;
	value: string;
	unit: string;
	icon: React.ComponentType<{ className?: string }>;
	trend?: 'up' | 'down' | 'stable';
	normalRange?: string;
}) => {
	const getTrendColor = () => {
		switch (trend) {
			case 'up':
				return 'text-red-500';
			case 'down':
				return 'text-blue-500';
			default:
				return 'text-green-500';
		}
	};

	const getTrendIcon = () => {
		switch (trend) {
			case 'up':
				return TrendingUp;
			case 'down':
				return TrendingDown;
			default:
				return Activity;
		}
	};

	const TrendIcon = getTrendIcon();

	return (
		<Card>
			<CardContent className="p-4">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-2">
						<Icon className="h-5 w-5 text-[oklch(0.7448_0.1256_202.74)]" />
						<span className="text-sm font-medium text-gray-600">{title}</span>
					</div>
					{trend && <TrendIcon className={`h-4 w-4 ${getTrendColor()}`} />}
				</div>
				<div className="mt-2">
					<div className="text-2xl font-bold text-gray-900">
						{value}{' '}
						<span className="text-sm font-normal text-gray-500">{unit}</span>
					</div>
					{normalRange && (
						<p className="mt-1 text-xs text-gray-500">Normal: {normalRange}</p>
					)}
				</div>
			</CardContent>
		</Card>
	);
};

const RecentVitalSigns = ({ vitalSign }: { vitalSign: IVitalSign }) => (
	<div className="space-y-4">
		<div className="flex items-center justify-between">
			<h3 className="text-lg font-semibold">Current Vital Signs</h3>
			<p className="text-sm text-gray-500">
				Recorded on {formatDate(vitalSign.created_at)}
			</p>
		</div>

		<div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
			<VitalSignCard
				title="Blood Pressure"
				value={`${vitalSign.systolic}/${vitalSign.diastolic}`}
				unit="mmHg"
				icon={Heart}
				normalRange="120/80"
			/>

			<VitalSignCard
				title="Pulse Rate"
				value={vitalSign.pulse_rate}
				unit="bpm"
				icon={Activity}
				normalRange="60-100"
			/>

			<VitalSignCard
				title="Temperature"
				value={vitalSign.temperature}
				unit={`°${vitalSign.temperature_type || 'C'}`}
				icon={Stethoscope}
				normalRange="36.1-37.2°C"
			/>

			<VitalSignCard
				title="Oxygen Saturation"
				value={vitalSign.oxygen_saturation}
				unit="%"
				icon={Activity}
				normalRange="95-100%"
			/>

			<VitalSignCard
				title="Respiration"
				value={vitalSign.respiration}
				unit="breaths/min"
				icon={Activity}
				normalRange="12-20"
			/>

			<VitalSignCard
				title="BMI"
				value={vitalSign.body_mass_index}
				unit="kg/m²"
				icon={Activity}
				normalRange="18.5-24.9"
			/>
		</div>

		{vitalSign.capillary_blood_glucose && (
			<div className="mt-4">
				<VitalSignCard
					title="Capillary Blood Glucose"
					value={vitalSign.capillary_blood_glucose}
					unit="mg/dL"
					icon={Activity}
					normalRange="70-140"
				/>
			</div>
		)}
	</div>
);

const VitalSignsHistory = ({ history }: { history: IVitalSign[] }) => (
	<div className="space-y-4">
		<div className="flex items-center justify-between">
			<h3 className="text-lg font-semibold">Vital Signs History</h3>
			<Badge variant="outline">{history.length} Records</Badge>
		</div>

		{history.length === 0 ? (
			<div className="flex min-h-[200px] items-center justify-center rounded-lg border border-dashed">
				<div className="text-center">
					<Stethoscope className="mx-auto h-8 w-8 text-gray-400" />
					<p className="mt-2 text-sm text-gray-500">
						No vital signs history available
					</p>
				</div>
			</div>
		) : (
			<div className="space-y-3">
				{history.map((vitalSign) => (
					<Card key={vitalSign.id}>
						<CardContent className="p-4">
							<div className="mb-3 flex items-center justify-between">
								<div className="flex items-center gap-2">
									<Calendar className="h-4 w-4 text-gray-400" />
									<span className="text-sm font-medium">
										{formatDate(vitalSign.created_at)}
									</span>
								</div>
								<Badge variant="outline">
									Appointment #{vitalSign.appointment_id}
								</Badge>
							</div>

							<div className="grid grid-cols-2 gap-4 md:grid-cols-4">
								<div>
									<p className="text-xs text-gray-500">Blood Pressure</p>
									<p className="text-sm font-medium">
										{vitalSign.systolic}/{vitalSign.diastolic} mmHg
									</p>
								</div>
								<div>
									<p className="text-xs text-gray-500">Pulse Rate</p>
									<p className="text-sm font-medium">
										{vitalSign.pulse_rate} bpm
									</p>
								</div>
								<div>
									<p className="text-xs text-gray-500">Temperature</p>
									<p className="text-sm font-medium">
										{vitalSign.temperature}°{vitalSign.temperature_type || 'C'}
									</p>
								</div>
								<div>
									<p className="text-xs text-gray-500">O2 Saturation</p>
									<p className="text-sm font-medium">
										{vitalSign.oxygen_saturation}%
									</p>
								</div>
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		)}
	</div>
);

const AddVitalSignsDialog = () => {
	const [open, setOpen] = useState(false);

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogTrigger asChild>
				<Button className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90">
					<Plus className="mr-2 h-4 w-4" />
					Record Vital Signs
				</Button>
			</DialogTrigger>
			<DialogContent className="max-w-2xl">
				<DialogHeader>
					<DialogTitle>Record New Vital Signs</DialogTitle>
					<DialogDescription>
						Record vital signs for the patient during consultation.
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-4">
					<p className="text-sm text-gray-500">
						Vital signs recording functionality will be implemented when
						appointment management is available. This feature requires an active
						appointment context.
					</p>

					<div className="flex justify-end gap-3">
						<Button variant="outline" onClick={() => setOpen(false)}>
							Cancel
						</Button>
						<Button
							disabled
							className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
						>
							Save Vital Signs
						</Button>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
};

export function VitalSignsTracker({
	recentVitalSign,
	vitalSignsHistory = [],
}: IVitalSignsTrackerProps) {
	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<h2 className="text-2xl font-bold text-gray-900">Vital Signs</h2>
				<AddVitalSignsDialog />
			</div>

			<Tabs defaultValue="current" className="w-full">
				<TabsList className="grid w-full grid-cols-3">
					<TabsTrigger value="current">Current</TabsTrigger>
					<TabsTrigger value="history">History</TabsTrigger>
					<TabsTrigger value="trends">Trends</TabsTrigger>
				</TabsList>

				<TabsContent value="current" className="space-y-4">
					{recentVitalSign ? (
						<RecentVitalSigns vitalSign={recentVitalSign} />
					) : (
						<div className="flex min-h-[400px] items-center justify-center rounded-lg border border-dashed">
							<div className="text-center">
								<Stethoscope className="mx-auto h-12 w-12 text-gray-400" />
								<h3 className="mt-2 text-sm font-semibold text-gray-900">
									No Vital Signs Recorded
								</h3>
								<p className="mt-1 text-sm text-gray-500">
									Record vital signs during patient consultation
								</p>
								<div className="mt-4">
									<AddVitalSignsDialog />
								</div>
							</div>
						</div>
					)}
				</TabsContent>

				<TabsContent value="history">
					<VitalSignsHistory history={vitalSignsHistory} />
				</TabsContent>

				<TabsContent value="trends">
					<div className="flex min-h-[400px] items-center justify-center rounded-lg border border-dashed">
						<div className="text-center">
							<Activity className="mx-auto h-12 w-12 text-gray-400" />
							<h3 className="mt-2 text-sm font-semibold text-gray-900">
								Vital Signs Trends
							</h3>
							<p className="mt-1 text-sm text-gray-500">
								Charts and trends analysis will be available here
							</p>
						</div>
					</div>
				</TabsContent>
			</Tabs>
		</div>
	);
}
