'use client';

import { format } from 'date-fns';
import {
	Activity,
	AlertTriangle,
	Edit,
	Heart,
	Mail,
	Phone,
	Stethoscope,
	User,
	UserCheck,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { usePatientDetail } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';

import { AppointmentHistory } from './appointment-history';
import { PatientProfileManagement } from './patient-profile-management';

interface IPatientDetailsProps {
	patientId: number;
	onEdit?: (id: number) => void;
	onBack?: () => void;
}

interface IPatientData {
	id: number;
	title?: string;
	first_name?: string;
	middle_name?: string;
	last_name?: string;
	suffix?: string;
	gender?: string;
	birthday?: string;
	nationality?: string;
	occupation?: string;
	civil_status?: string;
	phone?: string;
	user?: {
		email?: string;
	};
	patient?: {
		emergency_contact_name?: string;
		emergency_contact_number?: string;
		patient_allergy?: Array<unknown>;
		patient_illness?: Array<unknown>;
		patient_surgery?: Array<unknown>;
	};
	profile_current_address?: {
		address?: string;
	};
	profile_permanent_address?: {
		address?: string;
	};
	blood_type?: string;
	height?: string;
	height_type?: string;
	weight?: string;
	weight_type?: string;
	religion?: string;
	is_active?: boolean;
	is_verified?: boolean;
	is_completed?: boolean;
	created_at: string;
	updated_at: string;
	recent_vital_sign?: {
		systolic: string;
		diastolic: string;
		pulse_rate: string;
		temperature: string;
		temperature_type?: string;
		oxygen_saturation: string;
	};
}

const formatDate = (dateString: string) => {
	try {
		return format(new Date(dateString), 'MMMM dd, yyyy');
	} catch {
		return 'Invalid date';
	}
};

const formatDateTime = (dateString: string) => {
	try {
		return format(new Date(dateString), 'MMM dd, yyyy hh:mm a');
	} catch {
		return 'Invalid date';
	}
};

const LoadingSkeleton = () => (
	<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
		<div className="flex items-center gap-4">
			<Skeleton className="h-10 w-10" />
			<div className="space-y-2">
				<Skeleton className="h-6 w-[200px]" />
				<Skeleton className="h-4 w-[300px]" />
			</div>
		</div>
		<div className="grid grid-cols-1 gap-6 md:grid-cols-2">
			{[...Array(4)].map((_, i) => (
				<Card key={i}>
					<CardHeader>
						<Skeleton className="h-6 w-[150px]" />
					</CardHeader>
					<CardContent className="space-y-3">
						<Skeleton className="h-4 w-full" />
						<Skeleton className="h-4 w-[80%]" />
						<Skeleton className="h-4 w-[60%]" />
					</CardContent>
				</Card>
			))}
		</div>
	</div>
);

const PersonalInfoCard = ({ patient }: { patient: IPatientData }) => (
	<Card>
		<CardHeader>
			<CardTitle className="flex items-center gap-2 text-lg">
				<User className="h-5 w-5" />
				Personal Information
			</CardTitle>
		</CardHeader>
		<CardContent className="space-y-3">
			<div className="grid grid-cols-2 gap-4">
				<div>
					<p className="text-sm font-medium text-gray-500">Full Name</p>
					<p className="text-sm">
						{patient.title} {patient.first_name} {patient.middle_name}{' '}
						{patient.last_name} {patient.suffix}
					</p>
				</div>
				<div>
					<p className="text-sm font-medium text-gray-500">Gender</p>
					<p className="text-sm">{patient.gender || 'Not specified'}</p>
				</div>
				<div>
					<p className="text-sm font-medium text-gray-500">Birthday</p>
					<p className="text-sm">
						{patient.birthday ? formatDate(patient.birthday) : 'Not specified'}
					</p>
				</div>
				<div>
					<p className="text-sm font-medium text-gray-500">Nationality</p>
					<p className="text-sm">{patient.nationality || 'Not specified'}</p>
				</div>
				<div>
					<p className="text-sm font-medium text-gray-500">Occupation</p>
					<p className="text-sm">{patient.occupation || 'Not specified'}</p>
				</div>
				<div>
					<p className="text-sm font-medium text-gray-500">Civil Status</p>
					<p className="text-sm">{patient.civil_status || 'Not specified'}</p>
				</div>
			</div>
		</CardContent>
	</Card>
);

const ContactInfoCard = ({ patient }: { patient: IPatientData }) => (
	<Card>
		<CardHeader>
			<CardTitle className="flex items-center gap-2 text-lg">
				<Phone className="h-5 w-5" />
				Contact Information
			</CardTitle>
		</CardHeader>
		<CardContent className="space-y-3">
			<div className="space-y-3">
				<div className="flex items-center gap-2">
					<Phone className="h-4 w-4 text-gray-400" />
					<span className="text-sm">{patient.phone || 'No phone number'}</span>
				</div>
				{patient.user?.email && (
					<div className="flex items-center gap-2">
						<Mail className="h-4 w-4 text-gray-400" />
						<span className="text-sm">{patient.user.email}</span>
					</div>
				)}
			</div>
		</CardContent>
	</Card>
);

const MedicalInfoCard = ({ patient }: { patient: IPatientData }) => (
	<Card>
		<CardHeader>
			<CardTitle className="flex items-center gap-2 text-lg">
				<Heart className="h-5 w-5" />
				Medical Information
			</CardTitle>
		</CardHeader>
		<CardContent className="space-y-3">
			<div className="grid grid-cols-2 gap-4">
				<div>
					<p className="text-sm font-medium text-gray-500">Blood Type</p>
					<p className="text-sm">{patient.blood_type || 'Not specified'}</p>
				</div>
				<div>
					<p className="text-sm font-medium text-gray-500">Height</p>
					<p className="text-sm">
						{patient.height
							? `${patient.height} ${patient.height_type || ''}`
							: 'Not specified'}
					</p>
				</div>
				<div>
					<p className="text-sm font-medium text-gray-500">Weight</p>
					<p className="text-sm">
						{patient.weight
							? `${patient.weight} ${patient.weight_type || ''}`
							: 'Not specified'}
					</p>
				</div>
				<div>
					<p className="text-sm font-medium text-gray-500">Religion</p>
					<p className="text-sm">{patient.religion || 'Not specified'}</p>
				</div>
			</div>
		</CardContent>
	</Card>
);

const EmergencyContactCard = ({ patient }: { patient: IPatientData }) => (
	<Card>
		<CardHeader>
			<CardTitle className="flex items-center gap-2 text-lg">
				<UserCheck className="h-5 w-5" />
				Emergency Contact
			</CardTitle>
		</CardHeader>
		<CardContent className="space-y-3">
			{patient.patient?.emergency_contact_name ||
			patient.patient?.emergency_contact_number ? (
				<div className="space-y-2">
					<div>
						<p className="text-sm font-medium text-gray-500">Contact Person</p>
						<p className="text-sm">
							{patient.patient.emergency_contact_name || 'Not specified'}
						</p>
					</div>
					<div>
						<p className="text-sm font-medium text-gray-500">Contact Number</p>
						<p className="text-sm">
							{patient.patient.emergency_contact_number || 'Not specified'}
						</p>
					</div>
				</div>
			) : (
				<p className="text-sm text-gray-500">
					No emergency contact information
				</p>
			)}
		</CardContent>
	</Card>
);

const StatusCard = ({ patient }: { patient: IPatientData }) => (
	<Card>
		<CardHeader>
			<CardTitle className="flex items-center gap-2 text-lg">
				<Activity className="h-5 w-5" />
				Status & Registration
			</CardTitle>
		</CardHeader>
		<CardContent className="space-y-3">
			<div className="flex flex-wrap gap-2">
				<Badge variant={patient.is_active ? 'default' : 'secondary'}>
					{patient.is_active ? 'Active' : 'Inactive'}
				</Badge>
				<Badge variant={patient.is_verified ? 'default' : 'secondary'}>
					{patient.is_verified ? 'Verified' : 'Unverified'}
				</Badge>
				<Badge variant={patient.is_completed ? 'default' : 'secondary'}>
					{patient.is_completed ? 'Profile Complete' : 'Profile Incomplete'}
				</Badge>
			</div>
			<div className="space-y-2">
				<div>
					<p className="text-sm font-medium text-gray-500">Registered</p>
					<p className="text-sm">{formatDateTime(patient.created_at)}</p>
				</div>
				<div>
					<p className="text-sm font-medium text-gray-500">Last Updated</p>
					<p className="text-sm">{formatDateTime(patient.updated_at)}</p>
				</div>
			</div>
		</CardContent>
	</Card>
);

export function PatientDetails({ patientId, onEdit }: IPatientDetailsProps) {
	const { data: patient, isLoading, error } = usePatientDetail(patientId);

	const handleEditPatient = () => {
		onEdit?.(patientId);
	};

	if (isLoading) {
		return <LoadingSkeleton />;
	}

	if (error || !patient) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
				<Card className="border-destructive">
					<CardContent className="p-6">
						<div className="text-center">
							<User className="text-destructive mx-auto mb-2 h-8 w-8" />
							<p className="text-destructive text-sm">
								{error ? 'Failed to load patient details' : 'Patient not found'}
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="flex flex-1 flex-col gap-4 pt-0">
			<div className="space-y-2">
				{/* Header */}
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-4">
						<div>
							<h1 className="text-2xl font-bold text-gray-900">
								{patient.title} {patient.first_name} {patient.middle_name}{' '}
								{patient.last_name} {patient.suffix}
							</h1>
							<p className="text-sm text-gray-500">Patient ID: {patient.id}</p>
						</div>
					</div>
					<Button
						onClick={handleEditPatient}
						className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
					>
						<Edit className="mr-2 h-4 w-4" />
						Edit Patient
					</Button>
				</div>
			</div>

			{/* Patient Details Tabs */}
			<Tabs defaultValue="overview" className="w-full">
				<TabsList className="grid w-full grid-cols-3">
					<TabsTrigger value="overview">Overview</TabsTrigger>
					<TabsTrigger value="profile-management">
						Profile Management
					</TabsTrigger>
					<TabsTrigger value="appointments">Appointments</TabsTrigger>
				</TabsList>

				<TabsContent value="overview" className="space-y-6">
					{/* Patient Information Cards */}
					<div className="grid grid-cols-1 gap-6 md:grid-cols-2">
						<PersonalInfoCard patient={patient} />
						<ContactInfoCard patient={patient} />
						<MedicalInfoCard patient={patient} />
						<EmergencyContactCard patient={patient} />
						<StatusCard patient={patient} />
						<Card>
							<CardHeader>
								<CardTitle className="flex items-center gap-2 text-lg">
									<Stethoscope className="h-5 w-5" />
									Recent Vital Signs
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="grid grid-cols-2 gap-4">
									<div>
										<p className="text-sm font-medium text-gray-500">
											Blood Pressure
										</p>
										<p className="text-sm">
											{patient.recentVitalSign?.systolic}/
											{patient.recentVitalSign?.diastolic} mmHg
										</p>
									</div>
									<div>
										<p className="text-sm font-medium text-gray-500">
											Pulse Rate
										</p>
										<p className="text-sm">
											{patient.recentVitalSign?.pulse_rate} bpm
										</p>
									</div>
									<div>
										<p className="text-sm font-medium text-gray-500">
											Temperature
										</p>
										<p className="text-sm">
											{patient.recentVitalSign?.temperature}°
											{patient.recentVitalSign?.temperature_type || 'C'}
										</p>
									</div>
									<div>
										<p className="text-sm font-medium text-gray-500">
											Oxygen Saturation
										</p>
										<p className="text-sm">
											{patient.recentVitalSign?.oxygen_saturation}%
										</p>
									</div>
									<div>
										<p className="text-sm font-medium text-gray-500">
											Respiration
										</p>
										<p className="text-sm">
											{patient.recentVitalSign?.respiration} breaths/min
										</p>
									</div>
									<div>
										<p className="text-sm font-medium text-gray-500">Height</p>
										<p className="text-sm">
											{patient.recentVitalSign?.height}{' '}
											{patient.recentVitalSign?.height_type}
										</p>
									</div>
									<div>
										<p className="text-sm font-medium text-gray-500">Weight</p>
										<p className="text-sm">
											{patient.recentVitalSign?.weight}{' '}
											{patient.recentVitalSign?.weight_type}
										</p>
									</div>
									<div>
										<p className="text-sm font-medium text-gray-500">
											Capillary Blood Glucose
										</p>
										<p className="text-sm">
											{patient.recentVitalSign?.capillary_blood_glucose} mg/dL
										</p>
									</div>
									<div>
										<p className="text-sm font-medium text-gray-500">
											Body Mass Index
										</p>
										<p className="text-sm">
											{patient.recentVitalSign?.body_mass_index} kg/m²
										</p>
									</div>
								</div>
							</CardContent>
						</Card>
					</div>

					{/* Medical History Summary */}
					{patient.patient && (
						<div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
							{patient.patient.patient_allergy &&
								patient.patient.patient_allergy.length > 0 && (
									<Card>
										<CardHeader>
											<CardTitle className="flex items-center gap-2 text-lg">
												<AlertTriangle className="h-5 w-5" />
												Allergies ({patient.patient.patient_allergy.length})
											</CardTitle>
										</CardHeader>
										<CardContent>
											<div className="space-y-2">
												{patient.patient.patient_allergy
													.slice(0, 3)
													.map(
														(allergy: {
															id: number;
															allergen: string;
															reaction: string;
														}) => (
															<div key={allergy.id} className="text-sm">
																<p className="font-medium">
																	{allergy.allergen}
																</p>
																<p className="text-gray-600">
																	{allergy.reaction}
																</p>
															</div>
														)
													)}
												{patient.patient.patient_allergy.length > 3 && (
													<p className="text-sm text-gray-500">
														+{patient.patient.patient_allergy.length - 3} more
													</p>
												)}
											</div>
										</CardContent>
									</Card>
								)}

							{patient.patient.patient_illness &&
								patient.patient.patient_illness.length > 0 && (
									<Card>
										<CardHeader>
											<CardTitle className="flex items-center gap-2 text-lg">
												<Activity className="h-5 w-5" />
												Medical Conditions (
												{patient.patient.patient_illness.length})
											</CardTitle>
										</CardHeader>
										<CardContent>
											<div className="space-y-2">
												{patient.patient.patient_illness
													.slice(0, 3)
													.map(
														(illness: {
															id: number;
															illness: string;
															description: string;
														}) => (
															<div key={illness.id} className="text-sm">
																<p className="font-medium">{illness.illness}</p>
																<p className="text-gray-600">
																	{illness.description}
																</p>
															</div>
														)
													)}
												{patient.patient.patient_illness.length > 3 && (
													<p className="text-sm text-gray-500">
														+{patient.patient.patient_illness.length - 3} more
													</p>
												)}
											</div>
										</CardContent>
									</Card>
								)}

							{patient.patient.patient_surgery &&
								patient.patient.patient_surgery.length > 0 && (
									<Card>
										<CardHeader>
											<CardTitle className="flex items-center gap-2 text-lg">
												<Stethoscope className="h-5 w-5" />
												Surgical History (
												{patient.patient.patient_surgery.length})
											</CardTitle>
										</CardHeader>
										<CardContent>
											<div className="space-y-2">
												{patient.patient.patient_surgery
													.slice(0, 3)
													.map(
														(surgery: {
															id: number;
															surgery: string;
															date?: string;
														}) => (
															<div key={surgery.id} className="text-sm">
																<p className="font-medium">{surgery.surgery}</p>
																<p className="text-gray-600">
																	{surgery.date
																		? formatDate(surgery.date)
																		: 'Date not specified'}
																</p>
															</div>
														)
													)}
												{patient.patient.patient_surgery.length > 3 && (
													<p className="text-sm text-gray-500">
														+{patient.patient.patient_surgery.length - 3} more
													</p>
												)}
											</div>
										</CardContent>
									</Card>
								)}
						</div>
					)}
				</TabsContent>

				<TabsContent value="profile-management">
					<PatientProfileManagement
						profileId={patient.id}
						patient={patient.patient}
					/>
				</TabsContent>

				<TabsContent value="appointments">
					<AppointmentHistory patientId={patient.id} />
				</TabsContent>
			</Tabs>
		</div>
	);
}
