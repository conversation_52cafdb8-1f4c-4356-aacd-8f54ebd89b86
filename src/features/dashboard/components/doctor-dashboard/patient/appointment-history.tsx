'use client';

import { format } from 'date-fns';
import {
	Calendar,
	Clock,
	FileText,
	Filter,
	Loader2,
	MapPin,
	Search,
	Stethoscope,
	User,
} from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { usePatientAppointmentHistory } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import type { IAppointment } from '@/features/dashboard/types/doctor.types';

interface IAppointmentHistoryProps {
	patientId: number;
}

const formatDate = (dateString: string) => {
	try {
		return format(new Date(dateString), 'MMM dd, yyyy');
	} catch {
		return 'Invalid date';
	}
};

const formatTime = (dateString: string) => {
	try {
		return format(new Date(dateString), 'h:mm a');
	} catch {
		return 'Invalid time';
	}
};

const getStatusColor = (status: string) => {
	switch (status.toLowerCase()) {
		case 'completed':
			return 'bg-green-100 text-green-800';
		case 'confirmed':
			return 'bg-blue-100 text-blue-800';
		case 'pending':
			return 'bg-yellow-100 text-yellow-800';
		case 'cancelled':
			return 'bg-red-100 text-red-800';
		case 'rescheduled':
			return 'bg-orange-100 text-orange-800';
		default:
			return 'bg-gray-100 text-gray-800';
	}
};

const AppointmentCard = ({ appointment }: { appointment: IAppointment }) => (
	<Card className="transition-shadow hover:shadow-md">
		<CardContent className="p-4">
			<div className="flex items-start justify-between">
				<div className="flex-1">
					<div className="mb-2 flex items-center gap-2">
						<Calendar className="h-4 w-4 text-gray-400" />
						<span className="font-medium">
							{formatDate(appointment.appointment_date)}
						</span>
						<Clock className="ml-2 h-4 w-4 text-gray-400" />
						<span className="text-sm text-gray-600">
							{formatTime(appointment.appointment_date)}
						</span>
					</div>

					<div className="space-y-2">
						<div className="flex items-center gap-2">
							<User className="h-4 w-4 text-gray-400" />
							<span className="text-sm">
								{appointment.patient?.profile?.first_name}{' '}
								{appointment.patient?.profile?.last_name}
							</span>
							{appointment.visitReason?.name && (
								<Badge variant="outline" className="text-xs">
									{appointment.visitReason.name}
								</Badge>
							)}
						</div>

						<div className="flex items-center gap-2">
							<MapPin className="h-4 w-4 text-gray-400" />
							<span className="text-sm text-gray-600">
								{appointment.clinic?.name || 'Unknown Clinic'}
							</span>
						</div>

						{appointment.chief_complaint && (
							<div className="flex items-start gap-2">
								<Stethoscope className="mt-0.5 h-4 w-4 text-gray-400" />
								<div>
									<p className="text-xs text-gray-500">Chief Complaint:</p>
									<p className="text-sm">{appointment.chief_complaint}</p>
								</div>
							</div>
						)}

						{appointment.diagnosis && (
							<div className="flex items-start gap-2">
								<FileText className="mt-0.5 h-4 w-4 text-gray-400" />
								<div>
									<p className="text-xs text-gray-500">Diagnosis:</p>
									<p className="text-sm">{appointment.diagnosis}</p>
								</div>
							</div>
						)}
					</div>
				</div>

				<div className="flex flex-col items-end gap-2">
					<Badge className={getStatusColor(appointment.status)}>
						{appointment.status}
					</Badge>
					<Badge variant="outline" className="text-xs">
						{appointment.appointment_type}
					</Badge>
				</div>
			</div>
		</CardContent>
	</Card>
);

export function AppointmentHistory({ patientId }: IAppointmentHistoryProps) {
	const [searchQuery, setSearchQuery] = useState('');
	const [statusFilter, setStatusFilter] = useState('all');
	const [typeFilter, setTypeFilter] = useState('all');

	// Fetch appointment history
	const {
		data: appointments = [],
		isLoading,
		error,
	} = usePatientAppointmentHistory(patientId);

	// Loading state
	if (isLoading) {
		return (
			<div className="flex min-h-[400px] items-center justify-center">
				<div className="text-center">
					<Loader2 className="mx-auto h-8 w-8 animate-spin text-gray-400" />
					<p className="mt-2 text-sm text-gray-500">
						Loading appointment history...
					</p>
				</div>
			</div>
		);
	}

	// Error state
	if (error) {
		return (
			<div className="flex min-h-[400px] items-center justify-center">
				<div className="text-center">
					<Calendar className="mx-auto h-12 w-12 text-gray-400" />
					<h3 className="mt-2 text-sm font-semibold text-gray-900">
						Error Loading Appointments
					</h3>
					<p className="mt-1 text-sm text-gray-500">
						Unable to load appointment history. Please try again.
					</p>
				</div>
			</div>
		);
	}

	// Filter appointments based on search and filters
	const filteredAppointments = appointments.filter(
		(appointment: IAppointment) => {
			const matchesSearch =
				searchQuery === '' ||
				appointment.patient?.profile?.first_name
					?.toLowerCase()
					.includes(searchQuery.toLowerCase()) ||
				appointment.patient?.profile?.last_name
					?.toLowerCase()
					.includes(searchQuery.toLowerCase()) ||
				appointment.clinic?.name
					?.toLowerCase()
					.includes(searchQuery.toLowerCase()) ||
				appointment.chief_complaint
					?.toLowerCase()
					.includes(searchQuery.toLowerCase()) ||
				appointment.diagnosis
					?.toLowerCase()
					.includes(searchQuery.toLowerCase());

			const matchesStatus =
				statusFilter === 'all' || appointment.status === statusFilter;
			const matchesType =
				typeFilter === 'all' || appointment.appointment_type === typeFilter;

			return matchesSearch && matchesStatus && matchesType;
		}
	);

	// Sort appointments by date (most recent first)
	const sortedAppointments = filteredAppointments.sort(
		(a: IAppointment, b: IAppointment) => {
			const dateA = new Date(a.appointment_date);
			const dateB = new Date(b.appointment_date);
			return dateB.getTime() - dateA.getTime();
		}
	);

	// Get unique statuses and types for filter options
	const uniqueStatuses: string[] = [
		...new Set(
			appointments.map((apt: IAppointment) => apt.status).filter(Boolean)
		),
	] as string[];
	const uniqueTypes: string[] = [
		...new Set(
			appointments
				.map((apt: IAppointment) => apt.appointment_type)
				.filter(Boolean)
		),
	] as string[];

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<h2 className="text-2xl font-bold text-gray-900">
					Appointment History
				</h2>
				<Badge variant="outline">
					{appointments.length} Total Appointments
				</Badge>
			</div>

			{/* Search and Filters */}
			<div className="flex flex-col gap-4 sm:flex-row sm:items-center">
				<div className="relative max-w-sm flex-1">
					<Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
					<Input
						placeholder="Search appointments..."
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
						className="pl-10"
					/>
				</div>

				<div className="flex items-center gap-2">
					<Filter className="h-4 w-4 text-gray-400" />

					<Select value={statusFilter} onValueChange={setStatusFilter}>
						<SelectTrigger className="w-[140px]">
							<SelectValue placeholder="All Status" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="all">All Status</SelectItem>
							{uniqueStatuses.map((status) => (
								<SelectItem key={status} value={status}>
									{status}
								</SelectItem>
							))}
						</SelectContent>
					</Select>

					<Select value={typeFilter} onValueChange={setTypeFilter}>
						<SelectTrigger className="w-[140px]">
							<SelectValue placeholder="All Types" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="all">All Types</SelectItem>
							{uniqueTypes.map((type) => (
								<SelectItem key={type} value={type}>
									{type}
								</SelectItem>
							))}
						</SelectContent>
					</Select>

					{(searchQuery || statusFilter !== 'all' || typeFilter !== 'all') && (
						<Button
							variant="outline"
							size="sm"
							onClick={() => {
								setSearchQuery('');
								setStatusFilter('all');
								setTypeFilter('all');
							}}
						>
							Clear
						</Button>
					)}
				</div>
			</div>

			{/* Appointment List */}
			{sortedAppointments.length === 0 ? (
				<div className="flex min-h-[400px] items-center justify-center rounded-lg border border-dashed">
					<div className="text-center">
						<Calendar className="mx-auto h-12 w-12 text-gray-400" />
						<h3 className="mt-2 text-sm font-semibold text-gray-900">
							{appointments.length === 0
								? 'No Appointments Found'
								: 'No Matching Appointments'}
						</h3>
						<p className="mt-1 text-sm text-gray-500">
							{appointments.length === 0
								? 'This patient has no appointment history yet'
								: 'Try adjusting your search or filter criteria'}
						</p>
					</div>
				</div>
			) : (
				<div className="space-y-4">
					<div className="flex items-center justify-between">
						<p className="text-sm text-gray-600">
							Showing {sortedAppointments.length} of {appointments.length}{' '}
							appointments
						</p>
					</div>

					<div className="space-y-3">
						{sortedAppointments.map((appointment: IAppointment) => (
							<AppointmentCard key={appointment.id} appointment={appointment} />
						))}
					</div>
				</div>
			)}
		</div>
	);
}
