'use client';

import { format } from 'date-fns';
import {
	Activity,
	AlertTriangle,
	Calendar,
	Edit,
	Heart,
	Pill,
	Plus,
	Stethoscope,
	Trash2,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface IMedicalHistoryProps {
	patientId: number;
	medicalHistory?: {
		patient_allergy?: Array<{
			id: number;
			allergen: string;
			reaction: string;
			severity: string;
			date_diagnosed?: string;
		}>;
		patient_illness?: Array<{
			id: number;
			illness: string;
			description: string;
			date_diagnosed?: string;
			status: string;
		}>;
		patient_surgery?: Array<{
			id: number;
			surgery: string;
			description?: string;
			date: string;
			hospital?: string;
			surgeon?: string;
		}>;
		patient_medication?: Array<{
			id: number;
			medication: string;
			dosage: string;
			frequency: string;
			start_date: string;
			end_date?: string;
			status: string;
		}>;
	};
}

const formatDate = (dateString: string) => {
	try {
		return format(new Date(dateString), 'MMM dd, yyyy');
	} catch {
		return 'Date not specified';
	}
};

const AllergiesSection = ({
	allergies,
}: {
	allergies: Array<{
		id: number;
		allergen: string;
		reaction: string;
		severity: string;
		date_diagnosed?: string;
	}>;
}) => (
	<Card>
		<CardHeader>
			<div className="flex items-center justify-between">
				<CardTitle className="flex items-center gap-2 text-lg">
					<AlertTriangle className="h-5 w-5 text-orange-500" />
					Allergies ({allergies.length})
				</CardTitle>
				<Button size="sm" variant="outline">
					<Plus className="mr-2 h-4 w-4" />
					Add Allergy
				</Button>
			</div>
		</CardHeader>
		<CardContent>
			{allergies.length === 0 ? (
				<p className="text-sm text-gray-500">No known allergies</p>
			) : (
				<div className="space-y-3">
					{allergies.map((allergy) => (
						<div
							key={allergy.id}
							className="flex items-start justify-between rounded-lg border p-3"
						>
							<div className="flex-1">
								<div className="flex items-center gap-2">
									<h4 className="font-medium">{allergy.allergen}</h4>
									<Badge
										variant={
											allergy.severity === 'Severe'
												? 'destructive'
												: allergy.severity === 'Moderate'
													? 'default'
													: 'secondary'
										}
									>
										{allergy.severity}
									</Badge>
								</div>
								<p className="text-sm text-gray-600">{allergy.reaction}</p>
								{allergy.date_diagnosed && (
									<p className="text-xs text-gray-500">
										Diagnosed: {formatDate(allergy.date_diagnosed)}
									</p>
								)}
							</div>
							<div className="flex items-center gap-1">
								<Button size="sm" variant="ghost">
									<Edit className="h-4 w-4" />
								</Button>
								<Button size="sm" variant="ghost" className="text-red-600">
									<Trash2 className="h-4 w-4" />
								</Button>
							</div>
						</div>
					))}
				</div>
			)}
		</CardContent>
	</Card>
);

const IllnessesSection = ({
	illnesses,
}: {
	illnesses: Array<{
		id: number;
		illness: string;
		description: string;
		date_diagnosed?: string;
		status: string;
	}>;
}) => (
	<Card>
		<CardHeader>
			<div className="flex items-center justify-between">
				<CardTitle className="flex items-center gap-2 text-lg">
					<Activity className="h-5 w-5 text-blue-500" />
					Medical Conditions ({illnesses.length})
				</CardTitle>
				<Button size="sm" variant="outline">
					<Plus className="mr-2 h-4 w-4" />
					Add Condition
				</Button>
			</div>
		</CardHeader>
		<CardContent>
			{illnesses.length === 0 ? (
				<p className="text-sm text-gray-500">No medical conditions recorded</p>
			) : (
				<div className="space-y-3">
					{illnesses.map((illness) => (
						<div
							key={illness.id}
							className="flex items-start justify-between rounded-lg border p-3"
						>
							<div className="flex-1">
								<div className="flex items-center gap-2">
									<h4 className="font-medium">{illness.illness}</h4>
									<Badge
										variant={
											illness.status === 'Active' ? 'default' : 'secondary'
										}
									>
										{illness.status}
									</Badge>
								</div>
								<p className="text-sm text-gray-600">{illness.description}</p>
								{illness.date_diagnosed && (
									<p className="text-xs text-gray-500">
										Diagnosed: {formatDate(illness.date_diagnosed)}
									</p>
								)}
							</div>
							<div className="flex items-center gap-1">
								<Button size="sm" variant="ghost">
									<Edit className="h-4 w-4" />
								</Button>
								<Button size="sm" variant="ghost" className="text-red-600">
									<Trash2 className="h-4 w-4" />
								</Button>
							</div>
						</div>
					))}
				</div>
			)}
		</CardContent>
	</Card>
);

const SurgeriesSection = ({
	surgeries,
}: {
	surgeries: Array<{
		id: number;
		surgery: string;
		description?: string;
		date: string;
		hospital?: string;
		surgeon?: string;
	}>;
}) => (
	<Card>
		<CardHeader>
			<div className="flex items-center justify-between">
				<CardTitle className="flex items-center gap-2 text-lg">
					<Stethoscope className="h-5 w-5 text-green-500" />
					Surgical History ({surgeries.length})
				</CardTitle>
				<Button size="sm" variant="outline">
					<Plus className="mr-2 h-4 w-4" />
					Add Surgery
				</Button>
			</div>
		</CardHeader>
		<CardContent>
			{surgeries.length === 0 ? (
				<p className="text-sm text-gray-500">No surgical history recorded</p>
			) : (
				<div className="space-y-3">
					{surgeries.map((surgery) => (
						<div
							key={surgery.id}
							className="flex items-start justify-between rounded-lg border p-3"
						>
							<div className="flex-1">
								<h4 className="font-medium">{surgery.surgery}</h4>
								{surgery.description && (
									<p className="text-sm text-gray-600">{surgery.description}</p>
								)}
								<div className="mt-1 space-y-1">
									<p className="text-xs text-gray-500">
										<Calendar className="mr-1 inline h-3 w-3" />
										Date: {formatDate(surgery.date)}
									</p>
									{surgery.hospital && (
										<p className="text-xs text-gray-500">
											Hospital: {surgery.hospital}
										</p>
									)}
									{surgery.surgeon && (
										<p className="text-xs text-gray-500">
											Surgeon: {surgery.surgeon}
										</p>
									)}
								</div>
							</div>
							<div className="flex items-center gap-1">
								<Button size="sm" variant="ghost">
									<Edit className="h-4 w-4" />
								</Button>
								<Button size="sm" variant="ghost" className="text-red-600">
									<Trash2 className="h-4 w-4" />
								</Button>
							</div>
						</div>
					))}
				</div>
			)}
		</CardContent>
	</Card>
);

const MedicationsSection = ({
	medications,
}: {
	medications: Array<{
		id: number;
		medication: string;
		dosage: string;
		frequency: string;
		start_date: string;
		end_date?: string;
		status: string;
	}>;
}) => (
	<Card>
		<CardHeader>
			<div className="flex items-center justify-between">
				<CardTitle className="flex items-center gap-2 text-lg">
					<Pill className="h-5 w-5 text-purple-500" />
					Medications ({medications.length})
				</CardTitle>
				<Button size="sm" variant="outline">
					<Plus className="mr-2 h-4 w-4" />
					Add Medication
				</Button>
			</div>
		</CardHeader>
		<CardContent>
			{medications.length === 0 ? (
				<p className="text-sm text-gray-500">No medications recorded</p>
			) : (
				<div className="space-y-3">
					{medications.map((medication) => (
						<div
							key={medication.id}
							className="flex items-start justify-between rounded-lg border p-3"
						>
							<div className="flex-1">
								<div className="flex items-center gap-2">
									<h4 className="font-medium">{medication.medication}</h4>
									<Badge
										variant={
											medication.status === 'Active' ? 'default' : 'secondary'
										}
									>
										{medication.status}
									</Badge>
								</div>
								<p className="text-sm text-gray-600">
									{medication.dosage} - {medication.frequency}
								</p>
								<div className="mt-1 space-y-1">
									<p className="text-xs text-gray-500">
										Started: {formatDate(medication.start_date)}
									</p>
									{medication.end_date && (
										<p className="text-xs text-gray-500">
											Ended: {formatDate(medication.end_date)}
										</p>
									)}
								</div>
							</div>
							<div className="flex items-center gap-1">
								<Button size="sm" variant="ghost">
									<Edit className="h-4 w-4" />
								</Button>
								<Button size="sm" variant="ghost" className="text-red-600">
									<Trash2 className="h-4 w-4" />
								</Button>
							</div>
						</div>
					))}
				</div>
			)}
		</CardContent>
	</Card>
);

export function MedicalHistory({ medicalHistory }: IMedicalHistoryProps) {
	const allergies = medicalHistory?.patient_allergy || [];
	const illnesses = medicalHistory?.patient_illness || [];
	const surgeries = medicalHistory?.patient_surgery || [];
	const medications = medicalHistory?.patient_medication || [];

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<h2 className="text-2xl font-bold text-gray-900">Medical History</h2>
				<Button className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90">
					<Heart className="mr-2 h-4 w-4" />
					Generate Report
				</Button>
			</div>

			<Tabs defaultValue="overview" className="w-full">
				<TabsList className="grid w-full grid-cols-5">
					<TabsTrigger value="overview">Overview</TabsTrigger>
					<TabsTrigger value="allergies">Allergies</TabsTrigger>
					<TabsTrigger value="conditions">Conditions</TabsTrigger>
					<TabsTrigger value="surgeries">Surgeries</TabsTrigger>
					<TabsTrigger value="medications">Medications</TabsTrigger>
				</TabsList>

				<TabsContent value="overview" className="space-y-4">
					<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
						<Card>
							<CardHeader>
								<CardTitle className="text-lg">Quick Summary</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="space-y-2">
									<div className="flex justify-between">
										<span className="text-sm text-gray-600">Allergies:</span>
										<span className="text-sm font-medium">
											{allergies.length}
										</span>
									</div>
									<div className="flex justify-between">
										<span className="text-sm text-gray-600">Conditions:</span>
										<span className="text-sm font-medium">
											{illnesses.length}
										</span>
									</div>
									<div className="flex justify-between">
										<span className="text-sm text-gray-600">Surgeries:</span>
										<span className="text-sm font-medium">
											{surgeries.length}
										</span>
									</div>
									<div className="flex justify-between">
										<span className="text-sm text-gray-600">Medications:</span>
										<span className="text-sm font-medium">
											{medications.length}
										</span>
									</div>
								</div>
							</CardContent>
						</Card>

						<Card>
							<CardHeader>
								<CardTitle className="text-lg">Recent Updates</CardTitle>
							</CardHeader>
							<CardContent>
								<p className="text-sm text-gray-500">
									No recent medical history updates
								</p>
							</CardContent>
						</Card>
					</div>
				</TabsContent>

				<TabsContent value="allergies">
					<AllergiesSection allergies={allergies} />
				</TabsContent>

				<TabsContent value="conditions">
					<IllnessesSection illnesses={illnesses} />
				</TabsContent>

				<TabsContent value="surgeries">
					<SurgeriesSection surgeries={surgeries} />
				</TabsContent>

				<TabsContent value="medications">
					<MedicationsSection medications={medications} />
				</TabsContent>
			</Tabs>
		</div>
	);
}
