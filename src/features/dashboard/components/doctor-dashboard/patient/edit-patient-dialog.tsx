'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import {
	usePatientDetail,
	useUpdatePatient,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';

const updatePatientSchema = z.object({
	profile_id: z.number(),
	title: z.string().min(1, 'Title is required'),
	first_name: z.string().min(1, 'First name is required'),
	middle_name: z.string().optional(),
	last_name: z.string().min(1, 'Last name is required'),
	suffix: z.string().optional(),
	phone: z.string().min(1, 'Phone number is required').optional(),
	occupation: z.string().min(1, 'Occupation is required').optional(),
	emergency_contact_person: z
		.string()
		.min(1, 'Emergency contact person is required')
		.optional(),
	emergency_contact_number: z
		.string()
		.min(1, 'Emergency contact number is required')
		.optional(),
	blood_type: z.string().optional(),
});

type UpdatePatientForm = z.infer<typeof updatePatientSchema>;

interface IEditPatientDialogProps {
	patientId: number;
	onClose?: () => void;
	open?: boolean;
	onOpenChange?: (open: boolean) => void;
}

const LoadingSkeleton = () => (
	<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
		<div className="flex items-center gap-4">
			<Skeleton className="h-10 w-10" />
			<Skeleton className="h-6 w-[200px]" />
		</div>
		<Card>
			<CardHeader>
				<Skeleton className="h-6 w-[150px]" />
			</CardHeader>
			<CardContent className="space-y-4">
				{[...Array(8)].map((_, i) => (
					<div key={i} className="grid grid-cols-2 gap-4">
						<Skeleton className="h-10 w-full" />
						<Skeleton className="h-10 w-full" />
					</div>
				))}
			</CardContent>
		</Card>
	</div>
);

export function EditPatientDialog({
	open: controlledOpen,
	onOpenChange: controlledOnOpenChange,
	patientId,
	onClose,
}: IEditPatientDialogProps) {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const { data: patient, isLoading, error } = usePatientDetail(patientId);
	const { mutate: updatePatient } = useUpdatePatient();

	const form = useForm({
		resolver: zodResolver(updatePatientSchema),
	});

	// Populate form with patient data when loaded
	useEffect(() => {
		if (patient) {
			form.reset({
				profile_id: patient.id,
				title: patient.title || '',
				first_name: patient.first_name || '',
				middle_name: patient.middle_name || '',
				last_name: patient.last_name || '',
				suffix: patient.suffix || '',
				phone: patient.phone || '',
				occupation: patient.occupation || '',
				emergency_contact_person: patient.patient?.emergency_contact_name || '',
				emergency_contact_number:
					patient.patient?.emergency_contact_number || '',
				blood_type: patient.blood_type || '',
			});
		}
	}, [patient, form]);

	const onSubmit = (data: UpdatePatientForm) => {
		setIsSubmitting(true);

		// Transform data to match backend expectations (camelCase)
		const submitData = {
			profileId: data.profile_id,
			title: data.title,
			firstName: data.first_name,
			middleName: data.middle_name,
			lastName: data.last_name,
			suffix: data.suffix,
			phone: data.phone,
			occupation: data.occupation,
			emergencyContactName: data.emergency_contact_person,
			emergencyContactNumber: data.emergency_contact_number,
			bloodType: data.blood_type,
		};

		updatePatient(submitData, {
			onSuccess: () => {
				setIsSubmitting(false);
				onClose?.();
			},
			onError: () => {
				setIsSubmitting(false);
				console.error('Failed to update patient');
			},
		});
	};

	if (isLoading) {
		return <LoadingSkeleton />;
	}

	if (error || !patient) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
				<Card className="border-destructive">
					<CardContent className="p-6">
						<div className="text-center">
							<p className="text-destructive text-sm">
								{error ? 'Failed to load patient details' : 'Patient not found'}
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<Dialog open={controlledOpen} onOpenChange={controlledOnOpenChange}>
			<DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
				<DialogHeader>
					<DialogTitle>Edit Profile</DialogTitle>
					<DialogDescription>
						Edit the patient&apos;s profile.
					</DialogDescription>
				</DialogHeader>

				<div className="flex-1 overflow-y-auto">
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
							<div className="grid grid-cols-2 gap-4">
								<FormField
									control={form.control}
									name="title"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Title *</FormLabel>
											<Select
												onValueChange={field.onChange}
												value={field.value}
											>
												<FormControl>
													<SelectTrigger className="w-full">
														<SelectValue placeholder="Select title" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<SelectItem value="Mr.">Mr.</SelectItem>
													<SelectItem value="Ms.">Ms.</SelectItem>
													<SelectItem value="Mrs.">Mrs.</SelectItem>
													<SelectItem value="Dr.">Dr.</SelectItem>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="first_name"
									render={({ field }) => (
										<FormItem>
											<FormLabel>First Name *</FormLabel>
											<FormControl>
												<Input placeholder="Enter first name" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="middle_name"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Middle Name</FormLabel>
											<FormControl>
												<Input placeholder="Enter middle name" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="last_name"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Last Name *</FormLabel>
											<FormControl>
												<Input placeholder="Enter last name" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="suffix"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Suffix</FormLabel>
											<FormControl>
												<Input placeholder="Jr., Sr., III, etc." {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="phone"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Phone Number *</FormLabel>
											<FormControl>
												<Input placeholder="Enter phone number" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="occupation"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Occupation *</FormLabel>
											<FormControl>
												<Input placeholder="Enter occupation" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="blood_type"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Blood Type</FormLabel>
											<Select
												onValueChange={field.onChange}
												value={field.value}
											>
												<FormControl>
													<SelectTrigger className="w-full">
														<SelectValue placeholder="Select blood type" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<SelectItem value="A+">A+</SelectItem>
													<SelectItem value="A-">A-</SelectItem>
													<SelectItem value="B+">B+</SelectItem>
													<SelectItem value="B-">B-</SelectItem>
													<SelectItem value="AB+">AB+</SelectItem>
													<SelectItem value="AB-">AB-</SelectItem>
													<SelectItem value="O+">O+</SelectItem>
													<SelectItem value="O-">O-</SelectItem>
													<SelectItem value="Rh null">Rh null</SelectItem>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="emergency_contact_person"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Emergency Contact Person *</FormLabel>
											<FormControl>
												<Input
													placeholder="Enter contact person name"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="emergency_contact_number"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Emergency Contact Number *</FormLabel>
											<FormControl>
												<Input placeholder="Enter contact number" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<DialogFooter>
								<Button
									type="button"
									variant="outline"
									onClick={onClose}
									disabled={isSubmitting}
								>
									Cancel
								</Button>
								<Button
									type="submit"
									disabled={isSubmitting}
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
								>
									{isSubmitting ? 'Updating...' : 'Update Patient'}
								</Button>
							</DialogFooter>
						</form>
					</Form>
				</div>
			</DialogContent>
		</Dialog>
	);
}
