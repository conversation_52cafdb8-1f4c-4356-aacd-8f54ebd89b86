'use client';

import { Al<PERSON><PERSON>riangle, Edit, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';

import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
	useAddAllergyToPatient,
	useRemoveAllergyFromPatient,
	useUpdatePatientAllergy,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';

interface IPatientAllergy {
	id: number;
	name: string;
	description: string;
	created_at: string;
}

interface IPatientAllergiesManagementProps {
	profileId: number;
	allergies: IPatientAllergy[];
}

interface IAllergyFormData {
	name: string;
	description: string;
}

export function PatientAllergiesManagement({
	profileId,
	allergies,
}: IPatientAllergiesManagementProps) {
	const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [editingAllergy, setEditingAllergy] = useState<IPatientAllergy | null>(
		null
	);
	const [allergyToDelete, setAllergyToDelete] =
		useState<IPatientAllergy | null>(null);
	const [formData, setFormData] = useState<IAllergyFormData>({
		name: '',
		description: '',
	});

	const addAllergyMutation = useAddAllergyToPatient();
	const updateAllergyMutation = useUpdatePatientAllergy();
	const removeAllergyMutation = useRemoveAllergyFromPatient();

	const handleAddAllergy = () => {
		if (!formData.name.trim()) {
			console.error('Allergy name is required');
			return;
		}

		// Create payload with name and description as backend expects
		const allergyData = {
			name: formData.name,
			description: formData.description,
		};

		addAllergyMutation.mutate(
			{ profileId, allergyData },
			{
				onSuccess: () => {
					setIsAddDialogOpen(false);
					setFormData({
						name: '',
						description: '',
					});
				},
			}
		);
	};

	const handleEditAllergy = () => {
		if (!editingAllergy) return;

		if (!formData.name.trim()) {
			console.error('Allergy name is required');
			return;
		}

		// Create payload with name and description as backend expects
		const allergyData = {
			name: formData.name,
			description: formData.description,
		};

		updateAllergyMutation.mutate(
			{
				profileId,
				patientAllergyId: editingAllergy.id,
				allergyData,
			},
			{
				onSuccess: () => {
					setIsEditDialogOpen(false);
					setEditingAllergy(null);
					setFormData({
						name: '',
						description: '',
					});
				},
			}
		);
	};

	const handleRemoveAllergy = (allergy: IPatientAllergy) => {
		setAllergyToDelete(allergy);
		setIsDeleteDialogOpen(true);
	};

	const confirmRemoveAllergy = () => {
		if (allergyToDelete) {
			removeAllergyMutation.mutate({
				profileId,
				patientAllergyId: allergyToDelete.id,
			});
			setIsDeleteDialogOpen(false);
			setAllergyToDelete(null);
		}
	};

	const openEditDialog = (allergy: IPatientAllergy) => {
		setEditingAllergy(allergy);
		setFormData({
			name: allergy.name,
			description: allergy.description,
		});
		setIsEditDialogOpen(true);
	};

	return (
		<Card>
			<CardHeader>
				<div className="flex items-center justify-between">
					<CardTitle className="flex items-center gap-2">
						<AlertTriangle className="h-5 w-5" />
						Allergies ({allergies.length})
					</CardTitle>
					<Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
						<DialogTrigger asChild>
							<Button size="sm">
								<Plus className="mr-2 h-4 w-4" />
								Add Allergy
							</Button>
						</DialogTrigger>
						<DialogContent>
							<DialogHeader>
								<DialogTitle>Add New Allergy</DialogTitle>
								<DialogDescription>
									Add a new allergy to the patient&apos;s profile.
								</DialogDescription>
							</DialogHeader>
							<div className="space-y-4">
								<div>
									<Label htmlFor="name">Allergy Name</Label>
									<Input
										id="name"
										value={formData.name}
										onChange={(e) =>
											setFormData({ ...formData, name: e.target.value })
										}
										placeholder="Enter allergy name..."
										required
									/>
								</div>
								<div>
									<Label htmlFor="description">Description</Label>
									<Textarea
										id="description"
										value={formData.description}
										onChange={(e) =>
											setFormData({ ...formData, description: e.target.value })
										}
										placeholder="Describe the allergy details..."
									/>
								</div>
							</div>
							<DialogFooter>
								<Button
									variant="outline"
									onClick={() => setIsAddDialogOpen(false)}
								>
									Cancel
								</Button>
								<Button
									onClick={handleAddAllergy}
									disabled={addAllergyMutation.isPending}
								>
									{addAllergyMutation.isPending ? 'Adding...' : 'Add Allergy'}
								</Button>
							</DialogFooter>
						</DialogContent>
					</Dialog>
				</div>
			</CardHeader>
			<CardContent>
				{allergies.length === 0 ? (
					<p className="py-4 text-center text-gray-500">
						No allergies recorded for this patient.
					</p>
				) : (
					<div className="space-y-3">
						{allergies.map((allergy) => (
							<div
								key={allergy.id}
								className="flex items-center justify-between rounded-lg border p-3"
							>
								<div className="flex-1">
									<h4 className="font-medium">{allergy.name}</h4>
									<p className="text-sm text-gray-600">{allergy.description}</p>
								</div>
								<div className="flex gap-2">
									<Button
										size="sm"
										variant="outline"
										onClick={() => openEditDialog(allergy)}
									>
										<Edit className="h-4 w-4" />
									</Button>
									<Button
										size="sm"
										variant="destructive"
										onClick={() => handleRemoveAllergy(allergy)}
										disabled={removeAllergyMutation.isPending}
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								</div>
							</div>
						))}
					</div>
				)}
			</CardContent>

			{/* Edit Dialog */}
			<Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Edit Allergy</DialogTitle>
						<DialogDescription>
							Update the allergy information.
						</DialogDescription>
					</DialogHeader>
					<div className="space-y-4">
						<div>
							<Label htmlFor="editName">Allergy Name</Label>
							<Input
								id="editName"
								value={formData.name}
								onChange={(e) =>
									setFormData({ ...formData, name: e.target.value })
								}
								placeholder="Enter allergy name..."
								required
							/>
						</div>
						<div>
							<Label htmlFor="editDescription">Description</Label>
							<Textarea
								id="editDescription"
								value={formData.description}
								onChange={(e) =>
									setFormData({ ...formData, description: e.target.value })
								}
								placeholder="Describe the allergy details..."
							/>
						</div>
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setIsEditDialogOpen(false)}
						>
							Cancel
						</Button>
						<Button
							onClick={handleEditAllergy}
							disabled={updateAllergyMutation.isPending}
						>
							{updateAllergyMutation.isPending
								? 'Updating...'
								: 'Update Allergy'}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Delete Confirmation Dialog */}
			<AlertDialog
				open={isDeleteDialogOpen}
				onOpenChange={setIsDeleteDialogOpen}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Delete Allergy</AlertDialogTitle>
						<AlertDialogDescription>
							Are you sure you want to delete &quot;{allergyToDelete?.name}
							&quot;? This action cannot be undone.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={confirmRemoveAllergy}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
						>
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</Card>
	);
}
