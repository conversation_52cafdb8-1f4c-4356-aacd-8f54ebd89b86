'use client';

import { format } from 'date-fns';
import {
	Archive,
	Calendar,
	Edit,
	Eye,
	Mail,
	MoreHorizontal,
	Phone,
	Power,
	PowerOff,
	User,
	Users,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import {
	useArchivePatient,
	useDisablePatient,
	useEnablePatient,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import { IPatient } from '@/features/dashboard/types/doctor.types';

interface IPatientListProps {
	patients: IPatient[];
	isLoading: boolean;
	error: Error | null;
	meta?: {
		total: number;
		per_page: number;
		current_page: number;
		last_page: number;
	};
	onPageChange: (page: number) => void;
	currentPage: number;
	totalPages: number;
	onCreatePatient?: () => void;
	onViewPatient?: (id: number) => void;
	onEditPatient?: (id: number) => void;
}

const formatDate = (dateString: string) => {
	try {
		return format(new Date(dateString), 'MMM dd, yyyy');
	} catch {
		return 'Invalid date';
	}
};

const PatientRow = ({ patient }: { patient: IPatient }) => {
	const router = useRouter();
	const { mutate: enablePatient, isPending: isEnabling } = useEnablePatient();
	const { mutate: disablePatient, isPending: isDisabling } =
		useDisablePatient();
	const { mutate: archivePatient, isPending: isArchiving } =
		useArchivePatient();

	const handleView = () => {
		router.push(`/dashboard?tab=patients&id=${patient.id}`);
	};

	const handleEdit = () => {
		router.push(`/dashboard?tab=patients&id=${patient.id}`);
	};

	const handleToggleStatus = () => {
		if (patient.is_active) {
			disablePatient(patient.id);
		} else {
			enablePatient(patient.id);
		}
	};

	const handleArchive = () => {
		archivePatient(patient.id);
	};

	const isActionLoading = isEnabling || isDisabling || isArchiving;

	return (
		<TableRow className="hover:bg-gray-50">
			<TableCell>
				<div className="flex items-center gap-3">
					<div className="flex h-10 w-10 items-center justify-center rounded-full bg-[oklch(0.7448_0.1256_202.74)]/10">
						<User className="h-5 w-5 text-[oklch(0.7448_0.1256_202.74)]" />
					</div>
					<div>
						<p className="font-medium text-gray-900">
							{patient.title} {patient.first_name} {patient.middle_name}{' '}
							{patient.last_name} {patient.suffix}
						</p>
						<p className="text-sm text-gray-500">ID: {patient.id}</p>
					</div>
				</div>
			</TableCell>
			<TableCell>
				<div className="space-y-1">
					{patient.user?.email && (
						<div className="flex items-center gap-1 text-sm text-gray-600">
							<Mail className="h-3 w-3" />
							{patient.user.email}
						</div>
					)}
					<div className="flex items-center gap-1 text-sm text-gray-600">
						<Phone className="h-3 w-3" />
						{patient.phone || 'No phone'}
					</div>
				</div>
			</TableCell>
			<TableCell>
				<div className="space-y-1">
					<p className="text-sm font-medium">{patient.gender}</p>
					<p className="text-sm text-gray-500">
						{patient.birthday ? formatDate(patient.birthday) : 'No birthday'}
					</p>
				</div>
			</TableCell>
			<TableCell>
				<div className="space-y-1">
					<p className="text-sm font-medium">
						{patient.occupation || 'Not specified'}
					</p>
					<p className="text-sm text-gray-500">
						{patient.civil_status || 'Not specified'}
					</p>
				</div>
			</TableCell>
			<TableCell>
				<div className="flex items-center gap-1 text-sm text-gray-600">
					<Calendar className="h-3 w-3" />
					{formatDate(patient.created_at)}
				</div>
			</TableCell>
			<TableCell>
				<div className="flex items-center gap-2">
					<Badge variant={patient.is_active ? 'default' : 'secondary'}>
						{patient.is_active ? 'Active' : 'Inactive'}
					</Badge>
					{patient.blood_type && (
						<Badge variant="outline">{patient.blood_type}</Badge>
					)}
				</div>
			</TableCell>
			<TableCell>
				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						<Button
							variant="ghost"
							className="h-8 w-8 p-0"
							disabled={isActionLoading}
						>
							<MoreHorizontal className="h-4 w-4" />
						</Button>
					</DropdownMenuTrigger>
					<DropdownMenuContent align="end">
						<DropdownMenuItem onClick={handleView}>
							<Eye className="mr-2 h-4 w-4" />
							View Details
						</DropdownMenuItem>
						<DropdownMenuItem onClick={handleEdit}>
							<Edit className="mr-2 h-4 w-4" />
							Edit Patient
						</DropdownMenuItem>
						<DropdownMenuSeparator />
						<DropdownMenuItem
							onClick={handleToggleStatus}
							disabled={isActionLoading}
						>
							{patient.is_active ? (
								<>
									<PowerOff className="mr-2 h-4 w-4" />
									{isDisabling ? 'Disabling...' : 'Disable'}
								</>
							) : (
								<>
									<Power className="mr-2 h-4 w-4" />
									{isEnabling ? 'Enabling...' : 'Enable'}
								</>
							)}
						</DropdownMenuItem>
						<DropdownMenuItem
							className="text-orange-600"
							onClick={handleArchive}
							disabled={isActionLoading}
						>
							<Archive className="mr-2 h-4 w-4" />
							{isArchiving ? 'Archiving...' : 'Archive'}
						</DropdownMenuItem>
					</DropdownMenuContent>
				</DropdownMenu>
			</TableCell>
		</TableRow>
	);
};

const LoadingSkeleton = () => (
	<div className="space-y-3">
		{[...Array(5)].map((_, i) => (
			<div key={i} className="flex items-center space-x-4 p-4">
				<Skeleton className="h-10 w-10 rounded-full" />
				<div className="space-y-2">
					<Skeleton className="h-4 w-[200px]" />
					<Skeleton className="h-4 w-[150px]" />
				</div>
			</div>
		))}
	</div>
);

export function PatientList({
	patients,
	isLoading,
	error,
	meta,
	onPageChange,
	currentPage,
}: IPatientListProps) {
	if (error) {
		return (
			<Card className="border-destructive">
				<CardContent className="p-6">
					<div className="text-center">
						<Users className="text-destructive mx-auto mb-2 h-8 w-8" />
						<p className="text-destructive text-sm">Failed to load patients</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	if (isLoading) {
		return <LoadingSkeleton />;
	}

	if (!patients || patients.length === 0) {
		return (
			<div className="flex min-h-[400px] items-center justify-center rounded-lg border border-dashed">
				<div className="text-center">
					<Users className="mx-auto h-12 w-12 text-gray-400" />
					<h3 className="mt-2 text-sm font-semibold text-gray-900">
						No patients found
					</h3>
					<p className="mt-1 text-sm text-gray-500">
						Get started by adding your first patient
					</p>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-4">
			<div className="rounded-md border">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>Patient</TableHead>
							<TableHead>Contact</TableHead>
							<TableHead>Demographics</TableHead>
							<TableHead>Details</TableHead>
							<TableHead>Registered</TableHead>
							<TableHead>Status</TableHead>
							<TableHead className="w-[50px]">Actions</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{patients.map((patient) => (
							<PatientRow key={patient.id} patient={patient} />
						))}
					</TableBody>
				</Table>
			</div>

			{/* Pagination */}
			{meta && meta.last_page > 1 && (
				<div className="flex items-center justify-between">
					<p className="text-sm text-gray-700">
						Showing {(currentPage - 1) * meta.per_page + 1} to{' '}
						{Math.min(currentPage * meta.per_page, meta.total)} of {meta.total}{' '}
						patients
					</p>
					<div className="flex items-center gap-2">
						<Button
							variant="outline"
							size="sm"
							onClick={() => onPageChange(currentPage - 1)}
							disabled={currentPage <= 1}
						>
							Previous
						</Button>
						<span className="text-sm">
							Page {currentPage} of {meta.last_page}
						</span>
						<Button
							variant="outline"
							size="sm"
							onClick={() => onPageChange(currentPage + 1)}
							disabled={currentPage >= meta.last_page}
						>
							Next
						</Button>
					</div>
				</div>
			)}
		</div>
	);
}
