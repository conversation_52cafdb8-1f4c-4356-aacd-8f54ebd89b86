'use client';

import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>riangle,
	Apple,
	CreditCard,
	Heart,
	Scissors,
	Stethoscope,
	Users,
} from 'lucide-react';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';

import { PatientAllergiesManagement } from './patient-allergies-management';
import { PatientDietsManagement } from './patient-diets-management';
import { PatientFamilyManagement } from './patient-family-management';
import { PatientHabitsManagement } from './patient-habits-management';
import { PatientHistoriesManagement } from './patient-history-management';
import { PatientHmoManagement } from './patient-hmo-management';
import { PatientIllnessesManagement } from './patient-illness-management';
import { PatientSurgeriesManagement } from './patient-surgeries-management';

// Define interfaces for patient profile data
interface IPatientHabit {
	id: number;
	name: string;
	description: string;
	created_at: string;
}

interface IPatientAllergy {
	id: number;
	name: string;
	description: string;
	created_at: string;
}

interface IPatientIllness {
	id: number;
	name: string;
	description: string;
	created_at: string;
}

interface IPatientSurgery {
	id: number;
	name: string;
	description: string;
	created_at: string;
}

interface IPatientDiet {
	id: number;
	name: string;
	description: string;
	created_at: string;
}

interface IPatientHmo {
	id: number;
	hmo_company: string;
	hmo_detail: string;
	hmo_id: string;
	hmo_provider: string;
}

interface IPatientFamily {
	id: number;
	name: string;
	relationship: string;
}

interface IPatientHistory {
	id: number;
	name: string;
	description: string;
	created_at: string;
	updated_at: string;
}

interface IPatientProfileManagementProps {
	profileId: number;
	patient: {
		patientHabit: IPatientHabit[];
		patientAllergy: IPatientAllergy[];
		patientIllness: IPatientIllness[];
		patientSurgery: IPatientSurgery[];
		patientDiet: IPatientDiet[];
		patientHmo: IPatientHmo[];
		patientFamily: IPatientFamily[];
		patientHistory: IPatientHistory[];
	};
}

export function PatientProfileManagement({
	profileId,
	patient,
}: IPatientProfileManagementProps) {
	return (
		<div className="space-y-6">
			<Tabs defaultValue="habits" className="w-full">
				<TabsList className="grid w-full grid-cols-4 lg:grid-cols-8">
					<TabsTrigger value="habits" className="flex items-center gap-1">
						<Activity className="h-4 w-4" />
						<span className="hidden sm:inline">Habits</span>
					</TabsTrigger>
					<TabsTrigger value="allergies" className="flex items-center gap-1">
						<AlertTriangle className="h-4 w-4" />
						<span className="hidden sm:inline">Allergies</span>
					</TabsTrigger>
					<TabsTrigger value="illnesses" className="flex items-center gap-1">
						<Heart className="h-4 w-4" />
						<span className="hidden sm:inline">Illnesses</span>
					</TabsTrigger>
					<TabsTrigger value="history" className="flex items-center gap-1">
						<Stethoscope className="h-4 w-4" />
						<span className="hidden sm:inline">History</span>
					</TabsTrigger>
					<TabsTrigger value="surgeries" className="flex items-center gap-1">
						<Scissors className="h-4 w-4" />
						<span className="hidden sm:inline">Surgeries</span>
					</TabsTrigger>
					<TabsTrigger value="diets" className="flex items-center gap-1">
						<Apple className="h-4 w-4" />
						<span className="hidden sm:inline">Diets</span>
					</TabsTrigger>
					<TabsTrigger value="hmo" className="flex items-center gap-1">
						<CreditCard className="h-4 w-4" />
						<span className="hidden sm:inline">HMO</span>
					</TabsTrigger>
					<TabsTrigger value="family" className="flex items-center gap-1">
						<Users className="h-4 w-4" />
						<span className="hidden sm:inline">Family</span>
					</TabsTrigger>
				</TabsList>

				<TabsContent value="habits" className="mt-6">
					<PatientHabitsManagement
						profileId={profileId}
						habits={patient.patientHabit || []}
					/>
				</TabsContent>

				<TabsContent value="allergies" className="mt-6">
					<PatientAllergiesManagement
						profileId={profileId}
						allergies={patient.patientAllergy || []}
					/>
				</TabsContent>

				<TabsContent value="illnesses" className="mt-6">
					<PatientIllnessesManagement
						profileId={profileId}
						illnesses={patient.patientIllness || []}
					/>
				</TabsContent>

				<TabsContent value="history" className="mt-6">
					<PatientHistoriesManagement
						profileId={profileId}
						histories={patient.patientHistory || []}
					/>
				</TabsContent>

				<TabsContent value="surgeries" className="mt-6">
					<PatientSurgeriesManagement
						profileId={profileId}
						surgeries={patient.patientSurgery || []}
					/>
				</TabsContent>

				<TabsContent value="diets" className="mt-6">
					<PatientDietsManagement
						profileId={profileId}
						diets={patient.patientDiet || []}
					/>
				</TabsContent>

				<TabsContent value="hmo" className="mt-6">
					<PatientHmoManagement
						profileId={profileId}
						hmos={patient.patientHmo || []}
					/>
				</TabsContent>

				<TabsContent value="family" className="mt-6">
					<PatientFamilyManagement
						profileId={profileId}
						family={patient.patientFamily || []}
					/>
				</TabsContent>
			</Tabs>
		</div>
	);
}
