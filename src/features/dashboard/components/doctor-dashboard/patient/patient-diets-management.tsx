'use client';

import { Activity, Edit, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';

import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
	useAddDietToPatient,
	useRemoveDietFromPatient,
	useUpdatePatientDiet,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';

interface IPatientDiet {
	id: number;
	name: string;
	description: string;
	created_at: string;
}

interface IPatientDietsManagementProps {
	profileId: number;
	diets: IPatientDiet[];
}

interface IDietFormData {
	name: string;
	description: string;
}

export function PatientDietsManagement({
	profileId,
	diets,
}: IPatientDietsManagementProps) {
	const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [editingDiet, setEditingDiet] = useState<IPatientDiet | null>(null);
	const [dietToDelete, setDietToDelete] = useState<IPatientDiet | null>(null);
	const [formData, setFormData] = useState<IDietFormData>({
		name: '',
		description: '',
	});

	const addDietMutation = useAddDietToPatient();
	const updateDietMutation = useUpdatePatientDiet();
	const removeDietMutation = useRemoveDietFromPatient();

	const handleAddDiet = () => {
		if (!formData.name.trim()) {
			console.error('Diet name is required');
			return;
		}

		// Create payload with name and description as backend expects
		const dietData = {
			name: formData.name,
			description: formData.description,
		};

		addDietMutation.mutate(
			{ profileId, dietData },
			{
				onSuccess: () => {
					setIsAddDialogOpen(false);
					setFormData({
						name: '',
						description: '',
					});
				},
			}
		);
	};

	const handleEditDiet = () => {
		if (!editingDiet) return;

		if (!formData.name.trim()) {
			console.error('Diet name is required');
			return;
		}

		// Create payload with name and description as backend expects
		const dietData = {
			name: formData.name,
			description: formData.description,
		};

		updateDietMutation.mutate(
			{
				profileId,
				patientDietId: editingDiet.id,
				dietData,
			},
			{
				onSuccess: () => {
					setIsEditDialogOpen(false);
					setEditingDiet(null);
					setFormData({
						name: '',
						description: '',
					});
				},
			}
		);
	};

	const handleRemoveDiet = (diet: IPatientDiet) => {
		setDietToDelete(diet);
		setIsDeleteDialogOpen(true);
	};

	const confirmRemoveDiet = () => {
		if (dietToDelete) {
			removeDietMutation.mutate({
				profileId,
				patientDietId: dietToDelete.id,
			});
			setIsDeleteDialogOpen(false);
			setDietToDelete(null);
		}
	};

	const openEditDialog = (diet: IPatientDiet) => {
		setEditingDiet(diet);
		setFormData({
			name: diet.name,
			description: diet.description,
		});
		setIsEditDialogOpen(true);
	};

	return (
		<Card>
			<CardHeader>
				<div className="flex items-center justify-between">
					<CardTitle className="flex items-center gap-2">
						<Activity className="h-5 w-5" />
						Diets ({diets.length})
					</CardTitle>
					<Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
						<DialogTrigger asChild>
							<Button size="sm">
								<Plus className="mr-2 h-4 w-4" />
								Add Diet
							</Button>
						</DialogTrigger>
						<DialogContent>
							<DialogHeader>
								<DialogTitle>Add New Diet</DialogTitle>
								<DialogDescription>
									Add a new diet to the patient&apos;s profile.
								</DialogDescription>
							</DialogHeader>
							<div className="space-y-4">
								<div>
									<Label htmlFor="name">Diet Name</Label>
									<Input
										id="name"
										value={formData.name}
										onChange={(e) =>
											setFormData({ ...formData, name: e.target.value })
										}
										placeholder="Enter diet name..."
										required
									/>
								</div>
								<div>
									<Label htmlFor="description">Description</Label>
									<Textarea
										id="description"
										value={formData.description}
										onChange={(e) =>
											setFormData({ ...formData, description: e.target.value })
										}
										placeholder="Describe the diet..."
									/>
								</div>
							</div>
							<DialogFooter>
								<Button
									variant="outline"
									onClick={() => setIsAddDialogOpen(false)}
								>
									Cancel
								</Button>
								<Button
									onClick={handleAddDiet}
									disabled={addDietMutation.isPending}
								>
									{addDietMutation.isPending ? 'Adding...' : 'Add Diet'}
								</Button>
							</DialogFooter>
						</DialogContent>
					</Dialog>
				</div>
			</CardHeader>
			<CardContent>
				{diets.length === 0 ? (
					<p className="py-4 text-center text-gray-500">
						No diets recorded for this patient.
					</p>
				) : (
					<div className="space-y-3">
						{diets.map((diet) => (
							<div
								key={diet.id}
								className="flex items-center justify-between rounded-lg border p-3"
							>
								<div className="flex-1">
									<h4 className="font-medium">{diet.name}</h4>
									<p className="text-sm text-gray-600">{diet.description}</p>
								</div>
								<div className="flex gap-2">
									<Button
										size="sm"
										variant="outline"
										onClick={() => openEditDialog(diet)}
									>
										<Edit className="h-4 w-4" />
									</Button>
									<Button
										size="sm"
										variant="destructive"
										onClick={() => handleRemoveDiet(diet)}
										disabled={removeDietMutation.isPending}
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								</div>
							</div>
						))}
					</div>
				)}
			</CardContent>

			{/* Edit Dialog */}
			<Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Edit Diet</DialogTitle>
						<DialogDescription>Update the diet information.</DialogDescription>
					</DialogHeader>
					<div className="space-y-4">
						<div>
							<Label htmlFor="editName">Diet Name</Label>
							<Input
								id="editName"
								value={formData.name}
								onChange={(e) =>
									setFormData({ ...formData, name: e.target.value })
								}
								placeholder="Enter diet name..."
								required
							/>
						</div>
						<div>
							<Label htmlFor="editDescription">Description</Label>
							<Textarea
								id="editDescription"
								value={formData.description}
								onChange={(e) =>
									setFormData({ ...formData, description: e.target.value })
								}
								placeholder="Describe the diet..."
							/>
						</div>
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setIsEditDialogOpen(false)}
						>
							Cancel
						</Button>
						<Button
							onClick={handleEditDiet}
							disabled={updateDietMutation.isPending}
						>
							{updateDietMutation.isPending ? 'Updating...' : 'Update Diet'}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Delete Confirmation Dialog */}
			<AlertDialog
				open={isDeleteDialogOpen}
				onOpenChange={setIsDeleteDialogOpen}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Delete Diet</AlertDialogTitle>
						<AlertDialogDescription>
							Are you sure you want to delete &quot;{dietToDelete?.name}&quot;?
							This action cannot be undone.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={confirmRemoveDiet}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
						>
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</Card>
	);
}
