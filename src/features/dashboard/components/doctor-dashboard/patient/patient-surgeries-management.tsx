'use client';

import { Activity, Edit, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';

import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
	useAddSurgeryToPatient,
	useRemoveSurgeryFromPatient,
	useUpdatePatientSurgery,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';

interface IPatientSurgery {
	id: number;
	name: string;
	description: string;
	created_at: string;
}

interface IPatientSurgeriesManagementProps {
	profileId: number;
	surgeries: IPatientSurgery[];
}

interface ISurgeryFormData {
	name: string;
	description: string;
}

export function PatientSurgeriesManagement({
	profileId,
	surgeries,
}: IPatientSurgeriesManagementProps) {
	const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [editingSurgery, setEditingSurgery] = useState<IPatientSurgery | null>(
		null
	);
	const [surgeryToDelete, setSurgeryToDelete] =
		useState<IPatientSurgery | null>(null);
	const [formData, setFormData] = useState<ISurgeryFormData>({
		name: '',
		description: '',
	});

	const addSurgeryMutation = useAddSurgeryToPatient();
	const updateSurgeryMutation = useUpdatePatientSurgery();
	const removeSurgeryMutation = useRemoveSurgeryFromPatient();

	const handleAddSurgery = () => {
		if (!formData.name.trim()) {
			console.error('Surgery name is required');
			return;
		}

		// Create payload with name and description as backend expects
		const surgeryData = {
			name: formData.name,
			description: formData.description,
		};

		addSurgeryMutation.mutate(
			{ profileId, surgeryData },
			{
				onSuccess: () => {
					setIsAddDialogOpen(false);
					setFormData({
						name: '',
						description: '',
					});
				},
			}
		);
	};

	const handleEditSurgery = () => {
		if (!editingSurgery) return;

		if (!formData.name.trim()) {
			console.error('Surgery name is required');
			return;
		}

		// Create payload with name and description as backend expects
		const surgeryData = {
			name: formData.name,
			description: formData.description,
		};

		updateSurgeryMutation.mutate(
			{
				profileId,
				patientSurgeryId: editingSurgery.id,
				surgeryData,
			},
			{
				onSuccess: () => {
					setIsEditDialogOpen(false);
					setEditingSurgery(null);
					setFormData({
						name: '',
						description: '',
					});
				},
			}
		);
	};

	const handleRemoveSurgery = (surgery: IPatientSurgery) => {
		setSurgeryToDelete(surgery);
		setIsDeleteDialogOpen(true);
	};

	const confirmRemoveSurgery = () => {
		if (surgeryToDelete) {
			removeSurgeryMutation.mutate({
				profileId,
				patientSurgeryId: surgeryToDelete.id,
			});
			setIsDeleteDialogOpen(false);
			setSurgeryToDelete(null);
		}
	};

	const openEditDialog = (surgery: IPatientSurgery) => {
		setEditingSurgery(surgery);
		setFormData({
			name: surgery.name,
			description: surgery.description,
		});
		setIsEditDialogOpen(true);
	};

	return (
		<Card>
			<CardHeader>
				<div className="flex items-center justify-between">
					<CardTitle className="flex items-center gap-2">
						<Activity className="h-5 w-5" />
						Surgeries ({surgeries.length})
					</CardTitle>
					<Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
						<DialogTrigger asChild>
							<Button size="sm">
								<Plus className="mr-2 h-4 w-4" />
								Add Surgery
							</Button>
						</DialogTrigger>
						<DialogContent>
							<DialogHeader>
								<DialogTitle>Add New Surgery</DialogTitle>
								<DialogDescription>
									Add a new surgery to the patient&apos;s profile.
								</DialogDescription>
							</DialogHeader>
							<div className="space-y-4">
								<div>
									<Label htmlFor="name">Surgery Name</Label>
									<Input
										id="name"
										value={formData.name}
										onChange={(e) =>
											setFormData({ ...formData, name: e.target.value })
										}
										placeholder="Enter surgery name..."
										required
									/>
								</div>
								<div>
									<Label htmlFor="description">Description</Label>
									<Textarea
										id="description"
										value={formData.description}
										onChange={(e) =>
											setFormData({ ...formData, description: e.target.value })
										}
										placeholder="Describe the surgery..."
									/>
								</div>
							</div>
							<DialogFooter>
								<Button
									variant="outline"
									onClick={() => setIsAddDialogOpen(false)}
								>
									Cancel
								</Button>
								<Button
									onClick={handleAddSurgery}
									disabled={addSurgeryMutation.isPending}
								>
									{addSurgeryMutation.isPending ? 'Adding...' : 'Add Surgery'}
								</Button>
							</DialogFooter>
						</DialogContent>
					</Dialog>
				</div>
			</CardHeader>
			<CardContent>
				{surgeries.length === 0 ? (
					<p className="py-4 text-center text-gray-500">
						No surgeries recorded for this patient.
					</p>
				) : (
					<div className="space-y-3">
						{surgeries.map((surgery) => (
							<div
								key={surgery.id}
								className="flex items-center justify-between rounded-lg border p-3"
							>
								<div className="flex-1">
									<h4 className="font-medium">{surgery.name}</h4>
									<p className="text-sm text-gray-600">{surgery.description}</p>
								</div>
								<div className="flex gap-2">
									<Button
										size="sm"
										variant="outline"
										onClick={() => openEditDialog(surgery)}
									>
										<Edit className="h-4 w-4" />
									</Button>
									<Button
										size="sm"
										variant="destructive"
										onClick={() => handleRemoveSurgery(surgery)}
										disabled={removeSurgeryMutation.isPending}
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								</div>
							</div>
						))}
					</div>
				)}
			</CardContent>

			{/* Edit Dialog */}
			<Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Edit Surgery</DialogTitle>
						<DialogDescription>
							Update the surgery information.
						</DialogDescription>
					</DialogHeader>
					<div className="space-y-4">
						<div>
							<Label htmlFor="editName">Surgery Name</Label>
							<Input
								id="editName"
								value={formData.name}
								onChange={(e) =>
									setFormData({ ...formData, name: e.target.value })
								}
								placeholder="Enter surgery name..."
								required
							/>
						</div>
						<div>
							<Label htmlFor="editDescription">Description</Label>
							<Textarea
								id="editDescription"
								value={formData.description}
								onChange={(e) =>
									setFormData({ ...formData, description: e.target.value })
								}
								placeholder="Describe the surgery..."
							/>
						</div>
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setIsEditDialogOpen(false)}
						>
							Cancel
						</Button>
						<Button
							onClick={handleEditSurgery}
							disabled={updateSurgeryMutation.isPending}
						>
							{updateSurgeryMutation.isPending
								? 'Updating...'
								: 'Update Surgery'}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Delete Confirmation Dialog */}
			<AlertDialog
				open={isDeleteDialogOpen}
				onOpenChange={setIsDeleteDialogOpen}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Delete Surgery</AlertDialogTitle>
						<AlertDialogDescription>
							Are you sure you want to delete &quot;{surgeryToDelete?.name}
							&quot;? This action cannot be undone.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={confirmRemoveSurgery}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
						>
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</Card>
	);
}
