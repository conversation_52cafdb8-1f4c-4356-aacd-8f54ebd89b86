'use client';

import { Edit, Plus, Shield, Trash2 } from 'lucide-react';
import { useState } from 'react';

import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
	useAddHmoToPatient,
	useRemoveHmoFromPatient,
	useUpdatePatientHmo,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';

interface IPatientHmo {
	id: number;
	hmo_company: string;
	hmo_detail: string;
	hmo_id: string;
	hmo_provider: string;
}

interface IPatientHmoManagementProps {
	profileId: number;
	hmos: IPatientHmo[];
}

interface IHmoFormData {
	hmoId: string;
	hmoCompany: string;
	hmoDetail: string;
	hmoProvider: string;
}

export function PatientHmoManagement({
	profileId,
	hmos,
}: IPatientHmoManagementProps) {
	const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [editingHmo, setEditingHmo] = useState<IPatientHmo | null>(null);
	const [hmoToDelete, setHmoToDelete] = useState<IPatientHmo | null>(null);
	const [formData, setFormData] = useState<IHmoFormData>({
		hmoId: '',
		hmoCompany: '',
		hmoDetail: '',
		hmoProvider: '',
	});

	const addHmoMutation = useAddHmoToPatient();
	const updateHmoMutation = useUpdatePatientHmo();
	const removeHmoMutation = useRemoveHmoFromPatient();

	const handleAddHmo = () => {
		addHmoMutation.mutate(
			{ profileId, hmoData: formData },
			{
				onSuccess: () => {
					setIsAddDialogOpen(false);
					setFormData({
						hmoId: '',
						hmoCompany: '',
						hmoDetail: '',
						hmoProvider: '',
					});
				},
			}
		);
	};

	const handleEditHmo = () => {
		if (!editingHmo) return;

		updateHmoMutation.mutate(
			{
				profileId,
				patientHmoId: editingHmo.id,
				hmoData: formData,
			},
			{
				onSuccess: () => {
					setIsEditDialogOpen(false);
					setEditingHmo(null);
					setFormData({
						hmoId: '',
						hmoCompany: '',
						hmoDetail: '',
						hmoProvider: '',
					});
				},
			}
		);
	};

	const handleRemoveHmo = (hmo: IPatientHmo) => {
		setHmoToDelete(hmo);
		setIsDeleteDialogOpen(true);
	};

	const confirmRemoveHmo = () => {
		if (hmoToDelete) {
			removeHmoMutation.mutate({
				profileId,
				patientHmoId: hmoToDelete.id,
			});
			setIsDeleteDialogOpen(false);
			setHmoToDelete(null);
		}
	};

	const openEditDialog = (hmo: IPatientHmo) => {
		setEditingHmo(hmo);
		setFormData({
			hmoId: hmo.hmo_id,
			hmoCompany: hmo.hmo_company,
			hmoDetail: hmo.hmo_detail,
			hmoProvider: hmo.hmo_provider,
		});
		setIsEditDialogOpen(true);
	};

	return (
		<Card>
			<CardHeader>
				<div className="flex items-center justify-between">
					<CardTitle className="flex items-center gap-2">
						<Shield className="h-5 w-5" />
						HMO Information ({hmos.length})
					</CardTitle>
					<Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
						<DialogTrigger asChild>
							<Button size="sm">
								<Plus className="mr-2 h-4 w-4" />
								Add HMO
							</Button>
						</DialogTrigger>
						<DialogContent>
							<DialogHeader>
								<DialogTitle>Add New HMO</DialogTitle>
								<DialogDescription>
									Add new HMO information to the patient&apos;s profile.
								</DialogDescription>
							</DialogHeader>
							<div className="space-y-4">
								<div>
									<Label htmlFor="hmoId">HMO ID</Label>
									<Input
										id="hmoId"
										value={formData.hmoId}
										onChange={(e) =>
											setFormData({ ...formData, hmoId: e.target.value })
										}
										placeholder="HMO identification number"
									/>
								</div>
								<div>
									<Label htmlFor="hmoCompany">HMO Company</Label>
									<Input
										id="hmoCompany"
										value={formData.hmoCompany}
										onChange={(e) =>
											setFormData({ ...formData, hmoCompany: e.target.value })
										}
										placeholder="HMO company name"
									/>
								</div>
								<div>
									<Label htmlFor="hmoProvider">HMO Provider</Label>
									<Input
										id="hmoProvider"
										value={formData.hmoProvider}
										onChange={(e) =>
											setFormData({ ...formData, hmoProvider: e.target.value })
										}
										placeholder="HMO provider name"
									/>
								</div>
								<div>
									<Label htmlFor="hmoDetail">HMO Details</Label>
									<Textarea
										id="hmoDetail"
										value={formData.hmoDetail}
										onChange={(e) =>
											setFormData({ ...formData, hmoDetail: e.target.value })
										}
										placeholder="Additional HMO details..."
									/>
								</div>
							</div>
							<DialogFooter>
								<Button
									variant="outline"
									onClick={() => setIsAddDialogOpen(false)}
								>
									Cancel
								</Button>
								<Button
									onClick={handleAddHmo}
									disabled={addHmoMutation.isPending}
								>
									{addHmoMutation.isPending ? 'Adding...' : 'Add HMO'}
								</Button>
							</DialogFooter>
						</DialogContent>
					</Dialog>
				</div>
			</CardHeader>
			<CardContent>
				{hmos.length === 0 ? (
					<p className="py-4 text-center text-gray-500">
						No HMO information recorded for this patient.
					</p>
				) : (
					<div className="space-y-3">
						{hmos.map((hmo) => (
							<div
								key={hmo.id}
								className="flex items-center justify-between rounded-lg border p-3"
							>
								<div className="flex-1">
									<div className="mb-1 flex items-center gap-2">
										<h4 className="font-medium">{hmo.hmo_company}</h4>
									</div>
									<p className="text-sm text-gray-600">{hmo.hmo_detail}</p>
									<div className="mt-1 flex gap-4 text-xs text-gray-500">
										<span>ID: {hmo.hmo_id}</span>
										<span>Provider: {hmo.hmo_provider}</span>
									</div>
								</div>
								<div className="flex gap-2">
									<Button
										size="sm"
										variant="outline"
										onClick={() => openEditDialog(hmo)}
									>
										<Edit className="h-4 w-4" />
									</Button>
									<Button
										size="sm"
										variant="destructive"
										onClick={() => handleRemoveHmo(hmo)}
										disabled={removeHmoMutation.isPending}
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								</div>
							</div>
						))}
					</div>
				)}
			</CardContent>

			{/* Edit Dialog */}
			<Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Edit HMO Information</DialogTitle>
						<DialogDescription>Update the HMO information.</DialogDescription>
					</DialogHeader>
					<div className="space-y-4">
						<div>
							<Label htmlFor="editHmoId">HMO ID</Label>
							<Input
								id="editHmoId"
								value={formData.hmoId}
								onChange={(e) =>
									setFormData({ ...formData, hmoId: e.target.value })
								}
								placeholder="HMO identification number"
							/>
						</div>
						<div>
							<Label htmlFor="editHmoCompany">HMO Company</Label>
							<Input
								id="editHmoCompany"
								value={formData.hmoCompany}
								onChange={(e) =>
									setFormData({ ...formData, hmoCompany: e.target.value })
								}
								placeholder="HMO company name"
							/>
						</div>
						<div>
							<Label htmlFor="editHmoProvider">HMO Provider</Label>
							<Input
								id="editHmoProvider"
								value={formData.hmoProvider}
								onChange={(e) =>
									setFormData({ ...formData, hmoProvider: e.target.value })
								}
								placeholder="HMO provider name"
							/>
						</div>
						<div>
							<Label htmlFor="editHmoDetail">HMO Details</Label>
							<Textarea
								id="editHmoDetail"
								value={formData.hmoDetail}
								onChange={(e) =>
									setFormData({ ...formData, hmoDetail: e.target.value })
								}
								placeholder="Additional HMO details..."
							/>
						</div>
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setIsEditDialogOpen(false)}
						>
							Cancel
						</Button>
						<Button
							onClick={handleEditHmo}
							disabled={updateHmoMutation.isPending}
						>
							{updateHmoMutation.isPending ? 'Updating...' : 'Update HMO'}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Delete Confirmation Dialog */}
			<AlertDialog
				open={isDeleteDialogOpen}
				onOpenChange={setIsDeleteDialogOpen}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Delete HMO Information</AlertDialogTitle>
						<AlertDialogDescription>
							Are you sure you want to delete &quot;{hmoToDelete?.hmo_company}
							&quot;? This action cannot be undone.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={confirmRemoveHmo}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
						>
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</Card>
	);
}
