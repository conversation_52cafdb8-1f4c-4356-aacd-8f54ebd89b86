'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useCreatePatient } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import { IClinic } from '@/features/dashboard/types/doctor.types';

const createPatientSchema = z.object({
	clinic_id: z.number().min(1, 'Please select a clinic'),
	title: z.string().min(1, 'Title is required'),
	first_name: z.string().min(1, 'First name is required'),
	middle_name: z.string().optional(),
	last_name: z.string().min(1, 'Last name is required'),
	suffix: z.string().optional(),
	nationality: z.string().min(1, 'Nationality is required'),
	gender: z.string().min(1, 'Gender is required'),
	phone: z.string().min(1, 'Phone number is required'),
	birthday: z.string().min(1, 'Birthday is required'),
	occupation: z.string().min(1, 'Occupation is required'),
	civil_status: z.string().min(1, 'Civil status is required'),
	emergency_contact_person: z
		.string()
		.min(1, 'Emergency contact person is required'),
	emergency_contact_number: z
		.string()
		.min(1, 'Emergency contact number is required'),
	create_account: z.boolean().default(false),
	email: z
		.string()
		.email('Valid email is required')
		.optional()
		.or(z.literal('')),
	current_address: z.string().min(1, 'Current address is required'),
	permanent_address: z.string().optional(),
	blood_type: z.string().optional(),
});

type CreatePatientForm = z.infer<typeof createPatientSchema>;

interface ICreatePatientDialogProps {
	trigger?: React.ReactNode;
	clinics: IClinic[];
	open?: boolean;
	onOpenChange?: (open: boolean) => void;
	onSuccess?: (patientId: string) => void;
}

export function CreatePatientDialog({
	trigger,
	clinics,
	open: controlledOpen,
	onOpenChange: controlledOnOpenChange,
	onSuccess,
}: ICreatePatientDialogProps) {
	const [internalOpen, setInternalOpen] = useState(false);
	const { mutate: createPatient, isPending } = useCreatePatient();

	// Use controlled or internal state
	const open = controlledOpen !== undefined ? controlledOpen : internalOpen;
	const setOpen = controlledOnOpenChange || setInternalOpen;

	const form = useForm({
		resolver: zodResolver(createPatientSchema),
		defaultValues: {
			create_account: false,
			email: '',
		},
	});

	const watchCreateAccount = form.watch('create_account');

	const onSubmit = (data: CreatePatientForm) => {
		// Transform data to match backend expectations (camelCase)
		const submitData = {
			clinicId: data.clinic_id, // Convert snake_case to camelCase
			title: data.title,
			firstName: data.first_name,
			middleName: data.middle_name,
			lastName: data.last_name,
			suffix: data.suffix,
			ethnicity: data.nationality, // Map nationality to ethnicity for backend
			gender: data.gender,
			phone: data.phone,
			birthday: data.birthday,
			occupation: data.occupation,
			civilStatus: data.civil_status,
			emergencyContactName: data.emergency_contact_person,
			emergencyContactNumber: data.emergency_contact_number,
			currentAddress: data.current_address,
			permanentAddress: data.permanent_address,
			bloodType: data.blood_type,
			email: data.create_account ? data.email : undefined,
		};

		createPatient(submitData, {
			onSuccess: (response) => {
				setOpen(false);
				form.reset();
				// Call the onSuccess callback with the created patient ID
				if (onSuccess && response?.data?.id) {
					onSuccess(response.data.id.toString());
				}
			},
		});
	};

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogTrigger asChild>{trigger}</DialogTrigger>
			<DialogContent className="w-full max-w-2xl">
				<DialogHeader>
					<DialogTitle>Add New Patient</DialogTitle>
					<DialogDescription>
						Create a new patient record. Fill in the required information below.
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
						{/* Clinic Selection */}
						<FormField
							control={form.control}
							name="clinic_id"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Clinic *</FormLabel>
									<Select
										onValueChange={(value) => field.onChange(parseInt(value))}
										value={field.value?.toString()}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select a clinic" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											{clinics.map((clinic) => (
												<SelectItem
													key={clinic.id}
													value={clinic.id.toString()}
												>
													{clinic.name}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Personal Information */}
						<div className="space-y-4">
							<h3 className="text-lg font-semibold">Personal Information</h3>

							<div className="grid grid-cols-1 gap-4 md:grid-cols-4">
								<FormField
									control={form.control}
									name="title"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Title *</FormLabel>
											<Select
												onValueChange={field.onChange}
												value={field.value}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Select title" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<SelectItem value="Mr.">Mr.</SelectItem>
													<SelectItem value="Ms.">Ms.</SelectItem>
													<SelectItem value="Mrs.">Mrs.</SelectItem>
													<SelectItem value="Dr.">Dr.</SelectItem>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="first_name"
									render={({ field }) => (
										<FormItem>
											<FormLabel>First Name *</FormLabel>
											<FormControl>
												<Input placeholder="Enter first name" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="middle_name"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Middle Name</FormLabel>
											<FormControl>
												<Input placeholder="Enter middle name" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="last_name"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Last Name *</FormLabel>
											<FormControl>
												<Input placeholder="Enter last name" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
								<FormField
									control={form.control}
									name="suffix"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Suffix</FormLabel>
											<FormControl>
												<Input placeholder="Jr., Sr., III, etc." {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="nationality"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Nationality *</FormLabel>
											<FormControl>
												<Input placeholder="Enter nationality" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="gender"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Gender *</FormLabel>
											<Select
												onValueChange={field.onChange}
												value={field.value}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Select gender" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<SelectItem value="Male">Male</SelectItem>
													<SelectItem value="Female">Female</SelectItem>
													<SelectItem value="Other">Other</SelectItem>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						</div>

						{/* Contact Information */}
						<div className="space-y-4">
							<h3 className="text-lg font-semibold">Contact Information</h3>

							<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
								<FormField
									control={form.control}
									name="phone"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Phone Number *</FormLabel>
											<FormControl>
												<Input placeholder="Enter phone number" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="birthday"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Birthday *</FormLabel>
											<FormControl>
												<Input type="date" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						</div>

						{/* Additional Information */}
						<div className="space-y-4">
							<h3 className="text-lg font-semibold">Additional Information</h3>

							<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
								<FormField
									control={form.control}
									name="occupation"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Occupation *</FormLabel>
											<FormControl>
												<Input placeholder="Enter occupation" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="civil_status"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Civil Status *</FormLabel>
											<Select
												onValueChange={field.onChange}
												value={field.value}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Select civil status" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<SelectItem value="Single">Single</SelectItem>
													<SelectItem value="Married">Married</SelectItem>
													<SelectItem value="Divorced">Divorced</SelectItem>
													<SelectItem value="Widowed">Widowed</SelectItem>
													<SelectItem value="Live In">Live In</SelectItem>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<FormField
								control={form.control}
								name="blood_type"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Blood Type</FormLabel>
										<Select onValueChange={field.onChange} value={field.value}>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Select blood type" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												<SelectItem value="A+">A+</SelectItem>
												<SelectItem value="A-">A-</SelectItem>
												<SelectItem value="B+">B+</SelectItem>
												<SelectItem value="B-">B-</SelectItem>
												<SelectItem value="AB+">AB+</SelectItem>
												<SelectItem value="AB-">AB-</SelectItem>
												<SelectItem value="O+">O+</SelectItem>
												<SelectItem value="O-">O-</SelectItem>
												<SelectItem value="Rh null">Rh null</SelectItem>
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						{/* Emergency Contact */}
						<div className="space-y-4">
							<h3 className="text-lg font-semibold">Emergency Contact</h3>

							<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
								<FormField
									control={form.control}
									name="emergency_contact_person"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Emergency Contact Person *</FormLabel>
											<FormControl>
												<Input
													placeholder="Enter contact person name"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="emergency_contact_number"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Emergency Contact Number *</FormLabel>
											<FormControl>
												<Input placeholder="Enter contact number" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						</div>

						{/* Address Information */}
						<div className="space-y-4">
							<h3 className="text-lg font-semibold">Address Information</h3>

							<FormField
								control={form.control}
								name="current_address"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Current Address *</FormLabel>
										<FormControl>
											<Textarea
												placeholder="Enter current address"
												{...field}
												rows={3}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="permanent_address"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Permanent Address</FormLabel>
										<FormControl>
											<Textarea
												placeholder="Enter permanent address (optional)"
												{...field}
												rows={3}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						{/* Account Creation */}
						<div className="space-y-4">
							<h3 className="text-lg font-semibold">Account Settings</h3>

							<FormField
								control={form.control}
								name="create_account"
								render={({ field }) => (
									<FormItem className="flex flex-row items-start space-y-0 space-x-3">
										<FormControl>
											<Checkbox
												checked={field.value}
												onCheckedChange={field.onChange}
											/>
										</FormControl>
										<div className="space-y-1 leading-none">
											<FormLabel>Create patient account</FormLabel>
											<p className="text-sm text-gray-500">
												If enabled, an email will be sent to the patient for
												account verification
											</p>
										</div>
									</FormItem>
								)}
							/>

							{watchCreateAccount && (
								<FormField
									control={form.control}
									name="email"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Email Address *</FormLabel>
											<FormControl>
												<Input
													type="email"
													placeholder="Enter email address"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							)}
						</div>

						{/* Form Actions */}
						<div className="flex justify-end gap-3">
							<Button
								type="button"
								variant="outline"
								onClick={() => setOpen(false)}
								disabled={isPending}
							>
								Cancel
							</Button>
							<Button
								type="submit"
								disabled={isPending}
								className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
							>
								{isPending ? 'Creating...' : 'Create Patient'}
							</Button>
						</div>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
