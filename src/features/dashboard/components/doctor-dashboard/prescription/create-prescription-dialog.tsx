'use client';

import { format } from 'date-fns';
import { Search, User, UserPlus } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
	useClinics,
	useCreateStandalonePrescription,
	usePatients,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import {
	ICreateStandalonePrescriptionRequest,
	IPatient,
} from '@/features/dashboard/types/doctor.types';

interface ICreatePrescriptionDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onClose: () => void;
}

export function CreatePrescriptionDialog({
	open,
	onOpenChange,
	onClose,
}: ICreatePrescriptionDialogProps) {
	const [activeTab, setActiveTab] = useState('registered');
	const [selectedClinicId, setSelectedClinicId] = useState<string>('');
	const [selectedPatient, setSelectedPatient] = useState<IPatient | null>(null);
	const [patientSearch, setPatientSearch] = useState('');

	// Walk-in patient form
	const [walkInForm, setWalkInForm] = useState({
		patientName: '',
		patientAddress: '',
		patientPhone: '',
		patientGender: '',
		patientBirthdate: '',
	});

	// Fetch data
	const { data: clinicsResponse } = useClinics();
	const { data: patientsResponse } = usePatients({
		search: patientSearch,
	});
	const { mutate: createPrescription, isPending } =
		useCreateStandalonePrescription();

	const clinics = clinicsResponse?.data?.data || [];
	const patients = patientsResponse?.data?.data || [];

	const handleSubmit = () => {
		if (!selectedClinicId) {
			alert('Please select a clinic');
			return;
		}

		let prescriptionData: ICreateStandalonePrescriptionRequest;

		if (activeTab === 'registered' && selectedPatient) {
			prescriptionData = {
				clinicId: parseInt(selectedClinicId),
				patientProfileId: selectedPatient.id,
				patientName: `${selectedPatient.first_name} ${selectedPatient.last_name}`,
				patientAddress:
					selectedPatient.profileCurrentAddress?.street_name || '',
				patientPhone: selectedPatient.phone || '',
				patientGender: selectedPatient.gender || '',
				patientBirthdate: selectedPatient.birthday || '',
			};
		} else if (activeTab === 'walkin') {
			if (
				!walkInForm.patientName ||
				!walkInForm.patientGender ||
				!walkInForm.patientBirthdate
			) {
				alert('Please fill in all required fields');
				return;
			}

			prescriptionData = {
				clinicId: parseInt(selectedClinicId),
				patientName: walkInForm.patientName,
				patientAddress: walkInForm.patientAddress,
				patientPhone: walkInForm.patientPhone,
				patientGender: walkInForm.patientGender,
				patientBirthdate: walkInForm.patientBirthdate,
			};
		} else {
			alert('Please select a patient or fill in patient details');
			return;
		}

		createPrescription(prescriptionData, {
			onSuccess: () => {
				handleClose();
			},
		});
	};

	const handleClose = () => {
		setSelectedClinicId('');
		setSelectedPatient(null);
		setPatientSearch('');
		setWalkInForm({
			patientName: '',
			patientAddress: '',
			patientPhone: '',
			patientGender: '',
			patientBirthdate: '',
		});
		setActiveTab('registered');
		onClose();
	};

	const formatDate = (dateString: string) => {
		try {
			return format(new Date(dateString), 'MMM dd, yyyy');
		} catch {
			return dateString;
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-2xl">
				<DialogHeader>
					<DialogTitle>Create New Prescription</DialogTitle>
					<DialogDescription>
						Create a standalone prescription for a patient
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-6">
					{/* Clinic Selection */}
					<div className="space-y-2">
						<Label htmlFor="clinic">Select Clinic *</Label>
						<Select
							value={selectedClinicId}
							onValueChange={setSelectedClinicId}
						>
							<SelectTrigger>
								<SelectValue placeholder="Choose a clinic" />
							</SelectTrigger>
							<SelectContent>
								{clinics.map((clinic) => (
									<SelectItem key={clinic.id} value={clinic.id.toString()}>
										{clinic.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					{/* Patient Selection */}
					<div className="space-y-4">
						<Label>Patient Information *</Label>
						<Tabs value={activeTab} onValueChange={setActiveTab}>
							<TabsList className="grid w-full grid-cols-2">
								<TabsTrigger value="registered">
									<User className="mr-2 h-4 w-4" />
									Registered Patient
								</TabsTrigger>
								<TabsTrigger value="walkin">
									<UserPlus className="mr-2 h-4 w-4" />
									Walk-in Patient
								</TabsTrigger>
							</TabsList>

							<TabsContent value="registered" className="space-y-4">
								{/* Patient Search */}
								<div className="space-y-2">
									<Label htmlFor="patient-search">Search Patient</Label>
									<div className="relative">
										<Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
										<Input
											id="patient-search"
											placeholder="Search by name, phone, or email..."
											value={patientSearch}
											onChange={(e) => setPatientSearch(e.target.value)}
											className="pl-10"
										/>
									</div>
								</div>

								{/* Patient List */}
								{patients.length > 0 && (
									<div className="max-h-60 space-y-2 overflow-y-auto">
										{patients.map((patient) => (
											<Card
												key={patient.id}
												className={`cursor-pointer transition-colors ${
													selectedPatient?.id === patient.id
														? 'border-primary bg-primary/5'
														: 'hover:bg-muted/50'
												}`}
												onClick={() => setSelectedPatient(patient)}
											>
												<CardContent className="p-3">
													<div className="flex items-center justify-between">
														<div>
															<div className="font-medium">
																{patient.first_name} {patient.last_name}
															</div>
															<div className="text-muted-foreground text-sm">
																{patient.phone} • {patient.gender} •{' '}
																{formatDate(patient.birthday)}
															</div>
														</div>
													</div>
												</CardContent>
											</Card>
										))}
									</div>
								)}
							</TabsContent>

							<TabsContent value="walkin" className="space-y-4">
								<div className="grid grid-cols-2 gap-4">
									<div className="space-y-2">
										<Label htmlFor="patient-name">Patient Name *</Label>
										<Input
											id="patient-name"
											value={walkInForm.patientName}
											onChange={(e) =>
												setWalkInForm({
													...walkInForm,
													patientName: e.target.value,
												})
											}
											placeholder="Enter patient name"
										/>
									</div>
									<div className="space-y-2">
										<Label htmlFor="patient-phone">Phone Number</Label>
										<Input
											id="patient-phone"
											value={walkInForm.patientPhone}
											onChange={(e) =>
												setWalkInForm({
													...walkInForm,
													patientPhone: e.target.value,
												})
											}
											placeholder="Enter phone number"
										/>
									</div>
								</div>

								<div className="space-y-2">
									<Label htmlFor="patient-address">Address</Label>
									<Input
										id="patient-address"
										value={walkInForm.patientAddress}
										onChange={(e) =>
											setWalkInForm({
												...walkInForm,
												patientAddress: e.target.value,
											})
										}
										placeholder="Enter patient address"
									/>
								</div>

								<div className="grid grid-cols-2 gap-4">
									<div className="space-y-2">
										<Label htmlFor="patient-gender">Gender *</Label>
										<Select
											value={walkInForm.patientGender}
											onValueChange={(value) =>
												setWalkInForm({ ...walkInForm, patientGender: value })
											}
										>
											<SelectTrigger>
												<SelectValue placeholder="Select gender" />
											</SelectTrigger>
											<SelectContent>
												<SelectItem value="Male">Male</SelectItem>
												<SelectItem value="Female">Female</SelectItem>
											</SelectContent>
										</Select>
									</div>
									<div className="space-y-2">
										<Label htmlFor="patient-birthdate">Birth Date *</Label>
										<Input
											id="patient-birthdate"
											type="date"
											value={walkInForm.patientBirthdate}
											onChange={(e) =>
												setWalkInForm({
													...walkInForm,
													patientBirthdate: e.target.value,
												})
											}
										/>
									</div>
								</div>
							</TabsContent>
						</Tabs>
					</div>
				</div>

				<DialogFooter>
					<Button variant="outline" onClick={handleClose}>
						Cancel
					</Button>
					<Button
						onClick={handleSubmit}
						disabled={isPending}
						className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
					>
						{isPending ? 'Creating...' : 'Create Prescription'}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
