'use client';

import { format } from 'date-fns';
import {
	Calendar,
	Edit,
	MapPin,
	Pencil,
	Phone,
	Plus,
	Printer,
	Trash2,
	User,
} from 'lucide-react';
import { useEffect, useState } from 'react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Textarea } from '@/components/ui/textarea';
import { IPrescriptionType } from '@/core/api/data';
import {
	useCreatePrescriptionType,
	useCreateStandalonePrescriptionItem,
	usePrescriptionDetail,
	useRemoveStandalonePrescriptionItem,
	useUpdatePrescriptionPtr,
	useUpdateStandalonePrescriptionItem,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import { usePrescriptionTypes } from '@/features/dashboard/hooks/useReferenceData';
import { IPrescriptionItem } from '@/features/dashboard/types/doctor.types';

interface IPrescriptionDetailDialogProps {
	prescriptionId: number;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onClose: () => void;
	onPrint: (prescriptionId: number) => void;
}

interface IFormData {
	name: string;
	description: string;
}

export function PrescriptionDetailDialog({
	prescriptionId,
	open,
	onOpenChange,
	onPrint,
}: Omit<IPrescriptionDetailDialogProps, 'onClose'>) {
	const [showCreateDialog, setShowCreateDialog] = useState(false);
	const [showMedicationForm, setShowMedicationForm] = useState(false);
	const [medicationForm, setMedicationForm] = useState({
		generic: '',
		brand: '',
		dosageForm: '',
		dosage: '',
		frequency: '',
		quantity: 0,
		instruction: '',
	});
	const [formData, setFormData] = useState<IFormData>({
		name: '',
		description: '',
	});

	const [showPtrForm, setShowPtrForm] = useState(false);
	const [ptrForm, setPtrForm] = useState({
		professionalTaxReceipt: '',
	});

	// Fetch prescription details
	const {
		data: prescriptionResponse,
		isLoading,
		error,
	} = usePrescriptionDetail(prescriptionId, { enabled: open });

	const { mutate: createPrescriptionItem } =
		useCreateStandalonePrescriptionItem();
	const { mutate: removePrescriptionItem } =
		useRemoveStandalonePrescriptionItem();
	const { mutate: updatePrescriptionItem } =
		useUpdateStandalonePrescriptionItem();
	const { mutate: createPrescriptionType } = useCreatePrescriptionType();
	const { mutate: updateProfessionalTaxReceipt } = useUpdatePrescriptionPtr();

	const { data: prescriptionTypesResponse } = usePrescriptionTypes();

	const prescriptionTypes = prescriptionTypesResponse?.data || [];

	const prescription = prescriptionResponse?.data;

	const handleAddMedication = () => {
		if (!medicationForm.generic.trim()) return;

		createPrescriptionItem({
			prescriptionId,
			itemData: medicationForm,
		});

		setMedicationForm({
			generic: '',
			brand: '',
			dosageForm: '',
			dosage: '',
			frequency: '',
			quantity: 0,
			instruction: '',
		});
		setShowMedicationForm(false);
	};

	const handleRemoveMedication = (prescriptionItemId: number) => {
		if (confirm('Are you sure you want to remove this medication?')) {
			removePrescriptionItem({
				prescriptionId,
				prescriptionItemId,
			});
		}
	};

	const handleCreate = () => {
		if (formData.name.trim()) {
			createPrescriptionType({
				name: formData.name.trim(),
				description: formData.description.trim() || undefined,
			});
			setFormData({ name: '', description: '' });
			setShowCreateDialog(false);
		}
	};

	const formatDate = (dateString: string) => {
		try {
			return format(new Date(dateString), 'MMM dd, yyyy');
		} catch {
			return dateString;
		}
	};

	const handleUpdateMedication = (
		prescriptionItemId: number,
		itemData: {
			generic: string;
			brand: string;
			dosageForm: string;
			dosage: string;
			frequency: string;
			quantity: number;
			instruction: string;
		}
	) => {
		updatePrescriptionItem({
			prescriptionId,
			prescriptionItemId,
			itemData,
		});
	};

	const handleSubmitPtr = () => {
		if (!prescriptionId || !ptrForm.professionalTaxReceipt) return;

		updateProfessionalTaxReceipt({
			prescriptionId,
			data: {
				professionalTaxReceipt: ptrForm.professionalTaxReceipt,
			},
		});

		// Reset form and close dialog
		setShowPtrForm(false);
	};

	useEffect(() => {
		if (prescription) {
			setPtrForm({
				professionalTaxReceipt: prescription.professional_tax_receipt || '',
			});
		}
	}, [prescription]);

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-4xl">
				<DialogHeader>
					<DialogTitle>Prescription Details</DialogTitle>
					<DialogDescription>
						View and manage prescription #{prescriptionId}
					</DialogDescription>
				</DialogHeader>

				{isLoading ? (
					<div className="space-y-4">
						<Skeleton className="h-32 w-full" />
						<Skeleton className="h-24 w-full" />
						<Skeleton className="h-24 w-full" />
					</div>
				) : error ? (
					<Alert variant="destructive">
						<AlertDescription>
							Failed to load prescription details. Please try again.
						</AlertDescription>
					</Alert>
				) : prescription ? (
					<div className="space-y-6">
						{/* Patient Information */}
						<Card>
							<CardHeader>
								<div className="flex flex-row justify-between">
									<CardTitle className="flex items-center gap-2">
										<User className="h-5 w-5" />
										Patient Information
									</CardTitle>
									<Button
										onClick={() => onPrint(prescriptionId)}
										className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
									>
										<Printer className="mr-2 h-4 w-4" />
										Print Medication
									</Button>
								</div>
							</CardHeader>
							<CardContent className="grid grid-cols-2 gap-4">
								<div>
									<Label className="text-sm font-medium">Name</Label>
									<p className="text-sm">{prescription.patient_name}</p>
								</div>
								<div>
									<Label className="text-sm font-medium">Gender</Label>
									<p className="text-sm">{prescription.patient_gender}</p>
								</div>
								<div>
									<Label className="text-sm font-medium">Birth Date</Label>
									<p className="flex items-center gap-1 text-sm">
										<Calendar className="h-4 w-4" />
										{formatDate(prescription.patient_birthdate)}
									</p>
								</div>
								<div>
									<Label className="text-sm font-medium">Phone</Label>
									<p className="flex items-center gap-1 text-sm">
										<Phone className="h-4 w-4" />
										{prescription.patient_phone}
									</p>
								</div>
								<div className="col-span-2">
									<Label className="text-sm font-medium">Address</Label>
									<p className="flex items-center gap-1 text-sm">
										<MapPin className="h-4 w-4" />
										{prescription.patient_address}
									</p>
								</div>
							</CardContent>
						</Card>

						{/* Clinic Information */}
						{prescription.clinic && (
							<Card>
								<CardHeader>
									<CardTitle>Clinic Information</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="grid grid-cols-2 gap-4">
										<div>
											<Label className="text-sm font-medium">Clinic Name</Label>
											<p className="text-sm">{prescription.clinic.name}</p>
										</div>
										<div>
											<Label className="text-sm font-medium">Created</Label>
											<p className="text-sm">
												{formatDate(prescription.created_at)}
											</p>
										</div>
										<div>
											<Label className="text-sm font-medium">
												Professional Tax Receipt
											</Label>
											<div className="flex items-center gap-2">
												<p className="text-sm">
													{prescription?.professional_tax_receipt || '---'}
												</p>
												<Button
													variant={'ghost'}
													onClick={() => {
														setShowPtrForm(true);
													}}
													size="sm"
												>
													<Pencil />
													Edit
												</Button>
											</div>
										</div>
									</div>
								</CardContent>
							</Card>
						)}

						{/* Medications */}
						<Card>
							<CardHeader>
								<div className="flex items-center justify-between">
									<CardTitle>Medications</CardTitle>
									<Button
										onClick={() => setShowMedicationForm(true)}
										size="sm"
										className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
									>
										<Plus className="mr-2 h-4 w-4" />
										Add Medication
									</Button>
								</div>
							</CardHeader>
							<CardContent className="space-y-4">
								{/* Create Dialog */}
								<Dialog
									open={showCreateDialog}
									onOpenChange={setShowCreateDialog}
								>
									<DialogContent>
										<DialogHeader>
											<DialogTitle>Add New Prescription</DialogTitle>
											<DialogDescription>
												Create a new prescription entry.
											</DialogDescription>
										</DialogHeader>
										<div className="grid gap-4">
											<div className="grid gap-2">
												<Label htmlFor="name">Name</Label>
												<Input
													id="name"
													value={formData.name}
													onChange={(e) =>
														setFormData({ ...formData, name: e.target.value })
													}
													placeholder="Enter name"
												/>
											</div>
											<div className="grid gap-2">
												<Label htmlFor="description">
													Description (Optional)
												</Label>
												<Textarea
													id="description"
													value={formData.description}
													onChange={(e) =>
														setFormData({
															...formData,
															description: e.target.value,
														})
													}
													placeholder="Enter description"
													rows={3}
												/>
											</div>
										</div>
										<DialogFooter>
											<Button
												variant="outline"
												onClick={() => setShowCreateDialog(false)}
											>
												Cancel
											</Button>
											<Button
												onClick={handleCreate}
												disabled={!formData.name.trim()}
											>
												Create
											</Button>
										</DialogFooter>
									</DialogContent>
								</Dialog>
								{/* Ptr Form Dialog */}
								<Dialog
									open={showPtrForm}
									onOpenChange={(value) => {
										if (!value) {
											setShowPtrForm(false);
										}
									}}
								>
									<DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
										<DialogHeader>
											<DialogTitle>Edit Professional Tax Receipt</DialogTitle>
											<DialogDescription>
												Update the professional tax receipt number for this
												appointment.
											</DialogDescription>
										</DialogHeader>

										<div className="space-y-6">
											{/* Ptr Information */}
											<div className="space-y-4">
												<div className="space-y-2">
													<Label htmlFor="professionalTaxReceipt">
														Professional Tax Receipt *
													</Label>
													<Input
														id="professionalTaxReceipt"
														value={ptrForm.professionalTaxReceipt}
														onChange={(e) =>
															setPtrForm((prev) => ({
																...prev,
																professionalTaxReceipt: e.target.value,
															}))
														}
														placeholder="Enter professional tax receipt number"
													/>
												</div>
											</div>
										</div>

										<DialogFooter>
											<Button
												variant="outline"
												onClick={() => {
													setShowPtrForm(false);
												}}
											>
												Cancel
											</Button>
											<Button
												onClick={handleSubmitPtr}
												disabled={!ptrForm.professionalTaxReceipt}
												className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
											>
												Save
											</Button>
										</DialogFooter>
									</DialogContent>
								</Dialog>

								{/* Add Medication Form */}
								{showMedicationForm && (
									<Card className="border-dashed">
										<CardContent className="">
											<div className="grid grid-cols-2 gap-4">
												<div>
													<Label htmlFor="clinic">Generic Name *</Label>
													<Select
														value={medicationForm.generic}
														onValueChange={(value) => {
															if (value === 'new') {
																setShowCreateDialog(true);
																return;
															}
															setMedicationForm({
																...medicationForm,
																generic: value,
															});
														}}
													>
														<SelectTrigger className="w-full">
															<SelectValue placeholder="Choose a generic" />
														</SelectTrigger>
														<SelectContent>
															<SelectItem
																value="new"
																className="text-[oklch(0.7448_0.1256_202.74)]"
															>
																<div className="flex items-center gap-2">
																	<Plus className="h-4 w-4" />
																	Add Prescription
																</div>
															</SelectItem>
															{prescriptionTypes.map((prescriptionType) => (
																<SelectItem
																	key={prescriptionType.id}
																	value={prescriptionType.name}
																>
																	{prescriptionType.name}
																</SelectItem>
															))}
														</SelectContent>
													</Select>
												</div>
												<div>
													<Label htmlFor="brand">Brand Name</Label>
													<Input
														id="brand"
														value={medicationForm.brand}
														onChange={(e) =>
															setMedicationForm({
																...medicationForm,
																brand: e.target.value,
															})
														}
														placeholder="Enter brand name"
													/>
												</div>
												<div>
													<Label htmlFor="dosageForm">Dosage Form</Label>
													<Input
														id="dosageForm"
														value={medicationForm.dosageForm}
														onChange={(e) =>
															setMedicationForm({
																...medicationForm,
																dosageForm: e.target.value,
															})
														}
														placeholder="e.g., Tablet, Capsule"
													/>
												</div>
												<div>
													<Label htmlFor="dosage">Dosage</Label>
													<Input
														id="dosage"
														value={medicationForm.dosage}
														onChange={(e) =>
															setMedicationForm({
																...medicationForm,
																dosage: e.target.value,
															})
														}
														placeholder="e.g., 500mg"
													/>
												</div>
												<div>
													<Label htmlFor="frequency">Frequency</Label>
													<Input
														id="frequency"
														value={medicationForm.frequency}
														onChange={(e) =>
															setMedicationForm({
																...medicationForm,
																frequency: e.target.value,
															})
														}
														placeholder="e.g., 3 times daily"
													/>
												</div>
												<div>
													<Label htmlFor="quantity">Quantity</Label>
													<Input
														id="quantity"
														type="number"
														value={medicationForm.quantity}
														onChange={(e) =>
															setMedicationForm({
																...medicationForm,
																quantity: parseInt(e.target.value) || 0,
															})
														}
														placeholder="Enter quantity"
													/>
												</div>
											</div>
											<div className="mt-4">
												<Label htmlFor="instruction">Instructions</Label>
												<Input
													id="instruction"
													value={medicationForm.instruction}
													onChange={(e) =>
														setMedicationForm({
															...medicationForm,
															instruction: e.target.value,
														})
													}
													placeholder="Enter special instructions"
												/>
											</div>
											<div className="mt-4 flex gap-2">
												<Button onClick={handleAddMedication} size="sm">
													Add Medication
												</Button>
												<Button
													variant="outline"
													onClick={() => setShowMedicationForm(false)}
													size="sm"
												>
													Cancel
												</Button>
											</div>
										</CardContent>
									</Card>
								)}

								{/* Existing Medications */}
								{prescription.prescriptionItems &&
								prescription.prescriptionItems.length > 0 ? (
									<div className="space-y-3">
										{prescription.prescriptionItems.map(
											(item: IPrescriptionItem) => (
												<PrescriptionItemCard
													setShowCreateDialog={setShowCreateDialog}
													key={item.id}
													item={item}
													handleRemoveMedication={handleRemoveMedication}
													prescriptionTypes={prescriptionTypes}
													handleUpdateMedication={handleUpdateMedication}
												/>
											)
										)}
									</div>
								) : (
									<div className="py-8 text-center">
										<p className="text-muted-foreground">
											No medications added yet. Click &quot;Add Medication&quot;
											to get started.
										</p>
									</div>
								)}
							</CardContent>
						</Card>
					</div>
				) : null}
			</DialogContent>
		</Dialog>
	);
}

export function PrescriptionItemCard({
	item,
	prescriptionTypes,
	handleRemoveMedication,
	handleUpdateMedication,
	setShowCreateDialog,
}: {
	item: IPrescriptionItem;
	prescriptionTypes: IPrescriptionType[];
	handleRemoveMedication: (prescriptionItemId: number) => void;
	handleUpdateMedication: (
		prescriptionItemId: number,
		itemData: {
			generic: string;
			brand: string;
			dosageForm: string;
			dosage: string;
			frequency: string;
			quantity: number;
			instruction: string;
		}
	) => void;
	setShowCreateDialog: (open: boolean) => void;
}) {
	const [showUpdateMedicationForm, setShowUpdateMedicationForm] =
		useState(false);
	return (
		<Card key={item.id} className="border">
			<CardContent className="">
				{!showUpdateMedicationForm && (
					<div className="flex items-center justify-between gap-2">
						<div className="flex-1">
							<div className="flex items-center gap-4">
								<div>
									<p className="font-medium">
										{item.generic} ({item.brand})
									</p>
									<p className="text-muted-foreground text-sm">
										{item.dosage_form} • {item.dosage} • {item.frequency}
									</p>
									{item.quantity && (
										<p className="text-muted-foreground text-sm">
											Quantity: {item.quantity}
										</p>
									)}
									{item.instruction && (
										<p className="text-muted-foreground text-sm">
											Instructions: {item.instruction}
										</p>
									)}
								</div>
							</div>
						</div>
						<Button
							variant="outline"
							size="sm"
							onClick={() => {
								setShowUpdateMedicationForm(true);
							}}
						>
							<Edit className="h-4 w-4" />
						</Button>
						<Button
							variant="outline"
							size="sm"
							onClick={() => handleRemoveMedication(item.id)}
							className="text-red-600 hover:text-red-700"
						>
							<Trash2 className="h-4 w-4" />
						</Button>
					</div>
				)}
				{showUpdateMedicationForm && (
					<UpdatePrescriptionItemDialog
						setShowCreateDialog={setShowCreateDialog}
						onOpenChange={(open) => setShowUpdateMedicationForm(open)}
						prescriptionItem={item}
						prescriptionTypes={prescriptionTypes}
						handleUpdateMedication={handleUpdateMedication}
					/>
				)}
			</CardContent>
		</Card>
	);
}

const UpdatePrescriptionItemDialog = ({
	prescriptionItem,
	prescriptionTypes,
	onOpenChange,
	handleUpdateMedication,
	setShowCreateDialog,
}: {
	prescriptionItem: IPrescriptionItem;
	prescriptionTypes: IPrescriptionType[];
	onOpenChange: (open: boolean) => void;
	handleUpdateMedication: (
		prescriptionItemId: number,
		itemData: {
			generic: string;
			brand: string;
			dosageForm: string;
			dosage: string;
			frequency: string;
			quantity: number;
			instruction: string;
		}
	) => void;
	setShowCreateDialog: (open: boolean) => void;
}) => {
	const [medicationForm, setMedicationForm] = useState({
		generic: '',
		brand: '',
		dosageForm: '',
		dosage: '',
		frequency: '',
		quantity: 0,
		instruction: '',
	});

	useEffect(() => {
		setMedicationForm({
			generic: prescriptionItem.generic,
			brand: prescriptionItem.brand,
			dosageForm: prescriptionItem.dosage_form,
			dosage: prescriptionItem.dosage,
			frequency: prescriptionItem.frequency,
			quantity: prescriptionItem.quantity,
			instruction: prescriptionItem.instruction,
		});
	}, [prescriptionItem]);

	return (
		<div>
			<div className="grid grid-cols-2 gap-4">
				<div>
					<Label htmlFor="clinic">Generic Name *</Label>
					<Select
						value={medicationForm.generic}
						onValueChange={(value) => {
							if (value === 'new') {
								setShowCreateDialog(true);
								return;
							}

							setMedicationForm({
								...medicationForm,
								generic: value,
							});
						}}
					>
						<SelectTrigger className="w-full">
							<SelectValue placeholder="Choose a generic" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem
								value="new"
								className="text-[oklch(0.7448_0.1256_202.74)]"
							>
								<div className="flex items-center gap-2">
									<Plus className="h-4 w-4" />
									Add Prescription
								</div>
							</SelectItem>
							{prescriptionTypes.map((prescriptionType) => (
								<SelectItem
									key={prescriptionType.id}
									value={prescriptionType.name}
								>
									{prescriptionType.name}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>
				<div>
					<Label htmlFor="brand">Brand Name</Label>
					<Input
						id="brand"
						value={medicationForm.brand}
						onChange={(e) =>
							setMedicationForm({
								...medicationForm,
								brand: e.target.value,
							})
						}
						placeholder="Enter brand name"
					/>
				</div>
				<div>
					<Label htmlFor="dosageForm">Dosage Form</Label>
					<Input
						id="dosageForm"
						value={medicationForm.dosageForm}
						onChange={(e) =>
							setMedicationForm({
								...medicationForm,
								dosageForm: e.target.value,
							})
						}
						placeholder="e.g., Tablet, Capsule"
					/>
				</div>
				<div>
					<Label htmlFor="dosage">Dosage</Label>
					<Input
						id="dosage"
						value={medicationForm.dosage}
						onChange={(e) =>
							setMedicationForm({
								...medicationForm,
								dosage: e.target.value,
							})
						}
						placeholder="e.g., 500mg"
					/>
				</div>
				<div>
					<Label htmlFor="frequency">Frequency</Label>
					<Input
						id="frequency"
						value={medicationForm.frequency}
						onChange={(e) =>
							setMedicationForm({
								...medicationForm,
								frequency: e.target.value,
							})
						}
						placeholder="e.g., 3 times daily"
					/>
				</div>
				<div>
					<Label htmlFor="quantity">Quantity</Label>
					<Input
						id="quantity"
						type="number"
						value={medicationForm.quantity}
						onChange={(e) =>
							setMedicationForm({
								...medicationForm,
								quantity: parseInt(e.target.value) || 0,
							})
						}
						placeholder="Enter quantity"
					/>
				</div>
			</div>
			<div className="mt-4">
				<Label htmlFor="instruction">Instructions</Label>
				<Input
					id="instruction"
					value={medicationForm.instruction}
					onChange={(e) =>
						setMedicationForm({
							...medicationForm,
							instruction: e.target.value,
						})
					}
					placeholder="Enter special instructions"
				/>
			</div>
			<div className="mt-4 flex gap-2">
				<Button
					onClick={() => {
						handleUpdateMedication(prescriptionItem.id, medicationForm);
						onOpenChange(false);
					}}
					size="sm"
				>
					Save
				</Button>
				<Button variant="outline" onClick={() => onOpenChange(false)} size="sm">
					Cancel
				</Button>
			</div>
		</div>
	);
};
