'use client';

import { useRef } from 'react';
import { useReactToPrint } from 'react-to-print';

import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { usePrescriptionDetail } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';

import { PrescriptionPrint } from '../../shared/role-aware-prescription-print';

interface IPrescriptionPrintDialogProps {
	prescriptionId: number;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onClose: () => void;
}

export function PrescriptionPrintDialog({
	prescriptionId,
	open,
	onOpenChange,
}: Omit<IPrescriptionPrintDialogProps, 'onClose'>) {
	// Fetch prescription details
	const { data: prescriptionResponse } = usePrescriptionDetail(prescriptionId, {
		enabled: open,
	});

	const prescription = prescriptionResponse?.data;

	const componentRef = useRef<HTMLDivElement | null>(null);
	const handlePrint = useReactToPrint({
		contentRef: componentRef,
		documentTitle: 'Print This Document',
		onBeforePrint: async () => console.log('before printing...'),
		onAfterPrint: () => console.log('after printing...'),
	});

	const handleSubmit = async () => {
		handlePrint();
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogHeader className="hidden">
				<DialogTitle>Prescription Details</DialogTitle>
				<DialogDescription>
					Print prescription #{prescriptionId}
				</DialogDescription>
			</DialogHeader>
			<DialogContent className="max-w-2xl">
				{prescription && (
					<PrescriptionPrint data={prescription} componentRef={componentRef} />
				)}
				<Button onClick={handleSubmit}>Print</Button>
			</DialogContent>
		</Dialog>
	);
}
