'use client';

import { Key } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { ChangePasswordDialog } from './change-password-dialog';

export function DoctorSettingsManagement() {
	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
						Settings
					</h1>
					<p className="text-muted-foreground">
						Manage your account settings and preferences
					</p>
				</div>
			</div>

			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Key className="h-5 w-5" />
							Security Settings
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="py-8 text-center">
							<h3 className="mb-2 text-lg font-semibold">Change Password</h3>
							<p className="text-muted-foreground mb-4">
								Update your account password for security
							</p>
							<ChangePasswordDialog>
								<Button variant="outline">Change Password</Button>
							</ChangePasswordDialog>
						</div>
					</CardContent>
				</Card>

				{/* 
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<SettingsIcon className="h-5 w-5" />
							Preferences
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="py-8 text-center">
							<h3 className="mb-2 text-lg font-semibold">App Preferences</h3>
							<p className="text-muted-foreground mb-4">
								Configure notifications and display settings
							</p>
							<Button variant="outline">Manage Preferences</Button>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<SettingsIcon className="h-5 w-5" />
							Privacy
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="py-8 text-center">
							<h3 className="mb-2 text-lg font-semibold">Privacy Settings</h3>
							<p className="text-muted-foreground mb-4">
								Control your data privacy and sharing preferences
							</p>
							<Button variant="outline">Privacy Settings</Button>
						</div>
					</CardContent>
				</Card> */}
			</div>
		</div>
	);
}
