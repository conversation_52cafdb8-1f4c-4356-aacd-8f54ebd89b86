'use client';

import { useEffect, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { Checkbox } from '@/components/ui/checkbox';
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectGroup,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import useSelectAddress, {
	BarangayType,
	CityType,
	ProvinceType,
	RegionType,
} from '@/core/hooks/utils/useSelectAddress';
import { cn } from '@/core/lib/utils';
import type { ApiAddressPayload } from '@/core/validators/address.validator';
import barangays from '@/public/address/barangay.json';
import cities from '@/public/address/city.json';
import provinces from '@/public/address/province.json';
import regions from '@/public/address/region.json';

import { TAddressFormData } from '../types/onboarding.type';

interface IAddressFormProps {
	form: UseFormReturn<TAddressFormData>;
	hasExistingData: {
		isValid: boolean;
		address: ApiAddressPayload | null;
	};
}

export default function AddressForm({
	form,
	hasExistingData,
}: IAddressFormProps) {
	const currentAddress = useSelectAddress();
	const permanentAddress = useSelectAddress();
	const [sameAsCurrentAddress, setSameAsCurrentAddress] = useState(false);
	const [isInitialized, setIsInitialized] = useState(false);

	// Helper function to initialize an address
	const initializeAddress = (
		addressData: {
			province: ProvinceType;
			city: CityType;
			barangay: BarangayType;
			street_name?: string;
			lot_block_phase_street?: string;
			unit_room_floor_building?: string;
			subdivision_village_zone?: string;
			building_apartment?: string;
		},
		addressHook: ReturnType<typeof useSelectAddress>,
		addressType: 'current' | 'permanent'
	) => {
		const region = (regions as RegionType[]).find(
			(r: RegionType) => r.region_code === addressData.province.region_code
		);

		// Ensure option lists are populated synchronously based on known codes
		const provincesForRegion = (provinces as ProvinceType[]).filter(
			(p: ProvinceType) => p.region_code === addressData.province.region_code
		);
		addressHook.setProvinceList(provincesForRegion);

		const citiesForProvince = (cities as CityType[]).filter(
			(c: CityType) => c.province_code === addressData.province.province_code
		);
		addressHook.setCityList(citiesForProvince);

		const barangaysForCity = (barangays as BarangayType[]).filter(
			(b: BarangayType) => b.city_code === addressData.city.city_code
		);
		addressHook.setBarangayList(barangaysForCity);

		if (region) {
			addressHook.setRegionSelected(region);
			form.setValue(`${addressType}Address.region`, JSON.stringify(region));
		}

		addressHook.setProvinceSelected(addressData.province);
		form.setValue(
			`${addressType}Address.province`,
			JSON.stringify(addressData.province)
		);

		addressHook.setCitySelected(addressData.city);
		form.setValue(
			`${addressType}Address.city`,
			JSON.stringify(addressData.city)
		);

		addressHook.setBarangaySelected(addressData.barangay);
		form.setValue(
			`${addressType}Address.barangay`,
			JSON.stringify(addressData.barangay)
		);

		if (addressData.street_name) {
			addressHook.setStreet(addressData.street_name);
			form.setValue(
				`${addressType}Address.streetName`,
				addressData.street_name
			);
		}

		if (addressData.lot_block_phase_street) {
			form.setValue(
				`${addressType}Address.lotBlockPhaseStreet`,
				addressData.lot_block_phase_street
			);
		}

		if (addressData.unit_room_floor_building) {
			form.setValue(
				`${addressType}Address.unitRoomFloorBuilding`,
				addressData.unit_room_floor_building
			);
		}

		if (addressData.subdivision_village_zone) {
			form.setValue(
				`${addressType}Address.subdivisionVillageZone`,
				addressData.subdivision_village_zone
			);
		}

		if (addressData.building_apartment) {
			form.setValue(
				`${addressType}Address.buildingApartment`,
				addressData.building_apartment
			);
		}
	};

	// Helper function to compare two addresses
	const areAddressesEqual = (
		address1: {
			province: ProvinceType;
			city: CityType;
			barangay: BarangayType;
			street_name?: string;
			lot_block_phase_street?: string;
			unit_room_floor_building?: string;
			subdivision_village_zone?: string;
			building_apartment?: string;
		},
		address2: {
			province: ProvinceType;
			city: CityType;
			barangay: BarangayType;
			street_name?: string;
			lot_block_phase_street?: string;
			unit_room_floor_building?: string;
			subdivision_village_zone?: string;
			building_apartment?: string;
		}
	) => {
		return (
			address1.province.province_code === address2.province.province_code &&
			address1.city.city_code === address2.city.city_code &&
			address1.barangay.brgy_code === address2.barangay.brgy_code &&
			(address1.street_name || '') === (address2.street_name || '') &&
			(address1.lot_block_phase_street || '') ===
				(address2.lot_block_phase_street || '') &&
			(address1.unit_room_floor_building || '') ===
				(address2.unit_room_floor_building || '') &&
			(address1.subdivision_village_zone || '') ===
				(address2.subdivision_village_zone || '') &&
			(address1.building_apartment || '') ===
				(address2.building_apartment || '')
		);
	};

	const handleSameAddressChange = (checked: boolean) => {
		setSameAsCurrentAddress(checked);

		if (checked) {
			// Region
			permanentAddress.setRegionList(currentAddress.regionList);
			permanentAddress.setRegionSelected(currentAddress.regionSelected);
			form.setValue(
				'permanentAddress.region',
				JSON.stringify(currentAddress.regionSelected)
			);

			// Province
			permanentAddress.setProvinceList(currentAddress.provinceList);
			permanentAddress.setProvinceSelected(currentAddress.provinceSelected);
			form.setValue(
				'permanentAddress.province',
				JSON.stringify(currentAddress.provinceSelected)
			);

			// City
			permanentAddress.setCityList(currentAddress.cityList);
			permanentAddress.setCitySelected(currentAddress.citySelected);
			form.setValue(
				'permanentAddress.city',
				JSON.stringify(currentAddress.citySelected)
			);

			// Barangay
			permanentAddress.setBarangayList(currentAddress.barangayList);
			permanentAddress.setBarangaySelected(currentAddress.barangaySelected);
			form.setValue(
				'permanentAddress.barangay',
				JSON.stringify(currentAddress.barangaySelected)
			);

			// Street
			permanentAddress.setStreet(currentAddress.street);
			form.setValue('permanentAddress.streetName', currentAddress.street);

			const _currentAddress = form.getValues('currentAddress');

			// lotBlockPhaseStreet
			form.setValue(
				'permanentAddress.lotBlockPhaseStreet',
				_currentAddress.lotBlockPhaseStreet
			);

			// unitRoomFloorBuilding
			form.setValue(
				'permanentAddress.unitRoomFloorBuilding',
				_currentAddress.unitRoomFloorBuilding
			);

			// subdivisionVillageZone
			form.setValue(
				'permanentAddress.subdivisionVillageZone',
				_currentAddress.subdivisionVillageZone
			);

			// buildingApartment
			form.setValue(
				'permanentAddress.buildingApartment',
				_currentAddress.buildingApartment
			);
		} else {
			permanentAddress.setRegionSelected(null);
			form.setValue('permanentAddress.region', '');

			permanentAddress.setProvinceSelected(null);
			form.setValue('permanentAddress.province', '');

			permanentAddress.setCitySelected(null);
			form.setValue('permanentAddress.city', '');

			permanentAddress.setBarangaySelected(null);
			form.setValue('permanentAddress.barangay', '');

			permanentAddress.setStreet('');
			form.setValue('permanentAddress.streetName', '');

			form.setValue('permanentAddress.lotBlockPhaseStreet', '');
			form.setValue('permanentAddress.unitRoomFloorBuilding', '');
			form.setValue('permanentAddress.subdivisionVillageZone', '');
			form.setValue('permanentAddress.buildingApartment', '');
		}
	};

	useEffect(() => {
		// If we have existing data and region lists are populated, initialize
		if (
			hasExistingData.isValid &&
			hasExistingData.address &&
			currentAddress.regionList.length > 0 &&
			permanentAddress.regionList.length > 0
		) {
			const {
				profileCurrentAddress: existingCurrent,
				profilePermanentAddress: existingPermanent,
			} = hasExistingData.address;

			// Initialize addresses using helper functions
			if (existingCurrent) {
				initializeAddress(existingCurrent, currentAddress, 'current');
			}

			if (existingPermanent) {
				initializeAddress(existingPermanent, permanentAddress, 'permanent');
			}

			// Check if addresses are the same and handle checkbox
			if (
				existingCurrent &&
				existingPermanent &&
				areAddressesEqual(existingCurrent, existingPermanent)
			) {
				setSameAsCurrentAddress(true);
				initializeAddress(existingCurrent, permanentAddress, 'permanent');
			}

			setIsInitialized(true);
		}
		// If no existing data, mark as initialized immediately
		else if (!hasExistingData.isValid || !hasExistingData.address) {
			setIsInitialized(true);
		}
	}, [
		hasExistingData,
		currentAddress.regionList.length,
		permanentAddress.regionList.length,
	]);

	// Don't render the form until initialization is complete
	if (!isInitialized) {
		return (
			<div className="flex items-center justify-center py-8">
				<div className="text-sm text-gray-500">Loading address data...</div>
			</div>
		);
	}

	return (
		<>
			{/* Current Address Section */}
			<div className="space-y-4">
				<h3 className="text-elena-primary text-lg font-semibold">
					Current Address
				</h3>

				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					<FormField
						control={form.control}
						name="currentAddress.region"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Region *</FormLabel>
								<FormControl>
									<Select
										onValueChange={(v) => {
											field.onChange(v);
											if (v !== '')
												currentAddress.setRegionSelected(JSON.parse(v));
										}}
										value={field.value}
									>
										<SelectTrigger
											className={cn(
												'focus:border-elena-primary focus:ring-elena-primary',
												form.formState.errors.currentAddress?.region &&
													'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
											)}
										>
											<SelectValue placeholder="Select region" />
										</SelectTrigger>
										<SelectContent>
											<SelectGroup>
												{currentAddress.regionList.map((region: RegionType) => (
													<SelectItem
														key={region.region_code}
														value={JSON.stringify(region)}
													>
														{region.region_name}
													</SelectItem>
												))}
											</SelectGroup>
										</SelectContent>
									</Select>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="currentAddress.province"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Province *</FormLabel>
								<FormControl>
									<Select
										onValueChange={(v) => {
											field.onChange(v);
											if (v !== '')
												currentAddress.setProvinceSelected(JSON.parse(v));
										}}
										value={field.value}
										disabled={currentAddress.provinceList.length === 0}
									>
										<SelectTrigger
											className={cn(
												'focus:border-elena-primary focus:ring-elena-primary',
												form.formState.errors.currentAddress?.province &&
													'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
											)}
										>
											<SelectValue placeholder="Select province" />
										</SelectTrigger>
										<SelectContent>
											<SelectGroup>
												{currentAddress.provinceList.map(
													(province: ProvinceType) => (
														<SelectItem
															key={province.province_code}
															value={JSON.stringify(province)}
														>
															{province.province_name}
														</SelectItem>
													)
												)}
											</SelectGroup>
										</SelectContent>
									</Select>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					<FormField
						control={form.control}
						name="currentAddress.city"
						render={({ field }) => (
							<FormItem>
								<FormLabel>City *</FormLabel>
								<FormControl>
									<Select
										onValueChange={(v) => {
											field.onChange(v);
											if (v !== '')
												currentAddress.setCitySelected(JSON.parse(v));
										}}
										value={field.value}
										disabled={currentAddress.cityList.length === 0}
									>
										<SelectTrigger
											className={cn(
												'focus:border-elena-primary focus:ring-elena-primary',
												form.formState.errors.currentAddress?.city &&
													'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
											)}
										>
											<SelectValue placeholder="Select city/municipality" />
										</SelectTrigger>
										<SelectContent>
											<SelectGroup>
												{currentAddress.cityList.map((city: CityType) => (
													<SelectItem
														key={city.city_code}
														value={JSON.stringify(city)}
													>
														{city.city_name}
													</SelectItem>
												))}
											</SelectGroup>
										</SelectContent>
									</Select>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="currentAddress.barangay"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Barangay *</FormLabel>
								<FormControl>
									<Select
										onValueChange={(v) => {
											field.onChange(v);
											if (v !== '')
												currentAddress.setBarangaySelected(JSON.parse(v));
										}}
										value={field.value}
										disabled={currentAddress.barangayList.length === 0}
									>
										<SelectTrigger
											className={cn(
												'focus:border-elena-primary focus:ring-elena-primary',
												form.formState.errors.currentAddress?.barangay &&
													'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
											)}
										>
											<SelectValue placeholder="Select Barangay" />
										</SelectTrigger>
										<SelectContent>
											<SelectGroup>
												{currentAddress.barangayList.map(
													(barangay: BarangayType) => (
														<SelectItem
															key={barangay.brgy_code}
															value={JSON.stringify(barangay)}
														>
															{barangay.brgy_name}
														</SelectItem>
													)
												)}
											</SelectGroup>
										</SelectContent>
									</Select>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<FormField
					control={form.control}
					name="currentAddress.streetName"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Street Name *</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter street name"
									value={field.value}
									onChange={(v) => {
										field.onChange(v);
										currentAddress.setStreet(v.target.value);
									}}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					<FormField
						control={form.control}
						name="currentAddress.lotBlockPhaseStreet"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Lot/Block/Phase/Street No.</FormLabel>
								<FormControl>
									<Input
										placeholder="Enter Lot/Block/Phase/Street No."
										{...field}
										className="focus:border-elena-primary focus:ring-elena-primary"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="currentAddress.unitRoomFloorBuilding"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Unit/Room/Floor/Building No.</FormLabel>
								<FormControl>
									<Input
										placeholder="Enter Unit/Room/Floor/Building No."
										{...field}
										className="focus:border-elena-primary focus:ring-elena-primary"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					<FormField
						control={form.control}
						name="currentAddress.subdivisionVillageZone"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Subdivision/Village/Zone</FormLabel>
								<FormControl>
									<Input
										placeholder="Enter subdivision, village, or zone"
										{...field}
										className="focus:border-elena-primary focus:ring-elena-primary"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="currentAddress.buildingApartment"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Building/Apartment</FormLabel>
								<FormControl>
									<Input
										placeholder="Enter building or apartment"
										{...field}
										className="focus:border-elena-primary focus:ring-elena-primary"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
			</div>

			{/* Permanent Address Section */}
			<div className="space-y-4">
				<h3 className="text-elena-primary text-lg font-semibold">
					Permanent Address
				</h3>

				{/* Same as Current Address Checkbox */}
				<div className="flex items-center space-x-2">
					<Checkbox
						id="sameAsCurrentAddress"
						checked={sameAsCurrentAddress}
						onCheckedChange={handleSameAddressChange}
						className="border-elena-primary data-[state=checked]:bg-elena-primary data-[state=checked]:border-elena-primary"
					/>
					<label
						htmlFor="sameAsCurrentAddress"
						className="cursor-pointer text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
					>
						Same as current address
					</label>
				</div>

				{!sameAsCurrentAddress && (
					<>
						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<FormField
								control={form.control}
								name="permanentAddress.region"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Region *</FormLabel>
										<FormControl>
											<Select
												onValueChange={(v) => {
													field.onChange(v);
													if (v !== '')
														permanentAddress.setRegionSelected(JSON.parse(v));
												}}
												value={field.value}
											>
												<SelectTrigger
													className={cn(
														'focus:border-elena-primary focus:ring-elena-primary',
														form.formState.errors.permanentAddress?.region &&
															'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
													)}
												>
													<SelectValue placeholder="Select region" />
												</SelectTrigger>
												<SelectContent>
													<SelectGroup>
														{permanentAddress.regionList.map(
															(region: RegionType) => (
																<SelectItem
																	key={region.region_code}
																	value={JSON.stringify(region)}
																>
																	{region.region_name}
																</SelectItem>
															)
														)}
													</SelectGroup>
												</SelectContent>
											</Select>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="permanentAddress.province"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Province *</FormLabel>
										<FormControl>
											<Select
												onValueChange={(v) => {
													field.onChange(v);
													if (v !== '')
														permanentAddress.setProvinceSelected(JSON.parse(v));
												}}
												value={field.value}
												disabled={permanentAddress.provinceList.length === 0}
											>
												<SelectTrigger
													className={cn(
														'focus:border-elena-primary focus:ring-elena-primary',
														form.formState.errors.permanentAddress?.province &&
															'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
													)}
												>
													<SelectValue placeholder="Select province" />
												</SelectTrigger>
												<SelectContent>
													<SelectGroup>
														{permanentAddress.provinceList.map(
															(province: ProvinceType) => (
																<SelectItem
																	key={province.province_code}
																	value={JSON.stringify(province)}
																>
																	{province.province_name}
																</SelectItem>
															)
														)}
													</SelectGroup>
												</SelectContent>
											</Select>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="permanentAddress.city"
								render={({ field }) => (
									<FormItem>
										<FormLabel>City *</FormLabel>
										<FormControl>
											<Select
												onValueChange={(v) => {
													field.onChange(v);
													if (v !== '')
														permanentAddress.setCitySelected(JSON.parse(v));
												}}
												value={field.value}
												disabled={permanentAddress.cityList.length === 0}
											>
												<SelectTrigger
													className={cn(
														'focus:border-elena-primary focus:ring-elena-primary',
														form.formState.errors.permanentAddress?.city &&
															'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
													)}
												>
													<SelectValue placeholder="Select city/municipality" />
												</SelectTrigger>
												<SelectContent>
													<SelectGroup>
														{permanentAddress.cityList.map((city: CityType) => (
															<SelectItem
																key={city.city_code}
																value={JSON.stringify(city)}
															>
																{city.city_name}
															</SelectItem>
														))}
													</SelectGroup>
												</SelectContent>
											</Select>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="permanentAddress.barangay"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Barangay *</FormLabel>
										<FormControl>
											<Select
												onValueChange={(v) => {
													field.onChange(v);
													if (v !== '')
														permanentAddress.setBarangaySelected(JSON.parse(v));
												}}
												value={field.value}
												disabled={permanentAddress.barangayList.length === 0}
											>
												<SelectTrigger
													className={cn(
														'focus:border-elena-primary focus:ring-elena-primary',
														form.formState.errors.permanentAddress?.barangay &&
															'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
													)}
												>
													<SelectValue placeholder="Select Barangay" />
												</SelectTrigger>
												<SelectContent>
													<SelectGroup>
														{permanentAddress.barangayList.map(
															(barangay: BarangayType) => (
																<SelectItem
																	key={barangay.brgy_code}
																	value={JSON.stringify(barangay)}
																>
																	{barangay.brgy_name}
																</SelectItem>
															)
														)}
													</SelectGroup>
												</SelectContent>
											</Select>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<FormField
							control={form.control}
							name="permanentAddress.streetName"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Street Name *</FormLabel>
									<FormControl>
										<Input
											placeholder="Enter street name"
											value={field.value}
											onChange={(v) => {
												field.onChange(v);
												permanentAddress.setStreet(v.target.value);
											}}
											className="focus:border-elena-primary focus:ring-elena-primary"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<FormField
								control={form.control}
								name="permanentAddress.lotBlockPhaseStreet"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Lot/Block/Phase/Street No.</FormLabel>
										<FormControl>
											<Input
												placeholder="Enter Lot/Block/Phase/Street No."
												{...field}
												className="focus:border-elena-primary focus:ring-elena-primary"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="permanentAddress.unitRoomFloorBuilding"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Unit/Room/Floor/Building No.</FormLabel>
										<FormControl>
											<Input
												placeholder="Enter Unit/Room/Floor/Building No."
												{...field}
												className="focus:border-elena-primary focus:ring-elena-primary"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<FormField
								control={form.control}
								name="permanentAddress.subdivisionVillageZone"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Subdivision/Village/Zone</FormLabel>
										<FormControl>
											<Input
												placeholder="Enter subdivision, village, or zone"
												{...field}
												className="focus:border-elena-primary focus:ring-elena-primary"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="permanentAddress.buildingApartment"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Building/Apartment</FormLabel>
										<FormControl>
											<Input
												placeholder="Enter building or apartment"
												{...field}
												className="focus:border-elena-primary focus:ring-elena-primary"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
					</>
				)}
			</div>
		</>
	);
}
