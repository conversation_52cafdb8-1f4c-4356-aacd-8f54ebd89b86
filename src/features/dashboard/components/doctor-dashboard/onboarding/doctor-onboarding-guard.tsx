'use client';

import { useEffect, useState } from 'react';

import { DoctorOnboardingWizard } from '@/features/dashboard/components/doctor-dashboard/onboarding/doctor-onboarding-wizard';
import { useDoctorProfile } from '@/features/dashboard/hooks/useDoctorDashboard';
import { useUserRole } from '@/features/dashboard/hooks/useRoleAwareDashboard';
import { IDoctorDetails } from '@/features/dashboard/types/doctor-details.type';

const OnboardingGuard = ({ children }: { children: React.ReactNode }) => {
	const {
		data: profileData,
		isSuccess: profileSuccess,
		refetch: refetchProfile,
	} = useDoctorProfile();

	const [showOnboarding, setShowOnboarding] = useState(false);

	useEffect(() => {
		if (profileSuccess && profileData) {
			// Check if doctor needs onboarding
			const needsOnboarding = profileData.is_completed === 0; // Profile not completed
			setShowOnboarding(needsOnboarding);
		}
	}, [profileSuccess]);

	const handleOnboardingComplete = () => {
		setShowOnboarding(false);
		refetchProfile();
	};

	if (showOnboarding) {
		return (
			<DoctorOnboardingWizard
				onComplete={handleOnboardingComplete}
				doctorProfile={profileData as IDoctorDetails}
			/>
		);
	}

	return <>{children}</>;
};

export function DoctorOnboardingGuard({
	children,
}: {
	children: React.ReactNode;
}) {
	const role = useUserRole();

	if (role === 'doctor') {
		return <OnboardingGuard>{children}</OnboardingGuard>;
	}

	return <>{children}</>;
}
