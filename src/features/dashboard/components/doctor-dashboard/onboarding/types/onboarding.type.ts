import { z } from 'zod';

import { addressSchema } from '@/core/api/registration/patient.type';

// Validation schemas for each step
export const profileSchema = z.object({
	specialty: z.string().min(1, 'Specialty is required'),
	subSpecialty: z.string().optional(),
	subSpecialization: z.string().optional(),
	prcNumber: z.string().min(1, 'PRC number is required'),
	prcExpiryDate: z.string().min(1, 'PRC expiry date is required'),
	signature: z.any().optional(),
});

export type ProfileFormData = z.infer<typeof profileSchema>;

// Address data type for step 2
export const addressFormSchema = z.object({
	// Address Information
	currentAddress: addressSchema,
	permanentAddress: addressSchema,
});

export type TAddressFormData = z.infer<typeof addressFormSchema>;

// Clinic data type for step 3
export type ClinicFormData = {
	name: string;
	assistantProfileIds?: number[];
	address: string;
	longitude?: string;
	latitude?: string;
	phone?: string;
	email?: string;
	website?: string;
	startTime: string;
	endTime: string;
	dateEstablished: string;
	schedule: {
		day: string;
		startTime: string;
		endTime: string;
		isActive: boolean;
	}[];
};

// Assistant data type for step 4
export type AssistantFormData = {
	firstName: string;
	middleName: string;
	lastName: string;
	suffix: string;
	email: string;
	username: string;
	password: string;
	password_confirmation: string;
};

export type AssistantsStepData = {
	assistants: AssistantFormData[];
	skipped: boolean;
};
