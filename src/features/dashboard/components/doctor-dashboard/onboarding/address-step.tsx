'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { ArrowLeft, ArrowRight, MapPin } from 'lucide-react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form } from '@/components/ui/form';
import { isValidApiAddress } from '@/core/validators/address.validator';
import { IDoctorDetails } from '@/features/dashboard/types/doctor-details.type';

import AddressForm from './forms/address.form';
import { addressFormSchema, TAddressFormData } from './types/onboarding.type';

interface IAddressStepProps {
	onNext?: () => void;
	onPrevious?: () => void;
	onData: (data: TAddressFormData) => void;
	doctorProfile?: IDoctorDetails;
	isSubmitting?: boolean;
}

export default function AddressStep({
	onPrevious,
	onData,
	doctorProfile,
	isSubmitting,
}: IAddressStepProps) {
	const form = useForm<TAddressFormData>({
		resolver: zodResolver(addressFormSchema),
		defaultValues: {
			// Address
			currentAddress: {
				country: 'Philippines',
				region: '',
				province: '',
				city: '',
				barangay: '',
				streetName: '',
				buildingApartment: '',
				subdivisionVillageZone: '',
				unitRoomFloorBuilding: '',
				lotBlockPhaseStreet: '',
			},
			permanentAddress: {
				country: 'Philippines',
				region: '',
				province: '',
				city: '',
				barangay: '',
				streetName: '',
				buildingApartment: '',
				subdivisionVillageZone: '',
				unitRoomFloorBuilding: '',
				lotBlockPhaseStreet: '',
			},
		},
	});

	// Check if this step has existing data
	const hasExistingData = doctorProfile
		? isValidApiAddress({
				profileCurrentAddress: doctorProfile?.profileCurrentAddress,
				profilePermanentAddress: doctorProfile?.profilePermanentAddress,
			})
		: { isValid: false, address: null };

	const onSubmit = (data: TAddressFormData) => {
		onData(data);
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<MapPin className="h-5 w-5 text-[oklch(0.7448_0.1256_202.74)]" />
					Address Information
					{hasExistingData.isValid && (
						<span className="ml-2 rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
							Data Found
						</span>
					)}
				</CardTitle>
				{hasExistingData.isValid && (
					<p className="text-sm text-gray-600">
						We found existing address information. You can review and update it
						below.
					</p>
				)}
			</CardHeader>
			<CardContent>
				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
						{/* Address Section */}
						<AddressForm form={form} hasExistingData={hasExistingData} />

						{/* Navigation Buttons */}
						<div className="flex justify-between pt-6">
							<Button type="button" variant="outline" onClick={onPrevious}>
								<ArrowLeft className="mr-2 h-4 w-4" />
								Previous
							</Button>
							<Button type="submit" disabled={isSubmitting}>
								{isSubmitting ? 'Saving...' : 'Continue'}
								<ArrowRight className="ml-2 h-4 w-4" />
							</Button>
						</div>
					</form>
				</Form>
			</CardContent>
		</Card>
	);
}
