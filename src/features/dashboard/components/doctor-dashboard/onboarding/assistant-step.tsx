'use client';

import { <PERSON><PERSON>ef<PERSON>, Check, Plus, Users } from 'lucide-react';
import { useState } from 'react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';

import { AssistantFormData, AssistantsStepData } from './types/onboarding.type';

interface IAddAssistantsStepProps {
	onNext?: () => void;
	onPrevious?: () => void;
	onData: (data: AssistantsStepData) => void;
	isSubmitting?: boolean;
}

// AddAssistantsStep component
export default function AddAssistantsStep({
	onPrevious,
	onData,
	isSubmitting,
}: IAddAssistantsStepProps) {
	const [assistants, setAssistants] = useState<AssistantFormData[]>([]);
	const [showForm, setShowForm] = useState(false);
	const [currentAssistant, setCurrentAssistant] = useState<AssistantFormData>({
		firstName: '',
		middleName: '',
		lastName: '',
		suffix: '',
		email: '',
		username: '',
		password: '',
		password_confirmation: '',
	});
	const [errors, setErrors] = useState<Partial<AssistantFormData>>({});

	const handleInputChange = (field: keyof AssistantFormData, value: string) => {
		setCurrentAssistant((prev) => ({ ...prev, [field]: value }));
		// Clear error when user starts typing
		if (errors[field]) {
			setErrors((prev) => ({ ...prev, [field]: undefined }));
		}
	};

	const validateAssistant = (): boolean => {
		const newErrors: Partial<AssistantFormData> = {};

		if (!currentAssistant.firstName.trim()) {
			newErrors.firstName = 'First name is required';
		}

		if (!currentAssistant.lastName.trim()) {
			newErrors.lastName = 'Last name is required';
		}

		if (!currentAssistant.username.trim()) {
			newErrors.username = 'Username is required';
		} else if (currentAssistant.username.length < 3) {
			newErrors.username = 'Username must be at least 3 characters';
		}

		if (!currentAssistant.password.trim()) {
			newErrors.password = 'Password is required';
		} else if (currentAssistant.password.length < 8) {
			newErrors.password = 'Password must be at least 8 characters';
		}

		if (!currentAssistant.password_confirmation.trim()) {
			newErrors.password_confirmation = 'Password confirmation is required';
		} else if (
			currentAssistant.password !== currentAssistant.password_confirmation
		) {
			newErrors.password_confirmation = 'Passwords do not match';
		}

		if (
			currentAssistant.email &&
			!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(currentAssistant.email)
		) {
			newErrors.email = 'Please enter a valid email address';
		}

		setErrors(newErrors);
		return Object.keys(newErrors).length === 0;
	};

	const handleAddAssistant = () => {
		if (validateAssistant()) {
			setAssistants((prev) => [...prev, currentAssistant]);
			setCurrentAssistant({
				firstName: '',
				middleName: '',
				lastName: '',
				suffix: '',
				email: '',
				username: '',
				password: '',
				password_confirmation: '',
			});
			setErrors({});
			setShowForm(false);
		}
	};

	const handleRemoveAssistant = (index: number) => {
		setAssistants((prev) => prev.filter((_, i) => i !== index));
	};

	const handleContinue = async () => {
		await onData({ assistants, skipped: false });
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Users className="h-5 w-5 text-[oklch(0.7448_0.1256_202.74)]" />
					Add Assistants
				</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="space-y-6">
					{/* Description */}
					<div className="text-center">
						<p className="text-gray-600">
							Create accounts for your clinic assistants to help manage
							appointments and patients.
						</p>
						<p className="mt-2 text-sm text-gray-500">
							You can skip this step and add assistants later from your
							dashboard.
						</p>
					</div>

					{/* Added Assistants List */}
					{assistants.length > 0 && (
						<div className="space-y-3">
							<h3 className="text-lg font-medium">
								Added Assistants ({assistants.length})
							</h3>
							<div className="space-y-2">
								{assistants.map((assistant, index) => (
									<div
										key={index}
										className="flex items-center justify-between rounded-lg border bg-gray-50 p-3"
									>
										<div>
											<p className="font-medium">
												{assistant.firstName} {assistant.middleName}{' '}
												{assistant.lastName} {assistant.suffix}
											</p>
											<p className="text-sm text-gray-600">
												@{assistant.username}
											</p>
											{assistant.email && (
												<p className="text-sm text-gray-600">
													{assistant.email}
												</p>
											)}
										</div>
										<Button
											type="button"
											variant="outline"
											size="sm"
											onClick={() => handleRemoveAssistant(index)}
											className="text-red-600 hover:text-red-700"
										>
											Remove
										</Button>
									</div>
								))}
							</div>
						</div>
					)}

					{/* Add Assistant Form */}
					{showForm ? (
						<div className="space-y-4 rounded-lg border bg-gray-50 p-4">
							<h3 className="text-lg font-medium">Add New Assistant</h3>

							<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
								{/* First Name */}
								<div>
									<label className="text-sm font-medium">
										First Name <span className="text-red-500">*</span>
									</label>
									<Input
										placeholder="Enter first name"
										value={currentAssistant.firstName}
										onChange={(e) =>
											handleInputChange('firstName', e.target.value)
										}
										className={errors.firstName ? 'border-red-500' : ''}
									/>
									{errors.firstName && (
										<span className="text-sm text-red-500">
											{errors.firstName}
										</span>
									)}
								</div>

								{/* Middle Name */}
								<div>
									<label className="text-sm font-medium">Middle Name</label>
									<Input
										placeholder="Enter middle name"
										value={currentAssistant.middleName}
										onChange={(e) =>
											handleInputChange('middleName', e.target.value)
										}
									/>
								</div>

								{/* Last Name */}
								<div>
									<label className="text-sm font-medium">
										Last Name <span className="text-red-500">*</span>
									</label>
									<Input
										placeholder="Enter last name"
										value={currentAssistant.lastName}
										onChange={(e) =>
											handleInputChange('lastName', e.target.value)
										}
										className={errors.lastName ? 'border-red-500' : ''}
									/>
									{errors.lastName && (
										<span className="text-sm text-red-500">
											{errors.lastName}
										</span>
									)}
								</div>

								{/* Suffix */}
								<div>
									<label className="text-sm font-medium">Suffix</label>
									<Input
										placeholder="Jr., Sr., III, etc."
										value={currentAssistant.suffix}
										onChange={(e) =>
											handleInputChange('suffix', e.target.value)
										}
									/>
								</div>

								{/* Email */}
								<div className="md:col-span-2">
									<label className="text-sm font-medium">Email</label>
									<Input
										placeholder="Enter email address"
										value={currentAssistant.email}
										onChange={(e) => handleInputChange('email', e.target.value)}
										className={errors.email ? 'border-red-500' : ''}
									/>
									{errors.email && (
										<span className="text-sm text-red-500">{errors.email}</span>
									)}
								</div>

								{/* Username */}
								<div>
									<label className="text-sm font-medium">
										Username <span className="text-red-500">*</span>
									</label>
									<Input
										placeholder="Enter username"
										value={currentAssistant.username}
										onChange={(e) =>
											handleInputChange('username', e.target.value)
										}
										className={errors.username ? 'border-red-500' : ''}
									/>
									{errors.username && (
										<span className="text-sm text-red-500">
											{errors.username}
										</span>
									)}
								</div>

								{/* Password */}
								<div>
									<label className="text-sm font-medium">
										Password <span className="text-red-500">*</span>
									</label>
									<Input
										type="password"
										placeholder="Enter password"
										value={currentAssistant.password}
										onChange={(e) =>
											handleInputChange('password', e.target.value)
										}
										className={errors.password ? 'border-red-500' : ''}
									/>
									{errors.password && (
										<span className="text-sm text-red-500">
											{errors.password}
										</span>
									)}
								</div>

								{/* Confirm Password */}
								<div className="md:col-span-2">
									<label className="text-sm font-medium">
										Confirm Password <span className="text-red-500">*</span>
									</label>
									<Input
										type="password"
										placeholder="Confirm password"
										value={currentAssistant.password_confirmation}
										onChange={(e) =>
											handleInputChange('password_confirmation', e.target.value)
										}
										className={
											errors.password_confirmation ? 'border-red-500' : ''
										}
									/>
									{errors.password_confirmation && (
										<span className="text-sm text-red-500">
											{errors.password_confirmation}
										</span>
									)}
								</div>
							</div>

							{/* Form Actions */}
							<div className="flex justify-end gap-2">
								<Button
									type="button"
									variant="outline"
									onClick={() => {
										setShowForm(false);
										setCurrentAssistant({
											firstName: '',
											middleName: '',
											lastName: '',
											suffix: '',
											email: '',
											username: '',
											password: '',
											password_confirmation: '',
										});
										setErrors({});
									}}
								>
									Cancel
								</Button>
								<Button type="button" onClick={handleAddAssistant}>
									Add Assistant
								</Button>
							</div>
						</div>
					) : (
						<div className="text-center">
							<Button
								type="button"
								onClick={() => setShowForm(true)}
								className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
							>
								<Plus className="mr-2 h-4 w-4" />
								Add Assistant
							</Button>
						</div>
					)}

					{/* Navigation Buttons */}
					<div className="flex justify-between pt-6">
						<Button type="button" variant="outline" onClick={onPrevious}>
							<ArrowLeft className="mr-2 h-4 w-4" />
							Previous
						</Button>
						<div className="flex gap-2">
							<Button
								type="button"
								onClick={handleContinue}
								disabled={isSubmitting}
								className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
							>
								{isSubmitting ? 'Setting up...' : 'Complete Setup'}
								<Check className="ml-2 h-4 w-4" />
							</Button>
						</div>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
