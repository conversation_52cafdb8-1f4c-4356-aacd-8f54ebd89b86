'use client';

import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { useChangePassword } from '@/core/hooks/useAuth';
import { useDoctorProfile } from '@/features/dashboard/hooks/useDoctorDashboard';
import { useUserRole } from '@/features/dashboard/hooks/useRoleAwareDashboard';

interface IDoctorPasswordChangeGuardProps {
	children: React.ReactNode;
}

const PasswordChangeGuard = ({ children }: IDoctorPasswordChangeGuardProps) => {
	const {
		data: profileData,
		isLoading,
		isSuccess: profileSuccess,
		refetch: refetchProfile,
	} = useDoctorProfile();
	const {
		changePassword,
		isLoading: isChangingPassword,
		isSuccess,
	} = useChangePassword();

	const [showPasswordChange, setShowPasswordChange] = useState(false);
	const [passwordData, setPasswordData] = useState({
		currentPassword: '',
		newPassword: '',
		confirmPassword: '',
	});
	const [errors, setErrors] = useState<string[]>([]);
	const [message, setMessage] = useState('');

	useEffect(() => {
		if (profileSuccess && profileData) {
			// Check if doctor needs to change password
			const needsPasswordChange = profileData.is_new_password === 1;
			setShowPasswordChange(needsPasswordChange);
		}
	}, [profileSuccess, profileData]);

	useEffect(() => {
		if (isSuccess) {
			toast.success('Password Changed Successfully', {
				description:
					'Your password has been updated. You can now proceed with onboarding.',
			});
			setShowPasswordChange(false);
			refetchProfile();
		}
	}, [isSuccess]);

	const handleInputChange = (field: string, value: string) => {
		setPasswordData((prev) => ({ ...prev, [field]: value }));
		// Clear errors when user starts typing
		if (errors.includes(field)) {
			setErrors((prev) => prev.filter((error) => error !== field));
		}
		if (message) {
			setMessage('');
		}
	};

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		setErrors([]);
		setMessage('');

		// Validation
		const emptyFields = Object.keys(passwordData).filter(
			(key) => passwordData[key as keyof typeof passwordData] === ''
		);

		if (emptyFields.length > 0) {
			setMessage('Please fill in all fields');
			setErrors(emptyFields);
			return;
		}

		if (passwordData.newPassword !== passwordData.confirmPassword) {
			setMessage('New passwords do not match');
			setErrors(['newPassword', 'confirmPassword']);
			return;
		}

		if (passwordData.newPassword.length < 8) {
			setMessage('New password must be at least 8 characters long');
			setErrors(['newPassword']);
			return;
		}

		// Submit password change
		changePassword(passwordData);
	};

	const isFieldInvalid = (fieldName: string) => errors.includes(fieldName);

	if (isLoading) {
		return (
			<div className="min-h-screen bg-gray-50 py-8">
				<div className="mx-auto max-w-4xl px-4">
					<div className="mb-8 text-center">
						<Skeleton className="mx-auto mb-4 h-8 w-64" />
						<Skeleton className="mx-auto h-4 w-96" />
					</div>
					<div className="space-y-6">
						<Skeleton className="h-64 w-full" />
						<Skeleton className="h-48 w-full" />
					</div>
				</div>
			</div>
		);
	}

	if (showPasswordChange) {
		return (
			<div className="grid min-h-screen place-items-center bg-gray-50 py-8">
				<div className="mx-auto max-w-md px-4">
					<div className="rounded-lg bg-white p-8 shadow-md">
						<div className="mb-6 text-center">
							<h1 className="text-2xl font-bold text-gray-900">
								Change Your Password
							</h1>
							<p className="mt-2 text-sm text-gray-600">
								For security reasons, you need to change your password before
								proceeding.
							</p>
						</div>

						<form onSubmit={handleSubmit} className="space-y-4">
							{message && (
								<div className="rounded-md bg-red-50 p-3">
									<p className="text-sm text-red-600">{message}</p>
								</div>
							)}

							<div>
								<Label htmlFor="currentPassword">Current Password</Label>
								<Input
									id="currentPassword"
									type="password"
									value={passwordData.currentPassword}
									onChange={(e) =>
										handleInputChange('currentPassword', e.target.value)
									}
									className={
										isFieldInvalid('currentPassword') ? 'border-red-500' : ''
									}
									placeholder="Enter your current password"
								/>
							</div>

							<div>
								<Label htmlFor="newPassword">New Password</Label>
								<Input
									id="newPassword"
									type="password"
									value={passwordData.newPassword}
									onChange={(e) =>
										handleInputChange('newPassword', e.target.value)
									}
									className={
										isFieldInvalid('newPassword') ? 'border-red-500' : ''
									}
									placeholder="Enter your new password"
								/>
							</div>

							<div>
								<Label htmlFor="confirmPassword">Confirm New Password</Label>
								<Input
									id="confirmPassword"
									type="password"
									value={passwordData.confirmPassword}
									onChange={(e) =>
										handleInputChange('confirmPassword', e.target.value)
									}
									className={
										isFieldInvalid('confirmPassword') ? 'border-red-500' : ''
									}
									placeholder="Confirm your new password"
								/>
							</div>

							<div className="grid grid-cols-2 gap-3">
								<Button
									type="button"
									variant="outline"
									onClick={() => setShowPasswordChange(false)}
								>
									Skip
								</Button>
								<Button
									type="submit"
									className="w-full"
									disabled={isChangingPassword}
								>
									{isChangingPassword
										? 'Changing Password...'
										: 'Change Password'}
								</Button>
							</div>
						</form>
					</div>
				</div>
			</div>
		);
	}

	return <>{children}</>;
};

export function DoctorPasswordChangeGuard({
	children,
}: IDoctorPasswordChangeGuardProps) {
	const role = useUserRole();

	if (role === 'doctor') {
		return <PasswordChangeGuard>{children}</PasswordChangeGuard>;
	}

	return <>{children}</>;
}
