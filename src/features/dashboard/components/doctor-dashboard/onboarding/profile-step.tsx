'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Arrow<PERSON><PERSON>, Stethoscope, Upload } from 'lucide-react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { IDoctorDetails } from '@/features/dashboard/types/doctor-details.type';

import { ProfileFormData, profileSchema } from './types/onboarding.type';

interface IProfileStepProps {
	onNext?: () => void;
	onData: (data: ProfileFormData) => void;
	doctorProfile?: IDoctorDetails;
	isSubmitting?: boolean;
}

export default function ProfileStep({
	onData,
	doctorProfile,
	isSubmitting,
}: IProfileStepProps) {
	const form = useForm<ProfileFormData>({
		resolver: zodResolver(profileSchema),
		defaultValues: {
			specialty: doctorProfile?.doctor?.specialty || '',
			subSpecialty: doctorProfile?.doctor?.sub_specialty || '',
			subSpecialization: doctorProfile?.doctor?.sub_specialization || '',
			prcNumber: doctorProfile?.doctor?.prc_number || '',
			prcExpiryDate: doctorProfile?.doctor?.prc_expiry_date || '',
		},
	});

	const onSubmit = async (data: ProfileFormData) => {
		await onData(data);
	};

	// Check if this step has existing data
	const hasExistingData = !!(
		doctorProfile?.doctor?.specialty &&
		doctorProfile?.doctor?.prc_number &&
		doctorProfile?.doctor?.e_signature
	);

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Stethoscope className="h-5 w-5 text-[oklch(0.7448_0.1256_202.74)]" />
					Complete Your Professional Profile
					{hasExistingData && (
						<span className="ml-2 rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
							Data Found
						</span>
					)}
				</CardTitle>
				{hasExistingData && (
					<p className="text-sm text-gray-600">
						We found existing professional information. You can review and
						update it below.
					</p>
				)}
			</CardHeader>
			<CardContent>
				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
						<FormField
							control={form.control}
							name="specialty"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Medical Specialty</FormLabel>
									<FormControl>
										<Input
											placeholder="e.g., Internal Medicine, Pediatrics, etc."
											{...field}
											className="focus:border-elena-primary focus:ring-elena-primary"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<FormField
								control={form.control}
								name="subSpecialty"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Sub Specialty</FormLabel>
										<FormControl>
											<Input placeholder="Sub Specialty" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="subSpecialization"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Sub Specialization</FormLabel>
										<FormControl>
											<Input placeholder="Sub Specialization" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<FormField
								control={form.control}
								name="prcNumber"
								render={({ field }) => (
									<FormItem>
										<FormLabel>PRC License Number</FormLabel>
										<FormControl>
											<Input
												placeholder="Enter your PRC license number"
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="prcExpiryDate"
								render={({ field }) => (
									<FormItem>
										<FormLabel>PRC License Expiry Date</FormLabel>
										<FormControl>
											<Input type="date" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<FormField
							control={form.control}
							name="signature"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Digital Signature (Optional)</FormLabel>
									<FormControl>
										<div className="rounded-lg border-2 border-dashed border-gray-300 p-6 text-center">
											<Upload className="mx-auto mb-4 h-12 w-12 text-gray-400" />
											<p className="text-sm text-gray-600">
												Upload your digital signature
											</p>
											<p className="mt-1 text-xs text-gray-400">
												PNG, JPG up to 20MB
											</p>
											<Input
												type="file"
												accept="image/*"
												className="mt-2 inline-block w-max"
												onChange={(e) => field.onChange(e.target.files?.[0])}
											/>
										</div>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						{doctorProfile?.doctor?.e_signature && (
							<div className="overflow-hidden rounded-xl border">
								<img
									src={doctorProfile?.doctor?.e_signature}
									alt="e-signature"
								/>
							</div>
						)}

						<div className="flex justify-end">
							<Button
								type="submit"
								disabled={isSubmitting}
								className="flex items-center gap-2"
							>
								{isSubmitting ? 'Saving...' : 'Continue'}
								<ArrowRight className="h-4 w-4" />
							</Button>
						</div>
					</form>
				</Form>
			</CardContent>
		</Card>
	);
}
