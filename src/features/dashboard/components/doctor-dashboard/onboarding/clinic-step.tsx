'use client';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON>, Building } from 'lucide-react';
import { useState } from 'react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';

import { ClinicFormData } from './types/onboarding.type';

interface ICreateClinicStepProps {
	onNext?: () => void;
	onPrevious?: () => void;
	onData: (data: ClinicFormData) => void;
	isSubmitting?: boolean;
}

// CreateClinicStep component
export default function CreateClinicStep({
	onPrevious,
	onData,
	isSubmitting,
}: ICreateClinicStepProps) {
	const [formData, setFormData] = useState<ClinicFormData>({
		name: '',
		address: '',
		phone: '',
		email: '',
		website: '',
		longitude: '',
		latitude: '',
		startTime: '09:00',
		endTime: '17:00',
		dateEstablished: '',
		schedule: [
			{
				day: 'Monday',
				startTime: '09:00',
				endTime: '17:00',
				isActive: true,
			},
			{
				day: 'Tuesday',
				startTime: '09:00',
				endTime: '17:00',
				isActive: true,
			},
			{
				day: 'Wednesday',
				startTime: '09:00',
				endTime: '17:00',
				isActive: true,
			},
			{
				day: 'Thursday',
				startTime: '09:00',
				endTime: '17:00',
				isActive: true,
			},
			{
				day: 'Friday',
				startTime: '09:00',
				endTime: '17:00',
				isActive: true,
			},
			{
				day: 'Saturday',
				startTime: '09:00',
				endTime: '17:00',
				isActive: false,
			},
			{
				day: 'Sunday',
				startTime: '09:00',
				endTime: '17:00',
				isActive: false,
			},
		],
	});

	const [errors, setErrors] = useState<Partial<ClinicFormData>>({});

	const handleInputChange = (field: keyof ClinicFormData, value: string) => {
		setFormData((prev) => ({ ...prev, [field]: value }));
		// Clear error when user starts typing
		if (errors[field]) {
			setErrors((prev) => ({ ...prev, [field]: undefined }));
		}
	};

	const handleScheduleChange = (
		dayIndex: number,
		field: 'startTime' | 'endTime' | 'isActive',
		value: string | boolean
	) => {
		setFormData((prev) => ({
			...prev,
			schedule: prev.schedule.map((day, index) =>
				index === dayIndex ? { ...day, [field]: value } : day
			),
		}));
	};

	const validateForm = (): boolean => {
		const newErrors: Partial<ClinicFormData> = {};

		if (!formData.name.trim()) {
			newErrors.name = 'Clinic name is required';
		}

		if (!formData.address.trim()) {
			newErrors.address = 'Clinic address is required';
		}

		if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
			newErrors.email = 'Please enter a valid email address';
		}

		setErrors(newErrors);
		return Object.keys(newErrors).length === 0;
	};

	const handleSubmit = async () => {
		if (validateForm()) {
			await onData(formData);
		}
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Building className="h-5 w-5 text-[oklch(0.7448_0.1256_202.74)]" />
					Create Clinic
				</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="space-y-6">
					{/* Basic Information */}
					<div className="space-y-4">
						<h3 className="text-lg font-medium">Basic Information</h3>

						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							{/* Clinic Name */}
							<div className="md:col-span-2">
								<label className="text-sm font-medium">
									Clinic Name <span className="text-red-500">*</span>
								</label>
								<Input
									placeholder="Enter clinic name"
									value={formData.name}
									onChange={(e) => handleInputChange('name', e.target.value)}
									className={errors.name ? 'border-red-500' : ''}
								/>
								{errors.name && (
									<span className="text-sm text-red-500">{errors.name}</span>
								)}
							</div>

							{/* Address */}
							<div className="md:col-span-2">
								<label className="text-sm font-medium">
									Address <span className="text-red-500">*</span>
								</label>
								<Input
									placeholder="Enter clinic address"
									value={formData.address}
									onChange={(e) => handleInputChange('address', e.target.value)}
									className={errors.address ? 'border-red-500' : ''}
								/>
								{errors.address && (
									<span className="text-sm text-red-500">{errors.address}</span>
								)}
							</div>

							{/* Phone */}
							<div>
								<label className="text-sm font-medium">Phone</label>
								<Input
									placeholder="Enter phone number"
									value={formData.phone}
									onChange={(e) => handleInputChange('phone', e.target.value)}
								/>
							</div>

							{/* Email */}
							<div>
								<label className="text-sm font-medium">Email</label>
								<Input
									placeholder="Enter email address"
									value={formData.email}
									onChange={(e) => handleInputChange('email', e.target.value)}
									className={errors.email ? 'border-red-500' : ''}
								/>
								{errors.email && (
									<span className="text-sm text-red-500">{errors.email}</span>
								)}
							</div>

							{/* Website */}
							<div className="md:col-span-2">
								<label className="text-sm font-medium">Website</label>
								<Input
									placeholder="Enter website URL"
									value={formData.website}
									onChange={(e) => handleInputChange('website', e.target.value)}
								/>
							</div>

							{/* Date Established */}
							<div>
								<label className="text-sm font-medium">Date Established</label>
								<Input
									type="date"
									value={formData.dateEstablished}
									onChange={(e) =>
										handleInputChange('dateEstablished', e.target.value)
									}
								/>
							</div>

							{/* General Operating Hours */}
							<div>
								<label className="text-sm font-medium">General Hours</label>
								<div className="flex items-center gap-2">
									<Input
										type="time"
										value={formData.startTime}
										onChange={(e) =>
											handleInputChange('startTime', e.target.value)
										}
										className="w-32"
									/>
									<span className="text-sm text-gray-500">to</span>
									<Input
										type="time"
										value={formData.endTime}
										onChange={(e) =>
											handleInputChange('endTime', e.target.value)
										}
										className="w-32"
									/>
								</div>
							</div>

							{/* Location Coordinates */}
							<div>
								<label className="text-sm font-medium">Longitude</label>
								<Input
									placeholder="Enter longitude"
									value={formData.longitude}
									onChange={(e) =>
										handleInputChange('longitude', e.target.value)
									}
								/>
							</div>

							<div>
								<label className="text-sm font-medium">Latitude</label>
								<Input
									placeholder="Enter latitude"
									value={formData.latitude}
									onChange={(e) =>
										handleInputChange('latitude', e.target.value)
									}
								/>
							</div>
						</div>
					</div>

					{/* Schedule */}
					<div className="space-y-4">
						<h3 className="text-lg font-medium">Operating Hours</h3>
						<div className="space-y-3">
							{formData.schedule.map((day, index) => (
								<div
									key={day.day}
									className="flex items-center gap-4 rounded-lg border p-3"
								>
									<div className="flex items-center space-x-2">
										<input
											type="checkbox"
											id={`day-${index}`}
											checked={day.isActive}
											onChange={(e) =>
												handleScheduleChange(
													index,
													'isActive',
													e.target.checked
												)
											}
											className="h-4 w-4 rounded border-gray-300"
										/>
										<label
											htmlFor={`day-${index}`}
											className="w-20 text-sm font-medium"
										>
											{day.day}
										</label>
									</div>

									{day.isActive && (
										<div className="flex items-center gap-2">
											<Input
												type="time"
												value={day.startTime}
												onChange={(e) =>
													handleScheduleChange(
														index,
														'startTime',
														e.target.value
													)
												}
												className="w-32"
											/>
											<span className="text-sm text-gray-500">to</span>
											<Input
												type="time"
												value={day.endTime}
												onChange={(e) =>
													handleScheduleChange(index, 'endTime', e.target.value)
												}
												className="w-32"
											/>
										</div>
									)}

									{!day.isActive && (
										<span className="text-sm text-gray-500">Closed</span>
									)}
								</div>
							))}
						</div>
					</div>

					{/* Navigation Buttons */}
					<div className="flex justify-between pt-6">
						<Button type="button" variant="outline" onClick={onPrevious}>
							<ArrowLeft className="mr-2 h-4 w-4" />
							Previous
						</Button>
						<Button
							type="button"
							onClick={handleSubmit}
							disabled={isSubmitting}
						>
							{isSubmitting ? 'Creating...' : 'Continue'}
							<ArrowRight className="ml-2 h-4 w-4" />
						</Button>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
