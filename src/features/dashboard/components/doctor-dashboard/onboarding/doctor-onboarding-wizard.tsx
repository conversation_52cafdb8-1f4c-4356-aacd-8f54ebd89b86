'use client';

import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import {
	useCreateAssistant,
	useCreateClinic,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import {
	useCompleteOnboarding,
	useUpdateDoctorAddress,
	useUpdateDoctorBasicInfo,
	useUpdateDoctorESignature,
	useUpdateDoctorSpecialization,
} from '@/features/dashboard/hooks/useDoctorDashboard';

import { IDoctorDetails } from '../../../types/doctor-details.type';
import AddressStep from './address-step';
import AddAssistantsStep from './assistant-step';
import CreateClinicStep from './clinic-step';
import ProfileStep from './profile-step';
import StepIndicator, { ONBOARDING_STEPS } from './step-indicator';
import {
	AssistantsStepData,
	ClinicFormData,
	ProfileFormData,
	TAddressFormData,
} from './types/onboarding.type';

export function DoctorOnboardingWizard({
	onComplete,
	doctorProfile,
}: {
	onComplete: () => void;
	doctorProfile?: IDoctorDetails;
}) {
	const [currentStep, setCurrentStep] = useState(1);
	const [steps, setSteps] = useState(ONBOARDING_STEPS);
	const [isSubmitting, setIsSubmitting] = useState(false);

	// Helper function to check if a step is complete based on existing data
	const isStepComplete = (stepId: number): boolean => {
		if (!doctorProfile) return false;

		switch (stepId) {
			case 1: // Profile step
				return !!(
					doctorProfile.doctor?.specialty &&
					doctorProfile.doctor?.prc_number &&
					doctorProfile?.doctor?.e_signature
				);
			case 2: // Address step
				return !!(
					doctorProfile.profileCurrentAddress?.street_name &&
					doctorProfile.profileCurrentAddress?.province &&
					doctorProfile.profileCurrentAddress?.city &&
					doctorProfile.profileCurrentAddress?.barangay &&
					doctorProfile.profilePermanentAddress?.street_name &&
					doctorProfile.profilePermanentAddress?.province &&
					doctorProfile.profilePermanentAddress?.city &&
					doctorProfile.profilePermanentAddress?.barangay
				);
			case 3: // Clinic step - we'll assume incomplete for now since we don't have clinic data in the profile
				return doctorProfile.clinics.length > 0;
			case 4: // Assistants step - we'll assume incomplete for now since we don't have assistant data in the profile
				return false;
			default:
				return false;
		}
	};

	// Initialize steps completion status and find first incomplete step
	useEffect(() => {
		if (doctorProfile) {
			const updatedSteps = ONBOARDING_STEPS.map((step) => ({
				...step,
				completed: isStepComplete(step.id),
			}));

			setSteps(updatedSteps);

			// Find the first incomplete step
			const firstIncompleteStep = updatedSteps.find((step) => !step.completed);
			if (firstIncompleteStep) {
				setCurrentStep(firstIncompleteStep.id);
			} else {
				// All steps are complete, but we're still in onboarding
				// This might happen if the backend hasn't marked the profile as complete yet
				setCurrentStep(1);
			}
		}
	}, [doctorProfile]);

	const [clinicId, setClinicId] = useState<number | null>(null);

	// API hooks
	const updateSpecializationMutation = useUpdateDoctorSpecialization();
	const updateBasicInfo = useUpdateDoctorBasicInfo();
	const updateAddressMutation = useUpdateDoctorAddress();
	const updateESign = useUpdateDoctorESignature();

	const createClinicMutation = useCreateClinic();
	const createAssistantMutation = useCreateAssistant();
	const completeOnboardingMutation = useCompleteOnboarding();

	const handleNext = () => {
		setCurrentStep((prev) => Math.min(prev + 1, steps.length));
	};

	const handlePrevious = () => {
		setCurrentStep((prev) => Math.max(prev - 1, 1));
	};

	const handleStepComplete = (stepId: number) => {
		setSteps((prev) =>
			prev.map((step) =>
				step.id === stepId ? { ...step, completed: true } : step
			)
		);
		handleNext();
	};

	const handleProfileData = async (data: ProfileFormData) => {
		if (isStepComplete(1)) {
			handleStepComplete(1);
			return;
		}

		setIsSubmitting(true);
		updateSpecializationMutation.mutateAsync(
			{
				specialty: data.specialty,
				subSpecialty: data.subSpecialty,
				subSpecialization: data.subSpecialization,
			},
			{
				onSuccess: () => {
					updateBasicInfo.mutateAsync(
						{
							prcNumber: data.prcNumber,
							// TODO: Missing PRC expiry date in api
						},
						{
							onSuccess: () => {
								const fd = new FormData();
								fd.append('eSignature', data.signature);
								updateESign.mutateAsync(fd, {
									onSuccess: () => {
										handleStepComplete(1);
									},
									onSettled: () => {
										setIsSubmitting(false);
									},
								});
							},
							onError: () => {
								setIsSubmitting(false);
							},
						}
					);
				},
				onError: () => {
					setIsSubmitting(false);
				},
			}
		);
	};

	const handleAddressData = async (data: TAddressFormData) => {
		if (isStepComplete(2)) {
			handleStepComplete(2);
			return;
		}

		setIsSubmitting(true);
		updateAddressMutation.mutateAsync(data, {
			onSuccess: () => {
				handleStepComplete(2);
			},
			onSettled: () => {
				setIsSubmitting(false);
			},
		});
	};

	const handleClinicData = async (data: ClinicFormData) => {
		setIsSubmitting(true);
		// Transform schedule to backend format
		const transformedSchedule = data.schedule.map((schedule) => ({
			day: schedule.day,
			startTime: schedule.startTime,
			endTime: schedule.endTime,
			isActive: schedule.isActive ? 1 : 0,
		}));

		await createClinicMutation.mutateAsync(
			{
				name: data.name,
				address: data.address,
				phone: data.phone || undefined,
				email: data.email || undefined,
				website: data.website || undefined,
				longitude: data.longitude || undefined,
				latitude: data.latitude || undefined,
				startTime: data.startTime || undefined,
				endTime: data.endTime || undefined,
				dateEstablished: data.dateEstablished
					? new Date(data.dateEstablished)
					: undefined,
				schedule: transformedSchedule,
			},
			{
				// eslint-disable-next-line @typescript-eslint/no-explicit-any
				onSuccess: (data: any) => {
					setClinicId(data.data.id);
					handleStepComplete(3);
				},
				onSettled: () => {
					setIsSubmitting(false);
				},
			}
		);
	};

	const handleAssistantsData = async (data: AssistantsStepData) => {
		try {
			setIsSubmitting(true);

			// Create assistants (if any and if clinic was created)
			if (!data.skipped && data.assistants.length > 0 && clinicId) {
				const _clinicId = clinicId || doctorProfile?.clinics[0]?.id;

				// Create assistants for the newly created clinic
				for (const assistant of data.assistants) {
					await createAssistantMutation.mutateAsync({
						clinicId: _clinicId as number,
						assistantData: {
							firstName: assistant.firstName,
							middleName: assistant.middleName,
							lastName: assistant.lastName,
							suffix: assistant.suffix,
							email: assistant.email,
							username: assistant.username,
							password: assistant.password,
							password_confirmation: assistant.password_confirmation,
						},
					});
				}
			}

			handleStepComplete(4);

			// Success - mark profile as completed
			await completeOnboardingMutation.mutateAsync();

			onComplete();
		} catch (error) {
			console.error('Assistants submission error:', error);
			toast.error('Setup Failed', {
				description:
					'There was an error setting up your assistants. Please try again.',
			});
			throw error; // Re-throw to prevent navigation
		} finally {
			setIsSubmitting(false);
		}
	};

	const renderCurrentStep = () => {
		switch (currentStep) {
			case 1:
				return (
					<ProfileStep
						onData={handleProfileData}
						doctorProfile={doctorProfile}
						isSubmitting={isSubmitting}
					/>
				);
			case 2:
				return (
					<AddressStep
						onPrevious={handlePrevious}
						onData={handleAddressData}
						doctorProfile={doctorProfile}
						isSubmitting={isSubmitting}
					/>
				);
			case 3:
				return (
					<CreateClinicStep
						onPrevious={handlePrevious}
						onData={handleClinicData}
						isSubmitting={isSubmitting}
					/>
				);
			case 4:
				return (
					<AddAssistantsStep
						onPrevious={handlePrevious}
						onData={handleAssistantsData}
						isSubmitting={isSubmitting}
					/>
				);
			default:
				return null;
		}
	};

	return (
		<div className="min-h-screen bg-gray-50 py-8">
			<div className="mx-auto max-w-4xl px-4">
				<div className="mb-8 text-center">
					<h1 className="mb-2 text-3xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
						Welcome to Elena!
					</h1>
					<p className="text-gray-600">
						Let&apos;s set up your account to get you started with your practice
					</p>
				</div>

				<StepIndicator steps={steps} currentStep={currentStep} />

				<div className="mb-8">
					<div className="mb-6 text-center">
						<h2 className="mb-2 text-xl font-semibold">
							{steps[currentStep - 1]?.title}
						</h2>
						<p className="text-gray-600">
							{steps[currentStep - 1]?.description}
						</p>
					</div>

					{renderCurrentStep()}
				</div>
			</div>
		</div>
	);
}
