'use client';

import { Building, Check, MapPin, User, Users } from 'lucide-react';

export interface OnboardingStep {
	id: number;
	title: string;
	description: string;
	icon: React.ReactNode;
	completed: boolean;
}

export const ONBOARDING_STEPS: OnboardingStep[] = [
	{
		id: 1,
		title: 'Complete Profile',
		description: 'Add your professional information and signature',
		icon: <User className="h-5 w-5" />,
		completed: false,
	},
	{
		id: 2,
		title: 'Address Information',
		description: 'Provide your current and permanent address',
		icon: <MapPin className="h-5 w-5" />,
		completed: false,
	},
	{
		id: 3,
		title: 'Create Clinic',
		description: 'Set up your first clinic and schedule',
		icon: <Building className="h-5 w-5" />,
		completed: false,
	},
	{
		id: 4,
		title: 'Add Assistants',
		description: 'Create accounts for your clinic assistants',
		icon: <Users className="h-5 w-5" />,
		completed: false,
	},
];

export default function StepIndicator({
	steps,
	currentStep,
}: {
	steps: OnboardingStep[];
	currentStep: number;
}) {
	return (
		<div className="mb-8 flex items-center justify-center">
			{steps.map((step, index) => (
				<div key={step.id} className="flex items-center">
					<div
						className={`flex h-10 w-10 items-center justify-center rounded-full border-2 ${
							step.id <= currentStep
								? 'border-[oklch(0.7448_0.1256_202.74)] bg-[oklch(0.7448_0.1256_202.74)] text-white'
								: 'border-gray-300 text-gray-400'
						}`}
					>
						{step.id < currentStep ? (
							<Check className="h-5 w-5" />
						) : (
							<span className="text-sm font-medium">{step.id}</span>
						)}
					</div>
					{index < steps.length - 1 && (
						<div
							className={`mx-2 h-0.5 w-16 ${
								step.id < currentStep
									? 'bg-[oklch(0.7448_0.1256_202.74)]'
									: 'bg-gray-300'
							}`}
						/>
					)}
				</div>
			))}
		</div>
	);
}
