'use client';

import {
	<PERSON><PERSON>,
	Check,
	CreditCard,
	Gift,
	History,
	Sparkles,
	Wallet,
} from 'lucide-react';
import { useQueryState, useQueryStates } from 'nuqs';
import { useState } from 'react';

import {
	AlertDialog,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { searchParamParsers } from '@/core/lib/search-params';
import { cn } from '@/core/lib/utils';

import {
	useAllSubscription,
	useSubscribe,
} from '../../hooks/useDoctorAssistantDashboard';
import { ISubscriptionListParams } from '../../types/doctor.types';

export function DoctorSubscriptionManagement() {
	const [open, setOpen] = useState(false);
	const [selected, setSelected] = useState<string | null>(null);

	const [{ page }] = useQueryStates({
		page: searchParamParsers.page,
	});

	const queryParams: ISubscriptionListParams = {
		page,
		per_page: 10,
	};

	const { data: subscriptionResponse } = useAllSubscription(queryParams);
	const plans = subscriptionResponse?.data ?? [];
	const { mutate: subscribe, isPending } = useSubscribe();

	const paymentMethods = [
		{
			id: 'top-connect',
			name: 'Pay via Top Connect',
			icon: <CreditCard className="h-5 w-5 text-blue-600" />,
			disabled: false,
		},
		{
			id: 'manual',
			name: 'Pay Manually',
			icon: <Wallet className="h-5 w-5 text-green-600" />,
			disabled: true,
		},
	];

	const handleConfirm = ({ planId }: { planId: string }) => {
		if (!selected || !planId) return;

		subscribe(
			{ id: planId },
			{
				onSuccess: () => {
					setOpen(false);
					setSelected(null);
				},
			}
		);
	};

	const [subscriptionView, setSubscriptionView] = useQueryState(
		'view',
		searchParamParsers.subscriptionView
	);

	const handleViewPlans = () => {
		setSubscriptionView('plans');
	};

	const handleViewHistory = () => {
		setSubscriptionView('history');
	};

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
						Subscription Management
					</h1>
					<p className="text-muted-foreground">
						Manage your subscription plans and billing
					</p>
				</div>
			</div>

			<Tabs
				value={subscriptionView || 'overview'}
				onValueChange={(value) =>
					setSubscriptionView(value as 'overview' | 'plans' | 'history')
				}
				className="w-full"
			>
				<TabsList className="mb-6 grid w-full grid-cols-3">
					<TabsTrigger value="overview" className="flex items-center gap-2">
						<CreditCard className="h-4 w-4" />
						Overview
					</TabsTrigger>
					<TabsTrigger value="plans" className="flex items-center gap-2">
						<Gift className="h-4 w-4" />
						Plans
					</TabsTrigger>
					<TabsTrigger value="history" className="flex items-center gap-2">
						<History className="h-4 w-4" />
						History
					</TabsTrigger>
				</TabsList>

				{/* Overview Section */}
				<TabsContent value="overview" className="mt-0">
					<div className="grid gap-4 md:grid-cols-3">
						<Card>
							<CardHeader>
								<CardTitle className="flex items-center gap-2">
									<CreditCard className="h-5 w-5" />
									Current Plan
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="py-8 text-center">
									<h3 className="mb-2 text-lg font-semibold">
										Subscription Plans
									</h3>
									<p className="text-muted-foreground mb-4">
										View and manage your subscription plans
									</p>
									<Button variant="outline" onClick={handleViewPlans}>
										View Plans
									</Button>
								</div>
							</CardContent>
						</Card>

						<Card>
							<CardHeader>
								<CardTitle className="flex items-center gap-2">
									<Gift className="h-5 w-5" />
									Gift Codes
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="py-8 text-center">
									<h3 className="mb-2 text-lg font-semibold">Redeem Code</h3>
									<p className="text-muted-foreground mb-4">
										Enter a gift code to extend your subscription
									</p>
									<Button variant="outline" onClick={handleViewPlans}>
										Redeem Code
									</Button>
								</div>
							</CardContent>
						</Card>

						<Card>
							<CardHeader>
								<CardTitle className="flex items-center gap-2">
									<History className="h-5 w-5" />
									Billing History
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="py-8 text-center">
									<h3 className="mb-2 text-lg font-semibold">
										Payment History
									</h3>
									<p className="text-muted-foreground mb-4">
										View your subscription payment history
									</p>
									<Button variant="outline" onClick={handleViewHistory}>
										View History
									</Button>
								</div>
							</CardContent>
						</Card>
					</div>
				</TabsContent>

				{/* Plans Section */}
				<TabsContent
					value="plans"
					className="from-background to-muted/40 mt-0 flex w-full flex-wrap justify-center gap-6 bg-gradient-to-b"
				>
					{plans.map((plan) => (
						<div
							key={plan.id}
							className="relative flex-1 basis-full transition-opacity duration-500 sm:basis-[calc(50%-1.5rem)] lg:max-w-[320px] lg:basis-[calc(33.333%-1.5rem)]"
						>
							<div className="relative flex h-full flex-col justify-between overflow-hidden rounded-[1.5rem] bg-white/80 shadow-xl ring-1 ring-black/5 backdrop-blur-xl dark:bg-zinc-900/60 dark:ring-white/10">
								{/* Header */}
								<div className="flex items-center justify-between px-6 pt-6">
									<span className="inline-flex items-center gap-2 rounded-full bg-[oklch(0.7448_0.1256_202.74)]/10 px-3 py-1 text-xs font-medium text-[oklch(0.7448_0.1256_202.74)]">
										<Sparkles className="h-3.5 w-3.5" />
										{plan.is_active ? 'Active Plan' : 'Available'}
									</span>
									<span className="text-[10px] tracking-widest text-zinc-500 uppercase">
										{plan.coverage_type}
									</span>
								</div>

								{/* Title & Price */}
								<div className="px-6 pt-4 pb-2">
									<h3 className="text-2xl font-semibold tracking-tight">
										{plan.name}
									</h3>
									<div className="mt-3 flex items-baseline gap-2">
										{plan.is_discount_enabled ? (
											<>
												<span className="text-lg text-zinc-400 line-through">
													₱{plan.price.toLocaleString()}
												</span>
												<span className="text-4xl leading-none font-bold">
													₱{plan.discounted_price.toLocaleString()}
												</span>
											</>
										) : (
											<span className="text-4xl leading-none font-bold">
												₱{plan.price.toLocaleString()}
											</span>
										)}
										<span className="text-sm text-zinc-500">
											/ {plan.coverage} {plan.coverage_type.toLowerCase()}
											{plan.coverage > 1 ? 's' : ''}
										</span>
									</div>
									<p className="mt-3 text-sm text-zinc-500">
										{plan.description}
									</p>
								</div>

								{/* Features */}
								<ul className="space-y-3 px-6 py-4 text-sm">
									{[
										`${plan.patient_count} Patients Included`,
										`${plan.appointment_count} Appointments`,
										plan.clinic_enabled
											? 'Clinic Access Enabled'
											: 'No Clinic Access',
										plan.medical_document_enabled
											? 'Medical Docs Access'
											: 'No Medical Docs',
									].map((f) => (
										<li key={f} className="flex items-start gap-3">
											<span className="mt-0.5 inline-flex h-5 w-5 items-center justify-center rounded-full bg-emerald-500/10">
												<Check className="h-3.5 w-3.5 text-emerald-600" />
											</span>
											<span className="text-zinc-700 dark:text-zinc-300">
												{f}
											</span>
										</li>
									))}
								</ul>

								{/* CTA */}
								<div className="px-6 pb-6">
									<button
										disabled={plan.is_active === 1}
										onClick={() => {
											if (plan.is_active !== 1) {
												setOpen(true);
											}
										}}
										className={cn(
											'w-full rounded-2xl px-5 py-3 text-base font-medium shadow-lg transition-transform duration-150 focus:outline-none',
											plan.is_active
												? 'cursor-not-allowed bg-zinc-400 text-white dark:bg-zinc-700'
												: 'bg-[oklch(0.7448_0.1256_202.74)] text-white hover:scale-105 focus-visible:ring-2 focus-visible:ring-[oklch(0.7448_0.1256_202.74)]/70 focus-visible:ring-offset-2 focus-visible:ring-offset-white active:scale-95 dark:focus-visible:ring-offset-zinc-900'
										)}
									>
										{plan.is_active === 1 ? 'Current Plan' : 'Buy Now'}
									</button>

									{/* Payment Dialog */}
									<AlertDialog open={open} onOpenChange={setOpen}>
										<AlertDialogContent className="max-w-md">
											<AlertDialogHeader>
												<AlertDialogTitle>
													Select payment method
												</AlertDialogTitle>
												<AlertDialogDescription>
													Preferred method with secure transactions.
												</AlertDialogDescription>
											</AlertDialogHeader>

											<div className="space-y-3 py-4">
												{paymentMethods.map((method) => (
													<div
														key={method.id}
														onClick={() => {
															if (!method.disabled) {
																setSelected(method.id);
															}
														}}
														className={cn(
															'flex items-center gap-3 rounded-lg border p-4 shadow-sm transition',
															method.disabled
																? 'pointer-events-none cursor-not-allowed opacity-50'
																: 'cursor-pointer hover:bg-zinc-50 dark:hover:bg-zinc-800',
															selected === method.id && !method.disabled
																? 'border-[oklch(0.7448_0.1256_202.74)] bg-[oklch(0.7448_0.1256_202.74)]/5'
																: 'border-zinc-200 dark:border-zinc-700'
														)}
													>
														{method.icon}
														<span className="font-medium">{method.name}</span>
														{selected === method.id && (
															<Banknote className="ml-auto h-5 w-5 text-[oklch(0.7448_0.1256_202.74)]" />
														)}
													</div>
												))}
											</div>

											<AlertDialogFooter className="flex flex-col gap-2 sm:flex-row sm:justify-between">
												<AlertDialogCancel>Go Back</AlertDialogCancel>
												<button
													disabled={!selected}
													onClick={() => {
														handleConfirm({ planId: plan?.id.toString() });
													}}
													className={cn(
														'w-full rounded-md px-5 py-2.5 text-white shadow-md transition-transform duration-150 sm:w-auto',
														selected
															? 'bg-[oklch(0.7448_0.1256_202.74)] hover:scale-105 hover:bg-[oklch(0.7448_0.1256_202.74)]/90 active:scale-95'
															: 'cursor-not-allowed bg-zinc-400'
													)}
												>
													{isPending ? 'Processing...' : 'Continue'}
												</button>
											</AlertDialogFooter>
										</AlertDialogContent>
									</AlertDialog>
								</div>
							</div>
						</div>
					))}
				</TabsContent>

				{/* History Section */}
				<TabsContent value="history" className="mt-0">
					<Card>
						<CardHeader>
							<CardTitle>Payment History</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="py-8 text-center">
								<p className="text-muted-foreground mb-4">
									Payment history will be available here.
								</p>
								<p className="text-muted-foreground text-sm">
									This section is under development. For now, you can access the
									full history page externally.
								</p>
							</div>
						</CardContent>
					</Card>
				</TabsContent>
			</Tabs>
		</div>
	);
}
