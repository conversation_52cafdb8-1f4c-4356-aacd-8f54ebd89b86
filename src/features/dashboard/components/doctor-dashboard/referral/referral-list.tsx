'use client';

import { format } from 'date-fns';
import { Eye, MoreHorizontal, Printer, Trash2, User } from 'lucide-react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import { useRemoveReferral } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import { IReferral } from '@/features/dashboard/types/doctor.types';

interface IReferralListProps {
	referrals: IReferral[];
	isLoading: boolean;
	error: Error | null;
	currentPage: number;
	totalPages: number;
	onPageChange: (page: number) => void;
	onViewReferral: (referralId: number) => void;
	onPrintReferral: (referralId: number) => void;
}

export function ReferralList({
	referrals,
	isLoading,
	error,
	currentPage,
	totalPages,
	onPageChange,
	onViewReferral,
	onPrintReferral,
}: IReferralListProps) {
	const { mutate: removeReferral } = useRemoveReferral();

	const handleRemoveReferral = (referralId: number) => {
		if (confirm('Are you sure you want to remove this referral?')) {
			removeReferral({ referralId });
		}
	};

	const formatDate = (dateString: string) => {
		try {
			return format(new Date(dateString), 'MMM dd, yyyy, h:mm a');
		} catch {
			return dateString;
		}
	};

	if (isLoading) {
		return (
			<div className="space-y-4">
				{[...Array(5)].map((_, index) => (
					<div key={index} className="flex items-center space-x-4">
						<Skeleton className="h-12 w-12 rounded-full" />
						<div className="space-y-2">
							<Skeleton className="h-4 w-[250px]" />
							<Skeleton className="h-4 w-[200px]" />
						</div>
					</div>
				))}
			</div>
		);
	}

	if (error) {
		return (
			<Alert variant="destructive">
				<AlertDescription>
					Failed to load referrals. Please try again.
				</AlertDescription>
			</Alert>
		);
	}

	if (!referrals || referrals.length === 0) {
		return (
			<div className="py-8 text-center">
				<User className="mx-auto h-12 w-12 text-gray-400" />
				<h3 className="mt-2 text-sm font-semibold text-gray-900">
					No referrals found
				</h3>
				<p className="mt-1 text-sm text-gray-500">
					Get started by creating a new referral.
				</p>
			</div>
		);
	}

	return (
		<div className="space-y-4">
			{/* Table */}
			<div className="rounded-md border">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>Patient</TableHead>
							<TableHead>Contact</TableHead>
							<TableHead>Clinic</TableHead>
							<TableHead>Date</TableHead>
							<TableHead className="text-right">Actions</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{referrals.map((referral) => (
							<TableRow key={referral.id}>
								<TableCell>
									<div className="flex flex-row items-center gap-2">
										<img
											className="bg-muted flex h-8 w-8 rounded-full"
											alt="patient avatar"
											src={'/placeholder-profile.jpg'}
										/>
										<div>
											<div className="font-medium">{referral.patient_name}</div>
											<div className="text-muted-foreground text-sm">
												{referral.patient_gender} •{' '}
												{referral.patient_birthdate &&
													new Date().getFullYear() -
														new Date(referral.patient_birthdate).getFullYear() +
														' years'}
											</div>
										</div>
									</div>
								</TableCell>
								<TableCell>
									<div className="text-sm">
										<div>{referral.patient_phone}</div>
										<div className="text-muted-foreground text-xs">
											{referral.patient_address}
										</div>
									</div>
								</TableCell>
								<TableCell>
									<div className="text-sm">
										{referral.clinic?.name || 'N/A'}
									</div>
								</TableCell>
								<TableCell>
									<div className="text-sm">
										{formatDate(referral.created_at)}
									</div>
								</TableCell>
								<TableCell className="text-right">
									<DropdownMenu>
										<DropdownMenuTrigger asChild>
											<Button variant="ghost" className="h-8 w-8 p-0">
												<span className="sr-only">Open menu</span>
												<MoreHorizontal className="h-4 w-4" />
											</Button>
										</DropdownMenuTrigger>
										<DropdownMenuContent align="end">
											<DropdownMenuItem
												onClick={() => onViewReferral(referral.id)}
											>
												<Eye className="mr-2 h-4 w-4" />
												Open Referral
											</DropdownMenuItem>
											<DropdownMenuItem
												onClick={() => onPrintReferral(referral.id)}
											>
												<Printer className="mr-2 h-4 w-4" />
												Print Referral
											</DropdownMenuItem>
											<DropdownMenuItem
												onClick={() => handleRemoveReferral(referral.id)}
												className="text-red-600"
											>
												<Trash2 className="mr-2 h-4 w-4" />
												Remove Referral
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								</TableCell>
							</TableRow>
						))}
					</TableBody>
				</Table>
			</div>

			{/* Pagination */}
			{totalPages > 1 && (
				<div className="flex items-center justify-between">
					<div className="text-muted-foreground text-sm">
						Page {currentPage} of {totalPages}
					</div>
					<div className="flex items-center space-x-2">
						<Button
							variant="outline"
							size="sm"
							onClick={() => onPageChange(currentPage - 1)}
							disabled={currentPage <= 1}
						>
							Previous
						</Button>
						<Button
							variant="outline"
							size="sm"
							onClick={() => onPageChange(currentPage + 1)}
							disabled={currentPage >= totalPages}
						>
							Next
						</Button>
					</div>
				</div>
			)}
		</div>
	);
}
