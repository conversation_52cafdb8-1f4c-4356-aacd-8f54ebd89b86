'use client';

import { format } from 'date-fns';
import {
	FileText,
	MapPin,
	Pencil,
	Phone,
	Printer,
	QrCode,
	User,
} from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import {
	useReferralDetail,
	useUpdateReferralPtr,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';

interface IReferralDetailDialogProps {
	referralId: number;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onPrint: (referralId: number) => void;
}

export function ReferralDetailDialog({
	referralId,
	open,
	onOpenChange,
	onPrint,
}: IReferralDetailDialogProps) {
	const { data: referralResponse, isLoading } = useReferralDetail(referralId, {
		enabled: open,
	});

	const [showPtrForm, setShowPtrForm] = useState(false);
	const [ptrForm, setPtrForm] = useState({
		professionalTaxReceipt: '',
	});

	const { mutate: updateProfessionalTaxReceipt } = useUpdateReferralPtr();

	const handleSubmitPtr = () => {
		if (!referralId || !ptrForm.professionalTaxReceipt) return;

		updateProfessionalTaxReceipt({
			referralId,
			data: {
				professionalTaxReceipt: ptrForm.professionalTaxReceipt,
			},
		});

		// Reset form and close dialog
		setShowPtrForm(false);
	};

	const referral = referralResponse?.data;

	if (isLoading) {
		return (
			<Dialog open={open} onOpenChange={onOpenChange}>
				<DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
					<div className="flex items-center justify-center p-8">
						<div className="text-center">
							<div className="mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-[oklch(0.7448_0.1256_202.74)]"></div>
							<p className="text-muted-foreground mt-2">
								Loading referral details...
							</p>
						</div>
					</div>
				</DialogContent>
			</Dialog>
		);
	}

	if (!referral) {
		return (
			<Dialog open={open} onOpenChange={onOpenChange}>
				<DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
					<div className="flex items-center justify-center p-8">
						<div className="text-center">
							<FileText className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
							<p className="text-muted-foreground">Referral not found</p>
						</div>
					</div>
				</DialogContent>
			</Dialog>
		);
	}

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
				<DialogHeader>
					<DialogTitle>Referral Details</DialogTitle>
					<DialogDescription>
						Referral detail for {referral.patient_name}
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-4">
					{/* Ptr Form Dialog */}
					<Dialog
						open={showPtrForm}
						onOpenChange={(value) => {
							if (!value) {
								setShowPtrForm(false);
							}
						}}
					>
						<DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
							<DialogHeader>
								<DialogTitle>Edit Professional Tax Receipt</DialogTitle>
								<DialogDescription>
									Update the professional tax receipt number for this
									appointment.
								</DialogDescription>
							</DialogHeader>

							<div className="space-y-6">
								{/* Ptr Information */}
								<div className="space-y-4">
									<div className="space-y-2">
										<Label htmlFor="professionalTaxReceipt">
											Professional Tax Receipt *
										</Label>
										<Input
											id="professionalTaxReceipt"
											value={ptrForm.professionalTaxReceipt}
											onChange={(e) =>
												setPtrForm((prev) => ({
													...prev,
													professionalTaxReceipt: e.target.value,
												}))
											}
											placeholder="Enter professional tax receipt number"
										/>
									</div>
								</div>
							</div>

							<DialogFooter>
								<Button
									variant="outline"
									onClick={() => {
										setShowPtrForm(false);
									}}
								>
									Cancel
								</Button>
								<Button
									onClick={handleSubmitPtr}
									disabled={!ptrForm.professionalTaxReceipt}
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
								>
									Save
								</Button>
							</DialogFooter>
						</DialogContent>
					</Dialog>
					{/* Referral Header */}
					<Card>
						<CardHeader>
							<div className="flex items-center justify-between">
								<CardTitle className="text-lg">Referral Information</CardTitle>
								<Button
									onClick={() => onPrint(referralId)}
									className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
								>
									<Printer className="mr-2 h-4 w-4" />
									Print Referral
								</Button>
							</div>
						</CardHeader>

						<CardContent className="space-y-4">
							<div className="grid grid-cols-2 gap-4">
								<div>
									<p className="text-muted-foreground text-sm font-medium">
										Date Created
									</p>
									<p className="text-sm">
										{format(new Date(referral.created_at), 'PPP')}
									</p>
								</div>
								<div>
									<p className="text-muted-foreground text-sm font-medium">
										Clinic
									</p>
									<p className="text-sm">{referral.clinic?.name || 'N/A'}</p>
								</div>
								<div>
									<Label className="text-sm font-medium text-gray-600">
										Professional Tax Receipt
									</Label>
									<div className="flex items-center gap-2">
										<p className="text-sm">
											{referral?.professional_tax_receipt || '---'}
										</p>
										<Button
											variant={'ghost'}
											onClick={() => {
												setShowPtrForm(true);
											}}
											size="sm"
										>
											<Pencil />
											Edit
										</Button>
									</div>
								</div>
							</div>
						</CardContent>
					</Card>

					{/* Patient Information */}
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<User className="h-5 w-5" />
								Patient Information
							</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="grid grid-cols-2 gap-4">
								<div>
									<p className="text-muted-foreground text-sm font-medium">
										Full Name
									</p>
									<p className="text-sm font-medium">{referral.patient_name}</p>
								</div>
								<div>
									<p className="text-muted-foreground text-sm font-medium">
										Gender
									</p>
									<p className="text-sm capitalize">
										{referral.patient_gender}
									</p>
								</div>
							</div>

							<div className="grid grid-cols-2 gap-4">
								<div>
									<p className="text-muted-foreground text-sm font-medium">
										Birthdate
									</p>
									<p className="text-sm">
										{referral.patient_birthdate
											? format(new Date(referral.patient_birthdate), 'PPP')
											: 'N/A'}
									</p>
								</div>
								<div className="flex items-center gap-2">
									<Phone className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-muted-foreground text-sm font-medium">
											Contact
										</p>
										<p className="text-sm">{referral.patient_phone || 'N/A'}</p>
									</div>
								</div>
							</div>

							<div className="flex items-start gap-2">
								<MapPin className="text-muted-foreground mt-1 h-4 w-4" />
								<div className="flex-1">
									<p className="text-muted-foreground text-sm font-medium">
										Address
									</p>
									<p className="text-sm">{referral.patient_address || 'N/A'}</p>
								</div>
							</div>
						</CardContent>
					</Card>

					{/* Referral Details */}
					<Card>
						<CardHeader>
							<CardTitle>Referral Details</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							<div>
								<p className="text-muted-foreground mb-2 text-sm font-medium">
									Referred to Doctor
								</p>
								<Badge variant="secondary" className="text-sm">
									{referral.doctor_name}
								</Badge>
							</div>

							<Separator />

							<div>
								<p className="text-muted-foreground mb-2 text-sm font-medium">
									Purpose of Referral
								</p>
								<div className="bg-muted/50 rounded-lg p-4">
									<p className="text-sm whitespace-pre-wrap">
										{referral.purpose}
									</p>
								</div>
							</div>

							{referral.professional_tax_receipt && (
								<>
									<Separator />
									<div>
										<p className="text-muted-foreground mb-2 text-sm font-medium">
											Professional Tax Receipt
										</p>
										<p className="text-sm">
											{referral.professional_tax_receipt}
										</p>
									</div>
								</>
							)}
						</CardContent>
					</Card>

					{/* QR Code Section */}
					{referral.qr_code && (
						<Card>
							<CardHeader>
								<CardTitle className="flex items-center gap-2">
									<QrCode className="h-5 w-5" />
									Medical History QR Code
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="flex items-center justify-center p-4">
									<div className="rounded-lg border bg-white p-4">
										<img
											src={referral.qr_code}
											alt="Medical History QR Code"
											className="h-32 w-32"
										/>
									</div>
								</div>
								<p className="text-muted-foreground mt-2 text-center text-sm">
									Scan this QR code to access patient&apos;s medical history
								</p>
							</CardContent>
						</Card>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}
