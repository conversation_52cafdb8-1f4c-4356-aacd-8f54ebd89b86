'use client';

import { format } from 'date-fns';
import { Search, User, UserPlus } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import {
	useClinics,
	useCreateStandaloneReferral,
	usePatients,
} from '@/features/dashboard/hooks/useDoctorAssistantDashboard';
import {
	ICreateStandaloneReferralRequest,
	IPatient,
} from '@/features/dashboard/types/doctor.types';

interface ICreateReferralDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onClose: () => void;
}

export function CreateReferralDialog({
	open,
	onOpenChange,
	onClose,
}: ICreateReferralDialogProps) {
	const [activeTab, setActiveTab] = useState('registered');
	const [selectedClinicId, setSelectedClinicId] = useState<string>('');
	const [selectedPatient, setSelectedPatient] = useState<IPatient | null>(null);
	const [patientSearch, setPatientSearch] = useState('');
	const [isSubmitting, setIsSubmitting] = useState(false);

	// New patient form data
	const [newPatientData, setNewPatientData] = useState({
		firstName: '',
		middleName: '',
		lastName: '',
		suffix: '',
		gender: '',
		contactNumber: '',
		address: '',
		birthdate: '',
	});

	// Referral form data
	const [referralData, setReferralData] = useState({
		referredDoctorName: '',
		purposeOfReferral: '',
	});

	// Fetch data
	const { data: clinicsResponse } = useClinics();
	const { data: patientsResponse } = usePatients({
		search: patientSearch,
	});

	const createReferralMutation = useCreateStandaloneReferral();

	const clinics = clinicsResponse?.data?.data || [];
	const patients = patientsResponse?.data?.data || [];

	// Handle form submission
	const handleSubmit = async () => {
		if (
			!selectedClinicId ||
			!referralData.referredDoctorName ||
			!referralData.purposeOfReferral
		) {
			return;
		}

		setIsSubmitting(true);

		try {
			let patientName = '';
			let patientAddress = '';
			let patientPhone = '';
			let patientGender = '';
			let patientBirthdate = '';
			let patientProfileId: number | undefined;

			if (activeTab === 'registered' && selectedPatient) {
				patientName =
					`${selectedPatient.first_name} ${selectedPatient.middle_name || ''} ${selectedPatient.last_name}`.trim();
				const address = selectedPatient.profileCurrentAddress;
				patientAddress = address
					? [
							address.street_name,
							address.barangay,
							address.city,
							address.province,
							address.country,
						]
							.filter(Boolean)
							.join(', ')
					: '';
				patientPhone = selectedPatient.phone || '';
				patientGender = selectedPatient.gender || '';
				patientBirthdate = selectedPatient.birthday || '';
				patientProfileId = selectedPatient.id;
			} else {
				patientName =
					`${newPatientData.firstName} ${newPatientData.middleName || ''} ${newPatientData.lastName}`.trim();
				patientAddress = newPatientData.address;
				patientPhone = newPatientData.contactNumber;
				patientGender = newPatientData.gender;
				patientBirthdate = newPatientData.birthdate;
			}

			const requestData: ICreateStandaloneReferralRequest = {
				clinicId: parseInt(selectedClinicId),
				patientProfileId,
				patientName,
				patientAddress,
				patientPhone,
				patientGender,
				patientBirthdate,
				doctorName: referralData.referredDoctorName,
				purpose: referralData.purposeOfReferral,
			};

			await createReferralMutation.mutateAsync(requestData);
			handleClose();
		} catch (error) {
			console.error('Error creating referral:', error);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleClose = () => {
		// Reset form data
		setSelectedClinicId('');
		setSelectedPatient(null);
		setPatientSearch('');
		setNewPatientData({
			firstName: '',
			middleName: '',
			lastName: '',
			suffix: '',
			gender: '',
			contactNumber: '',
			address: '',
			birthdate: '',
		});
		setReferralData({
			referredDoctorName: '',
			purposeOfReferral: '',
		});
		setActiveTab('registered');
		onClose();
	};

	const isFormValid = () => {
		const hasClinic = !!selectedClinicId;
		const hasReferralData =
			referralData.referredDoctorName && referralData.purposeOfReferral;

		if (activeTab === 'registered') {
			return hasClinic && selectedPatient && hasReferralData;
		} else {
			const hasNewPatientData =
				newPatientData.firstName &&
				newPatientData.lastName &&
				newPatientData.gender &&
				newPatientData.contactNumber &&
				newPatientData.address &&
				newPatientData.birthdate;
			return hasClinic && hasNewPatientData && hasReferralData;
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
				<DialogHeader>
					<DialogTitle>Create New Referral</DialogTitle>
					<DialogDescription>
						Create a referral to another medical professional for your patient.
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-6">
					{/* Clinic Selection */}
					<div className="space-y-2">
						<Label htmlFor="clinic">Select Clinic</Label>
						<Select
							value={selectedClinicId}
							onValueChange={setSelectedClinicId}
						>
							<SelectTrigger>
								<SelectValue placeholder="Choose a clinic" />
							</SelectTrigger>
							<SelectContent>
								{clinics.map((clinic) => (
									<SelectItem key={clinic.id} value={clinic.id.toString()}>
										{clinic.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					{/* Patient Selection Tabs */}
					<Tabs value={activeTab} onValueChange={setActiveTab}>
						<TabsList className="grid w-full grid-cols-2">
							<TabsTrigger
								value="registered"
								className="flex items-center gap-2"
							>
								<User className="h-4 w-4" />
								Registered Patient
							</TabsTrigger>
							<TabsTrigger value="new" className="flex items-center gap-2">
								<UserPlus className="h-4 w-4" />
								New Patient
							</TabsTrigger>
						</TabsList>

						<TabsContent value="registered" className="space-y-4">
							<div className="space-y-2">
								<Label>Search Patient</Label>
								<div className="relative">
									<Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
									<Input
										placeholder="Search by name, phone, or email..."
										value={patientSearch}
										onChange={(e) => setPatientSearch(e.target.value)}
										className="pl-10"
									/>
								</div>
							</div>

							{patients.length > 0 && (
								<div className="max-h-48 space-y-2 overflow-y-auto">
									{patients.map((patient) => (
										<Card
											key={patient.id}
											className={`cursor-pointer transition-colors ${
												selectedPatient?.id === patient.id
													? 'border-[oklch(0.7448_0.1256_202.74)] bg-[oklch(0.7448_0.1256_202.74)]/5'
													: 'hover:bg-muted/50'
											}`}
											onClick={() => setSelectedPatient(patient)}
										>
											<CardContent className="p-3">
												<div className="flex items-start justify-between">
													<div>
														<p className="font-medium">
															{patient.first_name} {patient.middle_name}{' '}
															{patient.last_name}
														</p>
														<p className="text-muted-foreground text-sm">
															{patient.phone}
														</p>
														<p className="text-muted-foreground text-sm">
															{patient.birthday &&
																format(
																	new Date(patient.birthday),
																	'MMM dd, yyyy'
																)}
														</p>
													</div>
													<div className="text-right">
														<p className="text-muted-foreground text-sm capitalize">
															{patient.gender}
														</p>
													</div>
												</div>
											</CardContent>
										</Card>
									))}
								</div>
							)}
						</TabsContent>

						<TabsContent value="new" className="space-y-4">
							<div className="grid grid-cols-2 gap-4">
								<div className="space-y-2">
									<Label htmlFor="firstName">First Name *</Label>
									<Input
										id="firstName"
										value={newPatientData.firstName}
										onChange={(e) =>
											setNewPatientData((prev) => ({
												...prev,
												firstName: e.target.value,
											}))
										}
										placeholder="Enter first name"
									/>
								</div>
								<div className="space-y-2">
									<Label htmlFor="lastName">Last Name *</Label>
									<Input
										id="lastName"
										value={newPatientData.lastName}
										onChange={(e) =>
											setNewPatientData((prev) => ({
												...prev,
												lastName: e.target.value,
											}))
										}
										placeholder="Enter last name"
									/>
								</div>
							</div>

							<div className="grid grid-cols-2 gap-4">
								<div className="space-y-2">
									<Label htmlFor="gender">Gender *</Label>
									<Select
										value={newPatientData.gender}
										onValueChange={(value) =>
											setNewPatientData((prev) => ({ ...prev, gender: value }))
										}
									>
										<SelectTrigger>
											<SelectValue placeholder="Select gender" />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="male">Male</SelectItem>
											<SelectItem value="female">Female</SelectItem>
										</SelectContent>
									</Select>
								</div>
								<div className="space-y-2">
									<Label htmlFor="birthdate">Birthdate *</Label>
									<Input
										id="birthdate"
										type="date"
										value={newPatientData.birthdate}
										onChange={(e) =>
											setNewPatientData((prev) => ({
												...prev,
												birthdate: e.target.value,
											}))
										}
									/>
								</div>
							</div>

							<div className="space-y-2">
								<Label htmlFor="contactNumber">Contact Number *</Label>
								<Input
									id="contactNumber"
									value={newPatientData.contactNumber}
									onChange={(e) =>
										setNewPatientData((prev) => ({
											...prev,
											contactNumber: e.target.value,
										}))
									}
									placeholder="Enter contact number"
								/>
							</div>

							<div className="space-y-2">
								<Label htmlFor="address">Address *</Label>
								<Textarea
									id="address"
									value={newPatientData.address}
									onChange={(e) =>
										setNewPatientData((prev) => ({
											...prev,
											address: e.target.value,
										}))
									}
									placeholder="Enter complete address"
									rows={3}
								/>
							</div>
						</TabsContent>
					</Tabs>

					{/* Referral Information */}
					<div className="space-y-4">
						<h4 className="font-medium">Referral Information</h4>

						<div className="space-y-2">
							<Label htmlFor="referredDoctor">Referred Doctor Name *</Label>
							<Input
								id="referredDoctor"
								value={referralData.referredDoctorName}
								onChange={(e) =>
									setReferralData((prev) => ({
										...prev,
										referredDoctorName: e.target.value,
									}))
								}
								placeholder="Enter the name of the doctor you're referring to"
							/>
						</div>

						<div className="space-y-2">
							<Label htmlFor="purpose">Purpose of Referral *</Label>
							<Textarea
								id="purpose"
								value={referralData.purposeOfReferral}
								onChange={(e) =>
									setReferralData((prev) => ({
										...prev,
										purposeOfReferral: e.target.value,
									}))
								}
								placeholder="Describe the reason for this referral"
								rows={4}
							/>
						</div>
					</div>
				</div>

				<DialogFooter>
					<Button variant="outline" onClick={handleClose}>
						Cancel
					</Button>
					<Button
						onClick={handleSubmit}
						disabled={!isFormValid() || isSubmitting}
						className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
					>
						{isSubmitting ? 'Creating...' : 'Create Referral'}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
