'use client';

import { useRef } from 'react';
import { useReactToPrint } from 'react-to-print';

import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { useReferralDetail } from '@/features/dashboard/hooks/useDoctorAssistantDashboard';

import { ReferralPrint } from '../../shared/role-aware-referral-print';

interface IReferralPrintDialogProps {
	referralId: number;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onClose: () => void;
}

export function ReferralPrintDialog({
	referralId,
	open,
	onOpenChange,
}: Omit<IReferralPrintDialogProps, 'onClose'>) {
	// Fetch referral details
	const { data: referralResponse } = useReferralDetail(referralId, {
		enabled: open,
	});

	const referral = referralResponse?.data;

	const componentRef = useRef<HTMLDivElement | null>(null);
	const handlePrint = useReactToPrint({
		contentRef: componentRef,
		documentTitle: 'Print This Document',
		onBeforePrint: async () => console.log('before printing...'),
		onAfterPrint: () => console.log('after printing...'),
	});

	const handleSubmit = async () => {
		handlePrint();
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogHeader className="hidden">
				<DialogTitle>Referral Details</DialogTitle>
				<DialogDescription>Print referral #{referralId}</DialogDescription>
			</DialogHeader>
			<DialogContent className="max-w-2xl">
				{referral && (
					<ReferralPrint data={referral} componentRef={componentRef} />
				)}
				<Button onClick={handleSubmit}>Print</Button>
			</DialogContent>
		</Dialog>
	);
}
