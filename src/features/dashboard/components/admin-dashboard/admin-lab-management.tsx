'use client';

import { <PERSON>aker, Plus, Search, Settings } from 'lucide-react';
import { useState } from 'react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';

// Mock data for lab management - replace with actual API call
const mockLabData = [
	{
		id: 1,
		name: 'Complete Blood Count (CBC)',
		category: 'Hematology',
		price: 500,
		description:
			'Comprehensive blood analysis including RBC, WBC, and platelet count',
		isActive: true,
		createdAt: '2024-01-15',
	},
	{
		id: 2,
		name: 'Lipid Profile',
		category: 'Chemistry',
		price: 800,
		description: 'Cholesterol and triglyceride levels assessment',
		isActive: true,
		createdAt: '2024-01-10',
	},
	{
		id: 3,
		name: 'Thyroid Function Test',
		category: 'Endocrinology',
		price: 1200,
		description: 'TSH, T3, and T4 hormone levels',
		isActive: true,
		createdAt: '2024-01-05',
	},
];

export function AdminLabManagement() {
	const [searchTerm, setSearchTerm] = useState('');
	const isLoading = false; // Replace with actual loading state

	const filteredLabTests = mockLabData.filter(
		(test) =>
			test.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			test.category.toLowerCase().includes(searchTerm.toLowerCase())
	);

	const totalTests = mockLabData.length;
	const activeTests = mockLabData.filter((test) => test.isActive).length;
	const categories = [...new Set(mockLabData.map((test) => test.category))]
		.length;

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
						Lab Management
					</h1>
					<p className="text-muted-foreground">
						Manage laboratory tests, pricing, and categories
					</p>
				</div>
				<div className="flex items-center gap-2">
					<Button variant="outline">
						<Settings className="mr-2 h-4 w-4" />
						Lab Settings
					</Button>
					<Button>
						<Plus className="mr-2 h-4 w-4" />
						Add Lab Test
					</Button>
				</div>
			</div>

			{/* Stats Cards */}
			<div className="grid gap-4 md:grid-cols-4">
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Beaker className="h-5 w-5" />
							Total Tests
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="py-4 text-center">
							{isLoading ? (
								<Skeleton className="mx-auto h-8 w-16" />
							) : (
								<h3 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
									{totalTests}
								</h3>
							)}
							<p className="text-muted-foreground text-sm">Available tests</p>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Beaker className="h-5 w-5 text-green-600" />
							Active Tests
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="py-4 text-center">
							{isLoading ? (
								<Skeleton className="mx-auto h-8 w-16" />
							) : (
								<h3 className="text-2xl font-bold text-green-600">
									{activeTests}
								</h3>
							)}
							<p className="text-muted-foreground text-sm">Currently offered</p>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Settings className="h-5 w-5 text-blue-600" />
							Categories
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="py-4 text-center">
							{isLoading ? (
								<Skeleton className="mx-auto h-8 w-16" />
							) : (
								<h3 className="text-2xl font-bold text-blue-600">
									{categories}
								</h3>
							)}
							<p className="text-muted-foreground text-sm">Test categories</p>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Beaker className="h-5 w-5 text-purple-600" />
							Avg. Price
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="py-4 text-center">
							{isLoading ? (
								<Skeleton className="mx-auto h-8 w-20" />
							) : (
								<h3 className="text-2xl font-bold text-purple-600">
									₱
									{Math.round(
										mockLabData.reduce((sum, test) => sum + test.price, 0) /
											mockLabData.length
									).toLocaleString()}
								</h3>
							)}
							<p className="text-muted-foreground text-sm">Average cost</p>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Search and Filters */}
			<div className="flex items-center gap-4">
				<div className="relative flex-1">
					<Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
					<Input
						placeholder="Search lab tests..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="pl-10"
					/>
				</div>
			</div>

			{/* Lab Tests Table */}
			<Card>
				<CardHeader>
					<CardTitle>Laboratory Tests</CardTitle>
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="space-y-4">
							{[...Array(5)].map((_, i) => (
								<Skeleton key={i} className="h-12 w-full" />
							))}
						</div>
					) : (
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Test Name</TableHead>
									<TableHead>Category</TableHead>
									<TableHead>Price</TableHead>
									<TableHead>Description</TableHead>
									<TableHead>Status</TableHead>
									<TableHead>Actions</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{filteredLabTests.length === 0 ? (
									<TableRow>
										<TableCell colSpan={6} className="py-8 text-center">
											<div className="text-muted-foreground">
												{searchTerm
													? 'No lab tests match your search'
													: 'No lab tests found'}
											</div>
										</TableCell>
									</TableRow>
								) : (
									filteredLabTests.map((test) => (
										<TableRow key={test.id}>
											<TableCell className="font-medium">{test.name}</TableCell>
											<TableCell>{test.category}</TableCell>
											<TableCell>₱{test.price.toLocaleString()}</TableCell>
											<TableCell className="max-w-xs truncate">
												{test.description}
											</TableCell>
											<TableCell>
												<span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
													Active
												</span>
											</TableCell>
											<TableCell>
												<div className="flex items-center gap-2">
													<Button variant="outline" size="sm">
														Edit
													</Button>
													<Button variant="outline" size="sm">
														Settings
													</Button>
												</div>
											</TableCell>
										</TableRow>
									))
								)}
							</TableBody>
						</Table>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
