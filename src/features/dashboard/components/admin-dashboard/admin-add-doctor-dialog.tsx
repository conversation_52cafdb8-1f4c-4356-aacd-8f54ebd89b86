'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Plus } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import { Form } from '@/components/ui/form';
import { IDoctorRegistrationRequest } from '@/core/api/auth';
import {
	IRegisterDoctor,
	RegisterDoctorSchema,
} from '@/core/api/registration/doctor.type';
import { useCreateDoctor } from '@/features/dashboard/hooks/useAdminDashboard';
import PersonalInfoForm from '@/features/registration/components/doctor/personal-info.form';
import ProfessionalInfoForm from '@/features/registration/components/doctor/professional-info.form';

interface IAdminAddDoctorDialogProps {
	children?: React.ReactNode;
}

export function AdminAddDoctorDialog({ children }: IAdminAddDoctorDialogProps) {
	const [open, setOpen] = useState(false);
	const { mutate: createDoctor, isPending: isLoading } = useCreateDoctor();

	const form = useForm<IRegisterDoctor>({
		resolver: zodResolver(RegisterDoctorSchema),
		defaultValues: {
			// Personal Info
			firstName: '',
			middleName: '',
			lastName: '',
			suffix: '',
			email: '',
			phone: '',
			gender: '',
			birthday: '',

			// Professional Info
			prcNumber: '',
			prcExpiryDate: '',
			prcImageFront: undefined,
			prcImageBack: undefined,
			clinicAddress: '',
			clinicName: '',
			specialty: '',
		},
	});

	const onSubmit = (data: IRegisterDoctor) => {
		const registrationData: IDoctorRegistrationRequest = {
			firstName: data.firstName,
			middleName: data.middleName,
			lastName: data.lastName,
			suffix: data.suffix,
			email: data.email,
			phone: data.phone,
			gender: data.gender,
			birthday: data.birthday,
			prcNumber: data.prcNumber,
			prcExpiryDate: data.prcExpiryDate,
			prcImageFront: data.prcImageFront,
			prcImageBack: data.prcImageBack,
			clinicAddress: data.clinicAddress,
			clinicName: data.clinicName,
			specialty: data.specialty,
		};

		createDoctor(registrationData, {
			onSuccess: () => {
				setOpen(false);
				form.reset();
			},
		});
	};

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogTrigger asChild>
				{children || (
					<Button className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90">
						<Plus className="h-4 w-4" />
						Add Doctor
					</Button>
				)}
			</DialogTrigger>
			<DialogContent className="w-full max-w-2xl">
				<DialogHeader>
					<DialogTitle className="text-xl text-[oklch(0.7448_0.1256_202.74)]">
						Add New Doctor
					</DialogTitle>
					<DialogDescription>
						Create a new doctor account. The doctor will receive login
						credentials via email.
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
						{/* Personal Information Section */}
						<PersonalInfoForm form={form} />

						{/* Professional Information Section */}
						<ProfessionalInfoForm form={form} />

						{/* Submit Button */}
						<div className="flex justify-end space-x-2 pt-4">
							<Button
								type="button"
								variant="outline"
								onClick={() => setOpen(false)}
								disabled={isLoading}
							>
								Cancel
							</Button>
							<Button
								type="submit"
								disabled={isLoading}
								className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
							>
								{isLoading ? 'Creating...' : 'Create Doctor'}
							</Button>
						</div>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
