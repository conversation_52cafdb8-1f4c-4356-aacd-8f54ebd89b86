'use client';

import { <PERSON>, Mail, Save, Server, Settings, Shield } from 'lucide-react';
import { useState } from 'react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';

// Mock system configuration data
const mockSystemConfig = {
	general: {
		siteName: 'Elena Health Platform',
		siteDescription: 'Comprehensive healthcare management system',
		timezone: 'Asia/Manila',
		language: 'en',
		maintenanceMode: false,
	},
	email: {
		smtpHost: 'smtp.gmail.com',
		smtpPort: '587',
		smtpUsername: '<EMAIL>',
		smtpPassword: '••••••••',
		fromEmail: '<EMAIL>',
		fromName: 'Elena Health',
	},
	security: {
		sessionTimeout: '30',
		maxLoginAttempts: '5',
		passwordMinLength: '8',
		requireTwoFactor: false,
		allowRegistration: true,
	},
	notifications: {
		emailNotifications: true,
		smsNotifications: false,
		pushNotifications: true,
		appointmentReminders: true,
	},
	system: {
		backupFrequency: 'daily',
		logLevel: 'info',
		cacheEnabled: true,
		debugMode: false,
	},
};

export function AdminSystemConfig() {
	const [config, setConfig] = useState(mockSystemConfig);
	const [isLoading, setIsLoading] = useState(false);

	const handleSave = async () => {
		setIsLoading(true);
		// Simulate API call
		await new Promise((resolve) => setTimeout(resolve, 1000));
		setIsLoading(false);
		// Show success message
	};

	const updateConfig = (
		section: string,
		key: string,
		value: string | boolean
	) => {
		setConfig((prev) => ({
			...prev,
			[section]: {
				...prev[section as keyof typeof prev],
				[key]: value,
			},
		}));
	};

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
						System Configuration
					</h1>
					<p className="text-muted-foreground">
						Configure system-wide settings and preferences
					</p>
				</div>
				<Button onClick={handleSave} disabled={isLoading}>
					<Save className="mr-2 h-4 w-4" />
					{isLoading ? 'Saving...' : 'Save Changes'}
				</Button>
			</div>

			<div className="grid gap-6 md:grid-cols-2">
				{/* General Settings */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Settings className="h-5 w-5" />
							General Settings
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="space-y-2">
							<Label htmlFor="siteName">Site Name</Label>
							<Input
								id="siteName"
								value={config.general.siteName}
								onChange={(e) =>
									updateConfig('general', 'siteName', e.target.value)
								}
							/>
						</div>
						<div className="space-y-2">
							<Label htmlFor="siteDescription">Site Description</Label>
							<Textarea
								id="siteDescription"
								value={config.general.siteDescription}
								onChange={(e) =>
									updateConfig('general', 'siteDescription', e.target.value)
								}
							/>
						</div>
						<div className="space-y-2">
							<Label htmlFor="timezone">Timezone</Label>
							<Input
								id="timezone"
								value={config.general.timezone}
								onChange={(e) =>
									updateConfig('general', 'timezone', e.target.value)
								}
							/>
						</div>
						<div className="flex items-center justify-between">
							<Label htmlFor="maintenanceMode">Maintenance Mode</Label>
							<Switch
								id="maintenanceMode"
								checked={config.general.maintenanceMode}
								onCheckedChange={(checked) =>
									updateConfig('general', 'maintenanceMode', checked)
								}
							/>
						</div>
					</CardContent>
				</Card>

				{/* Email Configuration */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Mail className="h-5 w-5" />
							Email Configuration
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="space-y-2">
							<Label htmlFor="smtpHost">SMTP Host</Label>
							<Input
								id="smtpHost"
								value={config.email.smtpHost}
								onChange={(e) =>
									updateConfig('email', 'smtpHost', e.target.value)
								}
							/>
						</div>
						<div className="space-y-2">
							<Label htmlFor="smtpPort">SMTP Port</Label>
							<Input
								id="smtpPort"
								value={config.email.smtpPort}
								onChange={(e) =>
									updateConfig('email', 'smtpPort', e.target.value)
								}
							/>
						</div>
						<div className="space-y-2">
							<Label htmlFor="fromEmail">From Email</Label>
							<Input
								id="fromEmail"
								type="email"
								value={config.email.fromEmail}
								onChange={(e) =>
									updateConfig('email', 'fromEmail', e.target.value)
								}
							/>
						</div>
						<div className="space-y-2">
							<Label htmlFor="fromName">From Name</Label>
							<Input
								id="fromName"
								value={config.email.fromName}
								onChange={(e) =>
									updateConfig('email', 'fromName', e.target.value)
								}
							/>
						</div>
					</CardContent>
				</Card>

				{/* Security Settings */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Shield className="h-5 w-5" />
							Security Settings
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="space-y-2">
							<Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
							<Input
								id="sessionTimeout"
								type="number"
								value={config.security.sessionTimeout}
								onChange={(e) =>
									updateConfig('security', 'sessionTimeout', e.target.value)
								}
							/>
						</div>
						<div className="space-y-2">
							<Label htmlFor="maxLoginAttempts">Max Login Attempts</Label>
							<Input
								id="maxLoginAttempts"
								type="number"
								value={config.security.maxLoginAttempts}
								onChange={(e) =>
									updateConfig('security', 'maxLoginAttempts', e.target.value)
								}
							/>
						</div>
						<div className="flex items-center justify-between">
							<Label htmlFor="requireTwoFactor">Require Two-Factor Auth</Label>
							<Switch
								id="requireTwoFactor"
								checked={config.security.requireTwoFactor}
								onCheckedChange={(checked) =>
									updateConfig('security', 'requireTwoFactor', checked)
								}
							/>
						</div>
						<div className="flex items-center justify-between">
							<Label htmlFor="allowRegistration">Allow Registration</Label>
							<Switch
								id="allowRegistration"
								checked={config.security.allowRegistration}
								onCheckedChange={(checked) =>
									updateConfig('security', 'allowRegistration', checked)
								}
							/>
						</div>
					</CardContent>
				</Card>

				{/* Notification Settings */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Bell className="h-5 w-5" />
							Notification Settings
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="flex items-center justify-between">
							<Label htmlFor="emailNotifications">Email Notifications</Label>
							<Switch
								id="emailNotifications"
								checked={config.notifications.emailNotifications}
								onCheckedChange={(checked) =>
									updateConfig('notifications', 'emailNotifications', checked)
								}
							/>
						</div>
						<div className="flex items-center justify-between">
							<Label htmlFor="smsNotifications">SMS Notifications</Label>
							<Switch
								id="smsNotifications"
								checked={config.notifications.smsNotifications}
								onCheckedChange={(checked) =>
									updateConfig('notifications', 'smsNotifications', checked)
								}
							/>
						</div>
						<div className="flex items-center justify-between">
							<Label htmlFor="pushNotifications">Push Notifications</Label>
							<Switch
								id="pushNotifications"
								checked={config.notifications.pushNotifications}
								onCheckedChange={(checked) =>
									updateConfig('notifications', 'pushNotifications', checked)
								}
							/>
						</div>
						<div className="flex items-center justify-between">
							<Label htmlFor="appointmentReminders">
								Appointment Reminders
							</Label>
							<Switch
								id="appointmentReminders"
								checked={config.notifications.appointmentReminders}
								onCheckedChange={(checked) =>
									updateConfig('notifications', 'appointmentReminders', checked)
								}
							/>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* System Information */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Server className="h-5 w-5" />
						System Information
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid gap-4 md:grid-cols-3">
						<div className="space-y-2">
							<Label>Application Version</Label>
							<p className="text-muted-foreground text-sm">v2.1.0</p>
						</div>
						<div className="space-y-2">
							<Label>Database Version</Label>
							<p className="text-muted-foreground text-sm">PostgreSQL 14.2</p>
						</div>
						<div className="space-y-2">
							<Label>Last Backup</Label>
							<p className="text-muted-foreground text-sm">
								2024-01-15 03:00 AM
							</p>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
