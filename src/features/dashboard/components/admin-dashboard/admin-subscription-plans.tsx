'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryState } from 'nuqs';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { searchParamParsers } from '@/core/lib/search-params';
import {
	useSubscriptionsById,
	useUpdateSubscription,
} from '@/features/dashboard/hooks/useAdminDashboard';

import { Subscription } from '../../types/admin.types';

interface AdminSubscriptionPlansProps {
	subscriptionId?: number;
}

export function AdminSubscriptionPlans({
	subscriptionId: propSubscriptionId,
}: AdminSubscriptionPlansProps) {
	const [isEditing, setIsEditing] = useState(false);
	const [subscriptionIdParam] = useQueryState(
		'id',
		searchParamParsers.subscriptionId.withDefault(propSubscriptionId ?? 1)
	);

	const subscriptionId = subscriptionIdParam ?? propSubscriptionId;

	const { data: subscriptionResponse } = useSubscriptionsById({
		subscriptionId,
	});
	const sub = subscriptionResponse?.data as Subscription | undefined;

	const updateSubscriptionMutation = useUpdateSubscription();

	// Form validation schema
	const adminSubscriptionCreationSchema = z.object({
		// Basic subscription info
		name: z.string().min(1, 'Subscription Name is required'),
		description: z.string().optional(),
		price: z.string().min(1, 'Price is required'),
		discountedPrice: z.string().optional(),
		coverage: z.string().min(1, 'Coverage is required'),
		coverageType: z.string().min(1, 'Coverage Type is required'),
		isDiscountEnabled: z.union([z.literal(0), z.literal(1)]).optional(),
		isActive: z.union([z.literal(0), z.literal(1)]).optional(),

		// Patient settings
		patientEnabled: z.union([z.literal(0), z.literal(1)]).optional(),
		patientCount: z.string().optional(),
		patientLimitType: z.string().optional(),
		patientCoverageType: z.string().optional(),
		patientQrEnabled: z.union([z.literal(0), z.literal(1)]).optional(),

		// Appointment settings
		appointmentEnabled: z.union([z.literal(0), z.literal(1)]).optional(),
		appointmentCount: z.string().optional(),
		appointmentLimitType: z.string().optional(),
		appointmentCoverageType: z.string().optional(),
		appointmentQrEnabled: z.union([z.literal(0), z.literal(1)]).optional(),

		// Clinic settings
		clinicEnabled: z.union([z.literal(0), z.literal(1)]).optional(),
		clinicCount: z.string().optional(),
		clinicLimitType: z.string().optional(),

		// Clinic assistant settings
		clinicAssistantEnabled: z.union([z.literal(0), z.literal(1)]).optional(),
		clinicAssistantCount: z.string().optional(),
		clinicAssistantLimitType: z.string().optional(),

		// Medical document settings
		medicalDocumentEnabled: z.union([z.literal(0), z.literal(1)]).optional(),
	});

	type AdminSubscriptionCreationForm = z.infer<
		typeof adminSubscriptionCreationSchema
	>;

	const form = useForm<AdminSubscriptionCreationForm>({
		resolver: zodResolver(adminSubscriptionCreationSchema),
		defaultValues: {
			// Basic subscription info
			name: sub?.name ?? '',
			description: sub?.description ?? '',
			price: sub?.price?.toString() ?? '',
			discountedPrice: sub?.discounted_price?.toString() ?? '',
			coverage: sub?.coverage?.toString() ?? '',
			coverageType: sub?.coverage_type ?? '',
			isDiscountEnabled: (sub?.is_discount_enabled ?? 0) as 0 | 1,
			isActive: (sub?.is_active ?? 0) as 0 | 1,

			// Patient settings
			patientEnabled: (sub?.patient_enabled ?? 0) as 0 | 1,
			patientCount: sub?.patient_count?.toString() ?? '',
			patientLimitType: sub?.patient_limit_type ?? '',
			patientCoverageType: sub?.patient_coverage_type ?? '',
			patientQrEnabled: (sub?.patient_qr_enabled ?? 0) as 0 | 1,

			// Appointment settings
			appointmentEnabled: (sub?.appointment_enabled ?? 0) as 0 | 1,
			appointmentCount: sub?.appointment_count?.toString() ?? '',
			appointmentLimitType: sub?.appointment_limit_type ?? '',
			appointmentCoverageType: sub?.appointment_coverage_type ?? '',
			appointmentQrEnabled: (sub?.appointment_qr_enabled ?? 0) as 0 | 1,

			// Clinic settings
			clinicEnabled: (sub?.clinic_enabled ?? 0) as 0 | 1,
			clinicCount: sub?.clinic_count?.toString() ?? '',
			clinicLimitType: sub?.clinic_limit_type ?? '',

			// Clinic assistant settings
			clinicAssistantEnabled: (sub?.clinic_assistant_enabled ?? 0) as 0 | 1,
			clinicAssistantCount: sub?.clinic_assistant_count?.toString() ?? '',
			clinicAssistantLimitType: sub?.clinic_assistant_limit_type ?? '',

			// Medical document settings
			medicalDocumentEnabled: (sub?.medical_document_enabled ?? 0) as 0 | 1,
		},
	});

	// Update form values when subscription data changes
	useEffect(() => {
		if (sub) {
			form.reset({
				// Basic subscription info
				name: sub.name ?? '',
				description: sub.description ?? '',
				price: sub.price?.toString() ?? '',
				discountedPrice: sub.discounted_price?.toString() ?? '',
				coverage: sub.coverage?.toString() ?? '',
				coverageType: sub.coverage_type ?? '',
				isDiscountEnabled: (sub.is_discount_enabled ?? 0) as 0 | 1,
				isActive: (sub.is_active ?? 0) as 0 | 1,

				// Patient settings
				patientEnabled: (sub.patient_enabled ?? 0) as 0 | 1,
				patientCount: sub.patient_count?.toString() ?? '',
				patientLimitType: sub.patient_limit_type ?? '',
				patientCoverageType: sub.patient_coverage_type ?? '',
				patientQrEnabled: (sub.patient_qr_enabled ?? 0) as 0 | 1,

				// Appointment settings
				appointmentEnabled: (sub.appointment_enabled ?? 0) as 0 | 1,
				appointmentCount: sub.appointment_count?.toString() ?? '',
				appointmentLimitType: sub.appointment_limit_type ?? '',
				appointmentCoverageType: sub.appointment_coverage_type ?? '',
				appointmentQrEnabled: (sub.appointment_qr_enabled ?? 0) as 0 | 1,

				// Clinic settings
				clinicEnabled: (sub.clinic_enabled ?? 0) as 0 | 1,
				clinicCount: sub.clinic_count?.toString() ?? '',
				clinicLimitType: sub.clinic_limit_type ?? '',

				// Clinic assistant settings
				clinicAssistantEnabled: (sub.clinic_assistant_enabled ?? 0) as 0 | 1,
				clinicAssistantCount: sub.clinic_assistant_count?.toString() ?? '',
				clinicAssistantLimitType: sub.clinic_assistant_limit_type ?? '',

				// Medical document settings
				medicalDocumentEnabled: (sub.medical_document_enabled ?? 0) as 0 | 1,
			});
		}
	}, [sub, form]);

	const handleEdit = () => {
		setIsEditing(true);
	};

	const onSubmit = (data: AdminSubscriptionCreationForm) => {
		if (!subscriptionId) return;

		const subscriptionData = {
			name: data.name,
			description: data.description,
			price: parseFloat(data.price),
			discountedPrice: data.discountedPrice
				? parseFloat(data.discountedPrice)
				: undefined,
			coverage: parseInt(data.coverage),
			coverageType: data.coverageType,
			isActive: data.isActive,
			isDiscountEnabled: data.isDiscountEnabled,
			// Patient settings
			patientEnabled: data.patientEnabled,
			patientCount: data.patientCount ? parseInt(data.patientCount) : undefined,
			patientLimitType: data.patientLimitType,
			patientCoverageType: data.patientCoverageType,
			patientQrEnabled: data.patientQrEnabled,
			// Appointment settings
			appointmentEnabled: data.appointmentEnabled,
			appointmentCount: data.appointmentCount
				? parseInt(data.appointmentCount)
				: undefined,
			appointmentLimitType: data.appointmentLimitType,
			appointmentCoverageType: data.appointmentCoverageType,
			appointmentQrEnabled: data.appointmentQrEnabled,
			// Clinic settings
			clinicEnabled: data.clinicEnabled,
			clinicCount: data.clinicCount ? parseInt(data.clinicCount) : undefined,
			clinicLimitType: data.clinicLimitType,
			// Clinic assistant settings
			clinicAssistantEnabled: data.clinicAssistantEnabled,
			clinicAssistantCount: data.clinicAssistantCount
				? parseInt(data.clinicAssistantCount)
				: undefined,
			clinicAssistantLimitType: data.clinicAssistantLimitType,
			// Medical document settings
			medicalDocumentEnabled: data.medicalDocumentEnabled,
		};

		updateSubscriptionMutation.mutate(
			{
				subscriptionId,
				subscriptionData,
			},
			{
				onSuccess: () => {
					setIsEditing(false);
				},
			}
		);
	};

	const handleCancel = () => {
		// Reset form to original values and disable editing
		if (sub) {
			form.reset({
				// Basic subscription info
				name: sub.name ?? '',
				description: sub.description ?? '',
				price: sub.price?.toString() ?? '',
				discountedPrice: sub.discounted_price?.toString() ?? '',
				coverage: sub.coverage?.toString() ?? '',
				coverageType: sub.coverage_type ?? '',
				isDiscountEnabled: (sub.is_discount_enabled ?? 0) as 0 | 1,
				isActive: (sub.is_active ?? 0) as 0 | 1,

				// Patient settings
				patientEnabled: (sub.patient_enabled ?? 0) as 0 | 1,
				patientCount: sub.patient_count?.toString() ?? '',
				patientLimitType: sub.patient_limit_type ?? '',
				patientCoverageType: sub.patient_coverage_type ?? '',
				patientQrEnabled: (sub.patient_qr_enabled ?? 0) as 0 | 1,

				// Appointment settings
				appointmentEnabled: (sub.appointment_enabled ?? 0) as 0 | 1,
				appointmentCount: sub.appointment_count?.toString() ?? '',
				appointmentLimitType: sub.appointment_limit_type ?? '',
				appointmentCoverageType: sub.appointment_coverage_type ?? '',
				appointmentQrEnabled: (sub.appointment_qr_enabled ?? 0) as 0 | 1,

				// Clinic settings
				clinicEnabled: (sub.clinic_enabled ?? 0) as 0 | 1,
				clinicCount: sub.clinic_count?.toString() ?? '',
				clinicLimitType: sub.clinic_limit_type ?? '',

				// Clinic assistant settings
				clinicAssistantEnabled: (sub.clinic_assistant_enabled ?? 0) as 0 | 1,
				clinicAssistantCount: sub.clinic_assistant_count?.toString() ?? '',
				clinicAssistantLimitType: sub.clinic_assistant_limit_type ?? '',

				// Medical document settings
				medicalDocumentEnabled: (sub.medical_document_enabled ?? 0) as 0 | 1,
			});
		}
		setIsEditing(false);
	};

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
				<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
					{/* Header */}
					<div className="flex items-center justify-between">
						<h1 className="font-bold text-[#797979] uppercase">
							Subscription Info
						</h1>
						<div className="flex gap-2">
							{!isEditing ? (
								<Button
									type="button"
									onClick={handleEdit}
									className="bg-[oklch(0.7448_0.1256_202.74)] px-8 hover:bg-[oklch(0.7448_0.1256_202.74)]/50"
								>
									Edit
								</Button>
							) : (
								<>
									<Button
										type="button"
										variant="outline"
										onClick={handleCancel}
										className="px-6"
									>
										Cancel
									</Button>
									<Button
										type="submit"
										disabled={updateSubscriptionMutation.isPending}
										className="bg-[oklch(0.7448_0.1256_202.74)] px-8 hover:bg-[oklch(0.7448_0.1256_202.74)]/50"
									>
										{updateSubscriptionMutation.isPending
											? 'Saving...'
											: 'Save'}
									</Button>
								</>
							)}
						</div>
					</div>

					{/* Subscription Card */}
					<div className="relative overflow-hidden rounded-lg border shadow-lg">
						<div className="absolute inset-0 h-[180px] bg-[url('/loginbg.svg')] bg-cover bg-center bg-no-repeat opacity-30" />
						<div className="flex h-[180px] w-full flex-col items-center justify-center bg-[oklch(0.7448_0.1256_202.74)]/80">
							<h1 className="text-2xl font-bold text-white uppercase">
								{sub?.name}
							</h1>
							<p className="font-medium text-white">{sub?.description}</p>
						</div>

						<div className="grid grid-cols-1 gap-4 p-6">
							<div className="space-y-4">
								<FormField
									control={form.control}
									name="name"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Subscription Name</FormLabel>
											<FormControl>
												<Input
													placeholder="Enter subscription name"
													disabled={!isEditing}
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
									<FormField
										control={form.control}
										name="price"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Subscription Price</FormLabel>
												<FormControl>
													<Input
														type="number"
														placeholder="Enter Price"
														disabled={!isEditing}
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="discountedPrice"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Discounted Subscription Price</FormLabel>
												<FormControl>
													<Input
														placeholder="Enter Discounted Price"
														disabled={!isEditing}
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>

								<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
									<FormField
										control={form.control}
										name="coverage"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Coverage</FormLabel>
												<FormControl>
													<Input
														type="number"
														placeholder="Enter Coverage"
														disabled={!isEditing}
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="coverageType"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Coverage Type</FormLabel>
												<Select
													onValueChange={field.onChange}
													value={field.value}
													disabled={!isEditing}
												>
													<FormControl>
														<SelectTrigger className="w-full">
															<SelectValue placeholder="Select coverage type" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value="DAY">Day</SelectItem>
														<SelectItem value="WEEK">Week</SelectItem>
														<SelectItem value="MONTH">Month</SelectItem>
														<SelectItem value="YEAR">Year</SelectItem>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>

								<FormField
									control={form.control}
									name="description"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Description</FormLabel>
											<FormControl>
												<Textarea
													className="resize-none"
													placeholder="Enter Description"
													disabled={!isEditing}
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
									<FormField
										control={form.control}
										name="isDiscountEnabled"
										render={({ field }) => (
											<FormItem>
												<div className="flex items-center space-x-2">
													<FormControl>
														<Switch
															id="enableDiscount"
															checked={field.value === 1}
															onCheckedChange={(checked) =>
																field.onChange(checked ? 1 : 0)
															}
															disabled={!isEditing}
														/>
													</FormControl>
													<Label htmlFor="enableDiscount">
														Enable Discount
													</Label>
												</div>
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="isActive"
										render={({ field }) => (
											<FormItem>
												<div className="flex items-center space-x-2">
													<FormControl>
														<Switch
															id="isActive"
															checked={field.value === 1}
															onCheckedChange={(checked) =>
																field.onChange(checked ? 1 : 0)
															}
															disabled={!isEditing}
														/>
													</FormControl>
													<Label htmlFor="isActive">Enable Subscription</Label>
												</div>
											</FormItem>
										)}
									/>
								</div>
							</div>
						</div>
					</div>

					<div className="flex items-center justify-between">
						<h1 className="font-bold text-[#797979] uppercase">
							Subscription Settings
						</h1>
					</div>

					<div className="relative space-y-8 overflow-hidden rounded-lg border p-4 shadow-lg">
						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<FormField
								control={form.control}
								name="patientEnabled"
								render={({ field }) => (
									<FormItem>
										<div className="flex items-center space-x-2">
											<FormControl>
												<Switch
													id="patient"
													checked={field.value === 1}
													onCheckedChange={(checked) =>
														field.onChange(checked ? 1 : 0)
													}
													disabled={!isEditing}
												/>
											</FormControl>
											<Label htmlFor="patient">Patient</Label>
										</div>
									</FormItem>
								)}
							/>

							{form.watch('patientEnabled') === 1 && (
								<div className="grid gap-4 md:grid-cols-3">
									<FormField
										control={form.control}
										name="patientCount"
										render={({ field }) => (
											<FormItem>
												<FormControl>
													<Input
														type="number"
														placeholder="Enter Limit"
														disabled={!isEditing}
														{...field}
													/>
												</FormControl>
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="patientCoverageType"
										render={({ field }) => (
											<FormItem>
												<Select
													onValueChange={field.onChange}
													value={field.value}
													disabled={!isEditing}
												>
													<FormControl>
														<SelectTrigger className="w-full">
															<SelectValue placeholder="Coverage Type" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value="MONTHLY">Monthly</SelectItem>
														<SelectItem value="YEARLY">Yearly</SelectItem>
													</SelectContent>
												</Select>
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="patientLimitType"
										render={({ field }) => (
											<FormItem>
												<Select
													onValueChange={field.onChange}
													value={field.value || undefined}
													disabled={!isEditing}
												>
													<FormControl>
														<SelectTrigger className="w-full">
															<SelectValue placeholder="Limit Type" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value="LIMITED">Limited</SelectItem>
														<SelectItem value="UNLIMITED">Unlimited</SelectItem>
													</SelectContent>
												</Select>
											</FormItem>
										)}
									/>
								</div>
							)}
						</div>

						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<FormField
								control={form.control}
								name="patientQrEnabled"
								render={({ field }) => (
									<FormItem>
										<div className="flex items-center space-x-2">
											<FormControl>
												<Switch
													id="appointment-via-qr-patient"
													checked={field.value === 1}
													onCheckedChange={(checked) =>
														field.onChange(checked ? 1 : 0)
													}
													disabled={!isEditing}
												/>
											</FormControl>
											<Label htmlFor="appointment-via-qr-patient">
												Add Appointment via QR Code for Patient
											</Label>
										</div>
									</FormItem>
								)}
							/>
						</div>

						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<FormField
								control={form.control}
								name="appointmentEnabled"
								render={({ field }) => (
									<FormItem>
										<div className="flex items-center space-x-2">
											<FormControl>
												<Switch
													id="appointment"
													checked={field.value === 1}
													onCheckedChange={(checked) =>
														field.onChange(checked ? 1 : 0)
													}
													disabled={!isEditing}
												/>
											</FormControl>
											<Label htmlFor="appointment">Appointment</Label>
										</div>
									</FormItem>
								)}
							/>

							{form.watch('appointmentEnabled') === 1 && (
								<div className="grid gap-4 md:grid-cols-3">
									<FormField
										control={form.control}
										name="appointmentCount"
										render={({ field }) => (
											<FormItem>
												<FormControl>
													<Input
														type="number"
														placeholder="Enter Limit"
														className="w-full"
														disabled={!isEditing}
														{...field}
													/>
												</FormControl>
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="appointmentCoverageType"
										render={({ field }) => (
											<FormItem>
												<Select
													onValueChange={field.onChange}
													value={field.value}
													disabled={!isEditing}
												>
													<FormControl>
														<SelectTrigger className="w-full">
															<SelectValue placeholder="Coverage Type" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value="MONTHLY">Monthly</SelectItem>
														<SelectItem value="YEARLY">Yearly</SelectItem>
													</SelectContent>
												</Select>
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="appointmentLimitType"
										render={({ field }) => (
											<FormItem>
												<Select
													onValueChange={field.onChange}
													value={field.value}
													disabled={!isEditing}
												>
													<FormControl>
														<SelectTrigger className="w-full">
															<SelectValue placeholder="Limit Type" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value="LIMITED">Limited</SelectItem>
														<SelectItem value="UNLIMITED">Unlimited</SelectItem>
													</SelectContent>
												</Select>
											</FormItem>
										)}
									/>
								</div>
							)}
						</div>

						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<FormField
								control={form.control}
								name="appointmentQrEnabled"
								render={({ field }) => (
									<FormItem>
										<div className="flex items-center space-x-2">
											<FormControl>
												<Switch
													id="appointment-via-qr"
													checked={field.value === 1}
													onCheckedChange={(checked) =>
														field.onChange(checked ? 1 : 0)
													}
													disabled={!isEditing}
												/>
											</FormControl>
											<Label htmlFor="appointment-via-qr">
												Add Appointment via QR Code
											</Label>
										</div>
									</FormItem>
								)}
							/>
						</div>

						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<FormField
								control={form.control}
								name="clinicEnabled"
								render={({ field }) => (
									<FormItem>
										<div className="flex items-center space-x-2">
											<FormControl>
												<Switch
													id="clinic"
													checked={field.value === 1}
													onCheckedChange={(checked) =>
														field.onChange(checked ? 1 : 0)
													}
													disabled={!isEditing}
												/>
											</FormControl>
											<Label htmlFor="clinic">Clinic</Label>
										</div>
									</FormItem>
								)}
							/>

							{form.watch('clinicEnabled') === 1 && (
								<div className="grid gap-4 md:grid-cols-2">
									<FormField
										control={form.control}
										name="clinicCount"
										render={({ field }) => (
											<FormItem>
												<FormControl>
													<Input
														type="number"
														placeholder="Enter Limit"
														className="w-full"
														disabled={!isEditing}
														{...field}
													/>
												</FormControl>
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="clinicLimitType"
										render={({ field }) => (
											<FormItem>
												<Select
													onValueChange={field.onChange}
													value={field.value}
													disabled={!isEditing}
												>
													<FormControl>
														<SelectTrigger className="w-full">
															<SelectValue placeholder="Limit Type" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value="LIMITED">Limited</SelectItem>
														<SelectItem value="UNLIMITED">Unlimited</SelectItem>
													</SelectContent>
												</Select>
											</FormItem>
										)}
									/>
								</div>
							)}
						</div>

						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<FormField
								control={form.control}
								name="clinicAssistantEnabled"
								render={({ field }) => (
									<FormItem>
										<div className="flex items-center space-x-2">
											<FormControl>
												<Switch
													id="clinic-assistant"
													checked={field.value === 1}
													onCheckedChange={(checked) =>
														field.onChange(checked ? 1 : 0)
													}
													disabled={!isEditing}
												/>
											</FormControl>
											<Label htmlFor="clinic-assistant">Clinic Assistant</Label>
										</div>
									</FormItem>
								)}
							/>

							{form.watch('clinicAssistantEnabled') === 1 && (
								<div className="grid gap-4 md:grid-cols-2">
									<FormField
										control={form.control}
										name="clinicAssistantCount"
										render={({ field }) => (
											<FormItem>
												<FormControl>
													<Input
														type="number"
														placeholder="Enter Limit"
														className="w-full"
														disabled={!isEditing}
														{...field}
													/>
												</FormControl>
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="clinicAssistantLimitType"
										render={({ field }) => (
											<FormItem>
												<Select
													onValueChange={field.onChange}
													value={field.value}
													disabled={!isEditing}
												>
													<FormControl>
														<SelectTrigger className="w-full">
															<SelectValue placeholder="Limit Type" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value="LIMITED">Limited</SelectItem>
														<SelectItem value="UNLIMITED">Unlimited</SelectItem>
													</SelectContent>
												</Select>
											</FormItem>
										)}
									/>
								</div>
							)}
						</div>

						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<FormField
								control={form.control}
								name="medicalDocumentEnabled"
								render={({ field }) => (
									<FormItem>
										<div className="flex items-center space-x-2">
											<FormControl>
												<Switch
													id="medical-documents"
													checked={field.value === 1}
													onCheckedChange={(checked) =>
														field.onChange(checked ? 1 : 0)
													}
													disabled={!isEditing}
												/>
											</FormControl>
											<Label htmlFor="medical-documents">
												Medical Documents
											</Label>
										</div>
									</FormItem>
								)}
							/>
						</div>
					</div>
				</div>
			</form>
		</Form>
	);
}
