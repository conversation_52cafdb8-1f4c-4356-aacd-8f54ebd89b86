'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { useAllSubscriptions } from '@/features/dashboard/hooks/useAdminDashboard';
import { Subscription } from '@/features/dashboard/types/admin.types';

import { AdminAddSubscriptionDialog } from './admin-add-subscription-dialog';
import SubscriptionTable from './table';
import { columns } from './table/column';

export function AdminSubscriptionManagement() {
	const { data: subscriptions, error } = useAllSubscriptions();

	const subscriptionData: Subscription[] = subscriptions?.data || [];

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			<div className="flex justify-between md:items-center">
				<div>
					<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
						Subscription
					</h1>
					<p className="text-muted-foreground">
						Manage subscription plans and monitor revenue
					</p>
				</div>
				<AdminAddSubscriptionDialog />
			</div>

			<Card>
				<CardHeader>
					<CardTitle>Subscription Plans</CardTitle>
				</CardHeader>
				<CardContent>
					{error ? (
						<div className="py-8 text-center text-red-500">
							Error loading subscription plans. Please try again.
						</div>
					) : (
						<div className="">
							<SubscriptionTable
								columns={columns}
								data={subscriptionData}
								metadata={''}
							/>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
