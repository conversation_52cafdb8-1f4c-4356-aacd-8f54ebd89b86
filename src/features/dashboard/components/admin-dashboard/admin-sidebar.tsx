'use client';

import { BarChart3, Database, Stethoscope, UserCheck } from 'lucide-react';
import { useRouter } from 'next/navigation';
import * as React from 'react';

import {
	Sidebar,
	SidebarContent,
	SidebarFooter,
	SidebarHeader,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
} from '@/components/ui/sidebar';
import { useSession } from '@/core/hooks/useSession';
import { NavMain } from '@/features/dashboard/components/nav-main';
import { NavUser } from '@/features/dashboard/components/nav-user';

const adminNavData = {
	navMain: [
		{
			title: 'Dashboard',
			url: '/dashboard',
			icon: BarChart3,
			isActive: true,
		},
		{
			title: 'Doctor Management',
			url: '/dashboard?tab=all-doctors',
			icon: Stethoscope,
		},
		{
			title: 'Subscription Management',
			url: '/dashboard?tab=subscriptions',
			icon: UserCheck,
		},
		{
			title: 'Platform Data',
			url: '/dashboard?tab=platform-data',
			icon: Database,
		},
	],
	// navSecondary: [
	// 	{
	// 		title: 'Audit Logs',
	// 		url: '/dashboard?tab=audit-logs',
	// 		icon: FileText,
	// 	},
	// ],
};

export function AdminSidebar({
	...props
}: React.ComponentProps<typeof Sidebar>) {
	const { user } = useSession();
	const router = useRouter();

	return (
		<Sidebar variant="inset" {...props}>
			<SidebarHeader>
				<SidebarMenu>
					<SidebarMenuItem>
						<SidebarMenuButton
							size="lg"
							onClick={() => router.push('/dashboard')}
						>
							<div className="flex w-full items-center rounded-lg">
								<img src="/elena-banner.png" alt="Elena" className="h-9" />
							</div>
						</SidebarMenuButton>
					</SidebarMenuItem>
				</SidebarMenu>
			</SidebarHeader>
			<SidebarContent>
				<NavMain items={adminNavData.navMain} />
				{/* <NavSecondary items={adminNavData.navSecondary} className="mt-auto" /> */}
			</SidebarContent>
			<SidebarFooter>
				<NavUser
					user={{
						name: user ? `${user.first_name} ${user.last_name}` : 'Admin User',
						email: user?.email || '<EMAIL>',
						avatar: '/avatars/admin.jpg',
					}}
				/>
			</SidebarFooter>
		</Sidebar>
	);
}
