'use client';

import { Calendar, Gift, Plus, Search, Trash2 } from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';

// Mock data for gift codes - replace with actual API call
const mockGiftCodes = [
	{
		id: 1,
		code: 'ELENA2024',
		subscriptionPlan: 'Premium Plan',
		expiresAt: '2024-12-31',
		isActive: true,
		isUsed: false,
		usedBy: null,
		createdAt: '2024-01-15',
	},
	{
		id: 2,
		code: 'WELCOME50',
		subscriptionPlan: 'Basic Plan',
		expiresAt: '2024-06-30',
		isActive: true,
		isUsed: true,
		usedBy: '<EMAIL>',
		createdAt: '2024-01-10',
	},
	{
		id: 3,
		code: 'PROMO2024',
		subscriptionPlan: 'Premium Plan',
		expiresAt: '2024-03-31',
		isActive: false,
		isUsed: false,
		usedBy: null,
		createdAt: '2024-01-05',
	},
];

export function AdminGiftCodes() {
	const [searchTerm, setSearchTerm] = useState('');
	const isLoading = false; // Replace with actual loading state

	const filteredGiftCodes = mockGiftCodes.filter(
		(code) =>
			code.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
			code.subscriptionPlan.toLowerCase().includes(searchTerm.toLowerCase())
	);

	const activeCount = mockGiftCodes.filter(
		(code) => code.isActive && !code.isUsed
	).length;
	const usedCount = mockGiftCodes.filter((code) => code.isUsed).length;
	const expiredCount = mockGiftCodes.filter((code) => {
		const expiry = new Date(code.expiresAt);
		return expiry < new Date() && !code.isUsed;
	}).length;

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
						Gift Codes
					</h1>
					<p className="text-muted-foreground">
						Manage subscription gift codes and promotional offers
					</p>
				</div>
				<Button>
					<Plus className="mr-2 h-4 w-4" />
					Create Gift Code
				</Button>
			</div>

			{/* Stats Cards */}
			<div className="grid gap-4 md:grid-cols-4">
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Gift className="h-5 w-5" />
							Total Codes
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="py-4 text-center">
							{isLoading ? (
								<Skeleton className="mx-auto h-8 w-16" />
							) : (
								<h3 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
									{mockGiftCodes.length}
								</h3>
							)}
							<p className="text-muted-foreground text-sm">All gift codes</p>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Gift className="h-5 w-5 text-green-600" />
							Active Codes
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="py-4 text-center">
							{isLoading ? (
								<Skeleton className="mx-auto h-8 w-16" />
							) : (
								<h3 className="text-2xl font-bold text-green-600">
									{activeCount}
								</h3>
							)}
							<p className="text-muted-foreground text-sm">Available for use</p>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Gift className="h-5 w-5 text-blue-600" />
							Used Codes
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="py-4 text-center">
							{isLoading ? (
								<Skeleton className="mx-auto h-8 w-16" />
							) : (
								<h3 className="text-2xl font-bold text-blue-600">
									{usedCount}
								</h3>
							)}
							<p className="text-muted-foreground text-sm">
								Successfully redeemed
							</p>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Calendar className="h-5 w-5 text-red-600" />
							Expired Codes
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="py-4 text-center">
							{isLoading ? (
								<Skeleton className="mx-auto h-8 w-16" />
							) : (
								<h3 className="text-2xl font-bold text-red-600">
									{expiredCount}
								</h3>
							)}
							<p className="text-muted-foreground text-sm">Past expiry date</p>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Search and Filters */}
			<div className="flex items-center gap-4">
				<div className="relative flex-1">
					<Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
					<Input
						placeholder="Search gift codes..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="pl-10"
					/>
				</div>
			</div>

			{/* Gift Codes Table */}
			<Card>
				<CardHeader>
					<CardTitle>All Gift Codes</CardTitle>
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="space-y-4">
							{[...Array(5)].map((_, i) => (
								<Skeleton key={i} className="h-12 w-full" />
							))}
						</div>
					) : (
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Code</TableHead>
									<TableHead>Subscription Plan</TableHead>
									<TableHead>Status</TableHead>
									<TableHead>Expires At</TableHead>
									<TableHead>Used By</TableHead>
									<TableHead>Created At</TableHead>
									<TableHead>Actions</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{filteredGiftCodes.length === 0 ? (
									<TableRow>
										<TableCell colSpan={7} className="py-8 text-center">
											<div className="text-muted-foreground">
												{searchTerm
													? 'No gift codes match your search'
													: 'No gift codes found'}
											</div>
										</TableCell>
									</TableRow>
								) : (
									filteredGiftCodes.map((giftCode) => {
										const isExpired = new Date(giftCode.expiresAt) < new Date();
										const getStatus = () => {
											if (giftCode.isUsed)
												return { label: 'Used', variant: 'secondary' as const };
											if (isExpired)
												return {
													label: 'Expired',
													variant: 'destructive' as const,
												};
											if (giftCode.isActive)
												return { label: 'Active', variant: 'default' as const };
											return {
												label: 'Inactive',
												variant: 'secondary' as const,
											};
										};

										const status = getStatus();

										return (
											<TableRow key={giftCode.id}>
												<TableCell className="font-mono font-medium">
													{giftCode.code}
												</TableCell>
												<TableCell>{giftCode.subscriptionPlan}</TableCell>
												<TableCell>
													<Badge variant={status.variant}>{status.label}</Badge>
												</TableCell>
												<TableCell>
													{new Date(giftCode.expiresAt).toLocaleDateString()}
												</TableCell>
												<TableCell>
													{giftCode.usedBy || (
														<span className="text-muted-foreground">-</span>
													)}
												</TableCell>
												<TableCell>
													{new Date(giftCode.createdAt).toLocaleDateString()}
												</TableCell>
												<TableCell>
													<div className="flex items-center gap-2">
														<Button
															variant="outline"
															size="sm"
															disabled={giftCode.isUsed || isExpired}
														>
															Edit
														</Button>
														<Button
															variant="outline"
															size="sm"
															className="text-red-600 hover:text-red-700"
															disabled={giftCode.isUsed}
														>
															<Trash2 className="h-4 w-4" />
														</Button>
													</div>
												</TableCell>
											</TableRow>
										);
									})
								)}
							</TableBody>
						</Table>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
