'use client';

import {
	useAllVisitReasons,
	useCreateVisitReason,
	useRemoveVisitReason,
	useUpdateVisitReason,
} from '@/features/dashboard/hooks/useAdminDashboard';

import { AdminDataManagement } from './admin-data-management';

export function AdminVisitReasons() {
	const { data: visitReasons, isLoading, error } = useAllVisitReasons();
	const createMutation = useCreateVisitReason();
	const updateMutation = useUpdateVisitReason();
	const removeMutation = useRemoveVisitReason();

	const visitReasonsData = visitReasons?.data || [];
	const isActionLoading =
		createMutation.isPending ||
		updateMutation.isPending ||
		removeMutation.isPending;

	const handleCreate = (data: { name: string; description?: string }) => {
		createMutation.mutate(data);
	};

	const handleUpdate = (
		id: number,
		data: { name?: string; description?: string }
	) => {
		updateMutation.mutate({ visitReasonId: id, data });
	};

	const handleRemove = (id: number) => {
		removeMutation.mutate(id);
	};

	return (
		<AdminDataManagement
			title="Visit Reasons"
			description="Manage visit reasons for appointments and consultations"
			data={visitReasonsData}
			isLoading={isLoading}
			error={error}
			onCreate={handleCreate}
			onUpdate={handleUpdate}
			onRemove={handleRemove}
			isActionLoading={isActionLoading}
		/>
	);
}
