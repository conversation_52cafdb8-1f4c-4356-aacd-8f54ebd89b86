'use client';

import {
	useAllDiagnosticRequestTypes,
	useCreateDiagnosticRequestType,
	useRemoveDiagnosticRequestType,
	useUpdateDiagnosticRequestType,
} from '@/features/dashboard/hooks/useAdminDashboard';

import { AdminDataManagement } from './admin-data-management';

export function AdminDiagnosticRequestTypes() {
	const {
		data: diagnosticRequestTypes,
		isLoading,
		error,
	} = useAllDiagnosticRequestTypes();
	const createMutation = useCreateDiagnosticRequestType();
	const updateMutation = useUpdateDiagnosticRequestType();
	const removeMutation = useRemoveDiagnosticRequestType();

	const diagnosticRequestTypesData = diagnosticRequestTypes?.data || [];
	const isActionLoading =
		createMutation.isPending ||
		updateMutation.isPending ||
		removeMutation.isPending;

	const handleCreate = (data: { name: string; description?: string }) => {
		createMutation.mutate(data);
	};

	const handleUpdate = (
		id: number,
		data: { name?: string; description?: string }
	) => {
		updateMutation.mutate({ diagnosticRequestTypeId: id, data });
	};

	const handleRemove = (id: number) => {
		removeMutation.mutate(id);
	};

	return (
		<AdminDataManagement
			title="Diagnostic Request Types"
			description="Manage diagnostic test request types and imaging categories"
			data={diagnosticRequestTypesData}
			isLoading={isLoading}
			error={error}
			onCreate={handleCreate}
			onUpdate={handleUpdate}
			onRemove={handleRemove}
			isActionLoading={isActionLoading}
		/>
	);
}
