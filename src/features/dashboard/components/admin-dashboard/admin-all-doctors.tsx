'use client';

import { IDoctorData } from '../../api/doctors.type';
import { useDoctors } from '../../hooks/useDoctors';
import { DoctorsTable } from './doctor/doctors-table';
import { columnsDoctors } from './doctor/doctors-table/columns';

export default function AdminAllDoctors() {
	const { doctors, isSuccess, isLoading } = useDoctors();

	return (
		<div className="p-4 pt-0">
			<div className="flex items-start gap-1">
				<div className="">
					<h1 className="text-elena-primary text-xl font-bold tracking-tight sm:text-2xl">
						Doctors Management
					</h1>
					<p className="text-gray-600">
						Manage all registered doctors in the system
					</p>
				</div>
			</div>

			<div className="mt-6">
				<DoctorsTable
					columns={columnsDoctors}
					data={isSuccess ? (doctors?.data as IDoctorData[]) : []}
					metadata={doctors && doctors.meta}
					isLoading={isLoading}
				/>
			</div>
		</div>
	);
}
