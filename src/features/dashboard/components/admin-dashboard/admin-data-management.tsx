'use client';

import { Edit, Plus, Search, Trash2, X } from 'lucide-react';
import { useMemo, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import { IDataManagementItem } from '@/features/dashboard/types/admin.types';

import HorizontalScrollBar from '../HorizontalScrollBar';

interface IDataManagementProps {
	title: string;
	description: string;
	data: IDataManagementItem[];
	isLoading: boolean;
	error: Error | null;
	onCreate: (data: { name: string; description?: string }) => void;
	onUpdate: (id: number, data: { name?: string; description?: string }) => void;
	onRemove: (id: number) => void;
	isActionLoading: boolean;
}

interface IFormData {
	name: string;
	description: string;
}

export function AdminDataManagement({
	title,
	description,
	data,
	isLoading,
	error,
	onCreate,
	onUpdate,
	onRemove,
	isActionLoading,
}: IDataManagementProps) {
	const [showCreateDialog, setShowCreateDialog] = useState(false);
	const [showEditDialog, setShowEditDialog] = useState(false);
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [selectedItem, setSelectedItem] = useState<IDataManagementItem | null>(
		null
	);
	const [formData, setFormData] = useState<IFormData>({
		name: '',
		description: '',
	});
	const [searchTerm, setSearchTerm] = useState('');

	// Filter data based on search term
	const filteredData = useMemo(() => {
		if (!searchTerm.trim()) {
			return data;
		}

		const searchLower = searchTerm.toLowerCase();
		return data.filter(
			(item) =>
				item.name.toLowerCase().includes(searchLower) ||
				(item.description &&
					item.description.toLowerCase().includes(searchLower))
		);
	}, [data, searchTerm]);

	const handleCreate = () => {
		if (formData.name.trim()) {
			onCreate({
				name: formData.name.trim(),
				description: formData.description.trim() || undefined,
			});
			setFormData({ name: '', description: '' });
			setShowCreateDialog(false);
		}
	};

	const handleEdit = (item: IDataManagementItem) => {
		setSelectedItem(item);
		setFormData({ name: item.name, description: item.description || '' });
		setShowEditDialog(true);
	};

	const handleUpdate = () => {
		if (selectedItem && formData.name.trim()) {
			onUpdate(selectedItem.id, {
				name: formData.name.trim(),
				description: formData.description.trim() || undefined,
			});
			setFormData({ name: '', description: '' });
			setSelectedItem(null);
			setShowEditDialog(false);
		}
	};

	const handleDelete = (item: IDataManagementItem) => {
		setSelectedItem(item);
		setShowDeleteDialog(true);
	};

	const confirmDelete = () => {
		if (selectedItem) {
			onRemove(selectedItem.id);
			setSelectedItem(null);
			setShowDeleteDialog(false);
		}
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	};

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
						{title}
					</h1>
					<p className="text-muted-foreground">{description}</p>
				</div>
				<Button onClick={() => setShowCreateDialog(true)}>
					<Plus className="mr-2 h-4 w-4" />
					Add New
				</Button>
			</div>

			<Card>
				<CardHeader>
					<CardTitle>{title} List</CardTitle>
				</CardHeader>
				<CardContent>
					{/* Search Input */}
					<div className="mb-4">
						<div className="flex items-center justify-between gap-4">
							<div className="relative flex-1">
								<Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
								<Input
									placeholder={`Search ${title.toLowerCase()}...`}
									value={searchTerm}
									onChange={(e) => setSearchTerm(e.target.value)}
									className="pr-10 pl-10"
								/>
								{searchTerm && (
									<Button
										variant="ghost"
										size="sm"
										className="absolute top-1/2 right-1 h-6 w-6 -translate-y-1/2 p-0"
										onClick={() => setSearchTerm('')}
									>
										<X className="h-3 w-3" />
									</Button>
								)}
							</div>
							{!isLoading && (
								<div className="text-muted-foreground text-sm">
									{searchTerm.trim() ? (
										<span>
											{filteredData.length} of {data.length} items
										</span>
									) : (
										<span>{data.length} items</span>
									)}
								</div>
							)}
						</div>
					</div>
					{error ? (
						<div className="py-8 text-center text-red-500">
							Error loading {title.toLowerCase()}. Please try again.
						</div>
					) : (
						<div className="rounded-md border">
							<HorizontalScrollBar>
								<Table>
									<TableHeader>
										<TableRow>
											<TableHead>Name</TableHead>
											<TableHead>Description</TableHead>
											<TableHead>Created</TableHead>
											<TableHead>Actions</TableHead>
										</TableRow>
									</TableHeader>
									<TableBody>
										{isLoading ? (
											Array.from({ length: 5 }).map((_, index) => (
												<TableRow key={index}>
													<TableCell>
														<Skeleton className="h-4 w-32" />
													</TableCell>
													<TableCell>
														<Skeleton className="h-4 w-48" />
													</TableCell>
													<TableCell>
														<Skeleton className="h-4 w-24" />
													</TableCell>
													<TableCell>
														<Skeleton className="h-8 w-20" />
													</TableCell>
												</TableRow>
											))
										) : filteredData.length === 0 ? (
											<TableRow>
												<TableCell colSpan={4} className="py-8 text-center">
													<div className="text-muted-foreground">
														{searchTerm.trim() ? (
															<p>
																No {title.toLowerCase()} found matching &ldquo;
																{searchTerm}&rdquo;
															</p>
														) : (
															<p>No {title.toLowerCase()} found</p>
														)}
													</div>
												</TableCell>
											</TableRow>
										) : (
											filteredData.map((item) => (
												<TableRow key={item.id}>
													<TableCell className="font-medium">
														{item.name}
													</TableCell>
													<TableCell>{item.description || '-'}</TableCell>
													<TableCell>{formatDate(item.created_at)}</TableCell>
													<TableCell>
														<div className="flex items-center gap-2">
															<Button
																size="sm"
																variant="outline"
																onClick={() => handleEdit(item)}
																disabled={isActionLoading}
															>
																<Edit className="h-4 w-4" />
															</Button>
															<Button
																size="sm"
																variant="outline"
																className="text-red-600"
																onClick={() => handleDelete(item)}
																disabled={isActionLoading}
															>
																<Trash2 className="h-4 w-4" />
															</Button>
														</div>
													</TableCell>
												</TableRow>
											))
										)}
									</TableBody>
								</Table>
							</HorizontalScrollBar>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Create Dialog */}
			<Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Add New {title}</DialogTitle>
						<DialogDescription>Create a new {title} entry.</DialogDescription>
					</DialogHeader>
					<div className="grid gap-4 py-4">
						<div className="grid gap-2">
							<Label htmlFor="name">Name</Label>
							<Input
								id="name"
								value={formData.name}
								onChange={(e) =>
									setFormData({ ...formData, name: e.target.value })
								}
								placeholder="Enter name"
							/>
						</div>
						<div className="grid gap-2">
							<Label htmlFor="description">Description (Optional)</Label>
							<Textarea
								id="description"
								value={formData.description}
								onChange={(e) =>
									setFormData({ ...formData, description: e.target.value })
								}
								placeholder="Enter description"
								rows={3}
							/>
						</div>
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setShowCreateDialog(false)}
						>
							Cancel
						</Button>
						<Button
							onClick={handleCreate}
							disabled={!formData.name.trim() || isActionLoading}
						>
							Create
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Edit Dialog */}
			<Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Edit {title.slice(0, -1)}</DialogTitle>
						<DialogDescription>
							Update the {title.toLowerCase().slice(0, -1)} details.
						</DialogDescription>
					</DialogHeader>
					<div className="grid gap-4 py-4">
						<div className="grid gap-2">
							<Label htmlFor="edit-name">Name</Label>
							<Input
								id="edit-name"
								value={formData.name}
								onChange={(e) =>
									setFormData({ ...formData, name: e.target.value })
								}
								placeholder="Enter name"
							/>
						</div>
						<div className="grid gap-2">
							<Label htmlFor="edit-description">Description (Optional)</Label>
							<Textarea
								id="edit-description"
								value={formData.description}
								onChange={(e) =>
									setFormData({ ...formData, description: e.target.value })
								}
								placeholder="Enter description"
								rows={3}
							/>
						</div>
					</div>
					<DialogFooter>
						<Button variant="outline" onClick={() => setShowEditDialog(false)}>
							Cancel
						</Button>
						<Button
							onClick={handleUpdate}
							disabled={!formData.name.trim() || isActionLoading}
						>
							Update
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Delete Dialog */}
			<Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Delete {title.slice(0, -1)}</DialogTitle>
						<DialogDescription>
							Are you sure you want to delete &quot;{selectedItem?.name}&quot;?
							This action cannot be undone.
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setShowDeleteDialog(false)}
						>
							Cancel
						</Button>
						<Button
							variant="destructive"
							onClick={confirmDelete}
							disabled={isActionLoading}
						>
							Delete
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
}
