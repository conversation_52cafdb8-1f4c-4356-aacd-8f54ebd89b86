'use client';

import { Calendar, Check, Eye, Mail, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import {
	useDoctorActions,
	usePendingDoctors,
} from '@/features/dashboard/hooks/useAdminDashboard';

import { IPendingDoctorItem } from '../../types/admin.types';

interface IPendingDoctorRowProps {
	doctor: IPendingDoctorItem;
	onAccept: (profileId: number) => void;
	onReject: (profileId: number) => void;
	onView: (profileId: number) => void;
	isLoading: boolean;
}

const PendingDoctorRow = ({
	doctor,
	onAccept,
	onReject,
	onView,
	isLoading,
}: IPendingDoctorRowProps) => {
	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	};

	return (
		<TableRow>
			<TableCell>
				<div className="font-medium">
					{doctor.first_name} {doctor.middle_name} {doctor.last_name}{' '}
					{doctor.suffix}
				</div>
				<div className="text-muted-foreground text-sm">
					{doctor.doctor.specialty || 'N/A'}
				</div>
			</TableCell>
			<TableCell>
				<div className="flex items-center gap-1">
					<Calendar className="h-3 w-3" />
					{formatDate(doctor.created_at)}
				</div>
			</TableCell>
			<TableCell>
				<div className="flex items-center gap-2">
					<Button
						size="sm"
						variant="outline"
						onClick={() => onAccept(doctor.id)}
						disabled={isLoading}
						className="text-green-600 hover:bg-green-50 hover:text-green-700"
					>
						<Check className="h-4 w-4" />
						Accept
					</Button>
					<Button
						size="sm"
						variant="outline"
						onClick={() => onReject(doctor.id)}
						disabled={isLoading}
						className="text-red-600 hover:bg-red-50 hover:text-red-700"
					>
						<X className="h-4 w-4" />
						Reject
					</Button>
					<Button
						size="sm"
						variant="ghost"
						onClick={() => onView(doctor.id)}
						disabled={isLoading}
					>
						<Eye className="h-4 w-4" />
					</Button>
				</div>
			</TableCell>
		</TableRow>
	);
};

const LoadingSkeleton = () => (
	<TableRow>
		<TableCell>
			<div className="space-y-1">
				<Skeleton className="h-4 w-32" />
				<Skeleton className="h-3 w-48" />
			</div>
		</TableCell>
		<TableCell>
			<Skeleton className="h-4 w-24" />
		</TableCell>
		<TableCell>
			<Skeleton className="h-4 w-20" />
		</TableCell>
		<TableCell>
			<Skeleton className="h-5 w-16" />
		</TableCell>
		<TableCell>
			<div className="flex items-center gap-2">
				<Skeleton className="h-8 w-16" />
				<Skeleton className="h-8 w-16" />
				<Skeleton className="h-8 w-8" />
			</div>
		</TableCell>
	</TableRow>
);

export function AdminPendingDoctors() {
	const [currentPage, setCurrentPage] = useState(1);
	const pageSize = 10;
	const router = useRouter();

	const {
		data: pendingDoctors,
		isLoading,
		error,
	} = usePendingDoctors({
		page: currentPage,
		pageSize,
	});

	const {
		acceptDoctor,
		rejectDoctor,
		isLoading: isActionLoading,
	} = useDoctorActions();

	const handleAccept = (profileId: number) => {
		acceptDoctor({ profileId });
	};

	const handleReject = (profileId: number) => {
		rejectDoctor({ profileId });
	};

	const handleView = (profileId: number) => {
		router.push(`/dashboard?tab=doctor-details&id=${profileId}`);
	};

	if (error) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
				<Card className="border-destructive">
					<CardContent className="p-6">
						<div className="text-center">
							<Mail className="text-destructive mx-auto mb-2 h-8 w-8" />
							<p className="text-destructive text-sm">
								Failed to load pending doctors
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	const doctors = pendingDoctors?.data?.data || [];
	const pagination = pendingDoctors?.data?.meta;

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			<Card>
				<CardHeader>
					<CardTitle className="text-xl text-[oklch(0.7448_0.1256_202.74)]">
						Pending Doctor Applications
					</CardTitle>
					<p className="text-muted-foreground text-sm">
						Review and approve doctor registration applications
					</p>
				</CardHeader>
				<CardContent>
					<div className="rounded-md border">
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Doctor Information</TableHead>
									<TableHead>Applied Date</TableHead>
									<TableHead>Actions</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{isLoading ? (
									Array.from({ length: 5 }).map((_, index) => (
										<LoadingSkeleton key={index} />
									))
								) : doctors.length === 0 ? (
									<TableRow>
										<TableCell colSpan={5} className="py-8 text-center">
											<div className="text-muted-foreground">
												<Check className="mx-auto mb-2 h-8 w-8" />
												<p>No pending doctor applications</p>
											</div>
										</TableCell>
									</TableRow>
								) : (
									doctors.map((doctor) => (
										<PendingDoctorRow
											key={doctor.id}
											doctor={doctor}
											onAccept={handleAccept}
											onReject={handleReject}
											onView={handleView}
											isLoading={isActionLoading}
										/>
									))
								)}
							</TableBody>
						</Table>
					</div>

					{/* Pagination */}
					{pagination && pagination.last_page > 1 && (
						<div className="mt-4 flex items-center justify-between">
							<p className="text-muted-foreground text-sm">
								Showing{' '}
								{(pagination.current_page - 1) * pagination.per_page + 1} to{' '}
								{Math.min(
									pagination.current_page * pagination.per_page,
									pagination.total
								)}{' '}
								of {pagination.total} applications
							</p>
							<div className="flex items-center gap-2">
								<Button
									variant="outline"
									size="sm"
									onClick={() => setCurrentPage(currentPage - 1)}
									disabled={currentPage === 1}
								>
									Previous
								</Button>
								<span className="text-sm">
									Page {pagination.current_page} of {pagination.last_page}
								</span>
								<Button
									variant="outline"
									size="sm"
									onClick={() => setCurrentPage(currentPage + 1)}
									disabled={currentPage === pagination.last_page}
								>
									Next
								</Button>
							</div>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
