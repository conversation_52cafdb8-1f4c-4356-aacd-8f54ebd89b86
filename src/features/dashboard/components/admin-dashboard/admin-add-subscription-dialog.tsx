'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Plus } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useCreateSubscription } from '@/features/dashboard/hooks/useAdminDashboard';

import { ICreateSubscriptionRequest } from '../../types/admin.types';

// Form validation schema
const adminSubscriptionCreationSchema = z.object({
	name: z.string().min(1, 'Subscription Name is required'),
	description: z.string().optional(),
	price: z.string().min(1, 'Price is required'),
	discountedPrice: z.string().optional(),
	coverage: z.string().min(1, 'Coverage is required'),
	coverageType: z.string().min(1, 'Coverage Type is required'),
});

type AdminSubscriptionCreationForm = z.infer<
	typeof adminSubscriptionCreationSchema
>;

interface IAdminAddSubscriptionDialogProps {
	children?: React.ReactNode;
}

export function AdminAddSubscriptionDialog({
	children,
}: IAdminAddSubscriptionDialogProps) {
	const [open, setOpen] = useState(false);

	const { mutate: createSubscription, isPending: isLoading } =
		useCreateSubscription();

	const form = useForm<AdminSubscriptionCreationForm>({
		resolver: zodResolver(adminSubscriptionCreationSchema),
		defaultValues: {
			name: '',
			description: '',
			price: '',
			discountedPrice: '',
			coverage: '',
			coverageType: '',
		},
	});

	const onSubmit = (data: AdminSubscriptionCreationForm) => {
		const subscriptionData: ICreateSubscriptionRequest = {
			name: data.name,
			description: data.description,
			price: data.price,
			discountedPrice: data.discountedPrice || data.price,
			coverage: data.coverage,
			coverageType: data.coverageType,
		};
		createSubscription(subscriptionData, {
			onSuccess: () => {
				setOpen(false);
				form.reset();
			},
		});
	};

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogTrigger asChild>
				{children || (
					<Button className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90">
						<Plus className="h-4 w-4" />
						Create Plan
					</Button>
				)}
			</DialogTrigger>
			<DialogContent className="w-full max-w-lg">
				<DialogHeader>
					<DialogTitle className="text-xl text-[oklch(0.7448_0.1256_202.74)]">
						Create New Subscription
					</DialogTitle>
					<DialogDescription>Create a new subscription plan.</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
						<div className="space-y-4">
							<FormField
								control={form.control}
								name="name"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Subscription Name</FormLabel>
										<FormControl>
											<Input placeholder="Enter subscription name" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="price"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Subscription Price</FormLabel>
										<FormControl>
											<Input
												type="number"
												placeholder="Enter Price"
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="discountedPrice"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Discounted Subscription Price</FormLabel>
										<FormControl>
											<Input placeholder="Enter Discounted Price" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
								<FormField
									control={form.control}
									name="coverage"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Coverage</FormLabel>
											<FormControl>
												<Input
													type="number"
													placeholder="Enter Coverage"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="coverageType"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Coverage Type</FormLabel>
											<Select
												onValueChange={field.onChange}
												defaultValue={field.value}
											>
												<FormControl>
													<SelectTrigger className="w-full">
														<SelectValue placeholder="Select coverage type" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<SelectItem value="DAY">Day</SelectItem>
													<SelectItem value="WEEK">Week</SelectItem>
													<SelectItem value="MONTH">Month</SelectItem>
													<SelectItem value="YEAR">Year</SelectItem>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<FormField
								control={form.control}
								name="description"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Description</FormLabel>
										<FormControl>
											<Textarea
												className="resize-none"
												placeholder="Enter Description"
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						{/* Submit Button */}
						<div className="flex justify-between space-x-2 pt-4">
							<Button
								type="button"
								variant="outline"
								onClick={() => setOpen(false)}
								disabled={isLoading}
							>
								Cancel
							</Button>
							<Button
								type="submit"
								disabled={isLoading}
								className="bg-[oklch(0.7448_0.1256_202.74)] hover:bg-[oklch(0.7448_0.1256_202.74)]/90"
							>
								{isLoading ? 'Creating...' : 'Create Plan'}
							</Button>
						</div>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
