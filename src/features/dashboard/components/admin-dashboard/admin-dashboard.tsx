'use client';

import { useQueryState } from 'nuqs';

import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import {
	SidebarInset,
	SidebarProvider,
	SidebarTrigger,
} from '@/components/ui/sidebar';
import { searchParamParsers } from '@/core/lib/search-params';
import { AdminActiveSubscriptions } from '@/features/dashboard/components/admin-dashboard/admin-active-subscriptions';
import AdminAllDoctors from '@/features/dashboard/components/admin-dashboard/admin-all-doctors';
import { AdminAllergyTypes } from '@/features/dashboard/components/admin-dashboard/admin-allergy-types';
import { AdminAppointmentTypes } from '@/features/dashboard/components/admin-dashboard/admin-appointment-types';
import { AdminConsultationTypes } from '@/features/dashboard/components/admin-dashboard/admin-consultation-types';
import { AdminDashboardOverview } from '@/features/dashboard/components/admin-dashboard/admin-dashboard-overview';
import { AdminDiagnosticRequestTypes } from '@/features/dashboard/components/admin-dashboard/admin-diagnostic-request-types';
import { AdminDietTypes } from '@/features/dashboard/components/admin-dashboard/admin-diet-types';
import { AdminDoctorDetails } from '@/features/dashboard/components/admin-dashboard/admin-doctor-details';
import { AdminGiftCodes } from '@/features/dashboard/components/admin-dashboard/admin-gift-codes';
import { AdminHistoryTypes } from '@/features/dashboard/components/admin-dashboard/admin-history-types';
import { AdminIllnessTypes } from '@/features/dashboard/components/admin-dashboard/admin-illness-types';
import { AdminLabManagement } from '@/features/dashboard/components/admin-dashboard/admin-lab-management';
import { AdminLabRequestTypes } from '@/features/dashboard/components/admin-dashboard/admin-lab-request-types';
import { AdminMedicalDescriptions } from '@/features/dashboard/components/admin-dashboard/admin-medical-descriptions';
import { AdminPaymentTypes } from '@/features/dashboard/components/admin-dashboard/admin-payment-types';
import { AdminPendingDoctors } from '@/features/dashboard/components/admin-dashboard/admin-pending-doctors';
import { AdminPlatformDataManagement } from '@/features/dashboard/components/admin-dashboard/admin-platform-data-management';
import { AdminPrescriptionTypes } from '@/features/dashboard/components/admin-dashboard/admin-prescription-types';
import { AdminSidebar } from '@/features/dashboard/components/admin-dashboard/admin-sidebar';
import { AdminSubscriptionManagement } from '@/features/dashboard/components/admin-dashboard/admin-subscription-management';
import { AdminSubscriptionPlans } from '@/features/dashboard/components/admin-dashboard/admin-subscription-plans';
import { AdminSurgeryTypes } from '@/features/dashboard/components/admin-dashboard/admin-surgery-types';
import { AdminVisitReasons } from '@/features/dashboard/components/admin-dashboard/admin-visit-reasons';
import { EAdminDashboardTab } from '@/features/dashboard/types/admin.types';

import { AdminHabitTypes } from './admin-habit-types';
import { AdminHMOTypes } from './admin-hmo-types';

const getBreadcrumbData = (tab: string) => {
	switch (tab) {
		case EAdminDashboardTab.OVERVIEW:
			return {
				title: 'Dashboard Overview',
				parent: 'Dashboard',
				href: '/dashboard',
			};
		case EAdminDashboardTab.PENDING_DOCTORS:
			return {
				title: 'Pending Doctors',
				parent: 'Doctor Management',
				href: '/dashboard?tab=all-doctors',
			};
		case EAdminDashboardTab.ALL_DOCTORS:
			return {
				title: 'All Doctors',
				parent: 'Doctor Management',
				href: '/dashboard?tab=all-doctors',
			};
		case EAdminDashboardTab.DOCTOR_DETAILS:
			return {
				title: 'Doctor Details',
				parent: 'Doctor Management',
				href: '/dashboard?tab=all-doctors',
			};
		// TODO: hide patient management for now
		// case EAdminDashboardTab.ALL_PATIENTS:
		// 	return { title: 'All Patients', parent: 'Patient Management' };
		// case EAdminDashboardTab.PATIENT_DETAILS:
		// 	return { title: 'Patient Details', parent: 'Patient Management' };
		// case EAdminDashboardTab.AUDIT_LOGS:
		// 	return {
		// 		title: 'Audit Logs',
		// 		parent: 'System',
		// 		href: '/dashboard?tab=audit-logs',
		// 	};
		case EAdminDashboardTab.SUBSCRIPTIONS:
			return {
				title: 'Subscriptions',
				parent: 'Subscription Management',
				href: '/dashboard?tab=subscriptions',
			};
		case EAdminDashboardTab.ACTIVE_SUBSCRIPTIONS:
			return {
				title: 'Active Subscriptions',
				parent: 'Subscription Management',
				href: '/dashboard?tab=active-subscriptions',
			};
		case EAdminDashboardTab.SUBSCRIPTION_PLANS:
			return {
				title: 'View Subscription Plan',
				parent: 'Subscription Management',
				href: '/dashboard?tab=subscriptions',
			};
		case EAdminDashboardTab.GIFT_CODES:
			return {
				title: 'Gift Codes',
				parent: 'Subscription Management',
				href: '/dashboard?tab=gift-codes',
			};

		// Platform Data
		case EAdminDashboardTab.PLATFORM_DATA:
			return {
				title: 'Platform Data',
				parent: 'Platform Data',
				href: '/dashboard?tab=platform-data',
			};
		case EAdminDashboardTab.VISIT_REASONS:
			return {
				title: 'Visit Reasons',
				parent: 'Platform Data',
				href: '/dashboard?tab=platform-data',
			};
		case EAdminDashboardTab.CONSULTATION_TYPES:
			return {
				title: 'Consultation Types',
				parent: 'Platform Data',
				href: '/dashboard?tab=platform-data',
			};
		case EAdminDashboardTab.APPOINTMENT_TYPES:
			return {
				title: 'Appointment Types',
				parent: 'Platform Data',
				href: '/dashboard?tab=platform-data',
			};
		case EAdminDashboardTab.PAYMENT_TYPES:
			return {
				title: 'Payment Types',
				parent: 'Platform Data',
				href: '/dashboard?tab=platform-data',
			};
		case EAdminDashboardTab.DIAGNOSTIC_REQUEST_TYPES:
			return {
				title: 'Diagnostic Request Types',
				parent: 'Platform Data',
				href: '/dashboard?tab=platform-data',
			};
		case EAdminDashboardTab.LAB_REQUEST_TYPES:
			return {
				title: 'Lab Request Types',
				parent: 'Platform Data',
				href: '/dashboard?tab=platform-data',
			};
		case EAdminDashboardTab.PRESCRIPTION_TYPES:
			return {
				title: 'Prescription Types',
				parent: 'Platform Data',
				href: '/dashboard?tab=platform-data',
			};
		case EAdminDashboardTab.MEDICAL_DESCRIPTIONS:
			return {
				title: 'Medical Certificate',
				parent: 'Platform Data',
				href: '/dashboard?tab=platform-data',
			};
		case EAdminDashboardTab.HABIT_TYPES:
			return {
				title: 'Habit Types',
				parent: 'Platform Data',
				href: '/dashboard?tab=platform-data',
			};
		case EAdminDashboardTab.ALLERGY_TYPES:
			return {
				title: 'Allergy Types',
				parent: 'Platform Data',
				href: '/dashboard?tab=platform-data',
			};
		case EAdminDashboardTab.ILLNESS_TYPES:
			return {
				title: 'Illness Types',
				parent: 'Platform Data',
				href: '/dashboard?tab=platform-data',
			};
		case EAdminDashboardTab.SURGERY_TYPES:
			return {
				title: 'Surgery Types',
				parent: 'Platform Data',
				href: '/dashboard?tab=platform-data',
			};
		case EAdminDashboardTab.DIET_TYPES:
			return {
				title: 'Diet Types',
				parent: 'Platform Data',
				href: '/dashboard?tab=platform-data',
			};
		case EAdminDashboardTab.HISTORY_TYPES:
			return {
				title: 'History Types',
				parent: 'Platform Data',
				href: '/dashboard?tab=platform-data',
			};
		case EAdminDashboardTab.HMO_TYPES:
			return {
				title: 'HMO Types',
				parent: 'Platform Data',
				href: '/dashboard?tab=platform-data',
			};

		// TODO: hide system settings for now
		// case EAdminDashboardTab.USER_ROLES:
		// 	return { title: 'User Roles', parent: 'System Settings' };
		// case EAdminDashboardTab.SYSTEM_CONFIG:
		// 	return { title: 'System Configuration', parent: 'System Settings' };
		default:
			return { title: 'Dashboard Overview', parent: 'Dashboard' };
	}
};

const renderTabContent = (tab: string) => {
	switch (tab) {
		case EAdminDashboardTab.PENDING_DOCTORS:
			return <AdminPendingDoctors />;
		case EAdminDashboardTab.ALL_DOCTORS:
			return <AdminAllDoctors />;
		case EAdminDashboardTab.DOCTOR_DETAILS:
			return <AdminDoctorDetails />;

		// TODO: hide patient management for now
		// case EAdminDashboardTab.ALL_PATIENTS:
		// 	return <AdminAllPatients />;
		// case EAdminDashboardTab.PATIENT_DETAILS:
		// 	return <AdminPatientDetails />;
		// case EAdminDashboardTab.AUDIT_LOGS:
		// 	return <AdminAuditLogs />;
		case EAdminDashboardTab.SUBSCRIPTIONS:
			return <AdminSubscriptionManagement />;
		case EAdminDashboardTab.ACTIVE_SUBSCRIPTIONS:
			return <AdminActiveSubscriptions />;
		case EAdminDashboardTab.SUBSCRIPTION_PLANS:
			return <AdminSubscriptionPlans />;
		case EAdminDashboardTab.GIFT_CODES:
			return <AdminGiftCodes />;

		// Platform Data
		case EAdminDashboardTab.PLATFORM_DATA:
			return <AdminPlatformDataManagement />;
		case EAdminDashboardTab.VISIT_REASONS:
			return <AdminVisitReasons />;
		case EAdminDashboardTab.CONSULTATION_TYPES:
			return <AdminConsultationTypes />;
		case EAdminDashboardTab.APPOINTMENT_TYPES:
			return <AdminAppointmentTypes />;
		case EAdminDashboardTab.PAYMENT_TYPES:
			return <AdminPaymentTypes />;
		case EAdminDashboardTab.DIAGNOSTIC_REQUEST_TYPES:
			return <AdminDiagnosticRequestTypes />;
		case EAdminDashboardTab.LAB_REQUEST_TYPES:
			return <AdminLabRequestTypes />;
		case EAdminDashboardTab.PRESCRIPTION_TYPES:
			return <AdminPrescriptionTypes />;
		case EAdminDashboardTab.MEDICAL_DESCRIPTIONS:
			return <AdminMedicalDescriptions />;
		case EAdminDashboardTab.HABIT_TYPES:
			return <AdminHabitTypes />;
		case EAdminDashboardTab.ALLERGY_TYPES:
			return <AdminAllergyTypes />;
		case EAdminDashboardTab.ILLNESS_TYPES:
			return <AdminIllnessTypes />;
		case EAdminDashboardTab.SURGERY_TYPES:
			return <AdminSurgeryTypes />;
		case EAdminDashboardTab.DIET_TYPES:
			return <AdminDietTypes />;
		case EAdminDashboardTab.HISTORY_TYPES:
			return <AdminHistoryTypes />;
		case EAdminDashboardTab.HMO_TYPES:
			return <AdminHMOTypes />;
		case EAdminDashboardTab.LAB_MANAGEMENT:
			return <AdminLabManagement />;
		// TODO: hide system settings for now
		// case EAdminDashboardTab.USER_ROLES:
		// 	return <AdminUserRoles />;
		// case EAdminDashboardTab.SYSTEM_CONFIG:
		// 	return <AdminSystemConfig />;
		case EAdminDashboardTab.OVERVIEW:
		default:
			return <AdminDashboardOverview />;
	}
};

export function AdminDashboard() {
	const [currentTab] = useQueryState('tab', searchParamParsers.tab);
	const breadcrumbData = getBreadcrumbData(currentTab);

	return (
		<SidebarProvider>
			<AdminSidebar />
			<SidebarInset>
				<header className="flex h-16 shrink-0 items-center justify-between gap-2">
					<div className="flex items-center gap-2 px-4">
						<SidebarTrigger className="-ml-1" />
						<Separator
							orientation="vertical"
							className="mr-2 data-[orientation=vertical]:h-4"
						/>
						<Breadcrumb>
							<BreadcrumbList>
								<BreadcrumbItem className="hidden md:block">
									<BreadcrumbLink href={breadcrumbData.href}>
										{breadcrumbData.parent}
									</BreadcrumbLink>
								</BreadcrumbItem>
								<BreadcrumbSeparator className="hidden md:block" />
								<BreadcrumbItem>
									<BreadcrumbPage>{breadcrumbData.title}</BreadcrumbPage>
								</BreadcrumbItem>
							</BreadcrumbList>
						</Breadcrumb>
					</div>
				</header>
				{renderTabContent(currentTab)}
			</SidebarInset>
		</SidebarProvider>
	);
}
