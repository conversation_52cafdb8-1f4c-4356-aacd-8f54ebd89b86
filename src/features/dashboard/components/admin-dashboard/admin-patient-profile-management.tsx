'use client';

import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>riangle,
	Apple,
	CreditCard,
	Heart,
	Scissors,
	Stethoscope,
	Users,
} from 'lucide-react';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';

import { AdminPatientAllergiesManagement } from './admin-patient-allergies-management';
import { AdminPatientDietsManagement } from './admin-patient-diets-management';
import { AdminPatientFamilyManagement } from './admin-patient-family-management';
import { AdminPatientHabitsManagement } from './admin-patient-habits-management';
import { AdminPatientHistoryManagement } from './admin-patient-history-management';
import { AdminPatientHmoManagement } from './admin-patient-hmo-management';
import { AdminPatientIllnessesManagement } from './admin-patient-illnesses-management';
import { AdminPatientSurgeriesManagement } from './admin-patient-surgeries-management';

// Define interfaces for patient profile data
interface IPatientHabit {
	id: number;
	name: string;
	description: string;
	created_at: string;
}

interface IPatientAllergy {
	id: number;
	name: string;
	description: string;
	created_at: string;
}

interface IPatientIllness {
	id: number;
	name: string;
	description: string;
	created_at: string;
}

interface IPatientSurgery {
	id: number;
	name: string;
	description: string;
	created_at: string;
}

interface IPatientDiet {
	id: number;
	name: string;
	description: string;
	created_at: string;
}

interface IPatientHmo {
	id: number;
	hmo_company: string;
	hmo_detail: string;
	hmo_id: string;
	hmo_provider: string;
}

interface IPatientFamily {
	id: number;
	name: string;
	relationship: string;
}

interface IPatientHistory {
	id: number;
	name: string;
	description: string;
	created_at: string;
	updated_at: string;
}

interface IAdminPatientProfileManagementProps {
	profileId: number;
	patient: {
		patientHabit: IPatientHabit[];
		patientAllergy: IPatientAllergy[];
		patientIllness: IPatientIllness[];
		patientSurgery: IPatientSurgery[];
		patientDiet: IPatientDiet[];
		patientHmo: IPatientHmo[];
		patientFamily: IPatientFamily[];
		patientHistory: IPatientHistory[];
	};
}

export function AdminPatientProfileManagement({
	profileId,
	patient,
}: IAdminPatientProfileManagementProps) {
	return (
		<div className="space-y-6">
			<Tabs defaultValue="habits" className="w-full">
				<TabsList className="grid w-full grid-cols-4 lg:grid-cols-8">
					<TabsTrigger value="habits" className="flex items-center gap-1">
						<Activity className="h-4 w-4" />
						<span className="hidden sm:inline">Habits</span>
					</TabsTrigger>
					<TabsTrigger value="allergies" className="flex items-center gap-1">
						<AlertTriangle className="h-4 w-4" />
						<span className="hidden sm:inline">Allergies</span>
					</TabsTrigger>
					<TabsTrigger value="illnesses" className="flex items-center gap-1">
						<Heart className="h-4 w-4" />
						<span className="hidden sm:inline">Illnesses</span>
					</TabsTrigger>
					<TabsTrigger value="history" className="flex items-center gap-1">
						<Stethoscope className="h-4 w-4" />
						<span className="hidden sm:inline">History</span>
					</TabsTrigger>
					<TabsTrigger value="surgeries" className="flex items-center gap-1">
						<Scissors className="h-4 w-4" />
						<span className="hidden sm:inline">Surgeries</span>
					</TabsTrigger>
					<TabsTrigger value="diets" className="flex items-center gap-1">
						<Apple className="h-4 w-4" />
						<span className="hidden sm:inline">Diets</span>
					</TabsTrigger>
					<TabsTrigger value="hmo" className="flex items-center gap-1">
						<CreditCard className="h-4 w-4" />
						<span className="hidden sm:inline">HMO</span>
					</TabsTrigger>
					<TabsTrigger value="family" className="flex items-center gap-1">
						<Users className="h-4 w-4" />
						<span className="hidden sm:inline">Family</span>
					</TabsTrigger>
				</TabsList>

				<TabsContent value="habits" className="mt-6">
					<AdminPatientHabitsManagement
						profileId={profileId}
						habits={patient.patientHabit || []}
					/>
				</TabsContent>

				<TabsContent value="allergies" className="mt-6">
					<AdminPatientAllergiesManagement
						profileId={profileId}
						allergies={patient.patientAllergy || []}
					/>
				</TabsContent>

				<TabsContent value="illnesses" className="mt-6">
					<AdminPatientIllnessesManagement
						profileId={profileId}
						illnesses={patient.patientIllness || []}
					/>
				</TabsContent>

				<TabsContent value="history" className="mt-6">
					<AdminPatientHistoryManagement
						profileId={profileId}
						history={patient.patientHistory || []}
					/>
				</TabsContent>

				<TabsContent value="surgeries" className="mt-6">
					<AdminPatientSurgeriesManagement
						profileId={profileId}
						surgeries={patient.patientSurgery || []}
					/>
				</TabsContent>

				<TabsContent value="diets" className="mt-6">
					<AdminPatientDietsManagement
						profileId={profileId}
						diets={patient.patientDiet || []}
					/>
				</TabsContent>

				<TabsContent value="hmo" className="mt-6">
					<AdminPatientHmoManagement
						profileId={profileId}
						hmos={patient.patientHmo || []}
					/>
				</TabsContent>

				<TabsContent value="family" className="mt-6">
					<AdminPatientFamilyManagement
						profileId={profileId}
						family={patient.patientFamily || []}
					/>
				</TabsContent>
			</Tabs>
		</div>
	);
}
