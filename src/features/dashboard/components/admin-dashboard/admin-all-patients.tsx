'use client';

import { Calendar, Eye, Mail, Phone, User } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useQueryStates } from 'nuqs';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import { searchParamParsers } from '@/core/lib/search-params';
import { useAllPatients } from '@/features/dashboard/hooks/useAdminDashboard';
import { IPatientProfile } from '@/features/dashboard/types/admin.types';

interface IPatientRowProps {
	patient: IPatientProfile;
	onView: (profileId: number) => void;
}

const PatientRow = ({ patient, onView }: IPatientRowProps) => {
	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	};

	const calculateAge = (birthday: string) => {
		const birthDate = new Date(birthday);
		const today = new Date();
		let age = today.getFullYear() - birthDate.getFullYear();
		const monthDiff = today.getMonth() - birthDate.getMonth();

		if (
			monthDiff < 0 ||
			(monthDiff === 0 && today.getDate() < birthDate.getDate())
		) {
			age--;
		}

		return age;
	};

	return (
		<TableRow>
			<TableCell>
				<div className="font-medium">
					{patient.first_name} {patient.middle_name} {patient.last_name}{' '}
					{patient.suffix}
				</div>
				<div className="text-muted-foreground text-sm">ID: {patient.id}</div>
				<div className="text-muted-foreground text-sm">
					{patient.user?.email || 'N/A'}
				</div>
			</TableCell>
			<TableCell>
				<div className="flex items-center gap-1">
					<Phone className="h-3 w-3" />
					{patient.phone || 'N/A'}
				</div>
			</TableCell>
			<TableCell>
				<div className="flex items-center gap-1">
					<User className="h-3 w-3" />
					{patient.gender || 'N/A'}
				</div>
			</TableCell>
			<TableCell>
				<div className="flex items-center gap-1">
					<Calendar className="h-3 w-3" />
					{patient.birthday ? `${calculateAge(patient.birthday)} years` : 'N/A'}
				</div>
			</TableCell>
			<TableCell>
				<div className="flex items-center gap-1">
					<Calendar className="h-3 w-3" />
					{formatDate(patient.created_at)}
				</div>
			</TableCell>
			<TableCell>
				<div className="flex items-center gap-2">
					<Badge variant={patient.is_active ? 'default' : 'secondary'}>
						{patient.is_active ? 'Active' : 'Inactive'}
					</Badge>
					<Badge variant={patient.is_verified ? 'default' : 'secondary'}>
						{patient.is_verified ? 'Verified' : 'Unverified'}
					</Badge>
				</div>
			</TableCell>
			<TableCell>
				<Button
					size="sm"
					variant="outline"
					onClick={() => onView(patient.id)}
					className="flex items-center gap-1"
				>
					<Eye className="h-3 w-3" />
					View
				</Button>
			</TableCell>
		</TableRow>
	);
};

const LoadingSkeleton = () => (
	<TableRow>
		<TableCell>
			<div className="space-y-2">
				<Skeleton className="h-4 w-32" />
				<Skeleton className="h-3 w-24" />
			</div>
		</TableCell>
		<TableCell>
			<Skeleton className="h-4 w-24" />
		</TableCell>
		<TableCell>
			<Skeleton className="h-4 w-16" />
		</TableCell>
		<TableCell>
			<Skeleton className="h-4 w-20" />
		</TableCell>
		<TableCell>
			<Skeleton className="h-4 w-20" />
		</TableCell>
		<TableCell>
			<div className="flex gap-2">
				<Skeleton className="h-6 w-16" />
				<Skeleton className="h-6 w-20" />
			</div>
		</TableCell>
		<TableCell>
			<Skeleton className="h-8 w-16" />
		</TableCell>
	</TableRow>
);

export function AdminAllPatients() {
	// URL search parameters using nuqs
	const [{ search, page }, setSearchParams] = useQueryStates({
		search: searchParamParsers.search,
		page: searchParamParsers.page,
	});
	const pageSize = 10;
	const router = useRouter();

	const {
		data: allPatients,
		isLoading,
		error,
	} = useAllPatients({
		page,
		pageSize,
		search,
	});

	if (error) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
				<Card className="border-destructive">
					<CardContent className="p-6">
						<div className="text-center">
							<Mail className="text-destructive mx-auto mb-2 h-8 w-8" />
							<p className="text-destructive text-sm">
								Failed to load patients
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	const patients = allPatients?.data?.data || [];
	const totalPages = allPatients?.data?.meta?.last_page || 1;

	const handleView = (profileId: number) => {
		router.push(`/dashboard?tab=patient-details&id=${profileId}`);
	};

	const handleSearch = (value: string) => {
		setSearchParams({ search: value, page: 1 }); // Reset to first page when searching
	};

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			<Card>
				<CardHeader>
					<CardTitle className="text-xl text-[oklch(0.7448_0.1256_202.74)]">
						All Patients
					</CardTitle>
					<p className="text-muted-foreground text-sm">
						Manage and view all registered patients in the system
					</p>
				</CardHeader>
				<CardContent>
					{/* Search Input */}
					<div className="mb-4">
						<Input
							placeholder="Search patients by name, email, or phone..."
							value={search}
							onChange={(e) => handleSearch(e.target.value)}
							className="max-w-sm"
						/>
					</div>

					<div className="rounded-md border">
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Patient Information</TableHead>
									<TableHead>Contact</TableHead>
									<TableHead>Gender</TableHead>
									<TableHead>Age</TableHead>
									<TableHead>Registration Date</TableHead>
									<TableHead>Status</TableHead>
									<TableHead>Actions</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{isLoading ? (
									Array.from({ length: 5 }).map((_, index) => (
										<LoadingSkeleton key={index} />
									))
								) : patients.length === 0 ? (
									<TableRow>
										<TableCell colSpan={7} className="py-8 text-center">
											<div className="text-muted-foreground">
												<User className="mx-auto mb-2 h-8 w-8" />
												<p>
													{search
														? 'No patients found matching your search'
														: 'No patients registered yet'}
												</p>
											</div>
										</TableCell>
									</TableRow>
								) : (
									patients.map((patient) => (
										<PatientRow
											key={patient.id}
											patient={patient}
											onView={handleView}
										/>
									))
								)}
							</TableBody>
						</Table>
					</div>

					{/* Pagination */}
					{totalPages > 1 && (
						<div className="mt-4 flex items-center justify-between">
							<p className="text-muted-foreground text-sm">
								Page {page} of {totalPages}
							</p>
							<div className="flex gap-2">
								<Button
									variant="outline"
									size="sm"
									onClick={() =>
										setSearchParams({ page: Math.max(1, page - 1) })
									}
									disabled={page === 1}
								>
									Previous
								</Button>
								<Button
									variant="outline"
									size="sm"
									onClick={() =>
										setSearchParams({ page: Math.min(totalPages, page + 1) })
									}
									disabled={page === totalPages}
								>
									Next
								</Button>
							</div>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
