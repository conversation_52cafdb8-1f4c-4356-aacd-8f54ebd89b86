'use client';

import {
	useAllMedicalTypes,
	useCreateMedicalType,
	useRemoveMedicalType,
	useUpdateMedicalType,
} from '@/features/dashboard/hooks/useAdminDashboard';

import { AdminDataManagement } from './admin-data-management';

export function AdminMedicalDescriptions() {
	const { data: medicalCertTypes, isLoading, error } = useAllMedicalTypes();
	const createMutation = useCreateMedicalType();
	const updateMutation = useUpdateMedicalType();
	const removeMutation = useRemoveMedicalType();

	const medicalCertTypesData = medicalCertTypes?.data || [];
	const isActionLoading =
		createMutation.isPending ||
		updateMutation.isPending ||
		removeMutation.isPending;

	const handleCreate = (data: { name: string; description?: string }) => {
		createMutation.mutate(data);
	};

	const handleUpdate = (
		id: number,
		data: { name?: string; description?: string }
	) => {
		updateMutation.mutate({ medicalTypeId: id, data });
	};

	const handleRemove = (id: number) => {
		removeMutation.mutate(id);
	};

	return (
		<AdminDataManagement
			title="Medical Certificate"
			description="Manage medical terminology, allergy types, and medical descriptions used throughout the platform"
			data={medicalCertTypesData}
			isLoading={isLoading}
			error={error}
			onCreate={handleCreate}
			onUpdate={handleUpdate}
			onRemove={handleRemove}
			isActionLoading={isActionLoading}
		/>
	);
}
