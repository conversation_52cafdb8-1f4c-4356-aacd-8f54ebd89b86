'use client';

import {
	useAllDietTypes,
	useCreateDietType,
	useRemoveDietType,
	useUpdateDietType,
} from '@/features/dashboard/hooks/useAdminDashboard';

import { AdminDataManagement } from './admin-data-management';

export function AdminDietTypes() {
	const { data: dietTypes, isLoading, error } = useAllDietTypes();
	const createMutation = useCreateDietType();
	const updateMutation = useUpdateDietType();
	const removeMutation = useRemoveDietType();

	const dietTypesData = dietTypes?.data || [];
	const isActionLoading =
		createMutation.isPending ||
		updateMutation.isPending ||
		removeMutation.isPending;

	const handleCreate = (data: { name: string; description?: string }) => {
		createMutation.mutate(data);
	};

	const handleUpdate = (
		id: number,
		data: { name?: string; description?: string }
	) => {
		updateMutation.mutate({ dietTypeId: id, data });
	};

	const handleRemove = (id: number) => {
		removeMutation.mutate(id);
	};

	return (
		<AdminDataManagement
			title="Diet Types"
			description="Manage dietary restriction types and nutrition categories"
			data={dietTypesData}
			isLoading={isLoading}
			error={error}
			onCreate={handleCreate}
			onUpdate={handleUpdate}
			onRemove={handleRemove}
			isActionLoading={isActionLoading}
		/>
	);
}
