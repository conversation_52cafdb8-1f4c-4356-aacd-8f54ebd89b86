'use client';

import {
	useAllAppointmentTypes,
	useCreateAppointmentType,
	useRemoveAppointmentType,
	useUpdateAppointmentType,
} from '@/features/dashboard/hooks/useAdminDashboard';

import { AdminDataManagement } from './admin-data-management';

export function AdminAppointmentTypes() {
	const { data: appointmentTypes, isLoading, error } = useAllAppointmentTypes();
	const createMutation = useCreateAppointmentType();
	const updateMutation = useUpdateAppointmentType();
	const removeMutation = useRemoveAppointmentType();

	const appointmentTypesData = appointmentTypes?.data || [];
	const isActionLoading =
		createMutation.isPending ||
		updateMutation.isPending ||
		removeMutation.isPending;

	const handleCreate = (data: { name: string; description?: string }) => {
		createMutation.mutate(data);
	};

	const handleUpdate = (
		id: number,
		data: { name?: string; description?: string }
	) => {
		updateMutation.mutate({ appointmentTypeId: id, data });
	};

	const handleRemove = (id: number) => {
		removeMutation.mutate(id);
	};

	return (
		<AdminDataManagement
			title="Appointment Types"
			description="Manage appointment types for scheduling and categorization"
			data={appointmentTypesData}
			isLoading={isLoading}
			error={error}
			onCreate={handleCreate}
			onUpdate={handleUpdate}
			onRemove={handleRemove}
			isActionLoading={isActionLoading}
		/>
	);
}
