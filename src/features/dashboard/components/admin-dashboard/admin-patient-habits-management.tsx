'use client';

import { Activity, Edit, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';

import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
	useAdminAddHabitToPatient,
	useAdminRemoveHabitFromPatient,
	useAdminUpdatePatientHabit,
} from '@/features/dashboard/hooks/useAdminDashboard';

interface IPatientHabit {
	id: number;
	name: string;
	description: string;
	created_at: string;
}

interface IAdminPatientHabitsManagementProps {
	profileId: number;
	habits: IPatientHabit[];
}

interface IHabitFormData {
	name: string;
	description: string;
}

export function AdminPatientHabitsManagement({
	profileId,
	habits,
}: IAdminPatientHabitsManagementProps) {
	const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [editingHabit, setEditingHabit] = useState<IPatientHabit | null>(null);
	const [habitToDelete, setHabitToDelete] = useState<IPatientHabit | null>(
		null
	);
	const [formData, setFormData] = useState<IHabitFormData>({
		name: '',
		description: '',
	});

	const addHabitMutation = useAdminAddHabitToPatient();
	const updateHabitMutation = useAdminUpdatePatientHabit();
	const removeHabitMutation = useAdminRemoveHabitFromPatient();

	const handleAddHabit = () => {
		if (!formData.name.trim()) {
			console.error('Habit name is required');
			return;
		}

		// Create payload with name and description as backend expects
		const habitData = {
			name: formData.name,
			description: formData.description,
		};

		addHabitMutation.mutate(
			{ profileId, habitData },
			{
				onSuccess: () => {
					setIsAddDialogOpen(false);
					setFormData({
						name: '',
						description: '',
					});
				},
			}
		);
	};

	const handleEditHabit = () => {
		if (!editingHabit) return;

		if (!formData.name.trim()) {
			console.error('Habit name is required');
			return;
		}

		// Create payload with name and description as backend expects
		const habitData = {
			name: formData.name,
			description: formData.description,
		};

		updateHabitMutation.mutate(
			{
				profileId,
				patientHabitId: editingHabit.id,
				habitData,
			},
			{
				onSuccess: () => {
					setIsEditDialogOpen(false);
					setEditingHabit(null);
					setFormData({
						name: '',
						description: '',
					});
				},
			}
		);
	};

	const handleRemoveHabit = (habit: IPatientHabit) => {
		setHabitToDelete(habit);
		setIsDeleteDialogOpen(true);
	};

	const confirmRemoveHabit = () => {
		if (habitToDelete) {
			removeHabitMutation.mutate({
				profileId,
				patientHabitId: habitToDelete.id,
			});
			setIsDeleteDialogOpen(false);
			setHabitToDelete(null);
		}
	};

	const openEditDialog = (habit: IPatientHabit) => {
		setEditingHabit(habit);
		setFormData({
			name: habit.name,
			description: habit.description,
		});
		setIsEditDialogOpen(true);
	};

	return (
		<Card>
			<CardHeader>
				<div className="flex items-center justify-between">
					<CardTitle className="flex items-center gap-2">
						<Activity className="h-5 w-5" />
						Patient Habits ({habits.length})
					</CardTitle>
					<Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
						<DialogTrigger asChild>
							<Button size="sm">
								<Plus className="mr-2 h-4 w-4" />
								Add Habit
							</Button>
						</DialogTrigger>
						<DialogContent>
							<DialogHeader>
								<DialogTitle>Add New Habit</DialogTitle>
								<DialogDescription>
									Add a new habit to the patient&apos;s profile.
								</DialogDescription>
							</DialogHeader>
							<div className="space-y-4">
								<div>
									<Label htmlFor="name">Habit Name</Label>
									<Input
										id="name"
										value={formData.name}
										onChange={(e) =>
											setFormData({ ...formData, name: e.target.value })
										}
										placeholder="Enter habit name..."
										required
									/>
								</div>
								<div>
									<Label htmlFor="description">Description</Label>
									<Textarea
										id="description"
										value={formData.description}
										onChange={(e) =>
											setFormData({ ...formData, description: e.target.value })
										}
										placeholder="Describe the habit..."
									/>
								</div>
							</div>
							<DialogFooter>
								<Button
									variant="outline"
									onClick={() => setIsAddDialogOpen(false)}
								>
									Cancel
								</Button>
								<Button
									onClick={handleAddHabit}
									disabled={addHabitMutation.isPending}
								>
									{addHabitMutation.isPending ? 'Adding...' : 'Add Habit'}
								</Button>
							</DialogFooter>
						</DialogContent>
					</Dialog>
				</div>
			</CardHeader>
			<CardContent>
				{habits.length === 0 ? (
					<p className="py-4 text-center text-gray-500">
						No habits recorded for this patient.
					</p>
				) : (
					<div className="space-y-3">
						{habits.map((habit) => (
							<div
								key={habit.id}
								className="flex items-center justify-between rounded-lg border p-3"
							>
								<div className="flex-1">
									<h4 className="font-medium">{habit.name}</h4>
									<p className="text-sm text-gray-600">{habit.description}</p>
								</div>
								<div className="flex gap-2">
									<Button
										size="sm"
										variant="outline"
										onClick={() => openEditDialog(habit)}
									>
										<Edit className="h-4 w-4" />
									</Button>
									<Button
										size="sm"
										variant="destructive"
										onClick={() => handleRemoveHabit(habit)}
										disabled={removeHabitMutation.isPending}
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								</div>
							</div>
						))}
					</div>
				)}
			</CardContent>

			{/* Edit Dialog */}
			<Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Edit Habit</DialogTitle>
						<DialogDescription>Update the habit information.</DialogDescription>
					</DialogHeader>
					<div className="space-y-4">
						<div>
							<Label htmlFor="editName">Habit Name</Label>
							<Input
								id="editName"
								value={formData.name}
								onChange={(e) =>
									setFormData({ ...formData, name: e.target.value })
								}
								placeholder="Enter habit name..."
								required
							/>
						</div>
						<div>
							<Label htmlFor="editDescription">Description</Label>
							<Textarea
								id="editDescription"
								value={formData.description}
								onChange={(e) =>
									setFormData({ ...formData, description: e.target.value })
								}
								placeholder="Describe the habit..."
							/>
						</div>
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setIsEditDialogOpen(false)}
						>
							Cancel
						</Button>
						<Button
							onClick={handleEditHabit}
							disabled={updateHabitMutation.isPending}
						>
							{updateHabitMutation.isPending ? 'Updating...' : 'Update Habit'}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Delete Confirmation Dialog */}
			<AlertDialog
				open={isDeleteDialogOpen}
				onOpenChange={setIsDeleteDialogOpen}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Delete Habit</AlertDialogTitle>
						<AlertDialogDescription>
							Are you sure you want to delete &quot;{habitToDelete?.name}&quot;?
							This action cannot be undone.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={confirmRemoveHabit}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
						>
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</Card>
	);
}
