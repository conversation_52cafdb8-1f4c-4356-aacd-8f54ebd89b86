'use client';

import {
	Activity,
	AlertTriangle,
	ArrowLeft,
	Calendar,
	Heart,
	Home,
	Mail,
	MapPin,
	Phone,
	Stethoscope,
	User,
	UserCheck,
	Users,
} from 'lucide-react';
import { useQueryState } from 'nuqs';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { searchParamParsers } from '@/core/lib/search-params';
import { usePatientDetail } from '@/features/dashboard/hooks/useAdminDashboard';
import {
	IPatientAllergy,
	IPatientDetailProfile,
	IPatientFamily,
	IPatientIllness,
} from '@/features/dashboard/types/admin.types';

import { AdminPatientProfileManagement } from './admin-patient-profile-management';

const LoadingSkeleton = () => (
	<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
		<Card>
			<CardHeader>
				<Skeleton className="h-6 w-48" />
				<Skeleton className="h-4 w-64" />
			</CardHeader>
			<CardContent className="space-y-6">
				<div className="grid grid-cols-1 gap-6 md:grid-cols-2">
					{Array.from({ length: 4 }).map((_, index) => (
						<Card key={index}>
							<CardHeader>
								<Skeleton className="h-5 w-32" />
							</CardHeader>
							<CardContent className="space-y-4">
								{Array.from({ length: 3 }).map((_, i) => (
									<div key={i} className="flex items-center gap-3">
										<Skeleton className="h-4 w-4" />
										<div className="space-y-1">
											<Skeleton className="h-4 w-20" />
											<Skeleton className="h-3 w-32" />
										</div>
									</div>
								))}
							</CardContent>
						</Card>
					))}
				</div>
			</CardContent>
		</Card>
	</div>
);

const formatDate = (dateString: string) => {
	return new Date(dateString).toLocaleDateString('en-US', {
		year: 'numeric',
		month: 'long',
		day: 'numeric',
	});
};

const calculateAge = (birthday: string) => {
	const birthDate = new Date(birthday);
	const today = new Date();
	let age = today.getFullYear() - birthDate.getFullYear();
	const monthDiff = today.getMonth() - birthDate.getMonth();

	if (
		monthDiff < 0 ||
		(monthDiff === 0 && today.getDate() < birthDate.getDate())
	) {
		age--;
	}

	return age;
};

const PersonalInfoCard = ({ patient }: { patient: IPatientDetailProfile }) => (
	<Card>
		<CardHeader>
			<CardTitle className="flex items-center gap-2 text-lg">
				<User className="h-5 w-5" />
				Personal Information
			</CardTitle>
		</CardHeader>
		<CardContent className="space-y-4">
			<div className="flex items-center gap-3">
				<Mail className="text-muted-foreground h-4 w-4" />
				<div>
					<p className="text-sm font-medium">Email</p>
					<p className="text-sm text-gray-600">
						{patient.user?.email || 'N/A'}
					</p>
				</div>
			</div>
			<div className="flex items-center gap-3">
				<Phone className="text-muted-foreground h-4 w-4" />
				<div>
					<p className="text-sm font-medium">Phone</p>
					<p className="text-sm text-gray-600">{patient.phone || 'N/A'}</p>
				</div>
			</div>
			<div className="flex items-center gap-3">
				<Calendar className="text-muted-foreground h-4 w-4" />
				<div>
					<p className="text-sm font-medium">Birthday</p>
					<p className="text-sm text-gray-600">
						{patient.birthday
							? `${formatDate(patient.birthday)} (${calculateAge(patient.birthday)} years)`
							: 'N/A'}
					</p>
				</div>
			</div>
			<div className="flex items-center gap-3">
				<UserCheck className="text-muted-foreground h-4 w-4" />
				<div>
					<p className="text-sm font-medium">Gender</p>
					<p className="text-sm text-gray-600">{patient.gender || 'N/A'}</p>
				</div>
			</div>
			<div className="flex items-center gap-3">
				<Users className="text-muted-foreground h-4 w-4" />
				<div>
					<p className="text-sm font-medium">Civil Status</p>
					<p className="text-sm text-gray-600">
						{patient.civil_status || 'N/A'}
					</p>
				</div>
			</div>
			<div className="flex items-center gap-3">
				<User className="text-muted-foreground h-4 w-4" />
				<div>
					<p className="text-sm font-medium">Occupation</p>
					<p className="text-sm text-gray-600">{patient.occupation || 'N/A'}</p>
				</div>
			</div>
		</CardContent>
	</Card>
);

const MedicalInfoCard = ({ patient }: { patient: IPatientDetailProfile }) => (
	<Card>
		<CardHeader>
			<CardTitle className="flex items-center gap-2 text-lg">
				<Stethoscope className="h-5 w-5" />
				Medical Information
			</CardTitle>
		</CardHeader>
		<CardContent className="space-y-4">
			<div className="flex items-center gap-3">
				<Heart className="text-muted-foreground h-4 w-4" />
				<div>
					<p className="text-sm font-medium">Blood Type</p>
					<p className="text-sm text-gray-600">{patient.blood_type || 'N/A'}</p>
				</div>
			</div>
			<div className="flex items-center gap-3">
				<Activity className="text-muted-foreground h-4 w-4" />
				<div>
					<p className="text-sm font-medium">Height</p>
					<p className="text-sm text-gray-600">
						{patient.height
							? `${patient.height} ${patient.height_type}`
							: 'N/A'}
					</p>
				</div>
			</div>
			<div className="flex items-center gap-3">
				<Activity className="text-muted-foreground h-4 w-4" />
				<div>
					<p className="text-sm font-medium">Weight</p>
					<p className="text-sm text-gray-600">
						{patient.weight
							? `${patient.weight} ${patient.weight_type}`
							: 'N/A'}
					</p>
				</div>
			</div>
			<div className="flex items-center gap-3">
				<User className="text-muted-foreground h-4 w-4" />
				<div>
					<p className="text-sm font-medium">Religion</p>
					<p className="text-sm text-gray-600">{patient.religion || 'N/A'}</p>
				</div>
			</div>
			<div className="flex items-center gap-3">
				<Users className="text-muted-foreground h-4 w-4" />
				<div>
					<p className="text-sm font-medium">Ethnicity</p>
					<p className="text-sm text-gray-600">{patient.ethnicity || 'N/A'}</p>
				</div>
			</div>
			<div className="flex items-center gap-3">
				<MapPin className="text-muted-foreground h-4 w-4" />
				<div>
					<p className="text-sm font-medium">Nationality</p>
					<p className="text-sm text-gray-600">
						{patient.nationality || 'N/A'}
					</p>
				</div>
			</div>
		</CardContent>
	</Card>
);

const EmergencyContactCard = ({
	patient,
}: {
	patient: IPatientDetailProfile;
}) => (
	<Card>
		<CardHeader>
			<CardTitle className="flex items-center gap-2 text-lg">
				<AlertTriangle className="h-5 w-5" />
				Emergency Contact
			</CardTitle>
		</CardHeader>
		<CardContent className="space-y-4">
			<div className="flex items-center gap-3">
				<User className="text-muted-foreground h-4 w-4" />
				<div>
					<p className="text-sm font-medium">Contact Name</p>
					<p className="text-sm text-gray-600">
						{patient.patient?.emergency_contact_name || 'N/A'}
					</p>
				</div>
			</div>
			<div className="flex items-center gap-3">
				<Phone className="text-muted-foreground h-4 w-4" />
				<div>
					<p className="text-sm font-medium">Contact Number</p>
					<p className="text-sm text-gray-600">
						{patient.patient?.emergency_contact_number || 'N/A'}
					</p>
				</div>
			</div>
			<div className="flex items-center gap-3">
				<UserCheck className="text-muted-foreground h-4 w-4" />
				<div>
					<p className="text-sm font-medium">PWD Status</p>
					<Badge variant={patient.patient?.is_pwd ? 'default' : 'secondary'}>
						{patient.patient?.is_pwd ? 'Person with Disability' : 'Not PWD'}
					</Badge>
				</div>
			</div>
		</CardContent>
	</Card>
);

const StatusCard = ({ patient }: { patient: IPatientDetailProfile }) => (
	<Card>
		<CardHeader>
			<CardTitle className="flex items-center gap-2 text-lg">
				<UserCheck className="h-5 w-5" />
				Account Status
			</CardTitle>
		</CardHeader>
		<CardContent className="space-y-4">
			<div className="flex items-center gap-3">
				<Calendar className="text-muted-foreground h-4 w-4" />
				<div>
					<p className="text-sm font-medium">Registration Date</p>
					<p className="text-sm text-gray-600">
						{formatDate(patient.created_at)}
					</p>
				</div>
			</div>
			<div className="flex items-center gap-3">
				<UserCheck className="text-muted-foreground h-4 w-4" />
				<div>
					<p className="text-sm font-medium">Status</p>
					<div className="flex items-center gap-2">
						<Badge variant={patient.is_verified ? 'default' : 'secondary'}>
							{patient.is_verified ? 'Verified' : 'Unverified'}
						</Badge>
						<Badge variant={patient.is_active ? 'default' : 'secondary'}>
							{patient.is_active ? 'Active' : 'Inactive'}
						</Badge>
						<Badge variant={patient.is_completed ? 'default' : 'secondary'}>
							{patient.is_completed ? 'Complete' : 'Incomplete'}
						</Badge>
					</div>
				</div>
			</div>
		</CardContent>
	</Card>
);

export function AdminPatientDetails() {
	const [profileId] = useQueryState('id', searchParamParsers.id);

	const { data: patient, isLoading, error } = usePatientDetail(profileId || 0);

	const handleBackToPatients = () => {
		window.history.back();
	};

	if (!profileId) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
				<Card className="border-destructive">
					<CardContent className="p-6">
						<div className="text-center">
							<User className="text-destructive mx-auto mb-2 h-8 w-8" />
							<p className="text-destructive text-sm">
								No patient ID provided. Please select a patient to view details.
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	if (isLoading) {
		return <LoadingSkeleton />;
	}

	if (error || !patient) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
				<Card className="border-destructive">
					<CardContent className="p-6">
						<div className="text-center">
							<User className="text-destructive mx-auto mb-2 h-8 w-8" />
							<p className="text-destructive text-sm">
								Failed to load patient details. Please try again.
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			{/* Back Button */}
			<div className="flex items-center gap-2">
				<Button
					variant="outline"
					size="sm"
					onClick={handleBackToPatients}
					className="flex items-center gap-2"
				>
					<ArrowLeft className="h-4 w-4" />
					Back to Patients
				</Button>
			</div>

			<Card>
				<CardHeader>
					<CardTitle className="text-xl text-[oklch(0.7448_0.1256_202.74)]">
						Patient Details
					</CardTitle>
					<p className="text-muted-foreground text-sm">
						Detailed information about {patient.first_name}{' '}
						{patient.middle_name} {patient.last_name} {patient.suffix}
					</p>
				</CardHeader>
				<CardContent className="space-y-6">
					{/* Personal and Medical Information */}
					<div className="grid grid-cols-1 gap-6 md:grid-cols-2">
						<PersonalInfoCard patient={patient} />
						<MedicalInfoCard patient={patient} />
					</div>

					{/* Emergency Contact and Status */}
					<div className="grid grid-cols-1 gap-6 md:grid-cols-2">
						<EmergencyContactCard patient={patient} />
						<StatusCard patient={patient} />
					</div>

					{/* Address Information */}
					{(patient.profile_current_address ||
						patient.profile_permanent_address) && (
						<div className="grid grid-cols-1 gap-6 md:grid-cols-2">
							{patient.profile_current_address && (
								<Card>
									<CardHeader>
										<CardTitle className="flex items-center gap-2 text-lg">
											<Home className="h-5 w-5" />
											Current Address
										</CardTitle>
									</CardHeader>
									<CardContent className="space-y-2">
										<p className="text-sm text-gray-600">
											{patient.profile_current_address.address}
										</p>
										<p className="text-sm text-gray-600">
											{patient.profile_current_address.city},{' '}
											{patient.profile_current_address.province}
										</p>
										<p className="text-sm text-gray-600">
											{patient.profile_current_address.country}{' '}
											{patient.profile_current_address.postal_code}
										</p>
									</CardContent>
								</Card>
							)}

							{patient.profile_permanent_address && (
								<Card>
									<CardHeader>
										<CardTitle className="flex items-center gap-2 text-lg">
											<MapPin className="h-5 w-5" />
											Permanent Address
										</CardTitle>
									</CardHeader>
									<CardContent className="space-y-2">
										<p className="text-sm text-gray-600">
											{patient.profile_permanent_address.address}
										</p>
										<p className="text-sm text-gray-600">
											{patient.profile_permanent_address.city},{' '}
											{patient.profile_permanent_address.province}
										</p>
										<p className="text-sm text-gray-600">
											{patient.profile_permanent_address.country}{' '}
											{patient.profile_permanent_address.postal_code}
										</p>
									</CardContent>
								</Card>
							)}
						</div>
					)}

					{/* Recent Vital Signs */}
					{patient.recent_vital_sign && (
						<Card>
							<CardHeader>
								<CardTitle className="flex items-center gap-2 text-lg">
									<Activity className="h-5 w-5" />
									Recent Vital Signs
								</CardTitle>
								<p className="text-muted-foreground text-sm">
									Last recorded on{' '}
									{formatDate(patient.recent_vital_sign.created_at)}
								</p>
							</CardHeader>
							<CardContent>
								<div className="grid grid-cols-2 gap-4 md:grid-cols-4">
									<div>
										<p className="text-sm font-medium">Blood Pressure</p>
										<p className="text-sm text-gray-600">
											{patient.recent_vital_sign.systolic}/
											{patient.recent_vital_sign.diastolic} mmHg
										</p>
									</div>
									<div>
										<p className="text-sm font-medium">Pulse Rate</p>
										<p className="text-sm text-gray-600">
											{patient.recent_vital_sign.pulse_rate} bpm
										</p>
									</div>
									<div>
										<p className="text-sm font-medium">Temperature</p>
										<p className="text-sm text-gray-600">
											{patient.recent_vital_sign.temperature}°
											{patient.recent_vital_sign.temperature_type}
										</p>
									</div>
									<div>
										<p className="text-sm font-medium">Oxygen Saturation</p>
										<p className="text-sm text-gray-600">
											{patient.recent_vital_sign.oxygen_saturation}%
										</p>
									</div>
								</div>
							</CardContent>
						</Card>
					)}

					{/* Medical Records Summary */}
					{patient.patient && (
						<div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
							{patient.patient.patientAllergy &&
								patient.patient.patientAllergy.length > 0 && (
									<Card>
										<CardHeader>
											<CardTitle className="flex items-center gap-2 text-lg">
												<AlertTriangle className="h-5 w-5" />
												Allergies ({patient.patient.patientAllergy.length})
											</CardTitle>
										</CardHeader>
										<CardContent>
											<div className="space-y-2">
												{patient.patient.patientAllergy
													.slice(0, 3)
													.map((allergy: IPatientAllergy) => (
														<div key={allergy.id} className="text-sm">
															<p className="font-medium">{allergy.name}</p>
															<p className="text-gray-600">
																{allergy.description}
															</p>
														</div>
													))}
												{patient.patient.patientAllergy.length > 3 && (
													<p className="text-muted-foreground text-xs">
														+{patient.patient.patientAllergy.length - 3} more
													</p>
												)}
											</div>
										</CardContent>
									</Card>
								)}

							{patient.patient.patientIllness &&
								patient.patient.patientIllness.length > 0 && (
									<Card>
										<CardHeader>
											<CardTitle className="flex items-center gap-2 text-lg">
												<Stethoscope className="h-5 w-5" />
												Medical Conditions (
												{patient.patient.patientIllness.length})
											</CardTitle>
										</CardHeader>
										<CardContent>
											<div className="space-y-2">
												{patient.patient.patientIllness
													.slice(0, 3)
													.map((illness: IPatientIllness) => (
														<div key={illness.id} className="text-sm">
															<p className="font-medium">{illness.name}</p>
															<p className="text-gray-600">
																{illness.description}
															</p>
														</div>
													))}
												{patient.patient.patientIllness.length > 3 && (
													<p className="text-muted-foreground text-xs">
														+{patient.patient.patientIllness.length - 3} more
													</p>
												)}
											</div>
										</CardContent>
									</Card>
								)}

							{patient.patient.patientFamily &&
								patient.patient.patientFamily.length > 0 && (
									<Card>
										<CardHeader>
											<CardTitle className="flex items-center gap-2 text-lg">
												<Users className="h-5 w-5" />
												Family History ({patient.patient.patientFamily.length})
											</CardTitle>
										</CardHeader>
										<CardContent>
											<div className="space-y-2">
												{patient.patient.patientFamily
													.slice(0, 3)
													.map((family: IPatientFamily) => (
														<div key={family.id} className="text-sm">
															<p className="font-medium">
																{family.family_member}
															</p>
															<p className="text-gray-600">
																{family.medical_condition}
															</p>
														</div>
													))}
												{patient.patient.patientFamily.length > 3 && (
													<p className="text-muted-foreground text-xs">
														+{patient.patient.patientFamily.length - 3} more
													</p>
												)}
											</div>
										</CardContent>
									</Card>
								)}
						</div>
					)}
				</CardContent>
			</Card>

			{/* Patient Profile Management */}
			<AdminPatientProfileManagement
				profileId={patient.id}
				patient={patient.patient}
			/>
		</div>
	);
}
