'use client';

import {
	AlertCircle,
	Calendar,
	Clock,
	FileText,
	Shield,
	User,
} from 'lucide-react';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

export function AdminAuditLogs() {
	// Mock audit log data for demonstration
	const mockAuditLogs = [
		{
			id: 1,
			action: 'Doctor Registration Approved',
			user: 'Admin User',
			timestamp: '2025-01-15 10:30:00',
			details: 'Approved doctor registration for Dr. <PERSON>',
			category: 'User Management',
			severity: 'info',
		},
		{
			id: 2,
			action: 'Subscription Plan Updated',
			user: 'Admin User',
			timestamp: '2025-01-15 09:15:00',
			details: 'Modified Basic Plan pricing from ₱500 to ₱600',
			category: 'Subscription Management',
			severity: 'warning',
		},
		{
			id: 3,
			action: 'Patient Data Accessed',
			user: '<PERSON>. <PERSON>',
			timestamp: '2025-01-15 08:45:00',
			details: 'Viewed patient profile for <PERSON>',
			category: 'Data Access',
			severity: 'info',
		},
		{
			id: 4,
			action: 'System Configuration Changed',
			user: 'Admin User',
			timestamp: '2025-01-14 16:20:00',
			details: 'Updated email notification settings',
			category: 'System Configuration',
			severity: 'high',
		},
	];

	const getSeverityColor = (severity: string) => {
		switch (severity) {
			case 'high':
				return 'destructive';
			case 'warning':
				return 'secondary';
			case 'info':
			default:
				return 'default';
		}
	};

	const getCategoryIcon = (category: string) => {
		switch (category) {
			case 'User Management':
				return <User className="h-4 w-4" />;
			case 'Subscription Management':
				return <FileText className="h-4 w-4" />;
			case 'Data Access':
				return <Shield className="h-4 w-4" />;
			case 'System Configuration':
				return <AlertCircle className="h-4 w-4" />;
			default:
				return <FileText className="h-4 w-4" />;
		}
	};

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
						Audit Logs
					</h1>
					<p className="text-muted-foreground">
						Track system activities and user actions
					</p>
				</div>
			</div>

			<Alert>
				<AlertCircle className="h-4 w-4" />
				<AlertTitle>Development Notice</AlertTitle>
				<AlertDescription>
					The full audit logging system is currently under development. This is
					a preview of the planned functionality. Once implemented, this section
					will track all system activities including user registrations, data
					modifications, access logs, and administrative actions.
				</AlertDescription>
			</Alert>

			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Total Events</CardTitle>
						<FileText className="text-muted-foreground h-4 w-4" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">1,234</div>
						<p className="text-muted-foreground text-xs">
							+12% from last month
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">High Priority</CardTitle>
						<AlertCircle className="text-muted-foreground h-4 w-4" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">23</div>
						<p className="text-muted-foreground text-xs">Requires attention</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							Today&apos;s Events
						</CardTitle>
						<Calendar className="text-muted-foreground h-4 w-4" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">89</div>
						<p className="text-muted-foreground text-xs">+5% from yesterday</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Active Users</CardTitle>
						<User className="text-muted-foreground h-4 w-4" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">156</div>
						<p className="text-muted-foreground text-xs">Currently online</p>
					</CardContent>
				</Card>
			</div>

			<Card>
				<CardHeader>
					<CardTitle>Recent Activity</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						{mockAuditLogs.map((log, index) => (
							<div key={log.id}>
								<div className="flex items-start space-x-4">
									<div className="flex-shrink-0">
										{getCategoryIcon(log.category)}
									</div>
									<div className="flex-1 space-y-1">
										<div className="flex items-center justify-between">
											<p className="text-sm font-medium">{log.action}</p>
											<Badge variant={getSeverityColor(log.severity)}>
												{log.severity}
											</Badge>
										</div>
										<p className="text-muted-foreground text-sm">
											{log.details}
										</p>
										<div className="text-muted-foreground flex items-center space-x-2 text-xs">
											<User className="h-3 w-3" />
											<span>{log.user}</span>
											<Clock className="h-3 w-3" />
											<span>{log.timestamp}</span>
											<Badge variant="outline" className="text-xs">
												{log.category}
											</Badge>
										</div>
									</div>
								</div>
								{index < mockAuditLogs.length - 1 && (
									<Separator className="mt-4" />
								)}
							</div>
						))}
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Planned Features</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid gap-4 md:grid-cols-2">
						<div className="space-y-2">
							<h4 className="font-medium">User Activities</h4>
							<ul className="text-muted-foreground space-y-1 text-sm">
								<li>• Login/logout events</li>
								<li>• Profile modifications</li>
								<li>• Password changes</li>
								<li>• Permission updates</li>
							</ul>
						</div>
						<div className="space-y-2">
							<h4 className="font-medium">System Events</h4>
							<ul className="text-muted-foreground space-y-1 text-sm">
								<li>• Configuration changes</li>
								<li>• Subscription updates</li>
								<li>• Data exports</li>
								<li>• Security events</li>
							</ul>
						</div>
						<div className="space-y-2">
							<h4 className="font-medium">Data Operations</h4>
							<ul className="text-muted-foreground space-y-1 text-sm">
								<li>• Record creation/updates</li>
								<li>• Data deletions</li>
								<li>• File uploads</li>
								<li>• Bulk operations</li>
							</ul>
						</div>
						<div className="space-y-2">
							<h4 className="font-medium">Compliance</h4>
							<ul className="text-muted-foreground space-y-1 text-sm">
								<li>• HIPAA compliance tracking</li>
								<li>• Data retention policies</li>
								<li>• Access control logs</li>
								<li>• Audit trail exports</li>
							</ul>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
