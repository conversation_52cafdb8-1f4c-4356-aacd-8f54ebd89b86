'use client';

import {
	useAllConsultationTypes,
	useCreateConsultationType,
	useRemoveConsultationType,
	useUpdateConsultationType,
} from '@/features/dashboard/hooks/useAdminDashboard';

import { AdminDataManagement } from './admin-data-management';

export function AdminConsultationTypes() {
	const {
		data: consultationTypes,
		isLoading,
		error,
	} = useAllConsultationTypes();
	const createMutation = useCreateConsultationType();
	const updateMutation = useUpdateConsultationType();
	const removeMutation = useRemoveConsultationType();

	const consultationTypesData = consultationTypes?.data || [];
	const isActionLoading =
		createMutation.isPending ||
		updateMutation.isPending ||
		removeMutation.isPending;

	const handleCreate = (data: { name: string; description?: string }) => {
		createMutation.mutate(data);
	};

	const handleUpdate = (
		id: number,
		data: { name?: string; description?: string }
	) => {
		updateMutation.mutate({ consultationTypeId: id, data });
	};

	const handleRemove = (id: number) => {
		removeMutation.mutate(id);
	};

	return (
		<AdminDataManagement
			title="Consultation Types"
			description="Manage consultation types for appointments and medical services"
			data={consultationTypesData}
			isLoading={isLoading}
			error={error}
			onCreate={handleCreate}
			onUpdate={handleUpdate}
			onRemove={handleRemove}
			isActionLoading={isActionLoading}
		/>
	);
}
