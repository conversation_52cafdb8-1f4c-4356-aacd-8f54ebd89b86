'use client';

import {
	useAllPaymentTypes,
	useCreatePaymentType,
	useRemovePaymentType,
	useUpdatePaymentType,
} from '@/features/dashboard/hooks/useAdminDashboard';

import { AdminDataManagement } from './admin-data-management';

export function AdminPaymentTypes() {
	const { data: paymentTypes, isLoading, error } = useAllPaymentTypes();
	const createMutation = useCreatePaymentType();
	const updateMutation = useUpdatePaymentType();
	const removeMutation = useRemovePaymentType();

	const paymentTypesData = paymentTypes?.data || [];
	const isActionLoading =
		createMutation.isPending ||
		updateMutation.isPending ||
		removeMutation.isPending;

	const handleCreate = (data: { name: string; description?: string }) => {
		createMutation.mutate(data);
	};

	const handleUpdate = (
		id: number,
		data: { name?: string; description?: string }
	) => {
		updateMutation.mutate({ paymentTypeId: id, data });
	};

	const handleRemove = (id: number) => {
		removeMutation.mutate(id);
	};

	return (
		<AdminDataManagement
			title="Payment Types"
			description="Manage payment types for billing and financial transactions"
			data={paymentTypesData}
			isLoading={isLoading}
			error={error}
			onCreate={handleCreate}
			onUpdate={handleUpdate}
			onRemove={handleRemove}
			isActionLoading={isActionLoading}
		/>
	);
}
