'use client';

import {
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON>,
	CreditCard,
	Stethoscope,
	<PERSON>r<PERSON><PERSON><PERSON>,
	Users,
} from 'lucide-react';
import { useState } from 'react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useAdminDashboardMetrics } from '@/features/dashboard/hooks/useAdminDashboard';

import { TDashboardPeriod } from '../../types/admin.types';
import PendingDoctors from './doctor/pending-doctors';

interface IMetricCardProps {
	title: string;
	value: number;
	icon: React.ComponentType<{ className?: string }>;
	description: string;
	isLoading?: boolean;
}

const MetricCard = ({
	title,
	value,
	icon: Icon,
	description,
	isLoading,
}: IMetricCardProps) => {
	if (isLoading) {
		return (
			<Card>
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle className="text-sm font-medium">
						<Skeleton className="h-4 w-24" />
					</CardTitle>
					<Skeleton className="h-4 w-4" />
				</CardHeader>
				<CardContent>
					<Skeleton className="mb-1 h-8 w-16" />
					<Skeleton className="h-3 w-32" />
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle className="text-sm font-medium">{title}</CardTitle>
				<Icon className="text-muted-foreground h-4 w-4" />
			</CardHeader>
			<CardContent>
				<div className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
					{value.toLocaleString()}
				</div>
				<p className="text-muted-foreground text-xs">{description}</p>
			</CardContent>
		</Card>
	);
};

const periodLabel = (period: string) => {
	switch (period) {
		case 'day':
			return 'today';
		case 'week':
			return 'this week';
		case 'month':
			return 'this month';
		case 'year':
			return 'this year';
		default:
			return 'all time';
	}
};

export function AdminDashboardOverview() {
	const [period, setPeriod] = useState<TDashboardPeriod>('month');
	const { data: metrics, isLoading, error } = useAdminDashboardMetrics(period);

	if (error) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
				<div className="grid auto-rows-min gap-4 md:grid-cols-2 lg:grid-cols-4">
					<Card className="border-destructive">
						<CardContent className="p-6">
							<div className="text-center">
								<BarChart3 className="text-destructive mx-auto mb-2 h-8 w-8" />
								<p className="text-destructive text-sm">
									Failed to load dashboard metrics
								</p>
							</div>
						</CardContent>
					</Card>
				</div>
			</div>
		);
	}

	// Use metrics data with fallbacks - handle both frontend and backend formats
	const metricsData = metrics?.data;
	const totalDoctors =
		metricsData?.total_doctors || metricsData?.totalDoctors || 0;
	const totalSales = metricsData?.total_sales || metricsData?.totalSales || 0;
	const totalActiveSubscriptions =
		metricsData?.total_active_subscriptions ||
		metricsData?.totalActiveSubscriptions ||
		0;
	const pendingDoctorApplications =
		metricsData?.pending_doctor_applications ||
		metricsData?.totalPendingDoctors ||
		0;

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			{/* Time Period Selector */}
			<div className="flex items-center justify-between">
				<h2 className="text-elena-primary text-2xl font-bold tracking-tight">
					Dashboard Overview
				</h2>
				<div className="flex items-center gap-2">
					<Select
						value={period}
						onValueChange={(value) => setPeriod(value as TDashboardPeriod)}
					>
						<SelectTrigger className="w-[180px]">
							<div className="flex items-center gap-2">
								<Calendar className="text-muted-foreground h-4 w-4" />
								<SelectValue placeholder="Select period" />
							</div>
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="all">All</SelectItem>
							<SelectItem value="day">Today</SelectItem>
							<SelectItem value="week">This Week</SelectItem>
							<SelectItem value="month">This Month</SelectItem>
							<SelectItem value="year">This Year</SelectItem>
						</SelectContent>
					</Select>
				</div>
			</div>

			{/* Metrics Cards */}
			<div className="grid auto-rows-min gap-4 md:grid-cols-2 lg:grid-cols-4">
				<MetricCard
					title="Total Doctors"
					value={totalDoctors}
					icon={Stethoscope}
					description={`Registered medical practitioners (${periodLabel(period)})`}
					isLoading={isLoading}
				/>
				<MetricCard
					title="Total Sales"
					value={totalSales}
					icon={CreditCard}
					description={`Revenue generated (${periodLabel(period)})`}
					isLoading={isLoading}
				/>
				<MetricCard
					title="Active Subscriptions"
					value={totalActiveSubscriptions}
					icon={UserCheck}
					description={`Currently active plans (${periodLabel(period)})`}
					isLoading={isLoading}
				/>
				<MetricCard
					title="Pending Applications"
					value={pendingDoctorApplications}
					icon={Users}
					description={`Awaiting approval (${periodLabel(period)})`}
					isLoading={isLoading}
				/>
			</div>

			{/* Pending Doctors */}
			<div className="mt-2">
				<PendingDoctors />
			</div>
		</div>
	);
}
