'use client';

import {
	useAllAllergyTypes,
	useCreateAllergyType,
	useRemoveAllergyType,
	useUpdateAllergyType,
} from '@/features/dashboard/hooks/useAdminDashboard';

import { AdminDataManagement } from './admin-data-management';

export function AdminAllergyTypes() {
	const { data: allergyTypes, isLoading, error } = useAllAllergyTypes();
	const createMutation = useCreateAllergyType();
	const updateMutation = useUpdateAllergyType();
	const removeMutation = useRemoveAllergyType();

	const allergyTypesData = allergyTypes?.data || [];
	const isActionLoading =
		createMutation.isPending ||
		updateMutation.isPending ||
		removeMutation.isPending;

	const handleCreate = (data: { name: string; description?: string }) => {
		createMutation.mutate(data);
	};

	const handleUpdate = (
		id: number,
		data: { name?: string; description?: string }
	) => {
		updateMutation.mutate({ allergyTypeId: id, data });
	};

	const handleRemove = (id: number) => {
		removeMutation.mutate(id);
	};

	return (
		<AdminDataManagement
			title="Allergy Types"
			description="Manage allergy types for patient medical records"
			data={allergyTypesData}
			isLoading={isLoading}
			error={error}
			onCreate={handleCreate}
			onUpdate={handleUpdate}
			onRemove={handleRemove}
			isActionLoading={isActionLoading}
		/>
	);
}
