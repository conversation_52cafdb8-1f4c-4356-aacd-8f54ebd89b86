'use client';

import {
	useAllHmoTypes,
	useCreateHmoType,
	useRemoveHmoType,
	useUpdateHmoType,
} from '@/features/dashboard/hooks/useAdminDashboard';

import { AdminDataManagement } from './admin-data-management';

export function AdminHMOTypes() {
	const { data: hmoTypes, isLoading, error } = useAllHmoTypes();
	const createMutation = useCreateHmoType();
	const updateMutation = useUpdateHmoType();
	const removeMutation = useRemoveHmoType();

	const hmoTypesData = hmoTypes?.data || [];
	const isActionLoading =
		createMutation.isPending ||
		updateMutation.isPending ||
		removeMutation.isPending;

	const handleCreate = (data: { name: string; description?: string }) => {
		createMutation.mutate(data);
	};

	const handleUpdate = (
		id: number,
		data: { name?: string; description?: string }
	) => {
		updateMutation.mutate({ habitTypeId: id, data });
	};

	const handleRemove = (id: number) => {
		removeMutation.mutate(id);
	};

	return (
		<AdminDataManagement
			title="HMO Types"
			description="Manage HMO types for patient medical records"
			data={hmoTypesData}
			isLoading={isLoading}
			error={error}
			onCreate={handleCreate}
			onUpdate={handleUpdate}
			onRemove={handleRemove}
			isActionLoading={isActionLoading}
		/>
	);
}
