'use client';

import { Edit, Eye, Plus, Search, Shield, Trash2, Users } from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';

// Mock data for user roles - replace with actual API call
const mockUserRoles = [
	{
		id: 1,
		name: 'Super Admin',
		clinic_name: 'Elena Health System',
		permissions: ['ALL'],
		userCount: 2,
		isActive: true,
		createdAt: '2024-01-15',
	},
	{
		id: 2,
		name: 'Clinic Manager',
		clinic_name: 'Elena Main Clinic',
		permissions: ['MANAGE_DOCTORS', 'MANAGE_PATIENTS', 'VIEW_REPORTS'],
		userCount: 5,
		isActive: true,
		createdAt: '2024-01-10',
	},
	{
		id: 3,
		name: 'Receptionist',
		clinic_name: '<PERSON> Main Clinic',
		permissions: ['MANAGE_APPOINTMENTS', 'VIEW_PATIENTS'],
		userCount: 8,
		isActive: true,
		createdAt: '2024-01-05',
	},
	{
		id: 4,
		name: 'Nurse',
		clinic_name: 'Elena Branch Clinic',
		permissions: ['VIEW_PATIENTS', 'UPDATE_MEDICAL_RECORDS'],
		userCount: 12,
		isActive: true,
		createdAt: '2024-01-01',
	},
];

export function AdminUserRoles() {
	const [searchTerm, setSearchTerm] = useState('');
	const userRoles = mockUserRoles; // Replace with actual API call
	const isLoading = false; // Replace with actual loading state

	const filteredRoles = userRoles.filter(
		(role) =>
			role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			role.clinic_name.toLowerCase().includes(searchTerm.toLowerCase())
	);

	const totalRoles = userRoles.length;
	const activeRoles = userRoles.filter((role) => role.isActive).length;
	const totalUsers = userRoles.reduce((sum, role) => sum + role.userCount, 0);

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
						User Roles
					</h1>
					<p className="text-muted-foreground">
						Manage user roles and permissions across the platform
					</p>
				</div>
				<Button>
					<Plus className="mr-2 h-4 w-4" />
					Create Role
				</Button>
			</div>

			{/* Stats Cards */}
			<div className="grid gap-4 md:grid-cols-4">
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Shield className="h-5 w-5" />
							Total Roles
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="py-4 text-center">
							{isLoading ? (
								<Skeleton className="mx-auto h-8 w-16" />
							) : (
								<h3 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
									{totalRoles}
								</h3>
							)}
							<p className="text-muted-foreground text-sm">Defined roles</p>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Shield className="h-5 w-5 text-green-600" />
							Active Roles
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="py-4 text-center">
							{isLoading ? (
								<Skeleton className="mx-auto h-8 w-16" />
							) : (
								<h3 className="text-2xl font-bold text-green-600">
									{activeRoles}
								</h3>
							)}
							<p className="text-muted-foreground text-sm">Currently active</p>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Users className="h-5 w-5 text-blue-600" />
							Total Users
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="py-4 text-center">
							{isLoading ? (
								<Skeleton className="mx-auto h-8 w-16" />
							) : (
								<h3 className="text-2xl font-bold text-blue-600">
									{totalUsers}
								</h3>
							)}
							<p className="text-muted-foreground text-sm">Assigned to roles</p>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Shield className="h-5 w-5 text-purple-600" />
							Avg. Permissions
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="py-4 text-center">
							{isLoading ? (
								<Skeleton className="mx-auto h-8 w-16" />
							) : (
								<h3 className="text-2xl font-bold text-purple-600">
									{Math.round(
										userRoles.reduce(
											(sum, role) => sum + role.permissions.length,
											0
										) / userRoles.length
									)}
								</h3>
							)}
							<p className="text-muted-foreground text-sm">Per role</p>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Search and Filters */}
			<div className="flex items-center gap-4">
				<div className="relative flex-1">
					<Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
					<Input
						placeholder="Search user roles..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="pl-10"
					/>
				</div>
			</div>

			{/* User Roles Table */}
			<Card>
				<CardHeader>
					<CardTitle>User Roles Management</CardTitle>
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="space-y-4">
							{[...Array(5)].map((_, i) => (
								<Skeleton key={i} className="h-12 w-full" />
							))}
						</div>
					) : (
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Role Name</TableHead>
									<TableHead>Clinic</TableHead>
									<TableHead>Permissions</TableHead>
									<TableHead>Users</TableHead>
									<TableHead>Status</TableHead>
									<TableHead>Created</TableHead>
									<TableHead>Actions</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{filteredRoles.length === 0 ? (
									<TableRow>
										<TableCell colSpan={7} className="py-8 text-center">
											<div className="text-muted-foreground">
												{searchTerm
													? 'No roles match your search'
													: 'No user roles found'}
											</div>
										</TableCell>
									</TableRow>
								) : (
									filteredRoles.map((role) => (
										<TableRow key={role.id}>
											<TableCell className="font-medium">{role.name}</TableCell>
											<TableCell>{role.clinic_name}</TableCell>
											<TableCell>
												<div className="flex flex-wrap gap-1">
													{role.permissions
														.slice(0, 2)
														.map((permission, index) => (
															<Badge
																key={index}
																variant="secondary"
																className="text-xs"
															>
																{permission}
															</Badge>
														))}
													{role.permissions.length > 2 && (
														<Badge variant="outline" className="text-xs">
															+{role.permissions.length - 2} more
														</Badge>
													)}
												</div>
											</TableCell>
											<TableCell>
												<div className="flex items-center gap-1">
													<Users className="text-muted-foreground h-4 w-4" />
													{role.userCount}
												</div>
											</TableCell>
											<TableCell>
												<Badge
													variant={role.isActive ? 'default' : 'secondary'}
													className={
														role.isActive ? 'bg-green-100 text-green-800' : ''
													}
												>
													{role.isActive ? 'Active' : 'Inactive'}
												</Badge>
											</TableCell>
											<TableCell>
												{new Date(role.createdAt).toLocaleDateString()}
											</TableCell>
											<TableCell>
												<div className="flex items-center gap-2">
													<Button variant="outline" size="sm">
														<Eye className="h-4 w-4" />
													</Button>
													<Button variant="outline" size="sm">
														<Edit className="h-4 w-4" />
													</Button>
													<Button
														variant="outline"
														size="sm"
														className="text-red-600 hover:text-red-700"
													>
														<Trash2 className="h-4 w-4" />
													</Button>
												</div>
											</TableCell>
										</TableRow>
									))
								)}
							</TableBody>
						</Table>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
