'use client';

import { Clock, Edit, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';

import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
	useAdminAddHistoryToPatient,
	useAdminRemoveHistoryFromPatient,
	useAdminUpdatePatientHistory,
} from '@/features/dashboard/hooks/useAdminDashboard';

interface IPatientHistory {
	id: number;
	name: string;
	description: string;
	created_at: string;
	updated_at: string;
}

interface IAdminPatientHistoryManagementProps {
	profileId: number;
	history: IPatientHistory[];
}

interface IHistoryFormData {
	name: string;
	description: string;
}

export function AdminPatientHistoryManagement({
	profileId,
	history,
}: IAdminPatientHistoryManagementProps) {
	const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [editingHistory, setEditingHistory] = useState<IPatientHistory | null>(
		null
	);
	const [historyToDelete, setHistoryToDelete] =
		useState<IPatientHistory | null>(null);
	const [formData, setFormData] = useState<IHistoryFormData>({
		name: '',
		description: '',
	});

	const addHistoryMutation = useAdminAddHistoryToPatient();
	const updateHistoryMutation = useAdminUpdatePatientHistory();
	const removeHistoryMutation = useAdminRemoveHistoryFromPatient();

	const handleAddHistory = () => {
		if (!formData.name.trim()) {
			console.error('History name is required');
			return;
		}

		// Create payload with name and description as backend expects
		const historyData = {
			name: formData.name,
			description: formData.description,
		};

		addHistoryMutation.mutate(
			{ profileId, historyData },
			{
				onSuccess: () => {
					setIsAddDialogOpen(false);
					setFormData({
						name: '',
						description: '',
					});
				},
			}
		);
	};

	const handleEditHistory = () => {
		if (!editingHistory) return;

		if (!formData.name.trim()) {
			console.error('History name is required');
			return;
		}

		// Create payload with name and description as backend expects
		const historyData = {
			name: formData.name,
			description: formData.description,
		};

		updateHistoryMutation.mutate(
			{
				profileId,
				patientHistoryId: editingHistory.id,
				historyData,
			},
			{
				onSuccess: () => {
					setIsEditDialogOpen(false);
					setEditingHistory(null);
					setFormData({
						name: '',
						description: '',
					});
				},
			}
		);
	};

	const handleRemoveHistory = (history: IPatientHistory) => {
		setHistoryToDelete(history);
		setIsDeleteDialogOpen(true);
	};

	const confirmRemoveHistory = () => {
		if (historyToDelete) {
			removeHistoryMutation.mutate({
				profileId,
				patientHistoryId: historyToDelete.id,
			});
			setIsDeleteDialogOpen(false);
			setHistoryToDelete(null);
		}
	};

	const openEditDialog = (historyItem: IPatientHistory) => {
		setEditingHistory(historyItem);
		setFormData({
			name: historyItem.name,
			description: historyItem.description,
		});
		setIsEditDialogOpen(true);
	};

	return (
		<Card>
			<CardHeader>
				<div className="flex items-center justify-between">
					<CardTitle className="flex items-center gap-2">
						<Clock className="h-5 w-5" />
						Medical History ({history.length})
					</CardTitle>
					<Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
						<DialogTrigger asChild>
							<Button size="sm">
								<Plus className="mr-2 h-4 w-4" />
								Add History
							</Button>
						</DialogTrigger>
						<DialogContent>
							<DialogHeader>
								<DialogTitle>Add Medical History</DialogTitle>
								<DialogDescription>
									Add a new medical history entry to the patient&apos;s profile.
								</DialogDescription>
							</DialogHeader>
							<div className="space-y-4">
								<div>
									<Label htmlFor="name">History Name</Label>
									<Input
										id="name"
										value={formData.name}
										onChange={(e) =>
											setFormData({ ...formData, name: e.target.value })
										}
										placeholder="Enter history name..."
										required
									/>
								</div>
								<div>
									<Label htmlFor="description">Description</Label>
									<Textarea
										id="description"
										value={formData.description}
										onChange={(e) =>
											setFormData({ ...formData, description: e.target.value })
										}
										placeholder="Describe the medical history..."
									/>
								</div>
							</div>
							<DialogFooter>
								<Button
									variant="outline"
									onClick={() => setIsAddDialogOpen(false)}
								>
									Cancel
								</Button>
								<Button
									onClick={handleAddHistory}
									disabled={addHistoryMutation.isPending}
								>
									{addHistoryMutation.isPending ? 'Adding...' : 'Add History'}
								</Button>
							</DialogFooter>
						</DialogContent>
					</Dialog>
				</div>
			</CardHeader>
			<CardContent>
				{history.length === 0 ? (
					<p className="py-4 text-center text-gray-500">
						No medical history recorded for this patient.
					</p>
				) : (
					<div className="space-y-3">
						{history.map((historyItem) => (
							<div
								key={historyItem.id}
								className="flex items-center justify-between rounded-lg border p-3"
							>
								<div className="flex-1">
									<div className="mb-1 flex items-center gap-2">
										<h4 className="font-medium">{historyItem.name}</h4>
									</div>
									<p className="text-sm text-gray-600">
										{historyItem.description}
									</p>
									<p className="mt-1 text-xs text-gray-500">
										Added:{' '}
										{new Date(historyItem.created_at).toLocaleDateString(
											'en-US',
											{
												year: 'numeric',
												month: 'long',
												day: 'numeric',
											}
										)}
									</p>
								</div>
								<div className="flex gap-2">
									<Button
										size="sm"
										variant="outline"
										onClick={() => openEditDialog(historyItem)}
									>
										<Edit className="h-4 w-4" />
									</Button>
									<Button
										size="sm"
										variant="destructive"
										onClick={() => handleRemoveHistory(historyItem)}
										disabled={removeHistoryMutation.isPending}
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								</div>
							</div>
						))}
					</div>
				)}
			</CardContent>

			{/* Edit Dialog */}
			<Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Edit Medical History</DialogTitle>
						<DialogDescription>
							Update the medical history information.
						</DialogDescription>
					</DialogHeader>
					<div className="space-y-4">
						<div>
							<Label htmlFor="editName">History Name</Label>
							<Input
								id="editName"
								value={formData.name}
								onChange={(e) =>
									setFormData({ ...formData, name: e.target.value })
								}
								placeholder="Enter history name..."
								required
							/>
						</div>
						<div>
							<Label htmlFor="editDescription">Description</Label>
							<Textarea
								id="editDescription"
								value={formData.description}
								onChange={(e) =>
									setFormData({ ...formData, description: e.target.value })
								}
								placeholder="Describe the medical history..."
							/>
						</div>
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setIsEditDialogOpen(false)}
						>
							Cancel
						</Button>
						<Button
							onClick={handleEditHistory}
							disabled={updateHistoryMutation.isPending}
						>
							{updateHistoryMutation.isPending
								? 'Updating...'
								: 'Update History'}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Delete Confirmation Dialog */}
			<AlertDialog
				open={isDeleteDialogOpen}
				onOpenChange={setIsDeleteDialogOpen}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Delete Medical History</AlertDialogTitle>
						<AlertDialogDescription>
							Are you sure you want to delete &quot;
							{historyToDelete?.name}
							&quot;? This action cannot be undone.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={confirmRemoveHistory}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
						>
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</Card>
	);
}
