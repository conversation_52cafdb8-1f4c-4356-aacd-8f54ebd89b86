'use client';

import {
	useAllLabRequestTypes,
	useCreateLabRequestType,
	useRemoveLabRequestType,
	useUpdateLabRequestType,
} from '@/features/dashboard/hooks/useAdminDashboard';

import { AdminDataManagement } from './admin-data-management';

export function AdminLabRequestTypes() {
	const { data: labRequestTypes, isLoading, error } = useAllLabRequestTypes();
	const createMutation = useCreateLabRequestType();
	const updateMutation = useUpdateLabRequestType();
	const removeMutation = useRemoveLabRequestType();

	const labRequestTypesData = labRequestTypes?.data || [];
	const isActionLoading =
		createMutation.isPending ||
		updateMutation.isPending ||
		removeMutation.isPending;

	const handleCreate = (data: { name: string; description?: string }) => {
		createMutation.mutate(data);
	};

	const handleUpdate = (
		id: number,
		data: { name?: string; description?: string }
	) => {
		updateMutation.mutate({ labRequestTypeId: id, data });
	};

	const handleRemove = (id: number) => {
		removeMutation.mutate(id);
	};

	return (
		<AdminDataManagement
			title="Lab Request Types"
			description="Manage laboratory test request types and categories"
			data={labRequestTypesData}
			isLoading={isLoading}
			error={error}
			onCreate={handleCreate}
			onUpdate={handleUpdate}
			onRemove={handleRemove}
			isActionLoading={isActionLoading}
		/>
	);
}
