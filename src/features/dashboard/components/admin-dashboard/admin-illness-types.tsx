'use client';

import {
	useAllIllnessTypes,
	useCreateIllnessType,
	useRemoveIllnessType,
	useUpdateIllnessType,
} from '@/features/dashboard/hooks/useAdminDashboard';

import { AdminDataManagement } from './admin-data-management';

export function AdminIllnessTypes() {
	const { data: illnessTypes, isLoading, error } = useAllIllnessTypes();
	const createMutation = useCreateIllnessType();
	const updateMutation = useUpdateIllnessType();
	const removeMutation = useRemoveIllnessType();

	const illnessTypesData = illnessTypes?.data || [];
	const isActionLoading =
		createMutation.isPending ||
		updateMutation.isPending ||
		removeMutation.isPending;

	const handleCreate = (data: { name: string; description?: string }) => {
		createMutation.mutate(data);
	};

	const handleUpdate = (
		id: number,
		data: { name?: string; description?: string }
	) => {
		updateMutation.mutate({ illnessTypeId: id, data });
	};

	const handleRemove = (id: number) => {
		removeMutation.mutate(id);
	};

	return (
		<AdminDataManagement
			title="Illness Types"
			description="Manage illness types and disease categories"
			data={illnessTypesData}
			isLoading={isLoading}
			error={error}
			onCreate={handleCreate}
			onUpdate={handleUpdate}
			onRemove={handleRemove}
			isActionLoading={isActionLoading}
		/>
	);
}
