'use client';

import { Calendar, CreditCard, Search, Users } from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import { useAllSubscriptions } from '@/features/dashboard/hooks/useAdminDashboard';
import { Subscription } from '@/features/dashboard/types/admin.types';

export function AdminActiveSubscriptions() {
	const { data: subscriptions, isLoading, error } = useAllSubscriptions();
	const [searchTerm, setSearchTerm] = useState('');

	const subscriptionData: Subscription[] = subscriptions?.data || [];
	const activeSubscriptions = subscriptionData.filter(
		(sub) => sub.is_active === 1
	);

	const filteredSubscriptions = activeSubscriptions.filter((sub) =>
		sub.name.toLowerCase().includes(searchTerm.toLowerCase())
	);

	const totalRevenue = activeSubscriptions.reduce(
		(sum, sub) => sum + (sub.price || 0),
		0
	);

	if (error) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
				<div className="flex items-center justify-center rounded-lg border border-dashed p-8">
					<div className="text-center">
						<h3 className="text-lg font-semibold">Error Loading Data</h3>
						<p className="text-muted-foreground">
							Failed to load active subscriptions
						</p>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
						Active Subscriptions
					</h1>
					<p className="text-muted-foreground">
						Monitor and manage currently active subscription plans
					</p>
				</div>
			</div>

			{/* Stats Cards */}
			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Users className="h-5 w-5" />
							Active Plans
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="py-4 text-center">
							{isLoading ? (
								<Skeleton className="mx-auto h-8 w-16" />
							) : (
								<h3 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
									{activeSubscriptions.length}
								</h3>
							)}
							<p className="text-muted-foreground text-sm">Currently active</p>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<CreditCard className="h-5 w-5" />
							Total Revenue
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="py-4 text-center">
							{isLoading ? (
								<Skeleton className="mx-auto h-8 w-20" />
							) : (
								<h3 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
									₱{totalRevenue.toLocaleString()}
								</h3>
							)}
							<p className="text-muted-foreground text-sm">From active plans</p>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Calendar className="h-5 w-5" />
							This Month
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="py-4 text-center">
							{isLoading ? (
								<Skeleton className="mx-auto h-8 w-16" />
							) : (
								<h3 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
									{activeSubscriptions.length}
								</h3>
							)}
							<p className="text-muted-foreground text-sm">New subscriptions</p>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Users className="h-5 w-5" />
							Subscribers
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="py-4 text-center">
							{isLoading ? (
								<Skeleton className="mx-auto h-8 w-16" />
							) : (
								<h3 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
									{activeSubscriptions.length * 10}
								</h3>
							)}
							<p className="text-muted-foreground text-sm">Total users</p>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Search and Filters */}
			<div className="flex items-center gap-4">
				<div className="relative flex-1">
					<Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
					<Input
						placeholder="Search active subscriptions..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="pl-10"
					/>
				</div>
			</div>

			{/* Active Subscriptions Table */}
			<Card>
				<CardHeader>
					<CardTitle>Active Subscription Plans</CardTitle>
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="space-y-4">
							{[...Array(5)].map((_, i) => (
								<Skeleton key={i} className="h-12 w-full" />
							))}
						</div>
					) : (
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Plan Name</TableHead>
									<TableHead>Price</TableHead>
									<TableHead>Coverage</TableHead>
									<TableHead>Subscribers</TableHead>
									<TableHead>Status</TableHead>
									<TableHead>Actions</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{filteredSubscriptions.length === 0 ? (
									<TableRow>
										<TableCell colSpan={6} className="py-8 text-center">
											<div className="text-muted-foreground">
												{searchTerm
													? 'No subscriptions match your search'
													: 'No active subscriptions found'}
											</div>
										</TableCell>
									</TableRow>
								) : (
									filteredSubscriptions.map((subscription) => (
										<TableRow key={subscription.id}>
											<TableCell className="font-medium">
												{subscription.name}
											</TableCell>
											<TableCell>
												₱{subscription.price?.toLocaleString()}
											</TableCell>
											<TableCell>
												{subscription.coverage} {subscription.coverage_type}
											</TableCell>
											<TableCell>10</TableCell>
											<TableCell>
												<Badge
													variant="default"
													className="bg-green-100 text-green-800"
												>
													Active
												</Badge>
											</TableCell>
											<TableCell>
												<div className="flex items-center gap-2">
													<Button variant="outline" size="sm">
														View Details
													</Button>
													<Button variant="outline" size="sm">
														Manage
													</Button>
												</div>
											</TableCell>
										</TableRow>
									))
								)}
							</TableBody>
						</Table>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
