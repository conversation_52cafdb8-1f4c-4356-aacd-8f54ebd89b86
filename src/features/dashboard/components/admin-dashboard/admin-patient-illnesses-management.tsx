'use client';

import { Edit, Heart, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';

import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
	useAdminAddIllnessToPatient,
	useAdminRemoveIllnessFromPatient,
	useAdminUpdatePatientIllness,
} from '@/features/dashboard/hooks/useAdminDashboard';

interface IPatientIllness {
	id: number;
	name: string;
	description: string;
	created_at: string;
}

interface IAdminPatientIllnessesManagementProps {
	profileId: number;
	illnesses: IPatientIllness[];
}

interface IIllnessFormData {
	name: string;
	description: string;
}

export function AdminPatientIllnessesManagement({
	profileId,
	illnesses,
}: IAdminPatientIllnessesManagementProps) {
	const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [editingIllness, setEditingIllness] = useState<IPatientIllness | null>(
		null
	);
	const [illnessToDelete, setIllnessToDelete] =
		useState<IPatientIllness | null>(null);
	const [formData, setFormData] = useState<IIllnessFormData>({
		name: '',
		description: '',
	});

	const addIllnessMutation = useAdminAddIllnessToPatient();
	const updateIllnessMutation = useAdminUpdatePatientIllness();
	const removeIllnessMutation = useAdminRemoveIllnessFromPatient();

	const handleAddIllness = () => {
		if (!formData.name.trim()) {
			console.error('Illness name is required');
			return;
		}

		// Create payload with name and description as backend expects
		const illnessData = {
			name: formData.name,
			description: formData.description,
		};

		addIllnessMutation.mutate(
			{ profileId, illnessData },
			{
				onSuccess: () => {
					setIsAddDialogOpen(false);
					setFormData({
						name: '',
						description: '',
					});
				},
			}
		);
	};

	const handleEditIllness = () => {
		if (!editingIllness) return;

		if (!formData.name.trim()) {
			console.error('Illness name is required');
			return;
		}

		// Create payload with name and description as backend expects
		const illnessData = {
			name: formData.name,
			description: formData.description,
		};

		updateIllnessMutation.mutate(
			{
				profileId,
				patientIllnessId: editingIllness.id,
				illnessData,
			},
			{
				onSuccess: () => {
					setIsEditDialogOpen(false);
					setEditingIllness(null);
					setFormData({
						name: '',
						description: '',
					});
				},
			}
		);
	};

	const handleRemoveIllness = (illness: IPatientIllness) => {
		setIllnessToDelete(illness);
		setIsDeleteDialogOpen(true);
	};

	const confirmRemoveIllness = () => {
		if (illnessToDelete) {
			removeIllnessMutation.mutate({
				profileId,
				patientIllnessId: illnessToDelete.id,
			});
			setIsDeleteDialogOpen(false);
			setIllnessToDelete(null);
		}
	};

	const openEditDialog = (illness: IPatientIllness) => {
		setEditingIllness(illness);
		setFormData({
			name: illness.name,
			description: illness.description,
		});
		setIsEditDialogOpen(true);
	};

	return (
		<Card>
			<CardHeader>
				<div className="flex items-center justify-between">
					<CardTitle className="flex items-center gap-2">
						<Heart className="h-5 w-5" />
						Medical Conditions ({illnesses.length})
					</CardTitle>
					<Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
						<DialogTrigger asChild>
							<Button size="sm">
								<Plus className="mr-2 h-4 w-4" />
								Add Illness
							</Button>
						</DialogTrigger>
						<DialogContent>
							<DialogHeader>
								<DialogTitle>Add New Medical Condition</DialogTitle>
								<DialogDescription>
									Add a new medical condition to the patient&apos;s profile.
								</DialogDescription>
							</DialogHeader>
							<div className="space-y-4">
								<div>
									<Label htmlFor="name">Illness Name</Label>
									<Input
										id="name"
										value={formData.name}
										onChange={(e) =>
											setFormData({ ...formData, name: e.target.value })
										}
										placeholder="Enter illness name..."
										required
									/>
								</div>
								<div>
									<Label htmlFor="description">Description</Label>
									<Textarea
										id="description"
										value={formData.description}
										onChange={(e) =>
											setFormData({ ...formData, description: e.target.value })
										}
										placeholder="Describe the illness details..."
									/>
								</div>
							</div>
							<DialogFooter>
								<Button
									variant="outline"
									onClick={() => setIsAddDialogOpen(false)}
								>
									Cancel
								</Button>
								<Button
									onClick={handleAddIllness}
									disabled={addIllnessMutation.isPending}
								>
									{addIllnessMutation.isPending ? 'Adding...' : 'Add Illness'}
								</Button>
							</DialogFooter>
						</DialogContent>
					</Dialog>
				</div>
			</CardHeader>
			<CardContent>
				{illnesses.length === 0 ? (
					<p className="py-4 text-center text-gray-500">
						No medical conditions recorded for this patient.
					</p>
				) : (
					<div className="space-y-3">
						{illnesses.map((illness) => (
							<div
								key={illness.id}
								className="flex items-center justify-between rounded-lg border p-3"
							>
								<div className="flex-1">
									<h4 className="font-medium">{illness.name}</h4>
									<p className="text-sm text-gray-600">{illness.description}</p>
								</div>
								<div className="flex gap-2">
									<Button
										size="sm"
										variant="outline"
										onClick={() => openEditDialog(illness)}
									>
										<Edit className="h-4 w-4" />
									</Button>
									<Button
										size="sm"
										variant="destructive"
										onClick={() => handleRemoveIllness(illness)}
										disabled={removeIllnessMutation.isPending}
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								</div>
							</div>
						))}
					</div>
				)}
			</CardContent>

			{/* Edit Dialog */}
			<Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Edit Medical Condition</DialogTitle>
						<DialogDescription>
							Update the medical condition information.
						</DialogDescription>
					</DialogHeader>
					<div className="space-y-4">
						<div>
							<Label htmlFor="editName">Illness Name</Label>
							<Input
								id="editName"
								value={formData.name}
								onChange={(e) =>
									setFormData({ ...formData, name: e.target.value })
								}
								placeholder="Enter illness name..."
								required
							/>
						</div>
						<div>
							<Label htmlFor="editDescription">Description</Label>
							<Textarea
								id="editDescription"
								value={formData.description}
								onChange={(e) =>
									setFormData({ ...formData, description: e.target.value })
								}
								placeholder="Describe the illness details..."
							/>
						</div>
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setIsEditDialogOpen(false)}
						>
							Cancel
						</Button>
						<Button
							onClick={handleEditIllness}
							disabled={updateIllnessMutation.isPending}
						>
							{updateIllnessMutation.isPending
								? 'Updating...'
								: 'Update Illness'}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Delete Confirmation Dialog */}
			<AlertDialog
				open={isDeleteDialogOpen}
				onOpenChange={setIsDeleteDialogOpen}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Delete Medical Condition</AlertDialogTitle>
						<AlertDialogDescription>
							Are you sure you want to delete &quot;{illnessToDelete?.name}
							&quot;? This action cannot be undone.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={confirmRemoveIllness}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
						>
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</Card>
	);
}
