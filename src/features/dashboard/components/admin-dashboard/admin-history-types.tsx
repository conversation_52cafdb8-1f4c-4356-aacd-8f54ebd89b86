'use client';

import {
	useAllHistoryTypes,
	useCreateHistoryType,
	useRemoveHistoryType,
	useUpdateHistoryType,
} from '@/features/dashboard/hooks/useAdminDashboard';

import { AdminDataManagement } from './admin-data-management';

export function AdminHistoryTypes() {
	const { data: historyTypes, isLoading, error } = useAllHistoryTypes();
	const createMutation = useCreateHistoryType();
	const updateMutation = useUpdateHistoryType();
	const removeMutation = useRemoveHistoryType();

	const historyTypesData = historyTypes?.data || [];
	const isActionLoading =
		createMutation.isPending ||
		updateMutation.isPending ||
		removeMutation.isPending;

	const handleCreate = (data: { name: string; description?: string }) => {
		createMutation.mutate(data);
	};

	const handleUpdate = (
		id: number,
		data: { name?: string; description?: string }
	) => {
		updateMutation.mutate({ historyTypeId: id, data });
	};

	const handleRemove = (id: number) => {
		removeMutation.mutate(id);
	};

	return (
		<AdminDataManagement
			title="History Types"
			description="Manage medical history types and categories"
			data={historyTypesData}
			isLoading={isLoading}
			error={error}
			onCreate={handleCreate}
			onUpdate={handleUpdate}
			onRemove={handleRemove}
			isActionLoading={isActionLoading}
		/>
	);
}
