'use client';

import {
	ColumnDef,
	ColumnFiltersState,
	flexRender,
	getCoreRowModel,
	getFacetedUniqueValues,
	getFilteredRowModel,
	getPaginationRowModel,
	getSortedRowModel,
	SortingState,
	useReactTable,
	VisibilityState,
} from '@tanstack/react-table';
import { useState } from 'react';

import { DataTablePagination } from '@/components/ui-customs/table/table-pagination';
import { DataTablePaginationMeta } from '@/components/ui-customs/table/table-pagination-meta';
import { Skeleton } from '@/components/ui/skeleton';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import XScroll from '@/components/ui/x-scroll';
import { IMeta } from '@/core/types/index.type';
import { IDoctorData } from '@/features/dashboard/api/doctors.type';
import { useDoctors } from '@/features/dashboard/hooks/useDoctors';

import { DataTableToolbar } from './table-toolbar';

interface DoctorsTableProps {
	columns: ColumnDef<IDoctorData>[];
	data: IDoctorData[];
	metadata?: IMeta;
	isLoading?: boolean;
}

export function DoctorsTable({
	columns,
	data,
	metadata,
	isLoading = false,
}: DoctorsTableProps) {
	const [sorting, setSorting] = useState<SortingState>([]);
	const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
	const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

	const { state } = useDoctors();

	const table = useReactTable({
		data,
		columns,
		getCoreRowModel: getCoreRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		onSortingChange: setSorting,
		getSortedRowModel: getSortedRowModel(),
		onColumnFiltersChange: setColumnFilters,
		getFilteredRowModel: getFilteredRowModel(),
		onColumnVisibilityChange: setColumnVisibility,
		getFacetedUniqueValues: getFacetedUniqueValues(),
		state: {
			sorting,
			columnFilters,
			columnVisibility,
		},
	});

	return (
		<div className="space-y-4">
			<DataTableToolbar table={table} />

			<div className="overflow-hidden rounded-md border bg-white">
				<XScroll>
					<Table>
						<TableHeader className="bg-gray-50">
							{table.getHeaderGroups().map((headerGroup) => (
								<TableRow key={headerGroup.id}>
									{headerGroup.headers.map((header) => {
										return (
											<TableHead key={header.id} className="px-4">
												{header.isPlaceholder
													? null
													: flexRender(
															header.column.columnDef.header,
															header.getContext()
														)}
											</TableHead>
										);
									})}
								</TableRow>
							))}
						</TableHeader>
						<TableBody>
							{isLoading ? (
								// Loading skeleton rows
								Array.from({ length: 5 }).map((_, index) => (
									<TableRow key={`skeleton-${index}`}>
										{columns.map((column, colIndex) => (
											<TableCell
												key={`skeleton-cell-${index}-${colIndex}`}
												className="px-4"
											>
												<Skeleton className="h-4 w-full" />
											</TableCell>
										))}
									</TableRow>
								))
							) : table.getRowModel().rows?.length ? (
								table.getRowModel().rows.map((row) => (
									<TableRow
										key={row.id}
										data-state={row.getIsSelected() && 'selected'}
									>
										{row.getVisibleCells().map((cell) => (
											<TableCell key={cell.id} className="px-4">
												{flexRender(
													cell.column.columnDef.cell,
													cell.getContext()
												)}
											</TableCell>
										))}
									</TableRow>
								))
							) : (
								<TableRow>
									<TableCell
										colSpan={columns.length}
										className="h-24 text-center"
									>
										No results.
									</TableCell>
								</TableRow>
							)}
						</TableBody>
					</Table>
				</XScroll>
			</div>

			{metadata ? (
				<DataTablePaginationMeta
					table={table}
					meta={metadata}
					onChangePageSize={(pageSize) => {
						state.params.pageSize.set(pageSize);
					}}
					onChangePage={(page) => {
						state.params.page.set(page);
					}}
				/>
			) : (
				<DataTablePagination table={table} />
			)}
		</div>
	);
}
