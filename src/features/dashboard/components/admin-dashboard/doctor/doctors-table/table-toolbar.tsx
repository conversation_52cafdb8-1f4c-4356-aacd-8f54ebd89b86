'use client';

import { Cross2Icon } from '@radix-ui/react-icons';
import { Table } from '@tanstack/react-table';

import { DataTableViewOptions } from '@/components/ui-customs/table/table-view-option';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { IMeta } from '@/core/types/index.type';
import { useDoctors } from '@/features/dashboard/hooks/useDoctors';

import { CreateDoctorDialog } from './create-doctor';

interface DataTableToolbarProps<TData> {
	id?: string;
	table: Table<TData>;
	meta?: IMeta | null;
}

export function DataTableToolbar<TData>({
	table,
}: DataTableToolbarProps<TData>) {
	const isFiltered = table.getState().columnFilters.length > 0;
	const { state } = useDoctors();

	return (
		<div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
			<div className="flex flex-1 flex-wrap items-center gap-2">
				<Input
					placeholder="Search name, email, or specialty"
					onChange={(event) => {
						state.params.search.set(event.target.value);
					}}
					className="h-8 w-full sm:w-[150px] lg:w-[250px]"
				/>

				{isFiltered && (
					<Button
						variant="destructive"
						onClick={() => table.resetColumnFilters()}
						className="h-8 px-2 lg:px-3"
					>
						Reset
						<Cross2Icon className="ml-2 size-4" />
					</Button>
				)}
			</div>

			<div className="flex gap-2">
				<CreateDoctorDialog />

				<DataTableViewOptions table={table} />
			</div>
		</div>
	);
}
