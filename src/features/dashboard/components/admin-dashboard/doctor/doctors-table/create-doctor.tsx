'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { PlusIcon } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogClose,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import { Form } from '@/components/ui/form';
import {
	IRegisterDoctor,
	RegisterDoctorSchema,
} from '@/core/api/registration/doctor.type';
import { useDoctorActions } from '@/features/dashboard/hooks/useDoctorActions';
import { useDoctors } from '@/features/dashboard/hooks/useDoctors';
import PersonalInfoForm from '@/features/registration/components/doctor/personal-info.form';
import ProfessionalInfoForm from '@/features/registration/components/doctor/professional-info.form';

export function CreateDoctorDialog() {
	const [open, setOpen] = useState(false);
	const { refetch } = useDoctors();
	const { createAction, isCreating } = useDoctorActions('admin');

	const form = useForm<IRegisterDoctor>({
		resolver: zodResolver(RegisterDoctorSchema),
		defaultValues: {
			// Personal Info
			firstName: '',
			middleName: '',
			lastName: '',
			suffix: '',
			email: '',
			phone: '',
			gender: '',
			birthday: '',

			// Professional Info
			prcNumber: '',
			prcExpiryDate: '',
			prcImageFront: undefined,
			prcImageBack: undefined,
			clinicAddress: '',
			clinicName: '',
			specialty: '',
		},
	});

	const onSubmit = (data: IRegisterDoctor) => {
		createAction(data, {
			onSuccess: () => {
				refetch();
				form.reset();
				setOpen(false);
			},
		});
	};

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogTrigger asChild>
				<Button className="h-8">
					<PlusIcon className="size-4" />
					Create Doctor
				</Button>
			</DialogTrigger>
			<DialogContent className="font-sans sm:max-w-2xl">
				<DialogHeader>
					<DialogTitle className="text-primary">Add New Doctor</DialogTitle>
					<DialogDescription>Add new doctor to the system.</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
						{/* Personal Information Section */}
						<PersonalInfoForm form={form} />

						{/* Professional Information Section */}
						<ProfessionalInfoForm form={form} />

						<DialogFooter className="sm:justify-end">
							<DialogClose asChild>
								<Button type="button" variant="outline" disabled={isCreating}>
									Cancel
								</Button>
							</DialogClose>
							<Button type="submit" disabled={isCreating}>
								{isCreating ? 'Creating...' : 'Create Doctor'}
							</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
