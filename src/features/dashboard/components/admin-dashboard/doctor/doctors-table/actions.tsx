'use client';

import { useHookstate } from '@hookstate/core';
import { EllipsisVerticalIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';

import { ConfirmationDialog } from '@/components/common/alert-dialog/confirmation-alert-dialog';
import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { IDoctorData } from '@/features/dashboard/api/doctors.type';
import { useDoctorActions } from '@/features/dashboard/hooks/useDoctorActions';
import { useDoctors } from '@/features/dashboard/hooks/useDoctors';

interface DoctorsActionsProps {
	doctor: IDoctorData;
}

export default function DoctorsActions({ doctor }: DoctorsActionsProps) {
	const menu = useHookstate(false);
	const router = useRouter();
	const { refetch } = useDoctors();
	const { disableAction, enableAction, isDisabling, isEnabling } =
		useDoctorActions('admin');

	const disableDialog = useHookstate(false);
	const enableDialog = useHookstate(false);

	return (
		<div>
			<DropdownMenu open={menu.value} onOpenChange={(v) => menu.set(v)}>
				<DropdownMenuTrigger asChild>
					<Button className="" size={'icon'} variant={'ghost'} title="Actions">
						<EllipsisVerticalIcon className="size-4" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end">
					<DropdownMenuItem
						title="View Doctor Details"
						onClick={() => {
							menu.set(false);
							router.push(`/dashboard?tab=doctor-details&id=${doctor.id}`);
						}}
					>
						View Details
					</DropdownMenuItem>
					{doctor.is_active ? (
						<>
							<DropdownMenuSeparator />
							<DropdownMenuItem
								title="Disable Doctor"
								className="text-red-600 hover:!bg-red-50 hover:!text-red-600"
								disabled={isDisabling}
								onClick={() => {
									menu.set(false);
									disableDialog.set(true);
								}}
							>
								Disable
							</DropdownMenuItem>
						</>
					) : (
						<>
							<DropdownMenuSeparator />
							<DropdownMenuItem
								title="Enable Doctor"
								className="text-green-600 hover:!bg-green-50 hover:!text-green-600"
								disabled={isEnabling}
								onClick={() => {
									menu.set(false);
									enableDialog.set(true);
								}}
							>
								Enable
							</DropdownMenuItem>
						</>
					)}
				</DropdownMenuContent>
			</DropdownMenu>

			{/* Disable Confirmation Dialog */}
			<ConfirmationDialog
				open={disableDialog.value}
				onOpenChange={(v) => disableDialog.set(v)}
				title="Disable Doctor"
				description="Are you sure you want to disable this doctor? They will no longer be able to access their account."
				onClick={() =>
					disableAction(doctor.id, {
						onSuccess: () => {
							refetch();
							disableDialog.set(false);
						},
					})
				}
				isLoading={isDisabling}
				variant="destructive"
			/>

			{/* Enable Confirmation Dialog */}
			<ConfirmationDialog
				open={enableDialog.value}
				onOpenChange={(v) => enableDialog.set(v)}
				title="Enable Doctor"
				description="Are you sure you want to enable this doctor? They will be able to access their account again."
				onClick={() =>
					enableAction(doctor.id, {
						onSuccess: () => {
							refetch();
							enableDialog.set(false);
						},
					})
				}
				isLoading={isEnabling}
			/>
		</div>
	);
}
