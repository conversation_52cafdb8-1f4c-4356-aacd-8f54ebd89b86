'use client';

import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { CalendarIcon, MailIcon, PhoneIcon } from 'lucide-react';

import { DataTableColumnHeader } from '@/components/ui-customs/table/table-column-header';
import { cn } from '@/core/lib/utils';
import { IDoctorData } from '@/features/dashboard/api/doctors.type';

import DoctorsActions from './actions';

export const columnsDoctors: ColumnDef<IDoctorData>[] = [
	{
		id: 'name',
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Name" />
		),
		cell: ({ row }) => {
			const data = row.original;

			return (
				<div className="min-w-max font-semibold">
					{data.first_name} {data.last_name} {data.suffix}
				</div>
			);
		},
	},
	{
		id: 'email',
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Email" />
		),
		cell: ({ row }) => {
			const data = row.original;

			return (
				<div className="flex items-center gap-2 select-all">
					<MailIcon className="text-muted-foreground h-4 w-4" />
					{data.user?.email || '-'}
				</div>
			);
		},
	},
	{
		id: `specialty`,
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Specialty" />
		),
		cell: ({ row }) => {
			const data = row.original;

			return (
				<div
					className={cn(
						'inline-block rounded-md border px-2 py-0.5 text-xs tracking-wide capitalize',
						data.doctor?.specialty ? 'text-primary' : 'text-destructive'
					)}
				>
					{data.doctor?.specialty || 'N/A'}
				</div>
			);
		},
	},
	{
		id: 'contact',
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Contact" />
		),
		cell: ({ row }) => {
			const data = row.original;

			return (
				<div className="flex items-center gap-2 select-all">
					<PhoneIcon className="text-muted-foreground h-4 w-4" />
					{data.phone || '-'}
				</div>
			);
		},
	},
	{
		id: 'last_login',
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Last Login" />
		),
		cell: ({ row }) => {
			const data = row.original;

			return (
				<div className="flex items-center gap-2">
					<CalendarIcon className="text-muted-foreground size-4" />
					{data.user?.last_login
						? format(new Date(data.user.last_login), 'MMM dd, yyyy hh:mm aa')
						: 'Never'}
				</div>
			);
		},
	},
	{
		id: 'status',
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Status" />
		),
		cell: ({ row }) => {
			const data = row.original;

			return (
				<div className="flex items-center gap-3">
					<div
						className={cn(
							'rounded-md border px-2 py-0.5 text-xs font-semibold tracking-wide',
							'flex items-center gap-2'
						)}
					>
						<div
							className={cn('size-1.5 animate-pulse rounded-full', {
								'bg-green-500': data.is_active,
								'bg-red-500': !data.is_active,
							})}
						></div>
						{data.is_active ? 'Active' : 'Inactive'}
					</div>
				</div>
			);
		},
	},
	{
		id: 'action',
		header: ({ column }) => <DataTableColumnHeader column={column} title="" />,
		cell: ({ row }) => {
			const data = row.original;

			return <DoctorsActions doctor={data} />;
		},
	},
];
