'use client';

import { useHookstate } from '@hookstate/core';
import { format } from 'date-fns';
import {
	CalendarIcon,
	EllipsisVerticalIcon,
	RefreshCcwIcon,
	TriangleAlertIcon,
} from 'lucide-react';
import { useQueryState } from 'nuqs';

import { ConfirmationDialog } from '@/components/common/alert-dialog/confirmation-alert-dialog';
import LoadingFull from '@/components/common/loading/loading-full';
import { Button } from '@/components/ui/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import { cn } from '@/core/lib/utils';
import { usePendingDoctors } from '@/features/dashboard/hooks/usePendingDoctors';

export default function PendingDoctors() {
	const [, setTab] = useQueryState('tab');
	const [, setId] = useQueryState('id');

	const {
		pendingDoctors,
		isLoading,
		error,
		refetch,
		isSuccess,
		acceptAction,
		rejectAction,
		isAccepting,
		isRejecting,
	} = usePendingDoctors();

	const selectedDoctorId = useHookstate(0);
	const acceptDialog = useHookstate(false);
	const declineDialog = useHookstate(false);
	const menu = useHookstate(false);

	return (
		<div>
			<Card>
				<CardHeader className="relative">
					<CardTitle>Pending Doctors</CardTitle>
					<CardDescription>
						Review and approve doctor registration applications
					</CardDescription>

					<div className="absolute top-1 right-6">
						<Button variant={'ghost'} title="Refresh" onClick={() => refetch()}>
							<RefreshCcwIcon
								className={cn('size-4', {
									'animate-spin': isLoading,
								})}
							/>
						</Button>
					</div>
				</CardHeader>

				<CardContent>
					<div className="overflow-hidden rounded-lg border">
						<Table>
							<TableHeader className="bg-gray-50">
								<TableRow>
									<TableHead className="px-4">Name</TableHead>
									<TableHead className="px-4">Specialty</TableHead>
									<TableHead className="px-4">Applied Date</TableHead>
									<TableHead className="px-4">
										<span className="sr-only">Action</span>
									</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{isLoading && (
									<TableRow>
										<TableCell colSpan={4}>
											<div className="grid place-items-center gap-2 py-4">
												<LoadingFull />
											</div>
										</TableCell>
									</TableRow>
								)}

								{error && !isLoading && (
									<TableRow>
										<TableCell colSpan={4}>
											<div className="py-8 text-center">
												<div className="mx-auto grid size-12 place-items-center rounded-full bg-red-50">
													<TriangleAlertIcon className="mx-auto size-6 text-red-600" />
												</div>
												<div className="mt-2">
													Error has occured while loading pending doctors
												</div>
												<Button
													onClick={() => refetch()}
													className="mt-4 h-8"
													variant={'outline'}
												>
													Try Again
												</Button>
											</div>
										</TableCell>
									</TableRow>
								)}

								{isSuccess &&
									!isLoading &&
									pendingDoctors?.data.map((doctor) => (
										<TableRow key={doctor.id} className="hover:bg-white">
											<TableCell className="px-4 py-3 font-semibold">
												{doctor.first_name} {doctor.last_name} {doctor.suffix}
											</TableCell>
											<TableCell className="px-4 py-3 text-gray-600">
												<div
													className={cn(
														'inline-block rounded-md border px-2 py-0.5 text-xs tracking-wide',
														doctor.doctor.specialty
															? 'text-primary'
															: 'text-destructive'
													)}
												>
													{doctor.doctor.specialty || 'N/A'}
												</div>
											</TableCell>
											<TableCell className="px-4 py-3 text-gray-600">
												<div className="flex items-center gap-2">
													<CalendarIcon className="text-primary size-4" />
													{format(
														new Date(doctor.created_at),
														'MMM dd, yyyy hh:mm aa'
													)}
												</div>
											</TableCell>
											<TableCell>
												<div className="flex items-center justify-end gap-2">
													<DropdownMenu
														open={menu.value}
														onOpenChange={(v) => menu.set(v)}
													>
														<DropdownMenuTrigger asChild>
															<Button
																className=""
																size={'icon'}
																variant={'ghost'}
																title="Actions"
															>
																<EllipsisVerticalIcon className="size-4" />
															</Button>
														</DropdownMenuTrigger>
														<DropdownMenuContent align="end">
															<DropdownMenuItem
																title="View Doctor Details"
																onClick={() => {
																	menu.set(false);
																	// router.push(
																	// 	`/admin/doctor/details?pid=${doctor.id}`
																	// );
																	setTab('doctor-details');
																	setId(`${doctor.id}`);
																}}
															>
																View Details
															</DropdownMenuItem>
															<DropdownMenuItem
																title="Accept Application"
																className="text-green-600 hover:!bg-green-50 hover:!text-green-600"
																disabled={isAccepting}
																onClick={() => {
																	menu.set(false);
																	selectedDoctorId.set(doctor.id);
																	acceptDialog.set(true);
																}}
															>
																Accept
															</DropdownMenuItem>
															<DropdownMenuSeparator />
															<DropdownMenuItem
																title="Decline Application"
																className="text-red-600 hover:!bg-red-50 hover:!text-red-600"
																disabled={isRejecting}
																onClick={() => {
																	menu.set(false);
																	selectedDoctorId.set(doctor.id);
																	declineDialog.set(true);
																}}
															>
																Decline
															</DropdownMenuItem>
														</DropdownMenuContent>
													</DropdownMenu>
												</div>
											</TableCell>
										</TableRow>
									))}
							</TableBody>
						</Table>
					</div>
				</CardContent>
			</Card>

			{/* Accept */}
			<ConfirmationDialog
				open={acceptDialog.value}
				onOpenChange={(v) => acceptDialog.set(v)}
				title="Accept Doctor Application"
				description="Are you sure you want to accept this doctor application?"
				onClick={() =>
					acceptAction(selectedDoctorId.value, {
						onSuccess: () => acceptDialog.set(false),
					})
				}
				isLoading={isAccepting}
			/>

			{/* Decline */}
			<ConfirmationDialog
				open={declineDialog.value}
				onOpenChange={(v) => declineDialog.set(v)}
				title="Decline Doctor Application"
				description="Are you sure you want to decline this doctor application?"
				onClick={() =>
					rejectAction(selectedDoctorId.value, {
						onSuccess: () => declineDialog.set(false),
					})
				}
				isLoading={isRejecting}
				variant="destructive"
			/>
		</div>
	);
}
