'use client';

import { Mail, User } from 'lucide-react';
import { useQueryState } from 'nuqs';

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import XScroll from '@/components/ui/x-scroll';
import useClientSide from '@/core/hooks/utils/useClientSide';
import { searchParamParsers } from '@/core/lib/search-params';
import { useDoctorDetail } from '@/features/dashboard/hooks/useAdminDashboard';

import AccountInfoTab from '../doctor-dashboard/doctor/doctor-details-tab/account-info-tab';
import AddressTab from '../doctor-dashboard/doctor/doctor-details-tab/address-tab';
import ClinicsTab from '../doctor-dashboard/doctor/doctor-details-tab/clinics-tab';
import DocumentsTab from '../doctor-dashboard/doctor/doctor-details-tab/documents-tab';
import PersonalInfoTab from '../doctor-dashboard/doctor/doctor-details-tab/personal-info-tab';
import ProfessionalInfoTab from '../doctor-dashboard/doctor/doctor-details-tab/professional-info-tab';
import ProfileStatusOverview from '../doctor-dashboard/doctor/doctor-details-tab/profile-status-overview';
import SubscriptionTab from '../doctor-dashboard/doctor/doctor-details-tab/subscription-tab';

const LoadingSkeleton = () => (
	<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
		<Card>
			<CardHeader>
				<Skeleton className="h-8 w-64" />
				<Skeleton className="h-4 w-96" />
			</CardHeader>
			<CardContent className="space-y-6">
				<div className="grid grid-cols-1 gap-6 md:grid-cols-2">
					<Card>
						<CardHeader>
							<Skeleton className="h-6 w-32" />
						</CardHeader>
						<CardContent className="space-y-4">
							{Array.from({ length: 4 }).map((_, index) => (
								<div key={index} className="flex items-center gap-3">
									<Skeleton className="h-4 w-4" />
									<Skeleton className="h-4 w-48" />
								</div>
							))}
						</CardContent>
					</Card>
					<Card>
						<CardHeader>
							<Skeleton className="h-6 w-32" />
						</CardHeader>
						<CardContent className="space-y-4">
							{Array.from({ length: 3 }).map((_, index) => (
								<div key={index} className="flex items-center gap-3">
									<Skeleton className="h-4 w-4" />
									<Skeleton className="h-4 w-48" />
								</div>
							))}
						</CardContent>
					</Card>
				</div>
			</CardContent>
		</Card>
	</div>
);

export function AdminDoctorDetails() {
	const [profileId] = useQueryState('id', searchParamParsers.id);

	const { data: doctor, isLoading, error } = useDoctorDetail(profileId || 0);
	const { isBrowser } = useClientSide();

	if (!profileId) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
				<Card className="border-destructive">
					<CardContent className="p-6">
						<div className="text-center">
							<User className="text-destructive mx-auto mb-2 h-8 w-8" />
							<p className="text-destructive text-sm">
								No doctor ID provided. Please select a doctor to view details.
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	if (isLoading) {
		return <LoadingSkeleton />;
	}

	if (error) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
				<Card className="border-destructive">
					<CardContent className="p-6">
						<div className="text-center">
							<Mail className="text-destructive mx-auto mb-2 h-8 w-8" />
							<p className="text-destructive text-sm">
								Failed to load doctor details
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	if (!doctor) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
				<Card className="border-destructive">
					<CardContent className="p-6">
						<div className="text-center">
							<User className="text-destructive mx-auto mb-2 h-8 w-8" />
							<p className="text-destructive text-sm">Doctor not found</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="pt-0">
			{isBrowser && (
				<div className="p-4 pt-0">
					<div className="flex items-start gap-1">
						<div className="">
							<h1 className="text-elena-primary text-xl font-bold tracking-tight sm:text-2xl">
								Doctor Details
							</h1>
							{doctor && (
								<p className="text-gray-600">
									Detailed information about {doctor.first_name}{' '}
									{doctor.middle_name} {doctor.last_name} {doctor.suffix}
								</p>
							)}
						</div>
					</div>

					{/* Doctor Details Content */}
					{!isLoading && doctor && (
						<div className="mt-6">
							{/* Profile Status Overview */}
							<ProfileStatusOverview doctor={doctor} />

							{/* Tabbed Content */}
							<Tabs defaultValue="personal" className="w-full">
								<XScroll>
									<TabsList className="space-x-2">
										<TabsTrigger value="personal">Personal Info</TabsTrigger>
										<TabsTrigger value="professional">
											Professional Info
										</TabsTrigger>
										<TabsTrigger value="clinics">Clinics</TabsTrigger>
										<TabsTrigger value="address">Address</TabsTrigger>
										<TabsTrigger value="documents">Documents</TabsTrigger>
										<TabsTrigger value="account">Account Info</TabsTrigger>
										<TabsTrigger value="subscription">Subscription</TabsTrigger>
									</TabsList>
								</XScroll>

								<TabsContent value="personal" className="mt-6">
									<PersonalInfoTab doctor={doctor} />
								</TabsContent>

								<TabsContent value="professional" className="mt-6">
									<ProfessionalInfoTab doctor={doctor} />
								</TabsContent>

								<TabsContent value="clinics" className="mt-6">
									<ClinicsTab doctor={doctor} />
								</TabsContent>

								<TabsContent value="address" className="mt-6">
									<AddressTab doctor={doctor} />
								</TabsContent>

								<TabsContent value="documents" className="mt-6">
									<DocumentsTab doctor={doctor} />
								</TabsContent>

								<TabsContent value="account" className="mt-6">
									<AccountInfoTab doctor={doctor} />
								</TabsContent>

								<TabsContent value="subscription" className="mt-6">
									<SubscriptionTab doctor={doctor} />
								</TabsContent>
							</Tabs>
						</div>
					)}
				</div>
			)}
		</div>
	);
}
