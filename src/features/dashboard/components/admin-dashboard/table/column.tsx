'use client';

import { ColumnDef } from '@tanstack/react-table';
import { EyeI<PERSON>, Trash } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { cn } from '@/core/lib/utils';
import { useRemoveSubscription } from '@/features/dashboard/hooks/useAdminDashboard';
import { Subscription } from '@/features/dashboard/types/admin.types';

export const columns: ColumnDef<Subscription>[] = [
	{
		accessorKey: 'name',
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Name" />
		),
	},
	{
		accessorKey: 'coverage',
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Coverage" />
		),
		cell: ({ row }) => `${row.original.coverage} ${row.original.coverage_type}`,
	},
	{
		accessorKey: 'price',
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Price" />
		),
		cell: ({ row }) => {
			const { price } = row.original;
			return (
				<div className="min-w-max">
					{Number(price).toLocaleString('en-US', {
						style: 'currency',
						currency: 'PHP',
					})}
				</div>
			);
		},
	},
	{
		accessorKey: 'discounted_price',
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Discounted Price" />
		),
		cell: ({ row }) => {
			const { discounted_price } = row.original;
			return (
				<div className="min-w-max">
					{Number(discounted_price).toLocaleString('en-US', {
						style: 'currency',
						currency: 'PHP',
					})}
				</div>
			);
		},
	},
	{
		accessorKey: 'description',
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Description" />
		),
		cell: ({ row }) => row.original.description ?? '—',
	},
	{
		accessorKey: 'is_active',
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Status" />
		),

		cell: ({ row }) => {
			const data = row.original;

			return (
				<div className="flex items-center gap-3">
					<div
						className={cn(
							'rounded-md border px-2 py-0.5 text-xs font-semibold tracking-wide',
							'flex items-center gap-2'
						)}
					>
						<div
							className={cn('size-1.5 animate-pulse rounded-full', {
								'bg-green-500': data.is_active,
								'bg-red-500': !data.is_active,
							})}
						></div>
						{data.is_active ? 'Active' : 'Inactive'}
					</div>
				</div>
			);
		},
	},
	{
		accessorKey: 'is_discount_enabled',
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Discount Enabled" />
		),
		// cell: ({ row }) => (row.original.is_discount_enabled ? 'Yes' : 'No'),
		cell: ({ row }) => {
			const data = row.original;

			return (
				<div className="flex items-center gap-3">
					<div
						className={cn(
							'rounded-md border px-2 py-0.5 text-xs font-semibold tracking-wide',
							'flex items-center gap-2'
						)}
					>
						<div
							className={cn('size-1.5 animate-pulse rounded-full', {
								' bg-green-500': data.is_discount_enabled,
								'bg-red-500': !data.is_discount_enabled,
							})}
						></div>
						{data.is_discount_enabled ? 'Enabled' : 'Disabled'}
					</div>
				</div>
			);
		},
	},
	{
		accessorKey: 'created_at',
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Date Created" />
		),
		cell: ({ row }) => {
			const { created_at } = row.original;
			return (
				<div className="min-w-max">
					{`${new Date(created_at).toLocaleDateString('en-US', {
						day: '2-digit',
						month: 'short',
						year: 'numeric',
					})} | ${new Date(created_at).toLocaleTimeString('en-US', {
						hour: '2-digit',
						minute: '2-digit',
						hour12: true,
					})}`}
				</div>
			);
		},
	},
	{
		id: 'actions',
		header: ({ column }) => (
			<DataTableColumnHeader column={column} title="Actions" />
		),
		cell: ({ row }) => <Action data={row.original} />,
	},
];

type ActionProps = {
	data: Subscription;
};

// const Action = ({ data }: ActionProps) => {
// 	const { mutate: removeSubscription } = useRemoveSubscription();
// 	const router = useRouter();

// 	const handleRemove = () => {
// 		removeSubscription(data.id);
// 	};

// 	const handleView = () => {
// 		router.push(`/dashboard?tab=view-subscription-plan&id=${data.id}`);
// 	};
// 	return (
// 		<DropdownMenu>
// 			<DropdownMenuTrigger asChild>
// 				<Button variant="ghost" className="h-8 w-8 cursor-pointer p-0">
// 					<MoreHorizontal className="h-4 w-4" />
// 				</Button>
// 			</DropdownMenuTrigger>
// 			<DropdownMenuContent align="end">
// 				<DropdownMenuLabel>Actions</DropdownMenuLabel>
// 				<DropdownMenuItem onClick={handleView}>
// 					<Eye className="mr-2 h-4 w-4" />
// 					View Subscription
// 				</DropdownMenuItem>
// 				<DropdownMenuItem onClick={handleRemove}>
// 					<Trash className="mr-2 h-4 w-4 text-red-500" />
// 					Delete Subscription
// 				</DropdownMenuItem>
// 			</DropdownMenuContent>
// 		</DropdownMenu>

// 	);
// };

const Action = ({ data }: ActionProps) => {
	const { mutate: removeSubscription } = useRemoveSubscription();
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

	const handleRemove = () => {
		removeSubscription(data.id);
	};

	console.log('Data: ', data);

	return (
		<div className="flex justify-center gap-2">
			<Button asChild className="h-8 px-2 lg:px-3" variant="outline" size="sm">
				<Link
					href={`/dashboard?tab=view-subscription-plan&id=${data.id}`}
					className="flex items-center gap-2"
				>
					<EyeIcon className="size-4" />
				</Link>
			</Button>

			<Button
				onClick={() => setIsDeleteDialogOpen(true)}
				className="h-8 cursor-pointer px-2 lg:px-3"
				variant="outline"
				size="sm"
			>
				<Trash className="size-4 text-red-500" />
			</Button>

			<AlertDialog
				open={isDeleteDialogOpen}
				onOpenChange={setIsDeleteDialogOpen}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>
							Delete {data?.name} Subscription?
						</AlertDialogTitle>
						<AlertDialogDescription>
							This action cannot be undone. This will permanently delete from
							our servers.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={handleRemove}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
						>
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</div>
	);
};
