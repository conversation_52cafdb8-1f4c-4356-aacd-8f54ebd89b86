'use client';

import {
	useAllHabitTypes,
	useCreateHabitType,
	useRemoveHabitType,
	useUpdateHabitType,
} from '@/features/dashboard/hooks/useAdminDashboard';

import { AdminDataManagement } from './admin-data-management';

export function AdminHabitTypes() {
	const { data: habitTypes, isLoading, error } = useAllHabitTypes();
	const createMutation = useCreateHabitType();
	const updateMutation = useUpdateHabitType();
	const removeMutation = useRemoveHabitType();

	const habitTypesData = habitTypes?.data || [];
	const isActionLoading =
		createMutation.isPending ||
		updateMutation.isPending ||
		removeMutation.isPending;

	const handleCreate = (data: { name: string; description?: string }) => {
		createMutation.mutate(data);
	};

	const handleUpdate = (
		id: number,
		data: { name?: string; description?: string }
	) => {
		updateMutation.mutate({ habitTypeId: id, data });
	};

	const handleRemove = (id: number) => {
		removeMutation.mutate(id);
	};

	return (
		<AdminDataManagement
			title="Habit Types"
			description="Manage habit types for patient medical records"
			data={habitTypesData}
			isLoading={isLoading}
			error={error}
			onCreate={handleCreate}
			onUpdate={handleUpdate}
			onRemove={handleRemove}
			isActionLoading={isActionLoading}
		/>
	);
}
