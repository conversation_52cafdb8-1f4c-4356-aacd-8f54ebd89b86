'use client';

import { useRouter } from 'next/navigation';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export function AdminPlatformDataManagement() {
	const router = useRouter();

	const handleNavigate = (tab: string) => {
		router.push(`/dashboard?tab=${tab}`);
	};

	return (
		<div className="flex flex-1 flex-col gap-4 p-4 pt-0">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
						Platform Data Management
					</h1>
					<p className="text-muted-foreground">
						Manage reference data and system configurations
					</p>
				</div>
			</div>

			<div className="grid gap-4 md:grid-cols-2">
				<Card>
					<CardHeader>
						<CardTitle>Basic Data Types</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="space-y-3">
							<div className="flex items-center justify-between rounded-lg border p-3">
								<div>
									<h4 className="font-medium">Visit Reasons</h4>
									<p className="text-muted-foreground text-sm">
										Manage consultation visit reasons
									</p>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() => handleNavigate('visit-reasons')}
								>
									Manage
								</Button>
							</div>
							<div className="flex items-center justify-between rounded-lg border p-3">
								<div>
									<h4 className="font-medium">Consultation Types</h4>
									<p className="text-muted-foreground text-sm">
										Manage types of medical consultations
									</p>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() => handleNavigate('consultation-types')}
								>
									Manage
								</Button>
							</div>
							<div className="flex items-center justify-between rounded-lg border p-3">
								<div>
									<h4 className="font-medium">Appointment Types</h4>
									<p className="text-muted-foreground text-sm">
										Manage appointment scheduling types
									</p>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() => handleNavigate('appointment-types')}
								>
									Manage
								</Button>
							</div>
							<div className="flex items-center justify-between rounded-lg border p-3">
								<div>
									<h4 className="font-medium">Payment Types</h4>
									<p className="text-muted-foreground text-sm">
										Manage payment and billing types
									</p>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() => handleNavigate('payment-types')}
								>
									Manage
								</Button>
							</div>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Clinical Data Types</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="space-y-3">
							<div className="flex items-center justify-between rounded-lg border p-3">
								<div>
									<h4 className="font-medium">Diagnostic Request Types</h4>
									<p className="text-muted-foreground text-sm">
										Manage diagnostic test request types
									</p>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() => handleNavigate('diagnostic-request-types')}
								>
									Manage
								</Button>
							</div>

							<div className="flex items-center justify-between rounded-lg border p-3">
								<div>
									<h4 className="font-medium">Lab Request Types</h4>
									<p className="text-muted-foreground text-sm">
										Manage laboratory test request types
									</p>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() => handleNavigate('lab-request-types')}
								>
									Manage
								</Button>
							</div>
							<div className="flex items-center justify-between rounded-lg border p-3">
								<div>
									<h4 className="font-medium">Prescription Types</h4>
									<p className="text-muted-foreground text-sm">
										Manage prescription and medication types
									</p>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() => handleNavigate('prescription-types')}
								>
									Manage
								</Button>
							</div>

							<div className="flex items-center justify-between rounded-lg border p-3">
								<div>
									<h4 className="font-medium">Medical Certificate</h4>
									<p className="text-muted-foreground text-sm">
										Manage medical terminology and descriptions
									</p>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() => handleNavigate('medical-descriptions')}
								>
									Manage
								</Button>
							</div>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Patient Profile Data types</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="space-y-3">
							<div className="flex items-center justify-between rounded-lg border p-3">
								<div>
									<h4 className="font-medium">Habit</h4>
									<p className="text-muted-foreground text-sm">
										Manage patient habits types
									</p>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() => handleNavigate('habit-types')}
								>
									Manage
								</Button>
							</div>
							<div className="flex items-center justify-between rounded-lg border p-3">
								<div>
									<h4 className="font-medium">Allergy</h4>
									<p className="text-muted-foreground text-sm">
										Manage patient allergy types
									</p>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() => handleNavigate('allergy-types')}
								>
									Manage
								</Button>
							</div>
							<div className="flex items-center justify-between rounded-lg border p-3">
								<div>
									<h4 className="font-medium">Illness</h4>
									<p className="text-muted-foreground text-sm">
										Manage patient illness types
									</p>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() => handleNavigate('illness-types')}
								>
									Manage
								</Button>
							</div>
							<div className="flex items-center justify-between rounded-lg border p-3">
								<div>
									<h4 className="font-medium">Surgery</h4>
									<p className="text-muted-foreground text-sm">
										Manage patient surgery types
									</p>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() => handleNavigate('surgery-types')}
								>
									Manage
								</Button>
							</div>
							<div className="flex items-center justify-between rounded-lg border p-3">
								<div>
									<h4 className="font-medium">Diet</h4>
									<p className="text-muted-foreground text-sm">
										Manage patient diet types
									</p>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() => handleNavigate('diet-types')}
								>
									Manage
								</Button>
							</div>
							<div className="flex items-center justify-between rounded-lg border p-3">
								<div>
									<h4 className="font-medium">History</h4>
									<p className="text-muted-foreground text-sm">
										Manage patient history types
									</p>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() => handleNavigate('history-types')}
								>
									Manage
								</Button>
							</div>
							<div className="flex items-center justify-between rounded-lg border p-3">
								<div>
									<h4 className="font-medium">HMO</h4>
									<p className="text-muted-foreground text-sm">
										Manage patient HMO types
									</p>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() => handleNavigate('hmo-types')}
								>
									Manage
								</Button>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* TODO: hide system settings for now */}
			{/* <div className="grid gap-4 md:grid-cols-2">
				<Card>
					<CardHeader>
						<CardTitle>System Configuration</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="space-y-3">
							<div className="flex items-center justify-between rounded-lg border p-3">
								<div>
									<h4 className="font-medium">User Roles</h4>
									<p className="text-muted-foreground text-sm">
										Manage user roles and permissions
									</p>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() => handleNavigate('user-roles')}
								>
									Configure
								</Button>
							</div>
							<div className="flex items-center justify-between rounded-lg border p-3">
								<div>
									<h4 className="font-medium">System Configuration</h4>
									<p className="text-muted-foreground text-sm">
										Manage global system settings
									</p>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() => handleNavigate('system-config')}
								>
									Configure
								</Button>
							</div>
							<div className="flex items-center justify-between rounded-lg border p-3">
								<div>
									<h4 className="font-medium">Lab Management</h4>
									<p className="text-muted-foreground text-sm">
										Manage laboratory settings and configurations
									</p>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() => handleNavigate('lab-management')}
								>
									Configure
								</Button>
							</div>
						</div>
					</CardContent>
				</Card>
			</div> */}
		</div>
	);
}
