'use client';

import { Check, ChevronsUpDown, Plus, Trash2, Users } from 'lucide-react';
import { useMemo, useState } from 'react';

import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from '@/components/ui/command';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import useDebounce from '@/core/hooks/utils/useDebounce';
import {
	useAdminAddFamilyToPatient,
	useAdminRemoveFamilyFromPatient,
	useAllPatients,
} from '@/features/dashboard/hooks/useAdminDashboard';

interface IPatientFamily {
	id: number;
	name: string;
	relationship: string;
}

interface IAdminPatientFamilyManagementProps {
	profileId: number;
	family: IPatientFamily[];
}

interface IFamilyFormData {
	familyProfileId: number;
	relationship: string;
	familyMemberName: string; // For display purposes
}

interface IPatientOption {
	id: number;
	first_name: string;
	last_name: string;
	middle_name?: string;
	suffix?: string;
	phone?: string;
	gender?: string;
}

export function AdminPatientFamilyManagement({
	profileId,
	family,
}: IAdminPatientFamilyManagementProps) {
	const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [familyToDelete, setFamilyToDelete] = useState<IPatientFamily | null>(
		null
	);
	const [formData, setFormData] = useState<IFamilyFormData>({
		familyProfileId: 0,
		relationship: '',
		familyMemberName: '',
	});

	// Patient search state
	const [patientSearchOpen, setPatientSearchOpen] = useState(false);
	const [patientSearchQuery, setPatientSearchQuery] = useState('');
	const [selectedPatient, setSelectedPatient] = useState<IPatientOption | null>(
		null
	);

	const addFamilyMutation = useAdminAddFamilyToPatient();
	const removeFamilyMutation = useAdminRemoveFamilyFromPatient();

	// Debounce the search query to avoid too many API calls
	const { debouncedValue: debouncedSearchQuery } = useDebounce(
		patientSearchQuery,
		300 // 300ms delay
	);

	// Only search when we have at least 2 characters and the search is debounced
	const shouldSearch = debouncedSearchQuery.length >= 2;

	// Prepare search parameters - only include search if we should search
	const searchParams = useMemo(() => {
		if (!shouldSearch) {
			return { pageSize: 20 }; // Return minimal params to avoid search
		}
		return {
			search: debouncedSearchQuery,
			pageSize: 20, // Limit results for performance
		};
	}, [debouncedSearchQuery, shouldSearch]);

	// Fetch patients for search
	const { data: patientsData } = useAllPatients(searchParams);

	// Only show patients if we have a valid search query
	const patients = shouldSearch ? patientsData?.data?.data || [] : [];

	// Helper function to format patient display name
	const formatPatientName = (patient: IPatientOption) => {
		const parts = [patient.first_name];
		if (patient.middle_name) parts.push(patient.middle_name);
		parts.push(patient.last_name);
		if (patient.suffix) parts.push(patient.suffix);
		return parts.join(' ');
	};

	// Handle patient selection
	const handlePatientSelect = (patient: IPatientOption) => {
		setSelectedPatient(patient);
		setFormData({
			...formData,
			familyProfileId: patient.id,
			familyMemberName: formatPatientName(patient),
		});
		setPatientSearchOpen(false);
	};

	const handleAddFamily = () => {
		// Validate required fields
		if (!formData.relationship.trim()) {
			console.error('Relationship is required');
			return;
		}

		// Check if a patient is selected (required by backend)
		if (!selectedPatient) {
			console.error('Please select an existing patient as family member');
			return;
		}

		// Prepare family data - backend requires existing patient ID
		const familyData = {
			familyProfileId: selectedPatient.id,
			relationship: formData.relationship,
		};

		addFamilyMutation.mutate(
			{ profileId, familyData },
			{
				onSuccess: () => {
					setIsAddDialogOpen(false);
					// Reset form after successful addition
					setFormData({
						familyProfileId: 0,
						relationship: '',
						familyMemberName: '',
					});
					setSelectedPatient(null);
					setPatientSearchQuery('');
				},
				onError: (error) => {
					console.error('Failed to add family member:', error);
					// In a real app, you'd show a toast notification here
				},
			}
		);
	};

	const handleRemoveFamily = (family: IPatientFamily) => {
		setFamilyToDelete(family);
		setIsDeleteDialogOpen(true);
	};

	const confirmRemoveFamily = () => {
		if (familyToDelete) {
			removeFamilyMutation.mutate({
				profileId,
				patientFamilyId: familyToDelete.id,
			});
			setIsDeleteDialogOpen(false);
			setFamilyToDelete(null);
		}
	};

	const relationshipOptions = [
		'Parent',
		'Child',
		'Sibling',
		'Spouse',
		'Grandparent',
		'Grandchild',
		'Uncle/Aunt',
		'Nephew/Niece',
		'Cousin',
		'Other',
	];

	return (
		<Card>
			<CardHeader>
				<div className="flex items-center justify-between">
					<CardTitle className="flex items-center gap-2">
						<Users className="h-5 w-5" />
						Family Medical History ({family.length})
					</CardTitle>
					<Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
						<DialogTrigger asChild>
							<Button size="sm">
								<Plus className="mr-2 h-4 w-4" />
								Add Family Member
							</Button>
						</DialogTrigger>
						<DialogContent className="sm:max-w-md">
							<DialogHeader>
								<DialogTitle className="flex items-center gap-2">
									<Users className="h-5 w-5 text-blue-600" />
									Add Family Member
								</DialogTitle>
								<DialogDescription>
									Connect a family member to this patient&apos;s profile. This
									helps maintain comprehensive family medical history.
								</DialogDescription>
							</DialogHeader>
							<div className="space-y-6">
								{/* Relationship - Most important field first */}
								<div className="space-y-2">
									<Label htmlFor="relationship" className="text-sm font-medium">
										Relationship <span className="text-red-500">*</span>
									</Label>
									<Select
										value={formData.relationship}
										onValueChange={(value) =>
											setFormData({ ...formData, relationship: value })
										}
									>
										<SelectTrigger className="w-full">
											<SelectValue placeholder="How are they related to the patient?" />
										</SelectTrigger>
										<SelectContent>
											{relationshipOptions.map((relationship) => (
												<SelectItem
													key={relationship}
													value={relationship.toLowerCase()}
												>
													{relationship}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
								</div>

								{/* Patient Search and Selection */}
								<div className="space-y-2">
									<Label className="text-sm font-medium">
										Select Family Member <span className="text-red-500">*</span>
									</Label>
									<Popover
										open={patientSearchOpen}
										onOpenChange={setPatientSearchOpen}
									>
										<PopoverTrigger asChild>
											<Button
												variant="outline"
												role="combobox"
												aria-expanded={patientSearchOpen}
												className="w-full justify-between"
											>
												{selectedPatient ? (
													<span className="truncate">
														{formatPatientName(selectedPatient)}
													</span>
												) : (
													<span className="text-gray-500">
														Search and select a patient...
													</span>
												)}
												<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
											</Button>
										</PopoverTrigger>
										<PopoverContent className="w-full p-0" align="start">
											<Command>
												<CommandInput
													placeholder="Search patients by name..."
													value={patientSearchQuery}
													onValueChange={setPatientSearchQuery}
												/>
												<CommandList>
													<CommandEmpty>
														{debouncedSearchQuery.length < 2
															? 'Type at least 2 characters to search...'
															: 'No patients found.'}
													</CommandEmpty>
													{patients.length > 0 && (
														<CommandGroup>
															{patients.map((patient) => (
																<CommandItem
																	key={patient.id}
																	value={formatPatientName(patient)}
																	onSelect={() => handlePatientSelect(patient)}
																	className="flex items-center justify-between"
																>
																	<div className="flex flex-col">
																		<span className="font-medium">
																			{formatPatientName(patient)}
																		</span>
																		<span className="text-xs text-gray-500">
																			{patient.phone} • {patient.gender} • ID:{' '}
																			{patient.id}
																		</span>
																	</div>
																	<Check
																		className={`ml-2 h-4 w-4 ${
																			selectedPatient?.id === patient.id
																				? 'opacity-100'
																				: 'opacity-0'
																		}`}
																	/>
																</CommandItem>
															))}
														</CommandGroup>
													)}
												</CommandList>
											</Command>
										</PopoverContent>
									</Popover>
									<div className="rounded border border-amber-200 bg-amber-50 p-3">
										<p className="mb-1 text-xs font-medium text-amber-800">
											📋 Important Note
										</p>
										<p className="text-xs text-amber-700">
											Family members must be existing patients in the system. If
											the family member is not yet a patient, please register
											them as a patient first before adding them as a family
											member.
										</p>
									</div>
								</div>
							</div>
							<DialogFooter className="gap-2">
								<Button
									variant="outline"
									onClick={() => {
										setIsAddDialogOpen(false);
										// Reset form when canceling
										setFormData({
											familyProfileId: 0,
											relationship: '',
											familyMemberName: '',
										});
										setSelectedPatient(null);
										setPatientSearchQuery('');
									}}
									disabled={addFamilyMutation.isPending}
								>
									Cancel
								</Button>
								<Button
									onClick={handleAddFamily}
									disabled={
										addFamilyMutation.isPending ||
										!formData.relationship.trim() ||
										!selectedPatient
									}
									className="min-w-[140px]"
								>
									{addFamilyMutation.isPending ? (
										<>
											<div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
											Adding...
										</>
									) : (
										<>
											<Plus className="mr-2 h-4 w-4" />
											Add Family Member
										</>
									)}
								</Button>
							</DialogFooter>
						</DialogContent>
					</Dialog>
				</div>
			</CardHeader>
			<CardContent>
				{family.length === 0 ? (
					<p className="py-4 text-center text-gray-500">
						No family medical history recorded for this patient.
					</p>
				) : (
					<div className="space-y-3">
						{family.map((familyMember) => (
							<div
								key={familyMember.id}
								className="flex items-center justify-between rounded-lg border p-3"
							>
								<div className="flex-1">
									<div className="mb-1 flex items-center gap-2">
										<h4 className="font-medium">{familyMember.name}</h4>
										<span className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800">
											{familyMember.relationship}
										</span>
									</div>
									<p className="text-xs text-gray-500">
										Family member with medical history relevance
									</p>
								</div>
								<div className="flex gap-2">
									<Button
										size="sm"
										variant="destructive"
										onClick={() => handleRemoveFamily(familyMember)}
										disabled={removeFamilyMutation.isPending}
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								</div>
							</div>
						))}
					</div>
				)}
			</CardContent>

			{/* Delete Confirmation Dialog */}
			<AlertDialog
				open={isDeleteDialogOpen}
				onOpenChange={setIsDeleteDialogOpen}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Delete Family Member</AlertDialogTitle>
						<AlertDialogDescription>
							Are you sure you want to delete &quot;
							{familyToDelete?.name}
							&quot;? This action cannot be undone.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={confirmRemoveFamily}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
						>
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</Card>
	);
}
