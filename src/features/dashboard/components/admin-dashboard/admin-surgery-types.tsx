'use client';

import {
	useAllSurgeryTypes,
	useCreateSurgeryType,
	useRemoveSurgeryType,
	useUpdateSurgeryType,
} from '@/features/dashboard/hooks/useAdminDashboard';

import { AdminDataManagement } from './admin-data-management';

export function AdminSurgeryTypes() {
	const { data: surgeryTypes, isLoading, error } = useAllSurgeryTypes();
	const createMutation = useCreateSurgeryType();
	const updateMutation = useUpdateSurgeryType();
	const removeMutation = useRemoveSurgeryType();

	const surgeryTypesData = surgeryTypes?.data || [];
	const isActionLoading =
		createMutation.isPending ||
		updateMutation.isPending ||
		removeMutation.isPending;

	const handleCreate = (data: { name: string; description?: string }) => {
		createMutation.mutate(data);
	};

	const handleUpdate = (
		id: number,
		data: { name?: string; description?: string }
	) => {
		updateMutation.mutate({ surgeryTypeId: id, data });
	};

	const handleRemove = (id: number) => {
		removeMutation.mutate(id);
	};

	return (
		<AdminDataManagement
			title="Surgery Types"
			description="Manage surgical procedure types and categories"
			data={surgeryTypesData}
			isLoading={isLoading}
			error={error}
			onCreate={handleCreate}
			onUpdate={handleUpdate}
			onRemove={handleRemove}
			isActionLoading={isActionLoading}
		/>
	);
}
