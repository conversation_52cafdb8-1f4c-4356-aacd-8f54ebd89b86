'use client';

import {
	useAllPrescriptionTypes,
	useCreatePrescriptionType,
	useRemovePrescriptionType,
	useUpdatePrescriptionType,
} from '@/features/dashboard/hooks/useAdminDashboard';

import { AdminDataManagement } from './admin-data-management';

export function AdminPrescriptionTypes() {
	const {
		data: prescriptionTypes,
		isLoading,
		error,
	} = useAllPrescriptionTypes();
	const createMutation = useCreatePrescriptionType();
	const updateMutation = useUpdatePrescriptionType();
	const removeMutation = useRemovePrescriptionType();

	const prescriptionTypesData = prescriptionTypes?.data || [];
	const isActionLoading =
		createMutation.isPending ||
		updateMutation.isPending ||
		removeMutation.isPending;

	const handleCreate = (data: { name: string; description?: string }) => {
		createMutation.mutate(data);
	};

	const handleUpdate = (
		id: number,
		data: { name?: string; description?: string }
	) => {
		updateMutation.mutate({ prescriptionTypeId: id, data });
	};

	const handleRemove = (id: number) => {
		removeMutation.mutate(id);
	};

	return (
		<AdminDataManagement
			title="Prescription Types"
			description="Manage prescription types and medication categories"
			data={prescriptionTypesData}
			isLoading={isLoading}
			error={error}
			onCreate={handleCreate}
			onUpdate={handleUpdate}
			onRemove={handleRemove}
			isActionLoading={isActionLoading}
		/>
	);
}
