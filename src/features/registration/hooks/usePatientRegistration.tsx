'use client';

import { useMutation } from '@tanstack/react-query';
import { toast } from 'sonner';

import { registerPatient } from '@/core/api/registration/patient.api';

export default function usePatientRegistration() {
	const { mutateAsync, isPending } = useMutation({
		mutationFn: registerPatient,
		onSuccess: () => {
			toast.success('Patient registered successfully');
		},
		onError: (error) => {
			toast.error('Patient registration failed', {
				description: error.message,
			});
		},
	});

	return {
		registerPatient: mutateAsync,
		isLoading: isPending,
	};
}
