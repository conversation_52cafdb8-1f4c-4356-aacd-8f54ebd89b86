'use client';

import { useMutation } from '@tanstack/react-query';
import { toast } from 'sonner';

import { registerDoctor } from '@/core/api/registration/doctor.api';

export default function useDoctorRegistration() {
	const { mutateAsync, isPending } = useMutation({
		mutationFn: registerDoctor,
		onSuccess: () => {
			toast.success('Doctor registered successfully');
		},
		onError: (error) => {
			toast.error('Doctor registration failed', {
				description: error.message,
			});
		},
	});

	return {
		registerDoctor: mutateAsync,
		isLoading: isPending,
	};
}
