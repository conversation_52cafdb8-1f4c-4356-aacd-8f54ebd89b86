'use client';

import { UseFormReturn } from 'react-hook-form';

import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { IRegisterDoctor } from '@/core/api/registration/doctor.type';

interface IClinicInfoFormProps {
	form: UseFormReturn<IRegisterDoctor>;
}

export default function ClinicInfoForm({ form }: IClinicInfoFormProps) {
	return (
		<div className="space-y-4">
			<h3 className="text-elena-primary text-lg font-semibold">
				Clinic Information (Optional)
			</h3>

			<div className="space-y-4">
				<FormField
					control={form.control}
					name="clinicName"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Clinic Name</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter your clinic name"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="clinicAddress"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Clinic Address</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter your clinic address"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>
		</div>
	);
}
