'use client';

import { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { IRegisterDoctor } from '@/core/api/registration/doctor.type';

interface IProfessionalInfoFormProps {
	form: UseFormReturn<IRegisterDoctor>;
}

export default function ProfessionalInfoForm({
	form,
}: IProfessionalInfoFormProps) {
	const [frontImagePreview, setFrontImagePreview] = useState<string | null>(
		null
	);
	const [backImagePreview, setBackImagePreview] = useState<string | null>(null);

	const handleFileChange = (
		file: File | undefined,
		field: 'prcImageFront' | 'prcImageBack'
	) => {
		if (file) {
			const reader = new FileReader();
			reader.onload = (e) => {
				const result = e.target?.result as string;
				if (field === 'prcImageFront') {
					setFrontImagePreview(result);
				} else {
					setBackImagePreview(result);
				}
			};
			reader.readAsDataURL(file);
		}
	};

	return (
		<div className="space-y-4">
			<h3 className="text-elena-primary text-lg font-semibold">
				Professional Information
			</h3>

			{/* Specialty Field */}
			<FormField
				control={form.control}
				name="specialty"
				render={({ field }) => (
					<FormItem>
						<FormLabel>Medical Specialty</FormLabel>
						<FormControl>
							<Input
								placeholder="e.g., Internal Medicine, Pediatrics, etc."
								{...field}
								className="focus:border-elena-primary focus:ring-elena-primary"
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				)}
			/>

			{/* PRC Information Row */}
			<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
				<FormField
					control={form.control}
					name="prcNumber"
					render={({ field }) => (
						<FormItem>
							<FormLabel>PRC License Number</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter your PRC license number"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="prcExpiryDate"
					render={({ field }) => (
						<FormItem>
							<FormLabel>PRC License Expiry Date</FormLabel>
							<FormControl>
								<Input
									type="date"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			{/* PRC Images Upload */}
			<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
				<FormField
					control={form.control}
					name="prcImageFront"
					render={({ field: { onChange, name, onBlur } }) => (
						<FormItem>
							<FormLabel>PRC License (Front) *</FormLabel>
							<FormControl>
								<div className="space-y-2">
									<Input
										type="file"
										accept="image/*"
										name={name}
										onBlur={onBlur}
										onChange={(e) => {
											const file = e.target.files?.[0];
											if (file) {
												onChange(file);
												handleFileChange(file, 'prcImageFront');
											}
										}}
										className="focus:border-elena-primary focus:ring-elena-primary"
									/>
									{frontImagePreview && (
										<div className="mt-2">
											<img
												src={frontImagePreview}
												alt="PRC Front Preview"
												className="rounded border object-cover"
											/>
										</div>
									)}
								</div>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="prcImageBack"
					render={({ field: { onChange, name, onBlur } }) => (
						<FormItem>
							<FormLabel>PRC License (Back) *</FormLabel>
							<FormControl>
								<div className="space-y-2">
									<Input
										type="file"
										accept="image/*"
										name={name}
										onBlur={onBlur}
										onChange={(e) => {
											const file = e.target.files?.[0];
											if (file) {
												onChange(file);
												handleFileChange(file, 'prcImageBack');
											}
										}}
										className="focus:border-elena-primary focus:ring-elena-primary"
									/>
									{backImagePreview && (
										<div className="mt-2">
											<img
												src={backImagePreview}
												alt="PRC Back Preview"
												className="rounded border object-cover"
											/>
										</div>
									)}
								</div>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>
		</div>
	);
}
