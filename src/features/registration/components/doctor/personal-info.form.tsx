'use client';

import { UseFormReturn } from 'react-hook-form';

import PhoneInput from '@/components/ui-customs/phone-input';
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { IRegisterDoctor } from '@/core/api/registration/doctor.type';

interface IPersonalInfoFormProps {
	form: UseFormReturn<IRegisterDoctor>;
}

export default function PersonalInfoForm({ form }: IPersonalInfoFormProps) {
	return (
		<div className="space-y-4">
			<h3 className="text-elena-primary text-lg font-semibold">
				Personal Information
			</h3>

			{/* Name Fields Row */}
			<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
				<FormField
					control={form.control}
					name="firstName"
					render={({ field }) => (
						<FormItem>
							<FormLabel>First Name *</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter your first name"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="lastName"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Last Name *</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter your last name"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			{/* Middle Name and Suffix Row */}
			<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
				<FormField
					control={form.control}
					name="middleName"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Middle Name</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter your middle name"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="suffix"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Suffix</FormLabel>
							<FormControl>
								<Input
									placeholder="Jr., Sr., III, etc."
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			{/* Contact Information Row */}
			<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
				<FormField
					control={form.control}
					name="email"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Email Address *</FormLabel>
							<FormControl>
								<Input
									type="email"
									placeholder="Enter your email address"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="phone"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Phone Number *</FormLabel>
							<FormControl>
								<PhoneInput
									{...field}
									placeholder="Enter a phone number"
									className=""
									value={field.value || undefined}
									onChange={(val) => field.onChange(val || '')}
									onBlur={field.onBlur}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			{/* Gender and Birthday Row */}
			<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
				<FormField
					control={form.control}
					name="gender"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Gender *</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger className="focus:border-elena-primary focus:ring-elena-primary w-full">
										<SelectValue placeholder="Select gender" />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value="Male">Male</SelectItem>
									<SelectItem value="Female">Female</SelectItem>
									<SelectItem value="Other">Other</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="birthday"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Birthday *</FormLabel>
							<FormControl>
								<Input
									type="date"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>
		</div>
	);
}
