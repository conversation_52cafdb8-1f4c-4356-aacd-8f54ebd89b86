'use client';

import { UseFormReturn } from 'react-hook-form';

import PhoneInput from '@/components/ui-customs/phone-input';
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { IPatientRegistration } from '@/core/api/registration/patient.type';

interface IPersonalInfoFormProps {
	form: UseFormReturn<IPatientRegistration>;
}

export default function PersonalInfoForm({ form }: IPersonalInfoFormProps) {
	return (
		<div className="space-y-4">
			<h3 className="text-elena-primary text-lg font-semibold">
				Personal Information
			</h3>

			{/* Title and Name Fields Row */}
			<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
				<FormField
					control={form.control}
					name="title"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Title</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger className="focus:border-elena-primary focus:ring-elena-primary">
										<SelectValue placeholder="Select title" />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value="Mr.">Mr.</SelectItem>
									<SelectItem value="Ms.">Ms.</SelectItem>
									<SelectItem value="Mrs.">Mrs.</SelectItem>
									<SelectItem value="Dr.">Dr.</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="firstName"
					render={({ field }) => (
						<FormItem>
							<FormLabel>First Name *</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter your first name"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="lastName"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Last Name *</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter your last name"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			{/* Middle Name and Suffix Row */}
			<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
				<FormField
					control={form.control}
					name="middleName"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Middle Name</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter your middle name"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="suffix"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Suffix</FormLabel>
							<FormControl>
								<Input
									placeholder="Jr., Sr., III, etc."
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			{/* Contact Information Row */}
			<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
				<FormField
					control={form.control}
					name="email"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Email Address *</FormLabel>
							<FormControl>
								<Input
									type="email"
									placeholder="Enter your email address"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="phone"
					render={({ field }) => (
						<FormItem className="items-start">
							<FormLabel>Phone Number *</FormLabel>
							<FormControl>
								<PhoneInput
									{...field}
									placeholder="Enter a phone number"
									className=""
									value={field.value || undefined}
									onChange={(val) => field.onChange(val || '')}
									onBlur={field.onBlur}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			{/* Personal Details Row */}
			<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
				<FormField
					control={form.control}
					name="gender"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Gender *</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger className="focus:border-elena-primary focus:ring-elena-primary">
										<SelectValue placeholder="Select gender" />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value="Male">Male</SelectItem>
									<SelectItem value="Female">Female</SelectItem>
									<SelectItem value="Other">Other</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="birthday"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Birthday *</FormLabel>
							<FormControl>
								<Input
									type="date"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="civilStatus"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Civil Status *</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger className="focus:border-elena-primary focus:ring-elena-primary">
										<SelectValue placeholder="Select civil status" />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value="Single">Single</SelectItem>
									<SelectItem value="Live In">Live In</SelectItem>
									<SelectItem value="Married">Married</SelectItem>
									<SelectItem value="Divorced">Divorced</SelectItem>
									<SelectItem value="Widowed">Widowed</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>
		</div>
	);
}
