'use client';

import { UseFormReturn } from 'react-hook-form';

import PhoneInput from '@/components/ui-customs/phone-input';
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { IPatientRegistration } from '@/core/api/registration/patient.type';

interface IEmergencyFormProps {
	form: UseFormReturn<IPatientRegistration>;
}

export default function EmergencyForm({ form }: IEmergencyFormProps) {
	return (
		<div className="space-y-4">
			<h3 className="text-elena-primary text-lg font-semibold">
				Emergency Contact
			</h3>

			<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
				<FormField
					control={form.control}
					name="emergencyContactName"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Emergency Contact Name</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter emergency contact name"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="emergencyContactNumber"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Emergency Contact Number</FormLabel>
							<FormControl>
								<PhoneInput
									{...field}
									placeholder="Enter a phone number"
									className=""
									value={field.value || undefined}
									onChange={(val) => field.onChange(val || '')}
									onBlur={field.onBlur}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>
		</div>
	);
}
