'use client';

import { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { Checkbox } from '@/components/ui/checkbox';
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectGroup,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { IPatientRegistration } from '@/core/api/registration/patient.type';
import useSelectAddress, {
	BarangayType,
	CityType,
	ProvinceType,
	RegionType,
} from '@/core/hooks/utils/useSelectAddress';
import { cn } from '@/core/lib/utils';

interface IAddressFormProps {
	form: UseFormReturn<IPatientRegistration>;
}

export default function AddressForm({ form }: IAddressFormProps) {
	const currentAddress = useSelectAddress();
	const permanentAddress = useSelectAddress();
	const [sameAsCurrentAddress, setSameAsCurrentAddress] = useState(false);

	const handleSameAddressChange = (checked: boolean) => {
		setSameAsCurrentAddress(checked);

		if (checked) {
			// Region
			permanentAddress.setRegionList(currentAddress.regionList);
			permanentAddress.setRegionSelected(currentAddress.regionSelected);
			form.setValue(
				'permanentAddress.region',
				JSON.stringify(currentAddress.regionSelected)
			);

			// Province
			permanentAddress.setProvinceList(currentAddress.provinceList);
			permanentAddress.setProvinceSelected(currentAddress.provinceSelected);
			form.setValue(
				'permanentAddress.province',
				JSON.stringify(currentAddress.provinceSelected)
			);

			// City
			permanentAddress.setCityList(currentAddress.cityList);
			permanentAddress.setCitySelected(currentAddress.citySelected);
			form.setValue(
				'permanentAddress.city',
				JSON.stringify(currentAddress.citySelected)
			);

			// Barangay
			permanentAddress.setBarangayList(currentAddress.barangayList);
			permanentAddress.setBarangaySelected(currentAddress.barangaySelected);
			form.setValue(
				'permanentAddress.barangay',
				JSON.stringify(currentAddress.barangaySelected)
			);

			// Street
			permanentAddress.setStreet(currentAddress.street);
			form.setValue('permanentAddress.streetName', currentAddress.street);

			const _currentAddress = form.getValues('currentAddress');

			// lotBlockPhaseStreet
			form.setValue(
				'permanentAddress.lotBlockPhaseStreet',
				_currentAddress.lotBlockPhaseStreet
			);

			// unitRoomFloorBuilding
			form.setValue(
				'permanentAddress.unitRoomFloorBuilding',
				_currentAddress.unitRoomFloorBuilding
			);

			// subdivisionVillageZone
			form.setValue(
				'permanentAddress.subdivisionVillageZone',
				_currentAddress.subdivisionVillageZone
			);

			// buildingApartment
			form.setValue(
				'permanentAddress.buildingApartment',
				_currentAddress.buildingApartment
			);
		} else {
			permanentAddress.setRegionSelected(null);
			form.setValue('permanentAddress.region', '');

			permanentAddress.setProvinceSelected(null);
			form.setValue('permanentAddress.province', '');

			permanentAddress.setCitySelected(null);
			form.setValue('permanentAddress.city', '');

			permanentAddress.setBarangaySelected(null);
			form.setValue('permanentAddress.barangay', '');

			permanentAddress.setStreet('');
			form.setValue('permanentAddress.streetName', '');

			form.setValue('permanentAddress.lotBlockPhaseStreet', '');
			form.setValue('permanentAddress.unitRoomFloorBuilding', '');
			form.setValue('permanentAddress.subdivisionVillageZone', '');
			form.setValue('permanentAddress.buildingApartment', '');
		}
	};

	return (
		<>
			{/* Current Address Section */}
			<div className="space-y-4">
				<h3 className="text-elena-primary text-lg font-semibold">
					Current Address
				</h3>

				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					<FormField
						control={form.control}
						name="currentAddress.region"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Region *</FormLabel>
								<FormControl>
									<Select
										onValueChange={(v) => {
											field.onChange(v);
											if (v !== '')
												currentAddress.setRegionSelected(JSON.parse(v));
										}}
										value={field.value}
									>
										<SelectTrigger
											className={cn(
												'focus:border-elena-primary focus:ring-elena-primary',
												form.formState.errors.currentAddress?.region &&
													'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
											)}
										>
											<SelectValue placeholder="Select region" />
										</SelectTrigger>
										<SelectContent>
											<SelectGroup>
												{currentAddress.regionList.map((region: RegionType) => (
													<SelectItem
														key={region.region_code}
														value={JSON.stringify(region)}
													>
														{region.region_name}
													</SelectItem>
												))}
											</SelectGroup>
										</SelectContent>
									</Select>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="currentAddress.province"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Province *</FormLabel>
								<FormControl>
									<Select
										onValueChange={(v) => {
											field.onChange(v);
											if (v !== '')
												currentAddress.setProvinceSelected(JSON.parse(v));
										}}
										value={field.value}
										disabled={currentAddress.regionList.length === 0}
									>
										<SelectTrigger
											className={cn(
												'focus:border-elena-primary focus:ring-elena-primary',
												form.formState.errors.currentAddress?.province &&
													'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
											)}
										>
											<SelectValue placeholder="Select province" />
										</SelectTrigger>
										<SelectContent>
											<SelectGroup>
												{currentAddress.provinceList.map(
													(province: ProvinceType) => (
														<SelectItem
															key={province.province_code}
															value={JSON.stringify(province)}
														>
															{province.province_name}
														</SelectItem>
													)
												)}
											</SelectGroup>
										</SelectContent>
									</Select>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					<FormField
						control={form.control}
						name="currentAddress.city"
						render={({ field }) => (
							<FormItem>
								<FormLabel>City *</FormLabel>
								<FormControl>
									<Select
										onValueChange={(v) => {
											field.onChange(v);
											if (v !== '')
												currentAddress.setCitySelected(JSON.parse(v));
										}}
										value={field.value}
										disabled={currentAddress.cityList.length === 0}
									>
										<SelectTrigger
											className={cn(
												'focus:border-elena-primary focus:ring-elena-primary',
												form.formState.errors.currentAddress?.city &&
													'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
											)}
										>
											<SelectValue placeholder="Select city/municipality" />
										</SelectTrigger>
										<SelectContent>
											<SelectGroup>
												{currentAddress.cityList.map((city: CityType) => (
													<SelectItem
														key={city.city_code}
														value={JSON.stringify(city)}
													>
														{city.city_name}
													</SelectItem>
												))}
											</SelectGroup>
										</SelectContent>
									</Select>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="currentAddress.barangay"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Barangay *</FormLabel>
								<FormControl>
									<Select
										onValueChange={(v) => {
											field.onChange(v);
											if (v !== '')
												currentAddress.setBarangaySelected(JSON.parse(v));
										}}
										value={field.value}
										disabled={currentAddress.barangayList.length === 0}
									>
										<SelectTrigger
											className={cn(
												'focus:border-elena-primary focus:ring-elena-primary',
												form.formState.errors.currentAddress?.barangay &&
													'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
											)}
										>
											<SelectValue placeholder="Select Barangay" />
										</SelectTrigger>
										<SelectContent>
											<SelectGroup>
												{currentAddress.barangayList.map(
													(barangay: BarangayType) => (
														<SelectItem
															key={barangay.brgy_code}
															value={JSON.stringify(barangay)}
														>
															{barangay.brgy_name}
														</SelectItem>
													)
												)}
											</SelectGroup>
										</SelectContent>
									</Select>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<FormField
					control={form.control}
					name="currentAddress.streetName"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Street Name *</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter street name"
									value={field.value}
									onChange={(v) => {
										field.onChange(v);
										currentAddress.setStreet(v.target.value);
									}}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					<FormField
						control={form.control}
						name="currentAddress.lotBlockPhaseStreet"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Lot/Block/Phase/Street No.</FormLabel>
								<FormControl>
									<Input
										placeholder="Enter Lot/Block/Phase/Street No."
										{...field}
										className="focus:border-elena-primary focus:ring-elena-primary"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="currentAddress.unitRoomFloorBuilding"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Unit/Room/Floor/Building No.</FormLabel>
								<FormControl>
									<Input
										placeholder="Enter Unit/Room/Floor/Building No."
										{...field}
										className="focus:border-elena-primary focus:ring-elena-primary"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					<FormField
						control={form.control}
						name="currentAddress.subdivisionVillageZone"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Subdivision/Village/Zone</FormLabel>
								<FormControl>
									<Input
										placeholder="Enter subdivision, village, or zone"
										{...field}
										className="focus:border-elena-primary focus:ring-elena-primary"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="currentAddress.buildingApartment"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Building/Apartment</FormLabel>
								<FormControl>
									<Input
										placeholder="Enter building or apartment"
										{...field}
										className="focus:border-elena-primary focus:ring-elena-primary"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
			</div>

			{/* Permanent Address Section */}
			<div className="space-y-4">
				<h3 className="text-elena-primary text-lg font-semibold">
					Permanent Address
				</h3>

				{/* Same as Current Address Checkbox */}
				<div className="flex items-center space-x-2">
					<Checkbox
						id="sameAsCurrentAddress"
						checked={sameAsCurrentAddress}
						onCheckedChange={handleSameAddressChange}
						className="border-elena-primary data-[state=checked]:bg-elena-primary data-[state=checked]:border-elena-primary"
					/>
					<label
						htmlFor="sameAsCurrentAddress"
						className="cursor-pointer text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
					>
						Same as current address
					</label>
				</div>

				{!sameAsCurrentAddress && (
					<>
						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<FormField
								control={form.control}
								name="permanentAddress.region"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Region *</FormLabel>
										<FormControl>
											<Select
												onValueChange={(v) => {
													field.onChange(v);
													if (v !== '')
														permanentAddress.setRegionSelected(JSON.parse(v));
												}}
												value={field.value}
											>
												<SelectTrigger
													className={cn(
														'focus:border-elena-primary focus:ring-elena-primary',
														form.formState.errors.permanentAddress?.region &&
															'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
													)}
												>
													<SelectValue placeholder="Select region" />
												</SelectTrigger>
												<SelectContent>
													<SelectGroup>
														{permanentAddress.regionList.map(
															(region: RegionType) => (
																<SelectItem
																	key={region.region_code}
																	value={JSON.stringify(region)}
																>
																	{region.region_name}
																</SelectItem>
															)
														)}
													</SelectGroup>
												</SelectContent>
											</Select>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="permanentAddress.province"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Province *</FormLabel>
										<FormControl>
											<Select
												onValueChange={(v) => {
													field.onChange(v);
													if (v !== '')
														permanentAddress.setProvinceSelected(JSON.parse(v));
												}}
												value={field.value}
												disabled={permanentAddress.regionList.length === 0}
											>
												<SelectTrigger
													className={cn(
														'focus:border-elena-primary focus:ring-elena-primary',
														form.formState.errors.permanentAddress?.province &&
															'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
													)}
												>
													<SelectValue placeholder="Select province" />
												</SelectTrigger>
												<SelectContent>
													<SelectGroup>
														{permanentAddress.provinceList.map(
															(province: ProvinceType) => (
																<SelectItem
																	key={province.province_code}
																	value={JSON.stringify(province)}
																>
																	{province.province_name}
																</SelectItem>
															)
														)}
													</SelectGroup>
												</SelectContent>
											</Select>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="permanentAddress.city"
								render={({ field }) => (
									<FormItem>
										<FormLabel>City *</FormLabel>
										<FormControl>
											<Select
												onValueChange={(v) => {
													field.onChange(v);
													if (v !== '')
														permanentAddress.setCitySelected(JSON.parse(v));
												}}
												value={field.value}
												disabled={permanentAddress.cityList.length === 0}
											>
												<SelectTrigger
													className={cn(
														'focus:border-elena-primary focus:ring-elena-primary',
														form.formState.errors.permanentAddress?.city &&
															'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
													)}
												>
													<SelectValue placeholder="Select city/municipality" />
												</SelectTrigger>
												<SelectContent>
													<SelectGroup>
														{permanentAddress.cityList.map((city: CityType) => (
															<SelectItem
																key={city.city_code}
																value={JSON.stringify(city)}
															>
																{city.city_name}
															</SelectItem>
														))}
													</SelectGroup>
												</SelectContent>
											</Select>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="permanentAddress.barangay"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Barangay *</FormLabel>
										<FormControl>
											<Select
												onValueChange={(v) => {
													field.onChange(v);
													if (v !== '')
														permanentAddress.setBarangaySelected(JSON.parse(v));
												}}
												value={field.value}
												disabled={permanentAddress.barangayList.length === 0}
											>
												<SelectTrigger
													className={cn(
														'focus:border-elena-primary focus:ring-elena-primary',
														form.formState.errors.permanentAddress?.barangay &&
															'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
													)}
												>
													<SelectValue placeholder="Select Barangay" />
												</SelectTrigger>
												<SelectContent>
													<SelectGroup>
														{permanentAddress.barangayList.map(
															(barangay: BarangayType) => (
																<SelectItem
																	key={barangay.brgy_code}
																	value={JSON.stringify(barangay)}
																>
																	{barangay.brgy_name}
																</SelectItem>
															)
														)}
													</SelectGroup>
												</SelectContent>
											</Select>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<FormField
							control={form.control}
							name="permanentAddress.streetName"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Street Name *</FormLabel>
									<FormControl>
										<Input
											placeholder="Enter street name"
											value={field.value}
											onChange={(v) => {
												field.onChange(v);
												permanentAddress.setStreet(v.target.value);
											}}
											className="focus:border-elena-primary focus:ring-elena-primary"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<FormField
								control={form.control}
								name="permanentAddress.lotBlockPhaseStreet"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Lot/Block/Phase/Street No.</FormLabel>
										<FormControl>
											<Input
												placeholder="Enter Lot/Block/Phase/Street No."
												{...field}
												className="focus:border-elena-primary focus:ring-elena-primary"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="permanentAddress.unitRoomFloorBuilding"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Unit/Room/Floor/Building No.</FormLabel>
										<FormControl>
											<Input
												placeholder="Enter Unit/Room/Floor/Building No."
												{...field}
												className="focus:border-elena-primary focus:ring-elena-primary"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
							<FormField
								control={form.control}
								name="permanentAddress.subdivisionVillageZone"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Subdivision/Village/Zone</FormLabel>
										<FormControl>
											<Input
												placeholder="Enter subdivision, village, or zone"
												{...field}
												className="focus:border-elena-primary focus:ring-elena-primary"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="permanentAddress.buildingApartment"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Building/Apartment</FormLabel>
										<FormControl>
											<Input
												placeholder="Enter building or apartment"
												{...field}
												className="focus:border-elena-primary focus:ring-elena-primary"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
					</>
				)}
			</div>
		</>
	);
}
