'use client';

import { UseFormReturn } from 'react-hook-form';

import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { IPatientRegistration } from '@/core/api/registration/patient.type';

interface IAdditionalInfoFormProps {
	form: UseFormReturn<IPatientRegistration>;
}

export default function AdditionalInfoForm({ form }: IAdditionalInfoFormProps) {
	return (
		<div className="space-y-4">
			<h3 className="text-elena-primary text-lg font-semibold">
				Additional Information
			</h3>

			<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
				<FormField
					control={form.control}
					name="occupation"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Occupation</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter your occupation"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="religion"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Religion</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter your religion"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
				<FormField
					control={form.control}
					name="ethnicity"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Ethnicity</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter your ethnicity (e.g Tagalog, Bicolano, Cebuano, Ilocano, etc)"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="bloodType"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Blood Type</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger className="focus:border-elena-primary focus:ring-elena-primary">
										<SelectValue placeholder="Select blood type" />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value="A+">A+</SelectItem>
									<SelectItem value="A-">A-</SelectItem>
									<SelectItem value="B+">B+</SelectItem>
									<SelectItem value="B-">B-</SelectItem>
									<SelectItem value="AB+">AB+</SelectItem>
									<SelectItem value="AB-">AB-</SelectItem>
									<SelectItem value="O+">O+</SelectItem>
									<SelectItem value="O-">O-</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>
		</div>
	);
}
