'use client';

import { UseFormReturn } from 'react-hook-form';

import { Checkbox } from '@/components/ui/checkbox';
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { IPatientRegistration } from '@/core/api/registration/patient.type';

interface IPhysicalInfoFormProps {
	form: UseFormReturn<IPatientRegistration>;
}

export default function PhysicalInfoForm({ form }: IPhysicalInfoFormProps) {
	return (
		<div className="space-y-4">
			<h3 className="text-elena-primary text-lg font-semibold">
				Physical Information
			</h3>

			<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
				<div className="grid grid-cols-2 gap-2">
					<FormField
						control={form.control}
						name="height"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Height *</FormLabel>
								<FormControl>
									<Input
										placeholder="Enter height"
										{...field}
										className="focus:border-elena-primary focus:ring-elena-primary"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="heightType"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Unit *</FormLabel>
								<Select
									onValueChange={field.onChange}
									defaultValue={field.value}
								>
									<FormControl>
										<SelectTrigger className="focus:border-elena-primary focus:ring-elena-primary">
											<SelectValue placeholder="Unit" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										<SelectItem value="cm">cm</SelectItem>
										<SelectItem value="ft">ft</SelectItem>
										<SelectItem value="in">in</SelectItem>
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<div className="grid grid-cols-2 gap-2">
					<FormField
						control={form.control}
						name="weight"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Weight *</FormLabel>
								<FormControl>
									<Input
										placeholder="Enter weight"
										{...field}
										className="focus:border-elena-primary focus:ring-elena-primary"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="weightType"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Unit *</FormLabel>
								<Select
									onValueChange={field.onChange}
									defaultValue={field.value}
								>
									<FormControl>
										<SelectTrigger className="focus:border-elena-primary focus:ring-elena-primary">
											<SelectValue placeholder="Unit" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										<SelectItem value="kg">kg</SelectItem>
										<SelectItem value="lbs">lbs</SelectItem>
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
			</div>

			<FormField
				control={form.control}
				name="isPwd"
				render={({ field }) => (
					<FormItem className="flex flex-row items-start space-y-0 space-x-3 pt-3">
						<FormControl>
							<Checkbox
								checked={field.value === '1'}
								onCheckedChange={(checked) => {
									field.onChange(checked ? '1' : '0');
								}}
								className="focus:border-elena-primary focus:ring-elena-primary"
							/>
						</FormControl>
						<div className="space-y-1 leading-none">
							<FormLabel>
								Minor/Elderly/PWD or Person with Special Needs
							</FormLabel>
						</div>
						<FormMessage />
					</FormItem>
				)}
			/>
		</div>
	);
}
