'use client';

import { UseFormReturn } from 'react-hook-form';

import PhoneInput from '@/components/ui-customs/phone-input';
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { IPatientRegistration } from '@/core/api/registration/patient.type';

interface IGuardianInfoFormProps {
	form: UseFormReturn<IPatientRegistration>;
}

export default function GuardianInfoForm({ form }: IGuardianInfoFormProps) {
	const isPwd = form.watch('isPwd');

	if (isPwd !== '1') return null;

	return (
		<div className="space-y-4">
			<h3 className="text-elena-primary text-lg font-semibold">
				Guardian Information
			</h3>
			<p className="text-muted-foreground text-xs">
				For PWD patients, a guardian account will be created and the
				verification email will be sent to the guardian.
			</p>

			<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
				<FormField
					control={form.control}
					name="guardianFirstName"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Guardian First Name *</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter guardian first name"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="guardianMiddleName"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Guardian Middle Name</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter guardian middle name"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="guardianLastName"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Guardian Last Name *</FormLabel>
							<FormControl>
								<Input
									placeholder="Enter guardian last name"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
				<FormField
					control={form.control}
					name="guardianEmail"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Guardian Email *</FormLabel>
							<FormControl>
								<Input
									type="email"
									placeholder="Enter guardian email"
									{...field}
									className="focus:border-elena-primary focus:ring-elena-primary"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="guardianContactNumber"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Guardian Contact Number</FormLabel>
							<FormControl>
								<PhoneInput
									{...field}
									placeholder="Enter guardian phone number"
									value={field.value || undefined}
									onChange={(val) => field.onChange(val || '')}
									onBlur={field.onBlur}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			<FormField
				control={form.control}
				name="guardianBirthdate"
				render={({ field }) => (
					<FormItem>
						<FormLabel>Guardian Birthdate *</FormLabel>
						<FormControl>
							<Input
								type="date"
								{...field}
								className="focus:border-elena-primary focus:ring-elena-primary"
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				)}
			/>
		</div>
	);
}
