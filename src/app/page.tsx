'use client';

import {
	ArrowRightIcon,
	CalendarIcon,
	FileTextIcon,
	HeartHandshakeIcon,
	ShieldCheckIcon,
	StethoscopeIcon,
	UsersIcon,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { Button } from '@/components/ui/button';
import { useSession } from '@/core/hooks/useSession';
import useClientSide from '@/core/hooks/utils/useClientSide';

export default function Home() {
	const router = useRouter();
	const { isAuthenticated, logout } = useSession();
	const { isBrowser } = useClientSide();

	// Redirect to dashboard if already authenticated
	useEffect(() => {
		if (isBrowser && isAuthenticated) {
			router.push('/dashboard');
		}
	}, [isBrowser, isAuthenticated, router]);

	// Navigation handlers
	const handleGetStarted = () => {
		router.push('/register');
	};

	const handleLogin = () => {
		router.push('/login');
	};

	const handleWatchDemo = () => {
		// For now, scroll to features section
		const featuresSection = document.getElementById('features');
		featuresSection?.scrollIntoView({ behavior: 'smooth' });
	};

	const scrollToSection = (sectionId: string) => {
		const section = document.getElementById(sectionId);
		section?.scrollIntoView({ behavior: 'smooth' });
	};
	return (
		<div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
			{/* Header */}
			{isBrowser && (
				<header className="container mx-auto px-4 py-6">
					<nav className="flex items-center justify-between">
						<div className="flex items-center gap-3">
							<img
								src="/elena-banner.png"
								alt="Elena Logo"
								className="h-12 w-auto"
							/>
						</div>
						<div className="flex items-center gap-4">
							<Button
								variant="ghost"
								className="hover:text-elena-primary text-gray-600"
								onClick={() => scrollToSection('about')}
							>
								About
							</Button>
							<Button
								variant="ghost"
								className="hover:text-elena-primary text-gray-600"
								onClick={() => scrollToSection('features')}
							>
								Features
							</Button>
							<Button
								variant="ghost"
								className="hover:text-elena-primary text-gray-600"
								onClick={() => scrollToSection('contact')}
							>
								Contact
							</Button>
							{isAuthenticated ? (
								<Button
									variant="outline"
									className="border-elena-primary text-elena-primary hover:bg-elena-primary hover:text-white"
									onClick={logout}
								>
									Logout
								</Button>
							) : (
								<>
									<Button
										variant="ghost"
										className="hover:text-elena-primary text-gray-600"
										onClick={handleLogin}
									>
										Login
									</Button>
									<Button
										className="bg-elena-primary hover:bg-elena-primary-dark text-white"
										onClick={handleGetStarted}
									>
										Get Started
									</Button>
								</>
							)}
						</div>
					</nav>
				</header>
			)}

			{/* Hero Section */}
			<section className="container mx-auto px-4 py-20 text-center">
				<div className="mx-auto max-w-4xl">
					<div className="text-elena-primary mb-4 text-lg font-semibold">
						SMART, SECURED, SIMPLE
					</div>
					<h1 className="mb-6 text-5xl font-bold text-gray-900 md:text-6xl">
						All-in-one platform to{' '}
						<span className="text-elena-primary">automate your practice</span>
					</h1>
					<p className="mb-8 text-xl text-gray-600 md:text-2xl">
						Elena is a clinic and practice management web app providing tools to
						manage your patients, appointments, and medical records with
						Electronic Medical Records (EMR) and E-Prescription capabilities.
					</p>
					<div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
						<Button
							size="lg"
							className="bg-elena-primary hover:bg-elena-primary-dark px-8 py-4 text-lg text-white"
							onClick={handleGetStarted}
						>
							Doctors&apos; Clinic Assistant Free Trial!
							<ArrowRightIcon className="ml-2 h-5 w-5" />
						</Button>
						<Button
							size="lg"
							variant="outline"
							className="border-elena-primary text-elena-primary hover:bg-elena-primary px-8 py-4 text-lg hover:text-white"
							onClick={handleWatchDemo}
						>
							Learn More
						</Button>
					</div>
				</div>
			</section>

			{/* Features Section */}
			<section id="features" className="container mx-auto px-4 py-20">
				<div className="mb-16 text-center">
					<h2 className="mb-4 text-4xl font-bold text-gray-900">
						Premium Practice Management Web App for Doctors
					</h2>
					<p className="text-xl text-gray-600">
						Elena provides comprehensive tools to digitize your clinic and
						automate day-to-day tasks with advanced EMR and E-Prescription
						capabilities
					</p>
				</div>

				<div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
					{/* Feature 1 */}
					<div className="rounded-lg bg-white p-8 shadow-lg transition-transform hover:scale-105">
						<div className="bg-elena-primary/10 mb-4 flex h-12 w-12 items-center justify-center rounded-lg">
							<CalendarIcon className="text-elena-primary h-6 w-6" />
						</div>
						<h3 className="mb-3 text-xl font-semibold text-gray-900">
							Maximize your Every Day
						</h3>
						<p className="text-gray-600">
							Manage patient appointments and schedules through the app with
							fully-functional calendar views by day, week, or month.
						</p>
					</div>

					{/* Feature 2 */}
					<div className="rounded-lg bg-white p-8 shadow-lg transition-transform hover:scale-105">
						<div className="bg-elena-primary/10 mb-4 flex h-12 w-12 items-center justify-center rounded-lg">
							<FileTextIcon className="text-elena-primary h-6 w-6" />
						</div>
						<h3 className="mb-3 text-xl font-semibold text-gray-900">
							Digitize your Clinic
						</h3>
						<p className="text-gray-600">
							Go paperless with fully-integrated Electronic Medical Record (EMR)
							and manage your patients&apos; medical & treatment records.
						</p>
					</div>

					{/* Feature 3 */}
					<div className="rounded-lg bg-white p-8 shadow-lg transition-transform hover:scale-105">
						<div className="bg-elena-primary/10 mb-4 flex h-12 w-12 items-center justify-center rounded-lg">
							<StethoscopeIcon className="text-elena-primary h-6 w-6" />
						</div>
						<h3 className="mb-3 text-xl font-semibold text-gray-900">
							Quick Prescription Writing
						</h3>
						<p className="text-gray-600">
							Create an E-Prescription indicating necessary details such as
							medicine&apos;s generic name, brand name, dosage, and more.
						</p>
					</div>

					{/* Feature 4 */}
					<div className="rounded-lg bg-white p-8 shadow-lg transition-transform hover:scale-105">
						<div className="bg-elena-primary/10 mb-4 flex h-12 w-12 items-center justify-center rounded-lg">
							<UsersIcon className="text-elena-primary h-6 w-6" />
						</div>
						<h3 className="mb-3 text-xl font-semibold text-gray-900">
							Accessible Patient Records
						</h3>
						<p className="text-gray-600">
							Access patients&apos; medical records anytime, anywhere! Organize
							and sort patient records with comprehensive history.
						</p>
					</div>

					{/* Feature 5 */}
					<div className="rounded-lg bg-white p-8 shadow-lg transition-transform hover:scale-105">
						<div className="bg-elena-primary/10 mb-4 flex h-12 w-12 items-center justify-center rounded-lg">
							<ShieldCheckIcon className="text-elena-primary h-6 w-6" />
						</div>
						<h3 className="mb-3 text-xl font-semibold text-gray-900">
							Generate Records / Reports
						</h3>
						<p className="text-gray-600">
							Easily generate your patient records/reports in PDF format with
							secure QR codes for safe patient transfers.
						</p>
					</div>

					{/* Feature 6 */}
					<div className="rounded-lg bg-white p-8 shadow-lg transition-transform hover:scale-105">
						<div className="bg-elena-primary/10 mb-4 flex h-12 w-12 items-center justify-center rounded-lg">
							<HeartHandshakeIcon className="text-elena-primary h-6 w-6" />
						</div>
						<h3 className="mb-3 text-xl font-semibold text-gray-900">
							Automated Day-to-Day Tasks
						</h3>
						<p className="text-gray-600">
							Separate dashboard for personal clinic assistant/secretary to help
							you manage your clinic&apos;s day-to-day operation online.
						</p>
					</div>
				</div>
			</section>

			{/* About Section */}
			<section id="about" className="bg-white py-20">
				<div className="container mx-auto px-4">
					<div className="mx-auto max-w-4xl text-center">
						<h2 className="mb-6 text-4xl font-bold text-gray-900">
							Doctors&apos; Dashboard
						</h2>
						<p className="mb-8 text-xl text-gray-600">
							See the overview of your patient database with our simplified
							Doctor&apos;s Dashboard. Easily assess and check your appointments
							for the day, recent visits, total registered patients, patients
							who have HMO&apos;s, and monthly/daily visits. Generate report
							real-time with just a click of a button.
						</p>
						<div className="grid gap-8 md:grid-cols-3">
							<div className="text-center">
								<div className="text-elena-primary mb-4 text-3xl font-bold">
									EMR
								</div>
								<p className="text-gray-600">Electronic Medical Records</p>
							</div>
							<div className="text-center">
								<div className="text-elena-primary mb-4 text-3xl font-bold">
									24/7
								</div>
								<p className="text-gray-600">Access Anytime</p>
							</div>
							<div className="text-center">
								<div className="text-elena-primary mb-4 text-3xl font-bold">
									QR
								</div>
								<p className="text-gray-600">Secure Patient Transfer</p>
							</div>
						</div>
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section className="bg-elena-primary py-20">
				<div className="container mx-auto px-4 text-center">
					<h2 className="mb-4 text-4xl font-bold text-white">
						Patient Management Made Simple
					</h2>
					<p className="mx-auto mb-8 max-w-4xl text-xl text-blue-100">
						Organize, arrange and sort patient records anytime, anywhere. Access
						appointment history, patient information, diagnoses, prescriptions
						and lab requests with Elena.
					</p>
					<Button
						size="lg"
						className="text-elena-primary bg-white px-8 py-4 text-lg font-semibold hover:bg-gray-100"
						onClick={handleGetStarted}
					>
						Start Your Free Trial Today
						<ArrowRightIcon className="ml-2 h-5 w-5" />
					</Button>
				</div>
			</section>

			{/* Contact Section */}
			<section id="contact" className="bg-gray-50 py-20">
				<div className="container mx-auto px-4">
					<div className="mx-auto max-w-4xl text-center">
						<h2 className="mb-6 text-4xl font-bold text-gray-900">
							Looking for professional & trusted medical healthcare?
						</h2>
						<p className="mb-8 text-xl text-gray-600">
							Don&apos;t hesitate to contact us. Get started with Elena&apos;s
							comprehensive practice management solution today.
						</p>
						<div className="grid gap-8 md:grid-cols-3">
							<div className="text-center">
								<h3 className="mb-2 text-lg font-semibold text-gray-900">
									Email
								</h3>
								<p className="text-gray-600">Drop us an email</p>
							</div>
							<div className="text-center">
								<h3 className="mb-2 text-lg font-semibold text-gray-900">
									Phone
								</h3>
								<p className="text-gray-600">Call us now</p>
							</div>
							<div className="text-center">
								<h3 className="mb-2 text-lg font-semibold text-gray-900">
									Support
								</h3>
								<p className="text-gray-600">24 X 7 support</p>
							</div>
						</div>
						<div className="mt-8">
							<Button
								size="lg"
								className="bg-elena-primary hover:bg-elena-primary-dark px-8 py-4 text-white"
								onClick={handleGetStarted}
							>
								Make Appointment
							</Button>
						</div>
					</div>
				</div>
			</section>

			{/* Footer */}
			<footer className="bg-gray-900 py-12 text-white">
				<div className="container mx-auto px-4">
					<div className="grid gap-8 md:grid-cols-4">
						<div>
							<img
								src="/elena-banner.png"
								alt="Elena Logo"
								className="mb-4 h-10 w-auto brightness-0 invert"
							/>
							<p className="text-gray-400">
								Your intelligent clinic assistant for modern healthcare.
							</p>
						</div>
						<div>
							<h4 className="mb-4 font-semibold">Product</h4>
							<ul className="space-y-2 text-gray-400">
								<li>
									<a href="#" className="hover:text-white">
										Features
									</a>
								</li>
								<li>
									<a href="#" className="hover:text-white">
										Pricing
									</a>
								</li>
								<li>
									<a href="#" className="hover:text-white">
										Security
									</a>
								</li>
							</ul>
						</div>
						<div>
							<h4 className="mb-4 font-semibold">Company</h4>
							<ul className="space-y-2 text-gray-400">
								<li>
									<a href="#" className="hover:text-white">
										About
									</a>
								</li>
								<li>
									<a href="#" className="hover:text-white">
										Contact
									</a>
								</li>
								<li>
									<a href="#" className="hover:text-white">
										Support
									</a>
								</li>
							</ul>
						</div>
						<div>
							<h4 className="mb-4 font-semibold">Legal</h4>
							<ul className="space-y-2 text-gray-400">
								<li>
									<a href="#" className="hover:text-white">
										Privacy
									</a>
								</li>
								<li>
									<a href="#" className="hover:text-white">
										Terms
									</a>
								</li>
								<li>
									<a href="#" className="hover:text-white">
										HIPAA
									</a>
								</li>
							</ul>
						</div>
					</div>
					<div className="mt-8 border-t border-gray-800 pt-8 text-center text-gray-400">
						<p>&copy; {new Date().getFullYear()} Elena. All rights reserved.</p>
					</div>
				</div>
			</footer>
		</div>
	);
}
