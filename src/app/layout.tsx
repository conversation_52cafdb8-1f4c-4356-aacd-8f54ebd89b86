import '@/styles/globals.css';

import type { Metadata } from 'next';
import { NuqsAdapter } from 'nuqs/adapters/next/app';

import { geistMono, geistSans, inter, poppins } from '@/components/common/font';
import { Toaster } from '@/components/ui/sonner';
import { TooltipProvider } from '@/components/ui/tooltip';
import { isDevMode, isHideDevTools } from '@/core/constants';
import ReactQueryProvider from '@/providers/lib/react-query';

export const metadata: Metadata = {
	metadataBase: new URL('https://elena.dvcode.app'),
	title: '<PERSON> - Your Clinic Assistant',
	description:
		'<PERSON> is your clinic assistant. Manage appointments, medical records, prescriptions, and clinic operations with ease.',
	keywords: [
		'healthcare',
		'medical platform',
		'appointment management',
		'medical records',
		'prescription management',
		'clinic management',
		'doctor portal',
		'patient portal',
		'healthcare technology',
		'medical documents',
		'telemedicine',
		'healthcare administration',
	],
	openGraph: {
		siteName: 'Elena Healthcare Platform',
		title: 'Elena - Your Clinic Assistant',
		description:
			'<PERSON> is your clinic assistant. Manage appointments, medical records, prescriptions, and clinic operations with ease.',
		images: '/elena-banner.png',
		type: 'website',
	},
	twitter: {
		title: 'Elena - Your Clinic Assistant',
		description:
			'Elena is your clinic assistant. Manage appointments, medical records, prescriptions, and clinic operations with ease.',
		images: '/elena-banner.png',
		card: 'summary_large_image',
	},
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="en">
			<head>
				{isDevMode && !isHideDevTools && (
					<script
						src="https://unpkg.com/react-scan/dist/auto.global.js"
						async
					/>
				)}
			</head>
			<body
				className={`${geistSans.variable} ${geistMono.variable} ${poppins.variable} ${inter.variable} font-sans antialiased`}
			>
				<NuqsAdapter>
					<ReactQueryProvider>
						<TooltipProvider>{children}</TooltipProvider>
					</ReactQueryProvider>
				</NuqsAdapter>
				<Toaster />
			</body>
		</html>
	);
}
