'use client';

import { LogOut } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { Button } from '@/components/ui/button';
import { useSession } from '@/core/hooks/useSession';
import useClientSide from '@/core/hooks/utils/useClientSide';
import { AdminDashboard } from '@/features/dashboard/components/admin-dashboard/admin-dashboard';
import { PatientDashboard } from '@/features/dashboard/components/patient-dashboard/patient-dashboard';
import { UnifiedDashboard } from '@/features/dashboard/components/shared/unified-dashboard';

// Shared logout button component
const LogoutButton = ({ onClick }: { onClick: () => void }) => (
	<div className="absolute top-4 right-4">
		<Button
			variant="outline"
			size="sm"
			onClick={onClick}
			className="flex items-center gap-2"
		>
			<LogOut className="h-4 w-4" />
			<span className="hidden sm:inline">Logout</span>
		</Button>
	</div>
);

const PatientDashboardWrapper = ({ onLogout }: { onLogout: () => void }) => (
	<div className="flex min-h-screen">
		<div className="absolute top-4 right-4 z-50">
			<LogoutButton onClick={onLogout} />
		</div>
		<PatientDashboard />
	</div>
);

const LaboratoryDashboard = ({ onLogout }: { onLogout: () => void }) => (
	<div className="flex min-h-screen items-center justify-center">
		<div className="text-center">
			<LogoutButton onClick={onLogout} />
			<h1 className="mb-4 text-2xl font-bold text-[oklch(0.7448_0.1256_202.74)]">
				Laboratory Dashboard
			</h1>
			<p className="text-muted-foreground">Coming Soon</p>
		</div>
	</div>
);

export default function DashboardPage() {
	const { isAuthenticated, userRoles, logout } = useSession();
	const { isBrowser } = useClientSide();
	const router = useRouter();

	const handleLogout = () => {
		logout();
		router.push('/login');
	};

	// Redirect to login if not authenticated
	useEffect(() => {
		if (isBrowser && !isAuthenticated) {
			router.push('/login');
			return;
		}
	}, [isBrowser, isAuthenticated, router]);

	// Show loading state while checking authentication or during hydration
	if (!isBrowser || !isAuthenticated) {
		return (
			<div className="flex min-h-screen items-center justify-center">
				<div className="text-center">
					<div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-[oklch(0.7448_0.1256_202.74)]"></div>
					<p className="text-muted-foreground">Loading...</p>
				</div>
			</div>
		);
	}

	// Render appropriate dashboard based on user role
	if (userRoles.is_admin) {
		return <AdminDashboard />;
	}

	if (userRoles.is_doctor || userRoles.is_assistant) {
		return <UnifiedDashboard />;
	}

	if (userRoles.is_patient) {
		return <PatientDashboardWrapper onLogout={handleLogout} />;
	}

	if (userRoles.is_laboratory) {
		return <LaboratoryDashboard onLogout={handleLogout} />;
	}

	// Fallback for unknown roles
	return (
		<div className="flex min-h-screen items-center justify-center">
			<div className="text-center">
				<LogoutButton onClick={handleLogout} />
				<h1 className="text-destructive mb-4 text-2xl font-bold">
					Access Denied
				</h1>
				<p className="text-muted-foreground">
					Your account role is not recognized. Please contact support.
				</p>
			</div>
		</div>
	);
}
