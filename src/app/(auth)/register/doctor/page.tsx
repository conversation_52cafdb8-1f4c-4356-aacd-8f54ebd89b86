'use client';

import { zod<PERSON><PERSON>ol<PERSON> } from '@hookform/resolvers/zod';
import { ChevronLeft } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';

import Loading from '@/components/common/loading';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import {
	IRegisterDoctor,
	RegisterDoctorSchema,
} from '@/core/api/registration/doctor.type';
import PersonalInfoForm from '@/features/registration/components/doctor/personal-info.form';
import ProfessionalInfoForm from '@/features/registration/components/doctor/professional-info.form';
import useDoctorRegistration from '@/features/registration/hooks/useDoctorRegistration';

export default function RegisterDoctorPage() {
	const { registerDoctor, isLoading } = useDoctorRegistration();
	const router = useRouter();

	const form = useForm<IRegisterDoctor>({
		resolver: zod<PERSON><PERSON>olver(RegisterDoctorSchema),
		defaultValues: {
			// Personal Info
			firstName: '',
			middleName: '',
			lastName: '',
			suffix: '',
			email: '',
			phone: '',
			gender: '',
			birthday: '',

			// Professional Info
			prcNumber: '',
			prcExpiryDate: '',
			prcImageFront: undefined,
			prcImageBack: undefined,
			clinicAddress: '',
			clinicName: '',
			specialty: '',
		},
	});

	const onSubmit = (data: IRegisterDoctor) => {
		console.log('registerDoctor: ', data);
		registerDoctor(data, {
			onSuccess: () => {
				form.reset();
				router.replace('/login');
			},
		});
	};

	return (
		<div className="min-h-screen">
			{/* Registration Form */}
			<div
				className="relative grid w-full place-items-center p-8"
				style={{
					backgroundImage: 'url(/loginbg.svg)',
					backgroundSize: 'cover',
					backgroundPosition: 'center',
					backgroundRepeat: 'no-repeat',
				}}
			>
				{/* Mobile Logo */}
				<div className="mb-6">
					<img src="/elena-banner.png" alt="Elena" className="h-12 w-auto" />
				</div>

				{/* Registration Form Container */}
				<div className="w-full max-w-2xl rounded-2xl bg-white/95 p-8 shadow-xl backdrop-blur-sm">
					{/* Header */}
					<div className="mb-8 text-center">
						<h1 className="text-elena-primary mb-2 text-2xl font-bold">
							Doctor Registration
						</h1>
						<p className="text-sm text-gray-600">
							Join Elena as a healthcare professional
						</p>
					</div>

					{/* Registration Form */}
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
							{/* Personal Information Section */}
							<PersonalInfoForm form={form} />

							{/* Professional Information Section */}
							<ProfessionalInfoForm form={form} />

							{/* Submit Button */}
							<Button
								type="submit"
								disabled={isLoading}
								className="bg-elena-primary hover:bg-elena-primary-dark h-12 w-full rounded-lg font-medium text-white transition-colors disabled:opacity-50"
							>
								{isLoading ? (
									<>
										<Loading />
										<span>Registering...</span>
									</>
								) : (
									'Register as Doctor'
								)}
							</Button>

							{/* Divider */}
							<div className="relative my-6">
								<div className="absolute inset-0 flex items-center">
									<div className="w-full border-t border-gray-300" />
								</div>
								<div className="relative flex justify-center text-sm">
									<span className="bg-white px-4 text-gray-500">OR</span>
								</div>
							</div>

							{/* Back to Registration Selection */}
							<div className="text-center">
								<span className="text-gray-600">
									Want to register as a patient?{' '}
								</span>
								<Link
									href="/register/patient"
									className="text-elena-primary hover:text-elena-primary-dark font-medium"
								>
									Register as Patient
								</Link>
							</div>
						</form>
					</Form>

					{/* Back to Login */}
					<div className="mt-8 border-t border-gray-200 pt-6 text-center">
						<Button
							variant="ghost"
							asChild
							className="hover:text-elena-primary text-gray-600"
						>
							<Link href="/login" className="flex items-center gap-2">
								<ChevronLeft className="h-4 w-4" />
								Back to Login
							</Link>
						</Button>
					</div>

					{/* Copyright */}
					<div className="mt-4 text-center">
						<p className="text-xs text-gray-500">
							Copyright © {new Date().getFullYear()} ELENA. All Rights Reserved
						</p>
					</div>
				</div>
			</div>
		</div>
	);
}
