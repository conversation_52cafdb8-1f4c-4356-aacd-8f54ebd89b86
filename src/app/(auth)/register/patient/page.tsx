'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { ChevronLeft } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';

import { But<PERSON> } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import {
	IPatientRegistration,
	PatientRegistrationSchema,
} from '@/core/api/registration/patient.type';
import AdditionalInfoForm from '@/features/registration/components/patient/additional-info.form';
import AddressForm from '@/features/registration/components/patient/address.form';
import EmergencyForm from '@/features/registration/components/patient/emergency.form';
import GuardianInfoForm from '@/features/registration/components/patient/guardian-info.form';
import PersonalInfoForm from '@/features/registration/components/patient/personal-info.form';
import PhysicalInfoForm from '@/features/registration/components/patient/physical-info.form';
import usePatientRegistration from '@/features/registration/hooks/usePatientRegistration';

export default function RegisterPatientPage() {
	const { registerPatient, isLoading } = usePatientRegistration();
	const router = useRouter();

	const form = useForm<IPatientRegistration>({
		resolver: zodResolver(PatientRegistrationSchema),
		defaultValues: {
			// Personal Info
			title: '',
			firstName: '',
			middleName: '',
			lastName: '',
			suffix: '',
			email: '',
			phone: '',
			gender: '',
			birthday: '',
			civilStatus: '',

			// Additional Information
			occupation: '',
			ethnicity: '',
			religion: '',
			bloodType: '',

			// Physical Information
			height: '',
			heightType: 'cm',
			weight: '',
			weightType: 'kg',
			isPwd: '0',

			// Guardian (only when isPwd = '1')
			guardianFirstName: '',
			guardianMiddleName: '',
			guardianLastName: '',
			guardianContactNumber: '',
			guardianEmail: '',
			guardianBirthdate: '',

			// Address
			currentAddress: {
				country: 'Philippines',
				region: '',
				province: '',
				city: '',
				barangay: '',
				streetName: '',
				buildingApartment: '',
				subdivisionVillageZone: '',
				unitRoomFloorBuilding: '',
				lotBlockPhaseStreet: '',
			},
			permanentAddress: {
				country: 'Philippines',
				region: '',
				province: '',
				city: '',
				barangay: '',
				streetName: '',
				buildingApartment: '',
				subdivisionVillageZone: '',
				unitRoomFloorBuilding: '',
				lotBlockPhaseStreet: '',
			},

			// Emergency Contact
			emergencyContactName: '',
			emergencyContactNumber: '',

			// Additional
			clinicIds: [],
		},
	});

	const onSubmit = (data: IPatientRegistration) => {
		const updatedData: IPatientRegistration = {
			...data,
			clinicIds: [],
		};

		console.log('registerPatient: ', updatedData);
		registerPatient(updatedData, {
			onSuccess: () => {
				form.reset();
				router.replace('/login');
			},
		});
	};

	return (
		<div className="min-h-screen">
			{/* Registration Form */}
			<div
				className="relative grid w-full place-items-center p-8"
				style={{
					backgroundImage: 'url(/loginbg.svg)',
					backgroundSize: 'cover',
					backgroundPosition: 'center',
					backgroundRepeat: 'no-repeat',
				}}
			>
				{/* Mobile Logo */}
				<div className="mb-6">
					<img src="/elena-banner.png" alt="Elena" className="h-12 w-auto" />
				</div>

				{/* Registration Form Container */}
				<div className="w-full max-w-2xl rounded-2xl border border-gray-200 bg-white/95 p-8 shadow-xl">
					{/* Header */}
					<div className="mb-8 text-center">
						<h1 className="text-elena-primary mb-2 text-2xl font-bold">
							Patient Registration
						</h1>
						<p className="text-sm text-gray-600">
							Join Elena for comprehensive healthcare services
						</p>
					</div>

					{/* Back to Login Link */}
					<div className="mb-6">
						<Link
							href="/login"
							className="text-elena-primary hover:text-elena-primary-dark inline-flex items-center text-sm font-medium transition-colors"
						>
							<ChevronLeft className="mr-1 h-4 w-4" />
							Back to Login
						</Link>
					</div>

					{/* Registration Form */}
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
							{/* Personal Information Section */}
							<PersonalInfoForm form={form} />

							{/* Additional Information Section */}
							<AdditionalInfoForm form={form} />

							{/* Physical Information Section */}
							<PhysicalInfoForm form={form} />

							{/* Guardian Section (if PWD) */}
							<GuardianInfoForm form={form} />

							{/* Address Section */}
							<AddressForm form={form} />

							{/* Emergency Contact Section */}
							<EmergencyForm form={form} />

							{/* Submit Button */}
							<Button
								type="submit"
								disabled={isLoading}
								className="bg-elena-primary hover:bg-elena-primary-dark h-12 w-full rounded-lg font-medium text-white transition-colors disabled:opacity-50"
							>
								{isLoading ? 'Registering...' : 'Register as Patient'}
							</Button>
						</form>
					</Form>
				</div>
			</div>
		</div>
	);
}
