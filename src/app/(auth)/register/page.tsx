'use client';

import { ChevronLeft } from 'lucide-react';
import Link from 'next/link';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

export default function RegisterPage() {
	return (
		<div
			className="flex min-h-screen flex-col items-center justify-center p-4"
			style={{
				backgroundImage: 'url(/loginbg.svg)',
				backgroundSize: 'cover',
				backgroundPosition: 'center',
				backgroundRepeat: 'no-repeat',
			}}
		>
			{/* <PERSON> */}
			<div className="mb-12">
				<img src="/elena-banner.png" alt="Elena Logo" className="h-16 w-auto" />
			</div>

			{/* Registration Cards */}
			<div className="mb-12 grid w-full max-w-5xl grid-cols-1 gap-6 md:grid-cols-2">
				{/* Doctor Card */}
				<Link href="/register/doctor" className="block">
					<Card className="bg-elena-primary hover:bg-elena-primary-dark group h-80 cursor-pointer rounded-3xl border-none shadow-xl transition-all duration-300">
						<CardContent className="flex h-full items-center justify-center gap-8 p-8 text-white">
							<div className="flex-shrink-0">
								<img
									src="/elena-doctor-tab.png"
									alt="Doctor"
									className="h-48 w-48 object-contain"
								/>
							</div>
							<div className="flex flex-col justify-center">
								<p className="mb-2 text-xl font-medium">I am a</p>
								<h2 className="text-4xl font-bold">Doctor</h2>
							</div>
						</CardContent>
					</Card>
				</Link>

				{/* Patient Card */}
				<Link href="/register/patient" className="block">
					<Card className="group hover:border-elena-primary h-80 cursor-pointer rounded-3xl border-2 border-gray-200 bg-white shadow-xl transition-all duration-300 hover:bg-gray-50">
						<CardContent className="flex h-full items-center justify-center gap-8 p-8 text-gray-700">
							<div className="flex-shrink-0">
								<img
									src="/elena-patient-tab.png"
									alt="Patient"
									className="h-48 w-48 object-contain"
								/>
							</div>
							<div className="flex flex-col justify-center">
								<p className="mb-2 text-xl font-medium text-gray-600">I am a</p>
								<h2 className="text-4xl font-bold text-gray-800">Patient</h2>
							</div>
						</CardContent>
					</Card>
				</Link>
			</div>

			{/* Back to Login */}
			<Button
				variant="ghost"
				asChild
				className="hover:text-elena-primary text-gray-600"
			>
				<Link href="/login" className="flex items-center gap-2">
					<ChevronLeft className="h-4 w-4" />
					Back to Login
				</Link>
			</Button>
		</div>
	);
}
