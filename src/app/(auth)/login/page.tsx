'use client';

import { <PERSON>, EyeOff } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useLogin } from '@/core/hooks/useLogin';

export default function LoginPage() {
	const [showPassword, setShowPassword] = useState(false);
	const [username, setUsername] = useState('');
	const [password, setPassword] = useState('');

	const { login, isLoading } = useLogin();

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();

		if (!username.trim()) {
			return;
		}

		if (!password.trim()) {
			return;
		}

		login({ username: username.trim(), password });
	};

	return (
		<div className="flex min-h-screen">
			{/* Left Section - Elena <PERSON> */}
			<div className="bg-elena-primary relative hidden items-center justify-center overflow-hidden lg:flex lg:w-1/2">
				{/* <PERSON> */}
				<div className="relative z-10 -translate-y-16">
					<img
						src="/elena.svg"
						alt="Elena - Your Clinic Assistant"
						className="h-32 w-auto"
					/>
				</div>

				{/* Elena Diagonal */}
				<div className="absolute bottom-0 left-0">
					<img className="h-96" src="/elena/elena-diagonal.png" alt="" />
				</div>
			</div>

			{/* Right Section - Login Form */}
			<div
				className="relative flex w-full items-center justify-center p-8 lg:w-1/2"
				style={{
					backgroundImage: 'url(/loginbg.svg)',
					backgroundSize: 'cover',
					backgroundPosition: 'center',
					backgroundRepeat: 'no-repeat',
				}}
			>
				{/* Mobile Logo */}
				<div className="absolute top-8 left-1/2 -translate-x-1/2 transform lg:hidden">
					<img src="/elena-banner.png" alt="Elena" className="h-12 w-auto" />
				</div>

				{/* Login Form Container */}
				<div className="mt-16 w-full max-w-md rounded-2xl bg-white/95 p-8 shadow-xl backdrop-blur-sm lg:mt-0">
					{/* Welcome Text */}
					<div className="mb-8 text-center">
						<h1 className="text-elena-primary mb-2 text-2xl font-bold">
							Welcome Back!
						</h1>
						<p className="text-sm text-gray-600">
							Sign in to access your account
						</p>
					</div>

					{/* Login Form */}
					<form onSubmit={handleSubmit} className="space-y-6">
						{/* Username Field */}
						<div className="space-y-2">
							<Label htmlFor="username" className="font-medium text-gray-700">
								Username
							</Label>
							<Input
								id="username"
								type="text"
								placeholder="Enter your username"
								value={username}
								onChange={(e) => setUsername(e.target.value)}
								className="focus:border-elena-primary focus:ring-elena-primary h-12 border-gray-300"
								required
							/>
						</div>

						{/* Password Field */}
						<div className="space-y-2">
							<Label htmlFor="password" className="font-medium text-gray-700">
								Password
							</Label>
							<div className="relative">
								<Input
									id="password"
									type={showPassword ? 'text' : 'password'}
									placeholder="Enter your password"
									value={password}
									onChange={(e) => setPassword(e.target.value)}
									className="focus:border-elena-primary focus:ring-elena-primary h-12 border-gray-300 pr-12"
									required
								/>
								<button
									type="button"
									onClick={() => setShowPassword(!showPassword)}
									className="absolute top-1/2 right-3 -translate-y-1/2 transform text-gray-500 hover:text-gray-700"
								>
									{showPassword ? (
										<EyeOff className="h-5 w-5" />
									) : (
										<Eye className="h-5 w-5" />
									)}
								</button>
							</div>
						</div>

						{/* Keep me logged in & Forgot password */}
						{/* <div className="flex items-center justify-between">
							<div className="flex items-center space-x-2">
								<Checkbox
									id="keep-logged-in"
									checked={keepLoggedIn}
									onCheckedChange={(checked) =>
										setKeepLoggedIn(checked === true)
									}
								/>
								<Label
									htmlFor="keep-logged-in"
									className="cursor-pointer text-sm text-gray-600"
								>
									Keep me logged in
								</Label>
							</div>
							<Link
								href="/forgot-password"
								className="text-elena-primary hover:text-elena-primary-dark text-sm font-medium"
							>
								Forgot password?
							</Link>
						</div> */}

						{/* Login Button */}
						<Button
							type="submit"
							disabled={isLoading}
							className="bg-elena-primary hover:bg-elena-primary-dark h-12 w-full rounded-lg font-medium text-white transition-colors disabled:opacity-50"
						>
							{isLoading ? 'Logging in...' : 'Log In'}
						</Button>

						{/* Divider */}
						<div className="relative my-6">
							<div className="absolute inset-0 flex items-center">
								<div className="w-full border-t border-gray-300" />
							</div>
							<div className="relative flex justify-center text-sm">
								<span className="bg-white px-4 text-gray-500">OR</span>
							</div>
						</div>

						{/* Register Link */}
						<div className="text-center">
							<span className="text-gray-600">
								Don&apos;t have an account?{' '}
							</span>
							<Link
								href="/register"
								className="text-elena-primary hover:text-elena-primary-dark font-medium"
							>
								REGISTER NOW!
							</Link>
						</div>
					</form>

					{/* Copyright */}
					<div className="mt-8 border-t border-gray-200 pt-6 text-center">
						<p className="text-xs text-gray-500">
							Copyright © {new Date().getFullYear()} ELENA. All Rights Reserved
						</p>
					</div>
				</div>
			</div>
		</div>
	);
}
