'use client';

import { AlertCircle, CheckCircle, Mail, XCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useQueryStates } from 'nuqs';
import { Suspense, useEffect, useRef } from 'react';

import Loading from '@/components/common/loading';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useEmailVerification } from '@/core/hooks/useEmailVerification';
import { searchParamParsers } from '@/core/lib/search-params';

function VerifyPageContent() {
	const router = useRouter();
	// URL search parameters using nuqs
	const [{ token, email }] = useQueryStates({
		token: searchParamParsers.token,
		email: searchParamParsers.email,
	});
	const hasVerified = useRef(false);

	const { verifyEmail, isLoading, isSuccess, isError, data, error } =
		useEmailVerification();

	// Automatically verify if token is present and we haven't verified yet
	useEffect(() => {
		if (token && !hasVerified.current) {
			hasVerified.current = true;
			verifyEmail({ token });
		}
	}, [token, verifyEmail]);

	const handleRetryVerification = () => {
		if (token) {
			verifyEmail({ token });
		}
	};

	const handleGoToLogin = () => {
		router.push('/login');
	};

	const handleGoToHome = () => {
		router.push('/');
	};

	// Loading state
	if (isLoading) {
		return (
			<div
				className="flex min-h-screen items-center justify-center"
				style={{
					backgroundImage: 'url(/loginbg.svg)',
					backgroundSize: 'cover',
					backgroundPosition: 'center',
					backgroundRepeat: 'no-repeat',
				}}
			>
				<Card className="mx-4 w-full max-w-md">
					<CardContent className="pt-6">
						<div className="flex flex-col items-center space-y-4">
							<div className="mb-4">
								<img
									src="/elena-banner.png"
									alt="Elena"
									className="h-12 w-auto"
								/>
							</div>
							<Loading className="size-8" />
							<div className="text-center">
								<h2 className="text-lg font-semibold text-gray-900">
									Verifying Your Email
								</h2>
								<p className="mt-1 text-sm text-gray-600">
									Please wait while we verify your email address...
								</p>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	// No token provided
	if (!token) {
		return (
			<div
				className="flex min-h-screen items-center justify-center"
				style={{
					backgroundImage: 'url(/loginbg.svg)',
					backgroundSize: 'cover',
					backgroundPosition: 'center',
					backgroundRepeat: 'no-repeat',
				}}
			>
				<Card className="mx-4 w-full max-w-md">
					<CardHeader className="text-center">
						<div className="mb-4">
							<img
								src="/elena-banner.png"
								alt="Elena"
								className="mx-auto h-12 w-auto"
							/>
						</div>
						<div className="mx-auto mb-4 flex size-12 items-center justify-center rounded-full bg-red-100">
							<XCircle className="size-6 text-red-600" />
						</div>
						<CardTitle className="text-xl font-semibold text-gray-900">
							Invalid Verification Link
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<Alert variant="destructive">
							<AlertCircle className="size-4" />
							<AlertDescription>
								The verification link is invalid or missing required parameters.
							</AlertDescription>
						</Alert>
						<div className="flex flex-col space-y-2">
							<Button onClick={handleGoToHome} className="w-full">
								Go to Home
							</Button>
							<Button
								variant="outline"
								onClick={handleGoToLogin}
								className="w-full"
							>
								Go to Login
							</Button>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	// Success state
	if (isSuccess && data) {
		const isAlreadyVerified = data.message.includes('already verified');

		return (
			<div
				className="flex min-h-screen items-center justify-center"
				style={{
					backgroundImage: 'url(/loginbg.svg)',
					backgroundSize: 'cover',
					backgroundPosition: 'center',
					backgroundRepeat: 'no-repeat',
				}}
			>
				<Card className="mx-4 w-full max-w-md">
					<CardHeader className="text-center">
						<div className="mb-4">
							<img
								src="/elena-banner.png"
								alt="Elena"
								className="mx-auto h-12 w-auto"
							/>
						</div>
						<div className="mx-auto mb-4 flex size-12 items-center justify-center rounded-full bg-green-100">
							<CheckCircle className="size-6 text-green-600" />
						</div>
						<CardTitle className="text-xl font-semibold text-gray-900">
							{isAlreadyVerified
								? 'Already Verified'
								: 'Email Verified Successfully'}
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<Alert>
							<CheckCircle className="size-4" />
							<AlertDescription>{data.message}</AlertDescription>
						</Alert>
						{email && (
							<div className="text-center text-sm text-gray-600">
								<Mail className="mr-1 inline size-4" />
								{email}
							</div>
						)}
						<div className="flex flex-col space-y-2">
							<Button onClick={handleGoToLogin} className="w-full">
								Continue to Login
							</Button>
							<Button
								variant="outline"
								onClick={handleGoToHome}
								className="w-full"
							>
								Go to Home
							</Button>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	// Error state
	if (isError || (data && data.status !== 1)) {
		const isTokenExpired = data?.status === 2;
		const errorMessage =
			data?.message ||
			(error as Error & { response?: { data?: { message?: string } } })
				?.response?.data?.message ||
			'An error occurred during verification.';

		return (
			<div
				className="flex min-h-screen items-center justify-center"
				style={{
					backgroundImage: 'url(/loginbg.svg)',
					backgroundSize: 'cover',
					backgroundPosition: 'center',
					backgroundRepeat: 'no-repeat',
				}}
			>
				<Card className="mx-4 w-full max-w-md">
					<CardHeader className="text-center">
						<div className="mb-4">
							<img
								src="/elena-banner.png"
								alt="Elena"
								className="mx-auto h-12 w-auto"
							/>
						</div>
						<div className="mx-auto mb-4 flex size-12 items-center justify-center rounded-full bg-red-100">
							<XCircle className="size-6 text-red-600" />
						</div>
						<CardTitle className="text-xl font-semibold text-gray-900">
							Verification Failed
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<Alert variant="destructive">
							<AlertCircle className="size-4" />
							<AlertDescription>{errorMessage}</AlertDescription>
						</Alert>
						{isTokenExpired && data?.data?.email && (
							<div className="text-center text-sm text-gray-600">
								<Mail className="mr-1 inline size-4" />
								{data.data.email}
							</div>
						)}
						<div className="flex flex-col space-y-2">
							{!isTokenExpired && (
								<Button
									onClick={handleRetryVerification}
									className="w-full"
									disabled={isLoading}
								>
									{isLoading ? <Loading className="mr-2 size-4" /> : null}
									Retry Verification
								</Button>
							)}
							<Button
								variant="outline"
								onClick={handleGoToLogin}
								className="w-full"
							>
								Go to Login
							</Button>
							<Button
								variant="ghost"
								onClick={handleGoToHome}
								className="w-full"
							>
								Go to Home
							</Button>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	// Fallback (should not reach here)
	return null;
}

export default function VerifyPage() {
	return (
		<Suspense
			fallback={
				<div
					className="flex min-h-screen items-center justify-center"
					style={{
						backgroundImage: 'url(/loginbg.svg)',
						backgroundSize: 'cover',
						backgroundPosition: 'center',
						backgroundRepeat: 'no-repeat',
					}}
				>
					<Card className="mx-4 w-full max-w-md">
						<CardContent className="pt-6">
							<div className="flex flex-col items-center space-y-4">
								<div className="mb-4">
									<img
										src="/elena-banner.png"
										alt="Elena"
										className="h-12 w-auto"
									/>
								</div>
								<Loading className="size-8" />
								<div className="text-center">
									<h2 className="text-lg font-semibold text-gray-900">
										Loading...
									</h2>
									<p className="mt-1 text-sm text-gray-600">
										Please wait while we load the verification page.
									</p>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>
			}
		>
			<VerifyPageContent />
		</Suspense>
	);
}
